#!/usr/bin/env python3
"""
🧪 DEEPLICA TERMINAL SEPARATION TEST
Demonstrates how each service gets its own terminal and CLI messages go to Watchdog.
"""

import sys
import time
import threading
from pathlib import Path

# Add project root to path
PROJECT_ROOT = Path(__file__).parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

from shared.terminal_manager import (
    get_terminal_manager, 
    log_cli_to_watchdog, 
    get_all_terminal_stats
)


def test_service_terminal(service_name: str, duration: int = 10):
    """Test a service terminal with sample messages"""
    terminal = get_terminal_manager(service_name)
    
    # Log startup
    terminal.log_terminal_message("STARTUP", f"🚀 Starting {service_name} terminal test")
    
    # Simulate service startup
    time.sleep(1)
    terminal.log_terminal_message("SYSTEM", "🔧 Initializing service components...")
    time.sleep(1)
    terminal.log_terminal_message("SYSTEM", "🔗 Connecting to dependencies...")
    time.sleep(1)
    
    # Log startup complete
    port = 8000 + hash(service_name) % 100  # Fake port for demo
    terminal.log_startup_complete(
        port=port,
        additional_info={
            "Version": "1.0.0",
            "Mode": "Production",
            "Dependencies": "All Connected"
        }
    )
    
    # Simulate running service with periodic health checks
    for i in range(duration):
        time.sleep(1)
        
        if i % 3 == 0:
            terminal.log_health_status(
                "HEALTHY",
                {
                    "Active Connections": i + 1,
                    "Memory Usage": f"{50 + i}MB",
                    "CPU Usage": f"{10 + i % 20}%"
                }
            )
        
        if i == 5:
            # Simulate a recoverable error
            terminal.log_error(
                "CONNECTION_TIMEOUT",
                "Database connection timeout",
                "Retrying with exponential backoff"
            )
            terminal.log_bulletproof_action(
                "AUTO_RECOVERY",
                "Database connection restored"
            )
    
    terminal.log_terminal_message("SHUTDOWN", f"✅ {service_name} terminal test completed")


def test_cli_watchdog_redirection():
    """Test CLI messages being redirected to Watchdog terminal"""
    print("\n🔄 Testing CLI to Watchdog message redirection...")
    
    # These messages should go to Watchdog terminal, not CLI terminal
    log_cli_to_watchdog("USER_INPUT", "User entered command: 'status'", "INFO")
    log_cli_to_watchdog("COMMAND_EXECUTION", "Executing status command", "INFO")
    log_cli_to_watchdog("RESULT", "Status: All services operational", "INFO")
    log_cli_to_watchdog("ERROR", "Invalid command entered", "WARNING", {"command": "invalid_cmd"})
    log_cli_to_watchdog("RECOVERY", "Showing help message to user", "INFO")
    
    print("✅ CLI messages sent to Watchdog terminal (check Watchdog console)")


def main():
    """Main test function"""
    print("🧪 DEEPLICA TERMINAL SEPARATION TEST")
    print("=" * 60)
    print("This test demonstrates:")
    print("1. Each service gets its own terminal with proper naming")
    print("2. CLI messages are redirected to Watchdog terminal")
    print("3. Terminal health monitoring and statistics")
    print("=" * 60)
    
    # Test different service terminals in parallel
    services = [
        "BACKEND-API",
        "PHONE-AGENT", 
        "DIALOGUE-AGENT",
        "WEB-CHAT"
    ]
    
    threads = []
    
    # Start service terminal tests
    for service in services:
        thread = threading.Thread(
            target=test_service_terminal,
            args=(service, 8),
            daemon=True
        )
        thread.start()
        threads.append(thread)
        time.sleep(0.5)  # Stagger startup
    
    # Test CLI redirection
    time.sleep(2)
    test_cli_watchdog_redirection()
    
    # Wait for service tests to complete
    for thread in threads:
        thread.join()
    
    # Show terminal statistics
    print("\n📊 TERMINAL STATISTICS:")
    print("=" * 40)
    stats = get_all_terminal_stats()
    for service_name, service_stats in stats.items():
        print(f"🖥️ {service_name}:")
        print(f"   Terminal ID: {service_stats['terminal_id']}")
        print(f"   PID: {service_stats['pid']}")
        print(f"   Uptime: {service_stats['uptime_seconds']:.1f}s")
        print(f"   Messages: {service_stats['message_count']}")
        print()
    
    print("✅ Terminal separation test completed!")
    print("\n💡 In VS Code:")
    print("   - Each service should appear in its own terminal tab")
    print("   - Terminal tabs should be named after the service")
    print("   - CLI messages should appear in Watchdog terminal")
    print("   - Use 'FULL DEEPLICA SYSTEM' compound launch for best results")


if __name__ == "__main__":
    main()
