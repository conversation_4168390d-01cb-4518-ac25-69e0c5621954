# 🚀 Separate Terminals Launch Guide

## 📋 **Overview**

Each Deeplica microservice now runs in its **own separate Terminal.app window** on macOS, giving you complete isolation and easy monitoring of each service.

## 🎯 **Launch Methods**

### **🚀 Method 1: Shell Script (Recommended)**

```bash
./launch_separate_terminals.sh
```

**✅ Benefits:**
- ✅ Each service gets its own Terminal.app window
- ✅ Proper startup sequencing with dependency waiting
- ✅ Named terminal windows for easy identification
- ✅ Automatic health checks and readiness verification
- ✅ Complete process isolation

### **🚀 Method 2: VS Code Launch Configuration**

1. Open VS Code Debug panel (`Cmd+Shift+D`)
2. Select `🚀 SEPARATE TERMINALS (Recommended)`
3. Click the green play button

### **🚀 Method 3: Python Script**

```bash
python3 launch_separate_terminals.py
```

## 📺 **Separate Terminal Windows**

Each service gets its own Terminal.app window with distinctive titles:

| **Service** | **Terminal Title** | **Process Name** | **Port** |
|-------------|-------------------|------------------|----------|
| **🌐 Backend API** | `🌐 DEEPLICA-BACKEND-API` | `DEEPLICA-BACKEND-API` | 8888 |
| **🎯 Dispatcher** | `🎯 DEEPLICA-DISPATCHER` | `DEEPLICA-DISPATCHER` | 8001 |
| **💬 Dialogue Agent** | `💬 DEEPLICA-DIALOGUE-AGENT` | `DEEPLICA-DIALOGUE-AGENT` | 8002 |
| **🧠 Planner Agent** | `🧠 DEEPLICA-PLANNER-AGENT` | `DEEPLICA-PLANNER-AGENT` | 8003 |
| **📞 Phone Agent** | `📞 DEEPLICA-PHONE-AGENT` | `DEEPLICA-PHONE-AGENT` | 8004 |
| **🖥️ CLI Terminal** | `🖥️ DEEPLICA-CLI-TERMINAL` | `DEEPLICA-CLI-TERMINAL` | - |

## 🔄 **Startup Sequence**

The launcher follows proper dependency order:

1. **🌐 Backend API** starts first
2. **⏳ Wait** for Backend API to be fully ready
3. **🎯 Dispatcher** starts (waits for backend)
4. **💬 Dialogue Agent** starts (waits for backend)
5. **🧠 Planner Agent** starts (waits for backend)
6. **📞 Phone Agent** starts (waits for backend)
7. **⏳ Wait** for all services to be ready
8. **🖥️ CLI Terminal** starts last

## 🔍 **Monitoring Services**

### **Individual Terminal Windows:**
- Each service has its own Terminal.app window
- Window titles clearly identify each service
- Real-time logs and output in each window
- Easy to spot errors or issues per service

### **System Process Monitoring:**
```bash
# View all Deeplica services
ps aux | grep DEEPLICA | grep -v grep

# View specific service
ps aux | grep DEEPLICA-PHONE-AGENT | grep -v grep

# Monitor resource usage
top -p $(pgrep DEEPLICA-BACKEND-API)
```

### **Service Health Checks:**
```bash
# Backend API
curl http://localhost:8000/api/v1/health

# Dispatcher
curl http://localhost:8001/health

# Dialogue Agent
curl http://localhost:8002/health

# Planner Agent
curl http://localhost:8003/health

# Phone Agent
curl http://localhost:8004/health
```

## 🛑 **Stopping Services**

### **Method 1: Close Terminal Windows**
- Simply close individual Terminal.app windows
- Each service stops when its terminal is closed

### **Method 2: Kill All Services**
```bash
# Kill all Deeplica services
pkill -f DEEPLICA

# Kill specific service
pkill DEEPLICA-PHONE-AGENT
```

### **Method 3: Kill by Process ID**
```bash
# Find process ID
ps aux | grep DEEPLICA-BACKEND-API | grep -v grep

# Kill by PID
kill <PID>
```

## 🛡️ **Phone Agent Protection**

The Phone Agent terminal includes all error prevention measures:
- ✅ **Error calls disabled** (`DISABLE_ERROR_CALLS=true`)
- ✅ **Circuit breaker** - Automatic failure detection
- ✅ **Health checks** - System stability monitoring
- ✅ **Preventive abortion** - Stops calls during system issues
- ✅ **Silent error handling** - No more error calls to users

## 🔧 **Troubleshooting**

### **If Services Don't Start:**
1. Check individual Terminal windows for error messages
2. Verify ports are not already in use: `lsof -i :8000`
3. Check Python environment: `which python3`
4. Verify dependencies: `pip3 list`

### **If Backend Doesn't Start:**
1. Check MongoDB Atlas connection
2. Verify environment variables
3. Check port 8000 availability

### **If Terminal Windows Don't Open:**
1. Ensure Terminal.app has proper permissions
2. Try running script with `bash launch_separate_terminals.sh`
3. Check macOS security settings

## 🎯 **Advantages of Separate Terminals**

### **🔍 Easy Debugging:**
- Each service has isolated logs
- Clear error identification per service
- No mixed output from multiple services

### **🎛️ Individual Control:**
- Start/stop services independently
- Restart only problematic services
- Monitor resource usage per service

### **📊 Professional Monitoring:**
- Named terminal windows for easy identification
- Process names visible in system monitoring
- Clear separation of concerns

### **🛡️ Error Isolation:**
- Service crashes don't affect terminal output of other services
- Easy to identify which service has issues
- Independent debugging per service

## 🎉 **Result**

You now have:
- ✅ **6 separate Terminal.app windows** - one for each microservice
- ✅ **Named terminal titles** - easy identification
- ✅ **Distinctive process names** - clear system monitoring
- ✅ **Proper startup sequencing** - dependency management
- ✅ **Complete error prevention** - phone agent protection
- ✅ **Professional development environment** - isolated service monitoring

**Each Deeplica microservice now runs in its own dedicated terminal window for maximum clarity and control!** 🚀
