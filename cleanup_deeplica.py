#!/usr/bin/env python3
"""
🧹 BULLETPROOF DEEPLICA CLEANUP SCRIPT

This script performs comprehensive cleanup of all Deeplica processes, ports, 
and residual state before starting the system. It ensures a clean slate for startup.

Features:
- Kills all Deeplica processes safely
- Frees all Deeplica ports (8000-8006)
- Clears terminal state
- Removes temporary files
- Verifies complete cleanup
- Never crashes or affects VS Code
"""

import os
import sys
import time
import signal
import subprocess
import socket
import psutil
from pathlib import Path
from typing import List, Set
import json
from shared.port_manager import get_service_port

class BulletproofDeepplicaCleanup:
    """Comprehensive cleanup system for Deeplica microservices"""
    
    def __init__(self):
        self.cleanup_name = "DEEPLICA-CLEANUP"
        
        # All Deeplica ports
        self.deeplica_ports = [get_service_port("backend"), get_service_port("dispatcher"), get_service_port("dialogue"), get_service_port("planner"), get_service_port("phone"), get_service_port("watchdog"), get_service_port("web-chat"), get_service_port("cli")]
        
        # Deeplica process patterns
        self.deeplica_patterns = [
            "DEEPLICA-BACKEND-API",
            "DEEPLICA-DISPATCHER", 
            "DEEPLICA-DIALOGUE-AGENT",
            "DEEPLICA-PLANNER-AGENT",
            "DEEPLICA-PHONE-AGENT",
            "DEEPLICA-CLI-TERMINAL",
            "DEEPLICA-WATCHDOG",

            "backend/app/main.py",
            "dispatcher/app/main.py",
            "agents/dialogue/app/main.py",
            "agents/planner/app/main.py", 
            "agents/phone/app/main.py",
            "cli/main.py",
            "watchdog/main.py",
            ""
        ]
        
        self.killed_processes: Set[int] = set()
        self.freed_ports: Set[int] = set()
        
    def log(self, level: str, message: str):
        """Safe logging that never crashes"""
        try:
            timestamp = time.strftime("%H:%M:%S")
            icon = {"INFO": "ℹ️", "SUCCESS": "✅", "WARNING": "⚠️", "ERROR": "❌"}.get(level, "📝")
            print(f"{timestamp} {icon} [{self.cleanup_name}] {message}")
        except:
            pass
    
    def is_port_in_use(self, port: int) -> bool:
        """Check if a port is in use"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                result = s.connect_ex(('localhost', port))
                return result == 0  # Port is in use if connection succeeds
        except:
            return False
    
    def kill_process_by_pid(self, pid: int, name: str = "unknown") -> bool:
        """Safely kill a process by PID"""
        if pid in self.killed_processes:
            return True
            
        try:
            process = psutil.Process(pid)
            process_name = process.name()
            
            # Never kill VS Code or system processes
            if any(forbidden in process_name.lower() for forbidden in 
                   ['code', 'visual studio', 'finder', 'dock', 'systemui']):
                self.log("WARNING", f"Skipping protected process {pid} ({process_name})")
                return False
            
            # Try graceful termination first
            try:
                process.terminate()
                process.wait(timeout=3)
                self.log("SUCCESS", f"Gracefully terminated {pid} ({name})")
                self.killed_processes.add(pid)
                return True
            except psutil.TimeoutExpired:
                # Force kill if graceful termination fails
                process.kill()
                process.wait(timeout=2)
                self.log("SUCCESS", f"Force killed {pid} ({name})")
                self.killed_processes.add(pid)
                return True
                
        except psutil.NoSuchProcess:
            self.log("INFO", f"Process {pid} ({name}) no longer exists")
            self.killed_processes.add(pid)
            return True
        except psutil.AccessDenied:
            self.log("WARNING", f"Access denied to process {pid} ({name})")
            return False
        except Exception as e:
            self.log("ERROR", f"Error killing {pid} ({name}): {e}")
            return False
    
    def free_port(self, port: int) -> bool:
        """Free a specific port by killing processes using it"""
        if port in self.freed_ports:
            return True
            
        try:
            killed_any = False
            
            # Method 1: Use psutil to find processes
            for proc in psutil.process_iter(['pid', 'name', 'connections']):
                try:
                    connections = proc.info['connections']
                    if connections:
                        for conn in connections:
                            if (hasattr(conn, 'laddr') and conn.laddr and 
                                conn.laddr.port == port):
                                if self.kill_process_by_pid(proc.info['pid'], f"port-{port}"):
                                    killed_any = True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # Method 2: Use lsof as backup
            try:
                result = subprocess.run(['lsof', '-ti', f':{port}'], 
                                      capture_output=True, text=True, timeout=5)
                if result.stdout.strip():
                    pids = result.stdout.strip().split('\n')
                    for pid_str in pids:
                        try:
                            pid = int(pid_str.strip())
                            if self.kill_process_by_pid(pid, f"port-{port}"):
                                killed_any = True
                        except ValueError:
                            continue
            except (subprocess.TimeoutExpired, FileNotFoundError):
                pass
            
            self.freed_ports.add(port)
            if killed_any:
                self.log("SUCCESS", f"Port {port} freed")
            else:
                self.log("INFO", f"Port {port} was already free")
            return True
            
        except Exception as e:
            self.log("ERROR", f"Error freeing port {port}: {e}")
            return False
    
    def kill_deeplica_processes(self) -> bool:
        """Kill all Deeplica processes"""
        self.log("INFO", "Killing all Deeplica processes...")
        
        killed_count = 0
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    pid = proc.info['pid']
                    name = proc.info['name']
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    
                    # Check if this is a Deeplica process
                    is_deeplica = False
                    for pattern in self.deeplica_patterns:
                        if pattern in cmdline or pattern in name:
                            is_deeplica = True
                            break
                    
                    if is_deeplica:
                        if self.kill_process_by_pid(pid, name):
                            killed_count += 1
                            
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            self.log("SUCCESS", f"Killed {killed_count} Deeplica processes")
            return True
            
        except Exception as e:
            self.log("ERROR", f"Error killing Deeplica processes: {e}")
            return False
    
    def free_all_ports(self) -> bool:
        """Free all Deeplica ports"""
        self.log("INFO", "Freeing all Deeplica ports...")
        
        success_count = 0
        for port in self.deeplica_ports:
            if self.free_port(port):
                success_count += 1
        
        self.log("SUCCESS", f"Freed {success_count}/{len(self.deeplica_ports)} ports")
        return success_count == len(self.deeplica_ports)
    
    def clear_temporary_files(self) -> bool:
        """Clear temporary files and state"""
        self.log("INFO", "Clearing temporary files...")
        
        try:
            temp_patterns = [
                "*.pid",
                "*.lock",
                ".deeplica_stop",
                "stop_deeplica_service/registered_processes.json"
            ]
            
            cleared_count = 0
            for pattern in temp_patterns:
                try:
                    import glob
                    files = glob.glob(pattern)
                    for file_path in files:
                        try:
                            os.remove(file_path)
                            cleared_count += 1
                        except:
                            pass
                except:
                    pass
            
            self.log("SUCCESS", f"Cleared {cleared_count} temporary files")
            return True
            
        except Exception as e:
            self.log("ERROR", f"Error clearing temporary files: {e}")
            return False
    
    def verify_cleanup(self) -> bool:
        """Verify that cleanup was successful"""
        self.log("INFO", "Verifying cleanup...")
        
        # Check ports
        ports_in_use = []
        for port in self.deeplica_ports:
            if self.is_port_in_use(port):
                ports_in_use.append(port)
        
        # Check processes
        remaining_processes = []
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    for pattern in self.deeplica_patterns:
                        if pattern in cmdline:
                            remaining_processes.append(proc.info['pid'])
                            break
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except:
            pass
        
        if ports_in_use:
            self.log("WARNING", f"Ports still in use: {ports_in_use}")
        if remaining_processes:
            self.log("WARNING", f"Processes still running: {remaining_processes}")
        
        success = len(ports_in_use) == 0 and len(remaining_processes) == 0
        
        if success:
            self.log("SUCCESS", "Cleanup verification passed - system is clean")
        else:
            self.log("WARNING", "Cleanup verification found remaining resources")
        
        return success
    
    def execute_full_cleanup(self) -> bool:
        """Execute complete cleanup sequence"""
        self.log("INFO", "🧹 Starting bulletproof Deeplica cleanup...")
        
        success_count = 0
        total_operations = 4
        
        # 1. Kill processes
        if self.kill_deeplica_processes():
            success_count += 1
        
        # 2. Free ports
        if self.free_all_ports():
            success_count += 1
        
        # 3. Clear temporary files
        if self.clear_temporary_files():
            success_count += 1
        
        # 4. Verify cleanup
        if self.verify_cleanup():
            success_count += 1
        
        success = success_count >= 3  # Allow one operation to fail
        
        if success:
            self.log("SUCCESS", f"🎉 Cleanup completed successfully ({success_count}/{total_operations})")
        else:
            self.log("WARNING", f"⚠️ Cleanup completed with issues ({success_count}/{total_operations})")
        
        return success

def main():
    """Main cleanup function"""
    cleanup = BulletproofDeepplicaCleanup()
    
    try:
        success = cleanup.execute_full_cleanup()
        
        if success:
            print(f"\n✅ Deeplica cleanup completed successfully!")
            print(f"🚀 System is ready for fresh startup")
        else:
            print(f"\n⚠️ Cleanup completed with some issues")
            print(f"🔧 Manual intervention may be required")
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print(f"\n🛑 Cleanup cancelled by user")
        return 0
    except Exception as e:
        print(f"\n❌ Cleanup failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
