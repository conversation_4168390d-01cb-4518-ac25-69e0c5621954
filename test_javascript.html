<!DOCTYPE html>
<html>
<head>
    <title>JavaScript Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #1a1a1a; 
            color: #00ff00; 
        }
        button { 
            padding: 10px 20px; 
            margin: 5px; 
            background: #00ff00;
            color: #000;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #00cc00;
        }
        .output {
            margin-top: 10px;
            padding: 10px;
            background: #333;
            border-left: 3px solid #00ff00;
        }
    </style>
</head>
<body>
    <h1>🔧 Simple JavaScript Test</h1>
    <p>This tests if JavaScript works at all.</p>
    
    <button onclick="testFunction()">Click Me!</button>
    <div id="output" class="output">No clicks yet...</div>
    
    <script>
        console.log('🔧 JavaScript is loading...');
        
        function testFunction() {
            console.log('✅ Button clicked!');
            document.getElementById('output').innerHTML = '✅ JavaScript is working!';
            alert('JavaScript is working!');
        }
        
        console.log('✅ JavaScript loaded successfully');
        
        // Test immediate execution
        document.getElementById('output').innerHTML = '🔧 JavaScript loaded and ready';
    </script>
</body>
</html>
