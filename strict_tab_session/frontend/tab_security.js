/**
 * 🔐 STRICT SINGLE-TAB SESSION ENFORCEMENT - Frontend Security Module
 * 
 * This module implements cryptographically secure tab identification and
 * strict single-tab session enforcement to prevent multi-tab session abuse.
 * 
 * Security Features:
 * - Cryptographically secure UUIDv4 generation
 * - Tab-scoped identifier storage using window.name
 * - Cross-tab communication via BroadcastChannel
 * - Automatic session invalidation on multi-tab detection
 * - Browser compatibility handling
 * 
 * <AUTHOR> Security Team
 * @version 1.0.0
 * @date 2025-07-11
 */

class StrictTabSessionManager {
    constructor() {
        this.tabId = null;
        this.sessionToken = null;
        this.broadcastChannel = null;
        this.heartbeatInterval = null;
        this.validationInterval = null;
        this.isInitialized = false;
        
        // Security configuration
        this.config = {
            heartbeatIntervalMs: 5000,      // 5 seconds
            validationIntervalMs: 10000,    // 10 seconds
            sessionTimeoutMs: 1800000,      // 30 minutes
            maxRetries: 3,
            broadcastChannelName: 'strict_tab_session_channel'
        };
        
        this.initialize();
    }

    /**
     * Initialize the strict tab session manager
     */
    initialize() {
        try {
            console.log('🔐 Initializing Strict Tab Session Manager...');
            
            // Generate or retrieve tab ID
            this.tabId = this.getOrCreateTabId();
            
            // Initialize cross-tab communication
            this.initializeBroadcastChannel();
            
            // Check for existing sessions in other tabs
            this.checkForExistingSessions();
            
            // Handle browser-specific edge cases
            this.handleBrowserEdgeCases();

            // Start monitoring
            this.startHeartbeat();
            this.startValidation();

            this.isInitialized = true;
            console.log('✅ Strict Tab Session Manager initialized successfully');
            console.log('🔑 Tab ID:', this.tabId.substring(0, 8) + '...');
            console.log('🛡️ Enhanced multi-tab detection active');
            
        } catch (error) {
            console.error('❌ Failed to initialize Strict Tab Session Manager:', error);
            this.handleSecurityViolation('Initialization failure');
        }
    }

    /**
     * Generate or retrieve cryptographically secure Tab ID
     * Uses window.name for tab-scoped persistence
     */
    getOrCreateTabId() {
        try {
            // Check if Tab ID already exists in window.name
            if (window.name && this.isValidTabId(window.name)) {
                console.log('🔄 Retrieved existing Tab ID from window.name');
                return window.name;
            }
            
            // Generate new cryptographically secure UUIDv4
            const newTabId = this.generateSecureUUID();
            
            // Store in window.name for tab-scoped persistence
            window.name = newTabId;
            
            console.log('🆕 Generated new cryptographically secure Tab ID');
            return newTabId;
            
        } catch (error) {
            console.error('❌ Failed to generate Tab ID:', error);
            throw new Error('Tab ID generation failed');
        }
    }

    /**
     * Generate cryptographically secure UUIDv4
     * Uses Web Crypto API for maximum security
     */
    generateSecureUUID() {
        try {
            // Use Web Crypto API for cryptographically secure random values
            const array = new Uint8Array(16);
            crypto.getRandomValues(array);
            
            // Set version (4) and variant bits according to RFC 4122
            array[6] = (array[6] & 0x0f) | 0x40; // Version 4
            array[8] = (array[8] & 0x3f) | 0x80; // Variant 10
            
            // Convert to UUID string format
            const hex = Array.from(array, byte => 
                byte.toString(16).padStart(2, '0')).join('');
            
            return [
                hex.substring(0, 8),
                hex.substring(8, 12),
                hex.substring(12, 16),
                hex.substring(16, 20),
                hex.substring(20, 32)
            ].join('-');
            
        } catch (error) {
            console.error('❌ Crypto API failed, using fallback UUID generation');
            return this.generateFallbackUUID();
        }
    }

    /**
     * Fallback UUID generation for environments without Web Crypto API
     */
    generateFallbackUUID() {
        // High-quality fallback using Math.random() with timestamp
        const timestamp = Date.now().toString(16);
        const random1 = Math.random().toString(16).substring(2);
        const random2 = Math.random().toString(16).substring(2);
        const random3 = Math.random().toString(16).substring(2);
        
        return `${timestamp.substring(0, 8)}-${random1.substring(0, 4)}-4${random2.substring(0, 3)}-8${random3.substring(0, 3)}-${timestamp}${random1}`.substring(0, 36);
    }

    /**
     * Validate Tab ID format (UUIDv4)
     */
    isValidTabId(tabId) {
        if (!tabId || typeof tabId !== 'string') return false;
        
        // UUIDv4 regex pattern
        const uuidv4Regex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        return uuidv4Regex.test(tabId);
    }

    /**
     * Initialize BroadcastChannel for cross-tab communication
     */
    initializeBroadcastChannel() {
        try {
            if (typeof BroadcastChannel !== 'undefined') {
                this.broadcastChannel = new BroadcastChannel(this.config.broadcastChannelName);
                this.broadcastChannel.onmessage = (event) => this.handleBroadcastMessage(event);
                console.log('📡 BroadcastChannel initialized for cross-tab communication');
            } else {
                console.warn('⚠️ BroadcastChannel not supported, using fallback detection');
            }
        } catch (error) {
            console.error('❌ Failed to initialize BroadcastChannel:', error);
        }
    }

    /**
     * Check for existing sessions in other tabs with enhanced detection
     */
    checkForExistingSessions() {
        if (this.broadcastChannel) {
            // Enhanced presence check
            this.performEnhancedPresenceCheck();

            // Wait for responses and handle edge cases
            setTimeout(() => {
                console.log('🔍 Completed enhanced presence check for existing sessions');

                // Additional check using storage events (fallback)
                this.checkStorageBasedSessions();
            }, 1000);
        } else {
            // Fallback for browsers without BroadcastChannel
            this.checkStorageBasedSessions();
        }
    }

    /**
     * Fallback session detection using storage events
     */
    checkStorageBasedSessions() {
        try {
            // Use a temporary storage key to detect other tabs
            const detectionKey = 'tab_detection_' + Date.now();
            const detectionValue = this.tabId;

            // Listen for storage changes
            const storageListener = (event) => {
                if (event.key === detectionKey && event.newValue !== detectionValue) {
                    console.warn('🚨 STORAGE-BASED DETECTION: Another tab detected!');
                    this.handleMultiTabDetection(event.newValue);
                    window.removeEventListener('storage', storageListener);
                }
            };

            window.addEventListener('storage', storageListener);

            // Set our detection value
            localStorage.setItem(detectionKey, detectionValue);

            // Clean up after a short time
            setTimeout(() => {
                localStorage.removeItem(detectionKey);
                window.removeEventListener('storage', storageListener);
            }, 3000);

        } catch (error) {
            console.warn('⚠️ Storage-based detection failed:', error);
        }
    }

    /**
     * Handle browser-specific edge cases
     */
    handleBrowserEdgeCases() {
        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible' && this.sessionToken) {
                // Tab became visible - check for conflicts
                this.performEnhancedPresenceCheck();
            }
        });

        // Handle focus events
        window.addEventListener('focus', () => {
            if (this.sessionToken) {
                // Window gained focus - verify session integrity
                setTimeout(() => this.validateSession(), 100);
            }
        });

        // Handle page freeze/resume (mobile browsers)
        document.addEventListener('freeze', () => {
            console.log('📱 Page frozen - pausing session monitoring');
            this.pauseMonitoring();
        });

        document.addEventListener('resume', () => {
            console.log('📱 Page resumed - resuming session monitoring');
            this.resumeMonitoring();
        });

        // Handle connection changes
        window.addEventListener('online', () => {
            console.log('🌐 Connection restored - validating session');
            if (this.sessionToken) {
                this.validateSession();
            }
        });

        window.addEventListener('offline', () => {
            console.log('📡 Connection lost - session validation paused');
        });
    }

    /**
     * Pause monitoring (for mobile freeze events)
     */
    pauseMonitoring() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }

        if (this.validationInterval) {
            clearInterval(this.validationInterval);
            this.validationInterval = null;
        }
    }

    /**
     * Resume monitoring (for mobile resume events)
     */
    resumeMonitoring() {
        if (!this.heartbeatInterval && this.sessionToken) {
            this.startHeartbeat();
        }

        if (!this.validationInterval && this.sessionToken) {
            this.startValidation();
        }
    }

    /**
     * Handle cross-tab broadcast messages
     */
    handleBroadcastMessage(event) {
        const { type, tabId, timestamp, sessionToken, challenge, response } = event.data;

        switch (type) {
            case 'presence_check':
                if (tabId !== this.tabId) {
                    console.warn('🚨 SECURITY VIOLATION: Another tab detected!');

                    // Respond with our presence
                    this.broadcastChannel.postMessage({
                        type: 'presence_response',
                        tabId: this.tabId,
                        sessionToken: this.sessionToken,
                        timestamp: Date.now()
                    });

                    this.handleMultiTabDetection(tabId);
                }
                break;

            case 'presence_response':
                if (tabId !== this.tabId) {
                    console.warn('🚨 SECURITY VIOLATION: Another tab responded to presence check!');
                    this.handleMultiTabDetection(tabId);
                }
                break;

            case 'session_active':
                if (tabId !== this.tabId) {
                    console.warn('🚨 SECURITY VIOLATION: Active session in another tab!');
                    this.handleSecurityViolation('Multi-tab session detected');
                }
                break;

            case 'session_terminated':
                if (tabId !== this.tabId) {
                    console.log('📢 Received session termination notification from another tab');
                }
                break;

            case 'challenge_request':
                if (tabId !== this.tabId) {
                    // Another tab is challenging our legitimacy
                    this.handleChallengeRequest(challenge, tabId);
                }
                break;

            case 'challenge_response':
                if (tabId !== this.tabId) {
                    // Another tab responded to our challenge
                    this.handleChallengeResponse(response, tabId);
                }
                break;
        }
    }

    /**
     * Handle challenge request from another tab
     */
    handleChallengeRequest(challenge, challengerTabId) {
        console.warn('🔍 Received challenge request from another tab');

        // If we have a valid session, respond to the challenge
        if (this.sessionToken) {
            const response = this.generateChallengeResponse(challenge);

            this.broadcastChannel.postMessage({
                type: 'challenge_response',
                tabId: this.tabId,
                response: response,
                timestamp: Date.now()
            });

            // Also report this as a security violation
            this.handleSecurityViolation('Challenge received from another tab');
        }
    }

    /**
     * Handle challenge response from another tab
     */
    handleChallengeResponse(response, responderTabId) {
        console.warn('🚨 Another tab responded to challenge - multiple sessions detected!');
        this.handleSecurityViolation('Multiple tabs with valid sessions detected');
    }

    /**
     * Generate challenge response
     */
    generateChallengeResponse(challenge) {
        // Simple challenge-response mechanism
        return btoa(challenge + this.tabId + this.sessionToken).substring(0, 32);
    }

    /**
     * Handle multi-tab detection with enhanced verification
     */
    handleMultiTabDetection(otherTabId) {
        console.error('🚨 MULTI-TAB VIOLATION: Another tab with session detected');
        console.error('🔍 Other Tab ID:', otherTabId.substring(0, 8) + '...');
        console.error('🔍 Current Tab ID:', this.tabId.substring(0, 8) + '...');

        // Send challenge to verify if the other tab has a valid session
        if (this.sessionToken && this.broadcastChannel) {
            const challenge = this.generateSecureChallenge();

            this.broadcastChannel.postMessage({
                type: 'challenge_request',
                tabId: this.tabId,
                challenge: challenge,
                timestamp: Date.now()
            });

            // Wait for response, then invalidate if we get one
            setTimeout(() => {
                console.warn('🔒 Challenge timeout - proceeding with session invalidation');
                this.invalidateSession('Multi-tab session violation detected');
            }, 2000);
        } else {
            // Immediately invalidate if we don't have a session
            this.invalidateSession('Multi-tab session violation detected');
        }
    }

    /**
     * Generate secure challenge for tab verification
     */
    generateSecureChallenge() {
        const array = new Uint8Array(16);
        crypto.getRandomValues(array);
        return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    }

    /**
     * Enhanced presence check with periodic verification
     */
    performEnhancedPresenceCheck() {
        if (this.broadcastChannel && this.sessionToken) {
            // Send presence check with session info
            this.broadcastChannel.postMessage({
                type: 'presence_check',
                tabId: this.tabId,
                sessionToken: this.sessionToken ? 'present' : 'none',
                timestamp: Date.now()
            });

            // Also send a challenge to any existing tabs
            const challenge = this.generateSecureChallenge();
            this.broadcastChannel.postMessage({
                type: 'challenge_request',
                tabId: this.tabId,
                challenge: challenge,
                timestamp: Date.now()
            });
        }
    }

    /**
     * Start heartbeat to maintain session
     */
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.sessionToken && this.broadcastChannel) {
                this.broadcastChannel.postMessage({
                    type: 'session_active',
                    tabId: this.tabId,
                    timestamp: Date.now()
                });
            }
        }, this.config.heartbeatIntervalMs);
    }

    /**
     * Start session validation
     */
    startValidation() {
        this.validationInterval = setInterval(() => {
            if (this.sessionToken) {
                this.validateSession();
            }
        }, this.config.validationIntervalMs);
    }

    /**
     * Validate session with server
     */
    async validateSession() {
        try {
            const response = await fetch('/api/validate-session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Tab-ID': this.tabId,
                    'X-Session-Token': this.sessionToken,
                    'X-CSRF-Token': this.getCsrfToken()
                },
                credentials: 'same-origin'
            });

            if (!response.ok) {
                throw new Error(`Session validation failed: ${response.status}`);
            }

            const result = await response.json();
            
            if (!result.valid) {
                console.warn('❌ Session validation failed:', result.reason);
                this.handleSecurityViolation(result.reason);
            }
            
        } catch (error) {
            console.error('❌ Session validation error:', error);
            this.handleSecurityViolation('Session validation failed');
        }
    }

    /**
     * Handle security violations
     */
    handleSecurityViolation(reason) {
        console.error('🚨 SECURITY VIOLATION:', reason);
        
        // Broadcast termination to other tabs
        if (this.broadcastChannel) {
            this.broadcastChannel.postMessage({
                type: 'session_terminated',
                tabId: this.tabId,
                reason: reason,
                timestamp: Date.now()
            });
        }
        
        // Invalidate session
        this.invalidateSession(reason);
    }

    /**
     * Invalidate current session
     */
    invalidateSession(reason) {
        console.warn('🔒 Invalidating session:', reason);
        
        // Clear session data
        this.sessionToken = null;
        
        // Stop monitoring
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
        
        if (this.validationInterval) {
            clearInterval(this.validationInterval);
            this.validationInterval = null;
        }
        
        // Show security message and redirect
        this.showSecurityMessage(reason);
    }

    /**
     * Show security violation message
     */
    showSecurityMessage(reason) {
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0, 0, 0, 0.9); z-index: 10000;
            display: flex; align-items: center; justify-content: center;
        `;
        
        overlay.innerHTML = `
            <div style="
                background: linear-gradient(135deg, #1a1a2e, #16213e);
                border: 2px solid #ff4757; border-radius: 15px; padding: 40px;
                max-width: 600px; text-align: center;
                box-shadow: 0 20px 60px rgba(255, 71, 87, 0.3);
            ">
                <div style="color: #ff4757; font-size: 28px; margin-bottom: 20px;">
                    🚨 SECURITY ALERT
                </div>
                <div style="color: white; margin-bottom: 25px; line-height: 1.6; font-size: 16px;">
                    <strong>For your own safety and privacy, simultaneous sessions are not allowed.</strong><br><br>
                    Reason: ${reason}<br><br>
                    Please authenticate again to continue.
                </div>
                <button onclick="window.location.href='/login'" style="
                    background: linear-gradient(45deg, #ff4757, #ff3742);
                    color: white; border: none; padding: 15px 30px;
                    border-radius: 8px; cursor: pointer; font-weight: bold;
                    font-size: 16px; transition: all 0.3s ease;
                ">
                    🔐 Authenticate Again
                </button>
            </div>
        `;
        
        document.body.appendChild(overlay);
        
        // Auto-redirect after 10 seconds
        setTimeout(() => {
            window.location.href = '/login';
        }, 10000);
    }

    /**
     * Get CSRF token from meta tag or cookie
     */
    getCsrfToken() {
        // Try meta tag first
        const metaTag = document.querySelector('meta[name="csrf-token"]');
        if (metaTag) {
            return metaTag.getAttribute('content');
        }
        
        // Fallback to cookie
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrf_token') {
                return decodeURIComponent(value);
            }
        }
        
        return null;
    }

    /**
     * Get Tab ID for use in requests
     */
    getTabId() {
        return this.tabId;
    }

    /**
     * Set session token after successful login
     */
    setSessionToken(token) {
        this.sessionToken = token;
        console.log('🔑 Session token set for tab:', this.tabId.substring(0, 8) + '...');
    }

    /**
     * Clean up resources
     */
    destroy() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
        }
        
        if (this.validationInterval) {
            clearInterval(this.validationInterval);
        }
        
        if (this.broadcastChannel) {
            this.broadcastChannel.close();
        }
        
        console.log('🧹 Strict Tab Session Manager destroyed');
    }
}

// Global instance
let strictTabSessionManager = null;

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', function() {
    strictTabSessionManager = new StrictTabSessionManager();
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (strictTabSessionManager) {
        strictTabSessionManager.destroy();
    }
});

// Export for use in other scripts
window.StrictTabSessionManager = StrictTabSessionManager;
window.getStrictTabSessionManager = () => strictTabSessionManager;
