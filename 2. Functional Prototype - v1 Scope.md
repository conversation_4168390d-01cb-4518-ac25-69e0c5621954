### **🧩 v1 Scope**

**Goal:** Microservices-based AI mission orchestration system with terminal interface, phone calling capabilities,
 and comprehensive task execution through specialized agents.

---

### **✅ In Scope**

| Component | Purpose |
| ----- | ----- |
| **Terminal UI** | Enhanced chat-style CLI with session management and mission tracking |
| **Backend API** | REST API gateway for mission management and user interface |
| **Dispatcher Service** | Mission orchestration and agent coordination microservice |
| **Planner Agent** | Converts user input to task graphs (microservice) |
| **Dialogue Agent** | Executes dialogue tasks and user communication (microservice) |
| **Phone Agent** | Makes phone calls with LLM-based conversation handling (microservice) |
| **MongoDB** | Stores missions and tasks in separate collections with indexing |
| **Shared Models** | Unified data models across all microservices |
| **LLM Service** | Abstraction layer over Gemini API with fallback support |
| **Twilio Integration** | Voice calling with Hebrew/English language support |
| **Logging** | Comprehensive logging across all services and interactions |

---

### **🚫 Not Included (v1)**

| Item |
| ----- |
| **Frontend UI (Web/App)** |
| **Email Agent** |
| **Voice Input/Output** (beyond phone calls) |
| **User authentication or identification** |
| **User memory or long-term data** |
| **Prompt Engineering Agent** |
| **Guardian Agent** |
| **Parallel tasks** |
| **Conditional tasks** |
| **Cloud deployment** (local development only) |

---

### **🔧 Tech Stack**

| Layer | Tech |
| ----- | ----- |
| Terminal | Python CLI with prompt_toolkit |
| Microservices | FastAPI (Python) - Backend, Dispatcher, Agents |
| Agents | Planner, Dialogue, Phone (Gemini API) |
| Voice | Twilio Voice API with multilingual TTS/STT |
| Storage | MongoDB Atlas (missions & tasks collections) |
| Communication | HTTP REST APIs between microservices |
| Models | Shared Pydantic models package |
| Logging | Loguru across all services |

---

### **🏗️ Architecture**

```
                    ┌─────────────────┐
                    │   Terminal UI   │
                    │                 │
                    │ • Chat Interface│
                    │ • Session Mgmt  │
                    │ • Commands      │
                    └─────────┬───────┘
                              │ HTTP
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Backend API (Port 8888)                    │
│                                                                 │
│ • REST API Gateway     • Mission CRUD                           │
│ • Request Validation   • Error Handling                         │
└─────────────────────┬───────────────────────────────────────────┘
                      │ HTTP
                      ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Dispatcher (Port 8001)                       │
│                                                                 │
│ • Mission Orchestration    • Task Routing     • Agent Callbacks │
│ • Execution Flow Control   • State Management                   │
└─────────┬───────────────────────────────────────────────┬───────┘
          │ HTTP                                          │
          ▼                                               ▼
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│ Dialogue Agent  │  │ Planner Agent   │  │  Phone Agent    │
│   (Port 8002)   │  │   (Port 8003)   │  │   (Port 8004)   │
│                 │  │                 │  │                 │
│ • User Interact │  │ • Task Graphs   │  │ • Twilio Calls  │
│ • Conversations │  │ • Mission Plans │  │ • LLM Convos    │
│ • LLM Responses │  │ • Dependencies  │  │ • Multi-lang    │
└─────────┬───────┘  └─────────┬───────┘  └─────────┬───────┘
          │                    │                    │
          └────────────────────┼────────────────────┘
                               │
                               ▼
┌─────────────────────────────────────────────────────────────────┐
│                     Shared Database (MongoDB)                   │
│                                                                 │
│ Collections:                                                    │
│ • missions (metadata, state)                                    │
│ • tasks (work units, deps)                                      │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘

External Integrations:
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Gemini API    │    │   Twilio API    │    │ Shared Models   │
│                 │    │                 │    │                 │
│ • LLM Responses │    │ • Voice Calls   │    │                 │
│ • Task Planning │    │ • TTS/STT       │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### **📦 Services**

| Service | Port | Purpose |
| ----- | ----- | ----- |
| **Backend API** | 8888 | REST API gateway and mission management |
| **Dispatcher** | 8001 | Mission orchestration and agent coordination |
| **Dialogue Agent** | 8002 | User interaction and conversation handling |
| **Planner Agent** | 8003 | Mission planning and task graph generation |
| **Phone Agent** | 8004 | Phone call execution with Twilio integration |

---

### **🗄️ Database Schema**

**Collections:**
- **missions**: Complete user requests with metadata and execution state
- **tasks**: Individual work units with dependencies and results

---

### **📞 Phone Agent Capabilities**

- **Twilio Integration**: Real phone calls with webhook handling
- **LLM Conversations**: AI-powered call interactions
- **Multilingual Support**: Hebrew and English language support
- **Call Management**: 5-minute timeout, status tracking, error handling
- **Speech Processing**: Real-time transcription and text-to-speech

---

### **🧪 Testing Components**

- **Twilio Echo Bot**: Standalone PoC for testing voice capabilities
- **Test Scripts**: Comprehensive testing suite for all services
- **API Testing**: Automated endpoint validation

---

### **✅ Core Design Decisions**

* **Microservices Architecture:** Separate services for scalability and modularity
* **Shared Models Package:** Unified data models to eliminate duplication
* **Async Throughout:** Full async/await implementation across all services
* **Stateless Design:** No in-memory session tracking, database-driven state
* **Task Routing:** Dispatcher routes tasks based on type to appropriate agents
* **Agent Callbacks:** Agents notify dispatcher upon task completion
* **Mission Lifecycle:** Created → Started → In Progress → Completed/Failed
* **Task Statuses:** `pending`, `in_progress`, `completed`, `failed`
* **Database Collections:** Separate missions and tasks with proper indexing
* **LLM Integration:** Gemini API with structured prompting and validation
* **Error Handling:** Retry mechanisms and comprehensive error logging
* **Terminal Session:** Enhanced CLI with mission tracking and commands
* **Phone Integration:** Twilio Voice API with LLM-based conversation handling
* **Language Support:** Hebrew/English for voice interactions
* **Local Development:** Docker-free local development environment

---
