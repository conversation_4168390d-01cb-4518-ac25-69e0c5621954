#!/usr/bin/env python3
"""
Deeplica v0 CLI Terminal UI - Main Entry Point
Bulletproof CLI interface that waits for backend API and never crashes.
Now includes web chat API endpoint for web interface communication.
"""

import asyncio
import sys
import os
import threading
import logging
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import uvicorn
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup logging - suppress routine INFO messages
logging.basicConfig(level=logging.WARNING)
uvicorn_logger = logging.getLogger("uvicorn.access")
uvicorn_logger.setLevel(logging.WARNING)

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Add project root to path for port manager
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, PROJECT_ROOT)

from terminal_ui import Bulletproof<PERSON>erminal<PERSON>
from shared.port_manager import ensure_service_port_free, get_service_port, get_localhost
from shared.system_settings import get_service_host
from shared.terminal_manager import get_terminal_manager, log_cli_to_watchdog

# Web API for chat interface
app = FastAPI(title="CLI Terminal Web API")

class ChatMessage(BaseModel):
    message: str
    user: str
    sender: str = "user"  # "user" or "deeplica"
    timestamp: str = ""

class ChatResponse(BaseModel):
    response: str
    status: str = "success"

# Global terminal instance
terminal_instance = None

@app.post("/chat", response_model=ChatResponse)
async def chat_endpoint(chat_message: ChatMessage):
    """Handle chat messages from web interface"""
    global terminal_instance

    if not terminal_instance:
        raise HTTPException(status_code=503, detail="CLI Terminal not ready")

    try:
        # Process the message through the terminal UI
        response = await terminal_instance.process_web_message(chat_message.message, chat_message.user)
        return ChatResponse(response=response)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing message: {str(e)}")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "CLI Terminal"}

def run_web_server():
    """Run the web server in a separate thread"""
    try:
        server_port = ensure_service_port_free("CLI-TERMINAL", force=True)
        uvicorn.run(app, host=get_service_host(), port=server_port, log_level="warning", access_log=False)
    except Exception as e:
        print(f"[CLI-TERMINAL:web] ERROR | ❌ Failed to start web server: {e}")
        print(f"[CLI-TERMINAL:web] SYSTEM | 💡 Please check for port conflicts")

async def main():
    """Main entry point for CLI Terminal UI"""
    global terminal_instance

    # Initialize terminal manager for CLI service
    terminal = get_terminal_manager("CLI-TERMINAL")

    try:
        # Get API URL from environment or use default
        api_url = os.getenv("DEEPLICA_API_URL", f"http://{get_localhost()}:{get_service_port('backend')}")

        # Get the official CLI port
        cli_port = get_service_port("CLI-TERMINAL")

        # Log to CLI terminal (startup messages only)
        terminal.log_terminal_message("STARTUP", "🖥️ Starting Deeplica v0 CLI Terminal UI...")
        terminal.log_terminal_message("SYSTEM", f"🔗 Backend URL: {api_url}")
        terminal.log_terminal_message("SYSTEM", f"🌐 Web API on port {cli_port}")
        terminal.log_terminal_message("SYSTEM", "💪 CLI will wait for backend and NEVER crash!")
        terminal.log_terminal_message("SYSTEM", "🔄 User interactions will be logged to Watchdog terminal")

        # Start web server in background thread
        web_thread = threading.Thread(target=run_web_server, daemon=True)
        web_thread.start()
        terminal.log_terminal_message("SYSTEM", f"🌐 Web API server started on port {cli_port}")

        # Log startup complete
        terminal.log_startup_complete(
            port=cli_port,
            additional_info={
                "Backend URL": api_url,
                "Message Routing": "User interactions → Watchdog terminal",
                "Web API": "Active"
            }
        )

        # Create and run bulletproof terminal UI
        terminal_instance = BulletproofTerminalUI(api_url)
        await terminal_instance.run()

    except KeyboardInterrupt:
        terminal.log_terminal_message("SHUTDOWN", "👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        terminal.log_error("STARTUP_ERROR", f"Unexpected error in CLI: {e}", "Restart and retry")
        terminal.log_bulletproof_action("AUTO_RESTART", "CLI will restart and try again")
        # 🛡️ BULLETPROOF: Never exit - wait and retry instead
        import time
        time.sleep(5)
        terminal.log_terminal_message("SYSTEM", "🔄 Retrying CLI startup...")
        # Continue the loop instead of exiting


if __name__ == "__main__":
    # Terminal manager will handle process naming and terminal setup
    # Process detection removed per user request - manual management only

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
