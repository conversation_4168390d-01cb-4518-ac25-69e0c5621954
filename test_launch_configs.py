#!/usr/bin/env python3
"""
🧪 Test Launch Configurations
Tests all files referenced in launch.json for syntax errors
"""

import py_compile
import os
import sys

def test_launch_configs():
    """Test all launch configuration files"""
    print("🧪 Testing Launch Configuration Files...")
    print("=" * 60)
    
    # Files referenced in launch.json
    files_to_test = [
        'watchdog/main.py',
        'backend/app/main.py',
        'dispatcher/app/main.py',
        'agents/dialogue/app/main.py',
        'agents/planner/app/main.py',
        'agents/phone/app/main.py',
        'web_chat/main.py',
        'startup_orchestrator.py',
        'start_deeplica_manual.py',
        'open_deepchat.py',
        'test_mongodb.py',
        'cleanup_residuals.py',
        'dummy.py'
    ]
    
    errors = []
    
    for file in files_to_test:
        if not os.path.exists(file):
            print(f"❌ {file} - FILE NOT FOUND")
            errors.append(f"{file} - FILE NOT FOUND")
            continue
            
        try:
            py_compile.compile(file, doraise=True)
            print(f"✅ {file} - Syntax OK")
        except Exception as e:
            print(f"❌ {file} - Syntax Error: {e}")
            errors.append(f"{file} - {e}")
    
    print("=" * 60)
    
    if errors:
        print(f"❌ {len(errors)} files have issues:")
        for error in errors:
            print(f"  • {error}")
        return False
    else:
        print(f"✅ All {len(files_to_test)} files are ready for launch!")
        return True

if __name__ == "__main__":
    success = test_launch_configs()
    sys.exit(0 if success else 1)
