"""
Constants for the CLI application.
Defines display mappings for mission status.
"""

import sys
import os

# Add project root to path for importing shared constants
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared_models import MissionStatus


# Display mappings
STATUS_EMOJIS = {
    MissionStatus.COMPLETED: "✅",
    MissionStatus.IN_PROGRESS: "⏳",
    MissionStatus.FAILED: "❌",
    MissionStatus.CANCELLED: "🛑",
    MissionStatus.PLANNING: "🧠",
    MissionStatus.CREATED: "📝"
}
