#!/usr/bin/env python3
"""
Simple Phone Call Test Script
Tests the phone call functionality directly without the complex Phone Agent service.
"""

import os
import sys
import json
import requests
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_phone_call():
    """Test phone call functionality directly"""
    print("🔥 DEEPLICA PHONE CALL TEST")
    print("=" * 50)
    
    # Phone call details
    phone_number = "+************"
    message = "Hello Eran! This is a test call from DEEPLICA. I love you! This is just a test to verify the phone system is working correctly."
    
    print(f"📞 Phone Number: {phone_number}")
    print(f"💬 Message: {message}")
    print(f"⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test 1: Check if Backend API is available
    print("🔍 Test 1: Checking Backend API...")
    try:
        response = requests.get("http://127.0.0.1:8888/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend API is healthy")
            backend_data = response.json()
            print(f"   Status: {backend_data.get('status', 'unknown')}")
            print(f"   Database: {backend_data.get('database_connected', 'unknown')}")
        else:
            print(f"❌ Backend API returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend API not available: {e}")
        return False
    
    # Test 2: Create a phone call mission
    print("\n🔍 Test 2: Creating phone call mission...")
    try:
        mission_data = {
            "description": f"call {phone_number} and say you love me",
            "user_id": "admin",
            "priority": "high",
            "mission_type": "phone_call",
            "phone_number": phone_number,
            "message": message
        }
        
        response = requests.post(
            "http://127.0.0.1:8888/missions/create",
            json=mission_data,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ Mission created successfully")
            mission_result = response.json()
            print(f"   Mission ID: {mission_result.get('mission_id', 'unknown')}")
            print(f"   Status: {mission_result.get('status', 'unknown')}")
            return True
        else:
            print(f"❌ Mission creation failed with status {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Mission creation failed: {e}")
        return False

def test_direct_twilio():
    """Test Twilio integration directly"""
    print("\n🔍 Test 3: Testing Twilio integration directly...")
    
    # Check if Twilio credentials are available
    twilio_sid = os.getenv("TWILIO_ACCOUNT_SID")
    twilio_token = os.getenv("TWILIO_AUTH_TOKEN")
    twilio_phone = os.getenv("TWILIO_PHONE_NUMBER")
    
    if not all([twilio_sid, twilio_token, twilio_phone]):
        print("❌ Twilio credentials not found in environment")
        print(f"   SID: {'✅' if twilio_sid else '❌'}")
        print(f"   Token: {'✅' if twilio_token else '❌'}")
        print(f"   Phone: {'✅' if twilio_phone else '❌'}")
        return False
    
    print("✅ Twilio credentials found")
    print(f"   From Number: {twilio_phone}")
    
    try:
        from twilio.rest import Client
        client = Client(twilio_sid, twilio_token)
        
        # Test call
        call = client.calls.create(
            twiml='<Response><Say>Hello from DEEPLICA! This is a test call. I love you!</Say></Response>',
            to="+************",
            from_=twilio_phone
        )
        
        print(f"✅ Call initiated successfully")
        print(f"   Call SID: {call.sid}")
        print(f"   Status: {call.status}")
        return True
        
    except Exception as e:
        print(f"❌ Twilio call failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting DEEPLICA Phone Call Test...")
    print()
    
    # Run tests
    backend_test = test_phone_call()
    
    if backend_test:
        print("\n🎉 Backend API test passed!")
    else:
        print("\n💥 Backend API test failed!")
    
    # Test Twilio directly if backend test fails
    if not backend_test:
        twilio_test = test_direct_twilio()
        if twilio_test:
            print("\n🎉 Direct Twilio test passed!")
        else:
            print("\n💥 Direct Twilio test failed!")
    
    print("\n" + "=" * 50)
    print("🏁 Phone Call Test Complete")
