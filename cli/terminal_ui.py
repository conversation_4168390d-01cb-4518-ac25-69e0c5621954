#!/usr/bin/env python3
"""
Deeplica v0 Terminal UI
Chat-style CLI interface for interacting with the mission orchestration system.
"""

import asyncio
import sys
import os
import httpx
from typing import Optional, Dict, Any
from datetime import datetime
from shared.port_manager import get_service_port

# Add project root to path for shared modules
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    from prompt_toolkit import prompt
    from prompt_toolkit.history import InMemoryHistory
    from prompt_toolkit.shortcuts import confirm
    from prompt_toolkit.formatted_text import HTML
    PROMPT_TOOLKIT_AVAILABLE = True
except ImportError:
    PROMPT_TOOLKIT_AVAILABLE = False
    # prompt_toolkit not available, will use basic input (no need to log for user interface)

from cli.api_client import DeepplicaAPIClient, MissionNotFoundError
from cli.constants import STATUS_EMOJIS
from shared.backend_readiness import ensure_backend_ready_before_startup

# 🛡️ BULLETPROOF Watchdog Logger Integration for CLI
import requests
import json
from typing import Optional
import threading
import time

class BulletproofWatchdogLogger:
    """
    🛡️ BULLETPROOF watchdog logger that NEVER crashes CLI.
    Buffers messages when watchdog is offline and sends them when it comes online.
    NEVER prints debug messages to CLI terminal - only to watchdog.
    Uses unified logging format: time - Svc: [service], Mod: [module], Cod: [routine], msg: [message]
    """

    def __init__(self):
        self.watchdog_url = f"http://localhost:{get_service_port('watchdog')}"
        self.service_name = "CLI-TERMINAL"
        self.watchdog_available = False
        self.last_check_time = 0
        self.check_interval = 30  # Check watchdog availability every 30 seconds

        # Message buffering system
        self.message_buffer = []
        self.max_buffer_size = 1000  # Prevent memory issues
        self.buffer_lock = threading.Lock()

        # Start background thread to monitor watchdog availability
        self._start_watchdog_monitor()

    def _start_watchdog_monitor(self):
        """Start background thread to monitor watchdog availability"""
        def monitor_watchdog():
            while True:
                try:
                    current_time = time.time()
                    if current_time - self.last_check_time > self.check_interval:
                        self._check_watchdog_availability()
                        self.last_check_time = current_time
                    time.sleep(5)  # Check every 5 seconds
                except:
                    # Never let monitoring crash
                    time.sleep(10)

        monitor_thread = threading.Thread(target=monitor_watchdog, daemon=True)
        monitor_thread.start()

    def _check_watchdog_availability(self):
        """Check if watchdog is available and flush buffer if it comes online"""
        try:
            response = requests.get(f"{self.watchdog_url}/health", timeout=2)
            was_available = self.watchdog_available
            self.watchdog_available = response.status_code == 200

            if self.watchdog_available and not was_available:
                # Watchdog came online - flush buffered messages
                self._flush_message_buffer()

        except:
            if self.watchdog_available:
                # Watchdog went offline - start buffering
                pass  # No terminal logging - just buffer messages
            self.watchdog_available = False

    def _buffer_message(self, level: str, routine: str, category: str, message: str, data: Optional[dict] = None):
        """Buffer message when watchdog is unavailable - NEVER print to CLI terminal"""
        try:
            with self.buffer_lock:
                # Create message entry with timestamp
                buffered_message = {
                    "timestamp": time.time(),
                    "level": level,
                    "routine": routine,
                    "category": category,
                    "message": message,
                    "data": data or {}
                }

                # Add to buffer
                self.message_buffer.append(buffered_message)

                # Prevent buffer overflow
                if len(self.message_buffer) > self.max_buffer_size:
                    # Remove oldest messages
                    self.message_buffer = self.message_buffer[-self.max_buffer_size//2:]

        except:
            # Even buffering should never crash CLI
            pass

    def _flush_message_buffer(self):
        """Send all buffered messages to watchdog when it comes online"""
        try:
            with self.buffer_lock:
                if not self.message_buffer:
                    return

                # Send buffered messages to watchdog
                for buffered_msg in self.message_buffer:
                    try:
                        payload = {
                            "service": self.service_name,
                            "routine": buffered_msg["routine"],
                            "level": buffered_msg["level"],
                            "category": buffered_msg["category"],
                            "message": f"[BUFFERED] {buffered_msg['message']}",
                            "data": buffered_msg["data"]
                        }
                        requests.post(f"{self.watchdog_url}/log", json=payload, timeout=1)
                    except:
                        # If individual message fails, continue with others
                        pass

                # Clear buffer after successful flush
                buffer_count = len(self.message_buffer)
                self.message_buffer.clear()

                # Log successful flush
                flush_payload = {
                    "service": self.service_name,
                    "routine": "_flush_message_buffer",
                    "level": "INFO",
                    "category": "SYSTEM",
                    "message": f"🌐 Watchdog online - flushed {buffer_count} buffered messages",
                    "data": {"flushed_count": buffer_count}
                }
                requests.post(f"{self.watchdog_url}/log", json=flush_payload, timeout=1)

        except:
            # Even flushing should never crash CLI
            pass

    def _send_to_watchdog(self, level: str, routine: str, category: str, message: str, data: Optional[dict] = None):
        """🛡️ BULLETPROOF send to watchdog with unified logging format - NEVER prints to CLI terminal"""
        try:
            if self.watchdog_available:
                # Send directly to watchdog using unified format
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                unified_message = f"{timestamp} - Svc: {self.service_name}, Mod: {category}, Cod: {routine}, msg: {message}"

                payload = {
                    "service": self.service_name,
                    "routine": routine,
                    "level": level,
                    "category": category,
                    "message": message,
                    "unified_message": unified_message,
                    "data": data or {}
                }
                requests.post(f"{self.watchdog_url}/log", json=payload, timeout=1)
            else:
                # Buffer message - NEVER print to CLI terminal
                self._buffer_message(level, routine, category, message, data)
        except:
            # If watchdog fails, buffer the message instead of terminal logging
            try:
                self._buffer_message(level, routine, category, message, data)
                # Mark watchdog as unavailable for next check
                self.watchdog_available = False
            except:
                # Even buffering should never crash CLI
                pass

    def info(self, routine: str, category: str, message: str, data: Optional[dict] = None):
        """🛡️ BULLETPROOF info logging"""
        try:
            self._send_to_watchdog("INFO", routine, category, message, data)
        except:
            pass

    def error(self, routine: str, category: str, message: str, data: Optional[dict] = None):
        """🛡️ BULLETPROOF error logging"""
        try:
            self._send_to_watchdog("ERROR", routine, category, message, data)
        except:
            pass

    def debug(self, routine: str, category: str, message: str, data: Optional[dict] = None):
        """🛡️ BULLETPROOF debug logging"""
        try:
            self._send_to_watchdog("DEBUG", routine, category, message, data)
        except:
            pass

    def warning(self, routine: str, category: str, message: str, data: Optional[dict] = None):
        """🛡️ BULLETPROOF warning logging"""
        try:
            self._send_to_watchdog("WARNING", routine, category, message, data)
        except:
            pass

# 🛡️ Global bulletproof watchdog logger instance
watchdog_logger = BulletproofWatchdogLogger()

def bulletproof_execute(func):
    """🛡️ Decorator to make any function bulletproof - NEVER crashes CLI"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except KeyboardInterrupt:
            # Allow graceful shutdown
            raise
        except SystemExit:
            # Allow system exit
            raise
        except Exception as e:
            try:
                # Log error to watchdog (or terminal if watchdog unavailable)
                watchdog_logger.error(func.__name__, "ERROR", f"Bulletproof catch: {type(e).__name__}: {e}")
                # Return safe default
                if 'async' in str(func):
                    return None
                return None
            except:
                # Even error logging should never crash
                pass
            return None
    return wrapper

class BulletproofTerminalUI:
    """
    🛡️ BULLETPROOF Terminal-based chat interface for Deeplica.

    This interface NEVER crashes and handles ALL errors gracefully.
    Provides a conversational interface for creating and managing missions.
    """

    def __init__(self, api_base_url: str = None):
        if api_base_url is None:
            from shared.port_manager import get_service_port, get_service_host
            api_base_url = f"http://{get_service_host('backend')}:{get_service_port('backend')}"
        try:
            self.api_url = api_base_url  # Store the API URL
            self.api_client = DeepplicaAPIClient(api_base_url)
            self.current_mission_id: Optional[str] = None
            self.history = InMemoryHistory() if PROMPT_TOOLKIT_AVAILABLE else None
            self.session_active = True
            watchdog_logger.info("__init__", "STARTUP", "CLI initialized successfully")
        except Exception as e:
            # Even initialization errors should not crash
            watchdog_logger.error("__init__", "ERROR", f"CLI initialization error: {e}")
            # Set safe defaults
            self.api_url = api_base_url
            self.api_client = None
            self.current_mission_id = None
            self.history = None
            self.session_active = True
        
    @bulletproof_execute
    def print_banner(self):
        """🛡️ BULLETPROOF print clean, minimal banner"""
        try:
            banner = """
╔══════════════════════════════════════════════════════════════╗
║                    🤖 Deeplica v3                            ║
║                AI Mission Assistant                          ║
║                                                              ║
║  Type your request and I'll break it down into actionable    ║
║  tasks and help you accomplish your goals.                   ║
║                                                              ║
║  Commands: 'help', 'status', 'missions', 'cancel',           ║
║            'restart', 'clear', 'exit'                        ║
╚══════════════════════════════════════════════════════════════╝
"""
            print(banner)
            watchdog_logger.info("print_banner", "STARTUP", "CLI banner displayed")
        except Exception as e:
            # Fallback banner if anything fails
            print("🤖 Deeplica v1 - AI Mission Assistant")
            print("Ready to help! Type 'help' for commands.")
            watchdog_logger.error("print_banner", "ERROR", f"Banner error: {e}")
    
    @bulletproof_execute
    def print_help(self):
        """🛡️ BULLETPROOF print help information"""
        try:
            help_text = """
📚 Available Commands:

🎯 Mission Commands:
  • Just type your request to create a new mission
  • 'status' - Check current mission status
  • 'cancel' - Cancel current mission

📋 System Commands:
  • 'missions' - List recent missions
  • 'help' - Show this help
  • 'restart' - Restart the entire Deeplica system
  • 'clear' - Clear the terminal and show header
  • 'exit' - 🛑 Stop ALL Deeplica services and exit (SAFE - won't close VS Code)
  • 'quit' or 'q' - Exit CLI only (leaves services running)

💡 Example Requests:
  • "Book a table for 8 people tonight"
  • "Help me negotiate my cable bill"
  • "Plan a weekend trip to San Francisco"
  • "Find a good restaurant for a business dinner"
"""
            print(help_text)
            watchdog_logger.info("print_help", "HELP", "Help information displayed")
        except Exception as e:
            # Fallback help if anything fails
            print("Available commands: help, status, missions, cancel, exit")
            print("Just type your request to create a mission!")
            watchdog_logger.error("print_help", "ERROR", f"Help display error: {e}")
    
    async def get_user_input(self, prompt_text: str = "You: ") -> str:
        """Get user input with optional prompt_toolkit enhancement"""
        if PROMPT_TOOLKIT_AVAILABLE:
            try:
                return await asyncio.to_thread(
                    prompt,
                    HTML(f'<ansigreen>{prompt_text}</ansigreen>'),
                    history=self.history
                )
            except (KeyboardInterrupt, EOFError):
                return "exit"
        else:
            try:
                return input(prompt_text)
            except (KeyboardInterrupt, EOFError):
                return "exit"
    
    def format_timestamp(self, timestamp: str) -> str:
        """Format timestamp for display"""
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            return dt.strftime("%H:%M:%S")
        except:
            return timestamp
    
    def print_mission_response(self, response_data: Dict[str, Any]):
        """Print formatted mission response"""
        if not response_data:
            return

        responses = response_data.get("responses", [])
        progress = response_data.get("progress", {})

        # Print progress
        if progress:
            # Progress data is flat, not nested
            total = progress.get("total_tasks", 0)
            completed = progress.get("completed", 0)
            percentage = progress.get("completion_percentage", 0)

            print(f"📊 Progress: {completed}/{total} tasks ({percentage:.1f}%)")
            watchdog_logger.info("print_mission_response", "PROGRESS", f"Mission progress: {completed}/{total} tasks ({percentage:.1f}%)", {"completed": completed, "total": total, "percentage": percentage})
        
        # Print responses
        for response in responses:
            message = response.get("message", "")
            requires_input = response.get("requires_user_input", False)
            
            if requires_input:
                print(f"🤖 {message}")
            else:
                print(f"✅ {message}")
    
    async def handle_create_mission(self, user_input: str) -> bool:
        """Handle mission creation"""
        print("🧠 Planning your mission...")
        watchdog_logger.info("handle_create_mission", "MISSION", "Starting mission creation", {"user_input": user_input[:100]})

        try:
            response = await self.api_client.create_mission(user_input)

            if response["status"] in ["started", "created"]:
                self.current_mission_id = response["mission_id"]

                # Wait for mission to reach a stable state (needs input or complete)
                await self._wait_for_mission_stable_state(response.get("data", {}))

                return True
            else:
                return False

        except Exception as e:
            return False
    
    async def handle_continue_mission(self, user_response: str) -> bool:
        """Handle mission continuation"""
        if not self.current_mission_id:
            return False

        try:
            response = await self.api_client.continue_mission(
                self.current_mission_id,
                user_response
            )

            # Wait for mission to reach a stable state (needs input or complete)
            await self._wait_for_mission_stable_state(response.get("data", {}))

            # Check if mission is complete
            if response.get("data", {}).get("is_complete", False):
                print("🎉 Mission completed!")
                watchdog_logger.info("handle_create_mission", "MISSION", "Mission completed successfully")
                self.current_mission_id = None

            return True

        except Exception as e:
            return False
    
    async def handle_status_command(self):
        """Handle status command"""
        if not self.current_mission_id:
            return

        try:
            response = await self.api_client.get_mission_status(self.current_mission_id)
            status_data = response.get("data", {})

            print(f"📊 Mission Status: {self.current_mission_id[:8]}...")
            watchdog_logger.info("handle_status_command", "STATUS", f"Checking mission status: {self.current_mission_id[:8]}...", {"status": status_data.get('status', 'unknown')})

            progress = status_data.get("progress", {})
            if progress:
                # Progress data is flat, not nested
                total = progress.get("total_tasks", 0)
                completed = progress.get("completed", 0)
                in_progress = progress.get("in_progress", 0)
                pending = progress.get("pending", 0)

            # Use helper method to check and clear completed mission
            mission_completed = await self._check_and_clear_completed_mission()
            if mission_completed:
                return

            if status_data.get("requires_user_input", False):
                watchdog_logger.info("handle_status_command", "INPUT", "Mission waiting for user response")

        except MissionNotFoundError:
            print("⚠️ Mission no longer exists (may have been deleted)")
            watchdog_logger.error("handle_status_command", "MISSION", f"Mission {self.current_mission_id[:8]}... no longer exists")
            self.current_mission_id = None
        except Exception as e:
            watchdog_logger.error("handle_status_command", "ERROR", f"Error getting status: {e}")
            print("Unable to get mission status. Please try again.")

    async def handle_missions_command(self):
        """Handle missions list command"""
        try:
            missions = await self.api_client.list_missions(limit=10)
            
            if not missions:
                print("📭 No recent missions")
                watchdog_logger.info("handle_missions_command", "MISSIONS", "No recent missions found")
                return

            print(f"📋 Recent Missions ({len(missions)}):")
            watchdog_logger.info("handle_missions_command", "MISSIONS", f"Displaying {len(missions)} recent missions")
            
            for mission in missions:
                mission_id = mission["mission_id"][:8]
                title = mission["title"][:40]
                status = mission["status"]
                created = self.format_timestamp(mission["created_at"])
                
                status_emoji = STATUS_EMOJIS.get(status, "❓")
            
        except Exception as e:
    
            pass
    async def handle_cancel_command(self):
        """Handle cancel command"""
        if not self.current_mission_id:
            return
        
        # Confirm cancellation
        if PROMPT_TOOLKIT_AVAILABLE:
            try:
                confirmed = await asyncio.to_thread(
                    confirm,
                    "Are you sure you want to cancel the current mission?"
                )
            except (KeyboardInterrupt, EOFError):
                confirmed = False
        else:
            response = input("Are you sure you want to cancel the current mission? (y/N): ")
            confirmed = response.lower().startswith('y')
        
        if not confirmed:
            return
        
        try:
            response = await self.api_client.cancel_mission(self.current_mission_id)
            self.current_mission_id = None
            
        except Exception as e:
            watchdog_logger.error("handle_cancel_command", "ERROR", f"Cancel mission error: {e}")
            print("❌ Error canceling mission. Please try again.")

    @bulletproof_execute
    async def handle_restart_command(self):
        """🔄 Handle restart command - stops and restarts entire Deeplica system"""
        try:
            print("🔄 Restarting Deeplica system...")
            watchdog_logger.info("handle_restart_command", "SYSTEM", "User requested system restart")

            # Confirm restart
            if PROMPT_TOOLKIT_AVAILABLE:
                try:
                    confirmed = await asyncio.to_thread(
                        confirm,
                        "Are you sure you want to restart the entire Deeplica system?"
                    )
                except (KeyboardInterrupt, EOFError):
                    confirmed = False
            else:
                response = input("Are you sure you want to restart the entire Deeplica system? (y/N): ")
                confirmed = response.lower().startswith('y')

            if not confirmed:
                print("🚫 Restart cancelled.")
                return

            print("🛑 Stopping all Deeplica services...")
            watchdog_logger.info("handle_restart_command", "SYSTEM", "Stopping all services for restart")

            # Execute stop deeplica
            import subprocess
            import os

            try:
                # Run stop deeplica script
                stop_result = subprocess.run([
                    "python3", "stop_deeplica/main.py"
                ], cwd="/Users/<USER>/Documents/prototype", capture_output=True, text=True, timeout=30)

                if stop_result.returncode == 0:
                    print("✅ All services stopped successfully")
                    watchdog_logger.info("handle_restart_command", "SUCCESS", "All services stopped")
                else:
                    print("⚠️ Some services may not have stopped cleanly")
                    watchdog_logger.warning("handle_restart_command", "WARNING", f"Stop result: {stop_result.stderr}")

            except subprocess.TimeoutExpired:
                print("⚠️ Stop operation timed out, but continuing with restart...")
                watchdog_logger.warning("handle_restart_command", "WARNING", "Stop operation timed out")
            except Exception as e:
                print("⚠️ Error during stop, but continuing with restart...")
                watchdog_logger.error("handle_restart_command", "ERROR", f"Stop error: {e}")

            # Wait a moment for cleanup
            await asyncio.sleep(3)

            print("🚀 Starting Deeplica system...")
            watchdog_logger.info("handle_restart_command", "SYSTEM", "Starting system after restart")

            try:
                # Run start deeplica orchestrator
                start_result = subprocess.Popen([
                    "python3", "orchestrator/main.py"
                ], cwd="/Users/<USER>/Documents/prototype")

                print("✅ Deeplica system restart initiated!")
                print("🔄 Services are starting in the background...")
                print("💡 You can continue using this CLI once the backend is ready.")

                watchdog_logger.info("handle_restart_command", "SUCCESS", "System restart initiated")

            except Exception as e:
                print("❌ Error starting system. Please start manually.")
                watchdog_logger.error("handle_restart_command", "ERROR", f"Start error: {e}")

        except Exception as e:
            print("❌ Error during restart. Please restart manually.")
            watchdog_logger.error("handle_restart_command", "ERROR", f"Restart error: {e}")

    @bulletproof_execute
    async def handle_clear_command(self):
        """🧹 Handle clear command - clears terminal and shows header"""
        try:
            # Clear the terminal screen
            import os
            os.system('clear' if os.name == 'posix' else 'cls')

            # Show the banner again
            self.print_banner()

            # Log the clear action
            watchdog_logger.info("handle_clear_command", "SYSTEM", "Terminal cleared by user")

        except Exception as e:
            # Fallback: print newlines to simulate clear
            print("\n" * 50)
            self.print_banner()
            watchdog_logger.error("handle_clear_command", "ERROR", f"Clear error: {e}")

    @bulletproof_execute
    async def handle_exit_command(self):
        """🛑 Handle exit command - safely stops all registered Deeplica services"""
        try:
            print("🛑 Stopping all Deeplica services...")
            watchdog_logger.info("handle_exit_command", "SYSTEM", "User requested system shutdown via exit command")

            # Confirm exit
            if PROMPT_TOOLKIT_AVAILABLE:
                try:
                    confirmed = await asyncio.to_thread(
                        confirm,
                        "Are you sure you want to stop all Deeplica services and exit?"
                    )
                    if not confirmed:
                        print("❌ Exit cancelled")
                        return
                except Exception:
                    # Fallback to basic input if prompt_toolkit fails
                    response = input("Are you sure you want to stop all Deeplica services and exit? (y/N): ")
                    if response.lower() not in ['y', 'yes']:
                        print("❌ Exit cancelled")
                        return
            else:
                response = input("Are you sure you want to stop all Deeplica services and exit? (y/N): ")
                if response.lower() not in ['y', 'yes']:
                    print("❌ Exit cancelled")
                    return

            # Call Stop Deeplica Service to stop all registered services
            print("🛑 Requesting shutdown of all registered services...")

            try:
                import httpx
                async with httpx.AsyncClient(timeout=30.0) as client:
                    response = await client.post(
                        # REMOVED: stop-deeplica service no longer exists
                        json={
                            "force": False,
                            "reason": "CLI exit command",
                            "shutdown_self": True  # Shutdown Stop Deeplica Service too
                        }
                    )

                    if response.status_code == 200:
                        data = response.json()
                        results = data.get('results', {})

                        print("✅ Stop request completed successfully")
                        for service_name, success in results.items():
                            status = "✅ Stopped" if success else "❌ Failed"
                            print(f"   {status}: {service_name}")

                        print("🎉 All registered Deeplica services have been stopped")
                        print("✅ VS Code and other applications are unaffected")
                        watchdog_logger.info("handle_exit_command", "SUCCESS", "All services stopped successfully")

                    else:
                        print(f"⚠️ Stop request returned HTTP {response.status_code}")
                        print("Some services may still be running")

            except Exception as e:
                print(f"⚠️ Could not contact Stop Deeplica Service: {e}")
                print("💡 Services may still be running - use 'restart' command or manual stop")
                watchdog_logger.error("handle_exit_command", "ERROR", f"Stop service error: {e}")

            # Exit the CLI gracefully - don't crash the service
            print("👋 Goodbye!")
            # Instead of sys.exit(), return a special value to indicate shutdown
            return "EXIT_REQUESTED"

        except Exception as e:
            watchdog_logger.error("handle_exit_command", "ERROR", f"Exit command error: {e}")
            print("❌ Error during exit. You may need to stop services manually.")

    async def _wait_for_mission_stable_state(self, initial_data: Dict[str, Any]) -> None:
        """
        Wait for mission to reach a stable state where user input is needed or mission is complete.

        Args:
            initial_data: Initial mission execution data from API response
        """

        # Check initial state
        if self._is_mission_stable(initial_data):
            self.print_mission_response(initial_data)
            return

        # Mission is still processing, show initial response and start polling
        self.print_mission_response(initial_data)

        # Poll for stable state
        max_polls = 60  # 5 minutes max (5 second intervals)
        poll_count = 0

        while poll_count < max_polls:
            await asyncio.sleep(5)  # Wait 5 seconds between polls
            poll_count += 1

            try:
                # Get current mission status
                status_response = await self.api_client.get_mission_status(self.current_mission_id)
                status_data = status_response.get("data", {})

                if self._is_mission_stable(status_data):
                    # Mission reached stable state, show final response
                    print("="*50)
                    self.print_mission_response(status_data)

                    # Use helper method to check and clear completed mission
                    await self._check_and_clear_completed_mission()

                    return

                # Show progress indicator every 15 seconds
                if poll_count % 3 == 0:
                    progress = status_data.get("progress", {})
                    if progress:
                        completed = progress.get("completed", 0)
                        total = progress.get("total_tasks", 0)
                        if total > 0:
                            pass
                        else:
                            pass
                    else:
                        watchdog_logger.debug("_wait_for_mission_stable_state", "POLLING", "Still processing mission")
                        pass
            except MissionNotFoundError:
                # Mission no longer exists (deleted from database)
                print("⚠️ Mission no longer exists (may have been deleted)")
                watchdog_logger.error("_wait_for_mission_stable_state", "MISSION", f"Mission {self.current_mission_id[:8]}... no longer exists")
                self.current_mission_id = None
                return
            except Exception as e:
                watchdog_logger.error("_wait_for_mission_stable_state", "ERROR", f"Error checking mission status: {e}")
                # Continue polling in case it's a temporary error
                continue

        # Timeout reached
        print("⚠️ Mission is taking longer than expected. Use 'status' command to check progress.")
        watchdog_logger.info("_wait_for_mission_stable_state", "TIMEOUT", "Mission taking longer than expected")

    def _is_mission_stable(self, data: Dict[str, Any]) -> bool:
        """
        Check if mission is in a stable state (needs user input or is complete).

        Args:
            data: Mission execution data

        Returns:
            True if mission is stable (needs input or complete), False if still processing
        """
        # Mission is complete
        if data.get("is_complete", False):
            return True

        # Mission requires user input
        if data.get("requires_user_input", False):
            return True

        # Check if any responses require user input
        responses = data.get("responses", [])
        if responses:
            last_response = responses[-1]
            if last_response.get("requires_user_input", False):
                return True

        # Mission is still processing
        return False

    async def _check_and_clear_completed_mission(self) -> bool:
        """
        Check if current mission is completed and clear it if so.

        Returns:
            True if mission was completed and cleared, False otherwise
        """
        if not self.current_mission_id:
            return False

        try:
            response = await self.api_client.get_mission_status(self.current_mission_id)
            status_data = response.get("data", {})

            if status_data.get("is_complete", False):
                print(f"🎉 Mission completed!")
                self.current_mission_id = None
                return True

        except MissionNotFoundError:
            # Mission no longer exists (deleted from database)
            print(f"⚠️ Mission {self.current_mission_id[:8]}... no longer exists (may have been deleted)")
            self.current_mission_id = None
            return True  # Treat as "completed" to clear the mission
        except Exception as e:
            # Don't print error here as it might be called frequently
            pass

        return False

    async def process_user_input(self, user_input: str) -> bool:
        """Process user input and return whether to continue"""
        user_input = user_input.strip()

        if not user_input:
            # Check for mission completion even on empty input
            await self._check_and_clear_completed_mission()
            return True

        # Handle commands (case-insensitive)
        command = user_input.lower()
        if command in ['quit', 'q']:
            return False
        elif command == 'exit':
            await self.handle_exit_command()
            return False  # Exit after handling
        elif command == 'help':
            self.print_help()
        elif command == 'status':
            await self.handle_status_command()
        elif command == 'missions':
            await self.handle_missions_command()
        elif command == 'cancel':
            await self.handle_cancel_command()
        elif command == 'restart':
            await self.handle_restart_command()
        elif command == 'clear':
            await self.handle_clear_command()
        else:
            # Handle mission input
            if self.current_mission_id:
                # Continue existing mission
                await self.handle_continue_mission(user_input)
            else:
                # Create new mission
                await self.handle_create_mission(user_input)

        return True
    
    async def wait_for_backend_ready(self):
        """Wait for backend API to be ready - NEVER crashes, waits indefinitely"""

        attempt = 1
        while True:
            try:
                # Use bulletproof connection handling
                try:
                    await self.api_client.check_health()
                    return
                except Exception as e:
                    # Log the specific error for debugging but don't crash
                    if attempt == 1 or attempt % 15 == 0:  # Log every 15 attempts (30 seconds)
                        watchdog_logger.debug("wait_for_backend_ready", "BACKEND", f"Backend not ready yet (attempt {attempt}): {str(e)[:100]}")
                        watchdog_logger.info("wait_for_backend_ready", "SYSTEM", "CLI will keep trying indefinitely")

            except Exception as e:
                # Catch any unexpected errors in the outer try block
                if attempt % 15 == 0:

            # Bulletproof sleep with fallback
                    pass
            try:
                await asyncio.sleep(2)
            except Exception as e:
                # Use time.sleep as fallback if asyncio.sleep fails
                try:
                    import time
                    time.sleep(2)
                except Exception as sleep_error:
                    pass  # Continue anyway

            attempt += 1

    async def run(self):
        """🛡️ BULLETPROOF main application loop - NEVER crashes"""
        restart_count = 0
        max_restarts = 1000  # Allow many restarts

        while restart_count < max_restarts:
            try:
                watchdog_logger.info("run", "STARTUP", f"CLI starting (attempt #{restart_count + 1})")

                # Print banner (bulletproof)
                self.print_banner()

                # Wait for backend to be ready (bulletproof)
                await self.wait_for_backend_ready()

                print("Type 'help' for commands or just tell me what you'd like to accomplish!\n")

                # Main interaction loop
                while self.session_active:
                    try:
                        # Check if current mission completed before showing prompt
                        if self.current_mission_id:
                            await self._check_and_clear_completed_mission()

                        # Show current mission indicator
                        prompt_text = "You: "
                        if self.current_mission_id:
                            prompt_text = f"You ({self.current_mission_id[:8]}...): "

                        user_input = await self.get_user_input(prompt_text)

                        # Process input
                        should_continue = await self.process_user_input(user_input)
                        if not should_continue:
                            break

                        print()  # Add spacing

                    except KeyboardInterrupt:
                        print("\n👋 Goodbye!")
                        watchdog_logger.info("run", "SYSTEM", "CLI terminated by user")
                        return  # Exit completely
                    except Exception as e:
                        watchdog_logger.error("run", "ERROR", f"Interaction loop error: {e}")
                        print("Something went wrong. Please try again or type 'exit' to quit.\n")
                        # Continue the loop instead of crashing
                        continue

                # If we get here, session ended normally
                watchdog_logger.info("run", "SYSTEM", "CLI session ended normally")
                return

            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                watchdog_logger.info("run", "SYSTEM", "CLI terminated by user")
                return
            except SystemExit:
                watchdog_logger.info("run", "SYSTEM", "CLI system exit requested")
                return
            except Exception as e:
                restart_count += 1
                watchdog_logger.error("run", "ERROR", f"CLI crashed (restart #{restart_count}): {e}")

                if restart_count < max_restarts:
                    print(f"\n🛡️ CLI encountered an error but will restart automatically...")
                    print(f"🔄 Restarting CLI (attempt #{restart_count + 1})...\n")

                    # Wait a bit before restarting
                    try:
                        await asyncio.sleep(2)
                    except:
                        pass
                else:
                    print(f"\n❌ CLI has restarted {max_restarts} times. Please check system status.")
                    return

        watchdog_logger.error("run", "SYSTEM", f"CLI exceeded maximum restarts ({max_restarts})")

    def _classify_message_type(self, message: str) -> str:
        """Classify message as 'question', 'mission', or 'command'"""
        message_lower = message.lower().strip()

        # Command patterns - highest priority
        command_patterns = [
            'create mission', 'create a mission', 'make mission', 'execute', 'do it now', 'start mission',
            'call +', 'phone +', 'dial +',  # Phone numbers
            'call me', 'phone me', 'contact me',
            'send email', 'send message', 'send sms',
            'book', 'schedule', 'arrange', 'organize', 'manage',
            'remind me', 'set reminder', 'alert me', 'notify me',
            'create a', 'make a', 'build a', 'develop a', 'design a', 'plan a'
        ]

        # Mission patterns - medium priority
        mission_patterns = [
            'i need', 'i want', 'i would like', 'can you', 'could you', 'please',
            'help me', 'assist me', 'do this', 'handle this', 'take care of'
        ]
        question_patterns = [
            'what is', 'what are', 'what time', 'what day', 'what date', "what's",
            'how are', 'how is', 'how do', 'how can', 'how much', 'how many',
            'when is', 'when are', 'when do', 'when can', 'when will',
            'where is', 'where are', 'where can', 'where do',
            'why is', 'why are', 'why do', 'why can', 'why will',
            'who is', 'who are', 'who can', 'who will',
            'tell me', 'explain', 'describe', 'define',
            'hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening',
            'thank you', 'thanks', 'bye', 'goodbye', 'see you'
        ]

        # Check for command patterns first (highest priority)
        for pattern in command_patterns:
            if pattern in message_lower:
                return 'command'

        # Check for mission patterns
        for pattern in mission_patterns:
            if pattern in message_lower:
                return 'mission'

        # Check for question patterns
        for pattern in question_patterns:
            if pattern in message_lower:
                return 'question'

        # Check if message starts with question words
        question_starters = ['what', 'how', 'when', 'where', 'why', 'who']
        first_word = message_lower.split()[0] if message_lower.split() else ""
        if first_word in question_starters:
            return 'question'

        # For longer messages that don't match patterns, assume they're missions/commands
        if len(message.split()) > 6:
            return 'command'

        # Default to question for short unclear messages
        return 'question'

    async def _get_ai_response(self, message: str, username: str) -> str:
        """Get direct AI response for questions and conversations"""
        try:
            # Use Gemini API for direct responses
            import google.generativeai as genai
            import os
            from datetime import datetime

            # Configure Gemini
            api_key = os.getenv('GEMINI_API_KEY')
            if not api_key:
                return "I need a Gemini API key to provide intelligent responses. Please configure GEMINI_API_KEY."

            genai.configure(api_key=api_key)
            model = genai.GenerativeModel('gemini-1.5-flash')

            # Get current time for time-related questions
            current_time = datetime.now().strftime("%I:%M %p on %A, %B %d, %Y")

            # Create context-aware prompt
            prompt = f"""You are Deeplica, an AI mission orchestrator and assistant with advanced capabilities.

AVAILABLE SERVICES:
- Phone Service: I can make real phone calls to any number worldwide
- Mission Planning: I can break down complex tasks into executable steps
- Task Orchestration: I can execute multi-step missions with dependent tasks
- Data Cascading: I can use information from one task in subsequent tasks

CAPABILITIES:
- Make phone calls and record responses
- Create and execute complex missions
- Handle cascaded tasks that depend on previous results
- Manage multi-step workflows automatically
- Differentiate between questions (to answer) and commands (to execute)

Current time: {current_time}
User: {username}
Question: {message}

Provide a helpful, concise, and friendly response. If the user asks about time, use the current time provided above.

If this seems like a task I should execute (like making phone calls, creating missions, or complex multi-step actions), tell them to use command language like "Create a mission:" or "Call [number] and..." or "DO IT NOW" to trigger execution.

Keep responses conversational and under 200 words unless more detail is specifically requested."""

            response = model.generate_content(prompt)
            return response.text.strip()

        except Exception as e:
            watchdog_logger.error("_get_ai_response", "ERROR", f"AI response error: {e}")
            return f"I understand your question about '{message}', but I'm having trouble accessing my AI capabilities right now. You can try asking me to create a mission for more complex tasks."

    @bulletproof_execute
    async def process_web_message(self, message: str, username: str) -> str:
        """🛡️ BULLETPROOF process message from web chat interface with intelligent routing"""
        try:
            watchdog_logger.info("process_web_message", "WEB_CHAT", f"Processing web message from {username}: {message}")

            # Display the message in CLI terminal
            print(f"\n💬 [WEB-CHAT] {username}: {message}")

            # Classify message type
            message_type = self._classify_message_type(message)
            print(f"🧠 [CLASSIFICATION]: {message_type}")

            if message_type == 'question':
                # Handle as direct AI conversation
                ai_response = await self._get_ai_response(message, username)
                print(f"🤖 [DEEPLICA]: {ai_response}")
                watchdog_logger.info("process_web_message", "WEB_CHAT", f"AI response sent to {username}")
                return ai_response

            elif message_type in ['command', 'mission']:
                # Handle as mission creation and execution
                async with httpx.AsyncClient() as client:
                    try:
                        response = await client.post(
                            f"{self.api_url}/api/v1/missions",
                            json={"user_input": message, "user_id": username},
                            timeout=30.0
                        )

                        if response.status_code == 200:
                            response_data = response.json()
                            response_text = response_data.get("message", "Mission created successfully")
                            mission_id = response_data.get("mission_id", "unknown")

                            # Display the response in CLI terminal
                            print(f"🤖 [DEEPLICA]: {response_text}")
                            print(f"📋 [MISSION]: {mission_id}")

                            watchdog_logger.info("process_web_message", "WEB_CHAT", f"Mission response sent to {username}")
                            return response_text
                        else:
                            error_msg = f"Backend API error: {response.status_code}"
                            print(f"❌ [ERROR]: {error_msg}")
                            return f"I'm having trouble connecting to my backend service. Please try again later."

                    except httpx.TimeoutException:
                        error_msg = "Backend API timeout"
                        print(f"❌ [ERROR]: {error_msg}")
                        return "I'm taking longer than usual to respond. Please try again."
                    except httpx.ConnectError:
                        error_msg = "Backend API connection failed"
                        print(f"❌ [ERROR]: {error_msg}")
                        return "I'm having trouble connecting to my backend service. The backend API may not be running. Please check if all DEEPLICA services are started."
                    except Exception as e:
                        error_msg = f"Backend API error: {str(e)}"
                        print(f"❌ [ERROR]: {error_msg}")
                        # Provide more specific error message based on the error type
                        if "Connection" in str(e) or "connection" in str(e).lower():
                            return "I'm having trouble connecting to my backend service. The backend API may not be running. Please check if all DEEPLICA services are started."
                        else:
                            return f"I encountered an error processing your request: {str(e)}. Please try again."

        except Exception as e:
            error_msg = f"Error processing web message: {str(e)}"
            watchdog_logger.error("process_web_message", "ERROR", error_msg)
            print(f"❌ [ERROR]: {error_msg}")
            # Provide more helpful error message based on error type
            if "Connection" in str(e) or "connection" in str(e).lower():
                return "I'm having trouble connecting to my backend service. The backend API may not be running. Please check if all DEEPLICA services are started."
            else:
                return f"I encountered an error processing your request: {str(e)}. Please try again or contact support."

async def main():
    """Main entry point with backend readiness checking"""
    # Get API URL from environment or use default
    from shared.port_manager import get_service_port, get_service_host
    default_api_url = f"http://{get_service_host('backend')}:{get_service_port('backend')}"
    api_url = os.getenv("DEEPLICA_API_URL", default_api_url)

    # Check if we should wait for backend readiness
    backend_ready = await ensure_backend_ready_before_startup("cli-terminal", api_url)
    if not backend_ready:
        print(f"starting CLI anyway to avoid hanging")

    # Create and run bulletproof terminal UI
    terminal = BulletproofTerminalUI(api_url)
    await terminal.run()

if __name__ == "__main__":
    # Set distinctive process name for easy identification
    try:
        import setproctitle
        setproctitle.setproctitle("DEEPLICA-CLI-TERMINAL")
        # CLI process name set (no need to log this for user interface)
    except ImportError:
        # setproctitle not available (no need to log this for user interface)
        pass

    # Set terminal title and clear identification banner - FORCE RENAME EVEN IF ALREADY NAMED
    service_name = os.getenv("SERVICE_NAME", "CLI-TERMINAL")

    # Multiple methods to ensure terminal gets renamed
    print(f"\033]0;🖥️ {service_name}\007", end="")  # xterm title
    print(f"\033]2;🖥️ {service_name}\007", end="")  # window title
    print(f"\033]1;🖥️ {service_name}\007", end="")  # icon title

    # Also try VS Code specific terminal naming
    import sys
    if hasattr(sys, 'ps1') or hasattr(sys, 'ps2'):
        try:
            import os
            os.system(f'echo -ne "\\033]0;🖥️ {service_name}\\007"')
        except:
            pass

    print(f"" + "="*80)
    print(f"🖥️ {service_name} TERMINAL")
    print("="*80 + "\n")

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print(f"👋 Goodbye!")
        sys.exit(0)
