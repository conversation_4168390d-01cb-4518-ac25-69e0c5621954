#!/usr/bin/env python3
"""
🔑 DEEPLICA API MANAGER
Centralized management for all external service configurations, API keys, and system parameters.

This module provides secure, centralized access to:
- API Keys (<PERSON>, <PERSON>wilio, Ngrok)
- Database Configurations (MongoDB Atlas)
- Service URLs and Endpoints
- System Parameters and Settings
- Authentication Tokens and Secrets

SECURITY POLICY:
- ONLY this module can access .env file directly
- ALL services must use API manager functions
- NO direct os.getenv() calls for external configs
- Provides validation, fallbacks, and error handling
"""

import os
import sys
import time
import json
from typing import Dict, Any, Optional, Union
from pathlib import Path
from dotenv import load_dotenv

# Add project root to path for imports
project_root = Path(__file__).parent.parent.absolute()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

class APIManager:
    """
    🔑 DEEPLICA API MANAGER
    
    Centralized management system for all external service configurations:
    - Secure API key management
    - Database connection parameters
    - Service URLs and endpoints
    - System configuration parameters
    - Authentication tokens and secrets
    """
    
    def __init__(self, service_name: str = "API-MANAGER"):
        self.service_name = service_name
        self.PROJECT_ROOT = Path(__file__).parent.parent.absolute()
        self.ENV_FILE = self.PROJECT_ROOT / ".env"

        # Initialize anti-spam tracking first
        self._last_warning_messages = {}
        self._message_cooldown = 60  # seconds

        # Cache for configurations to avoid repeated file reads
        self._config_cache = {}
        self._cache_timestamp = 0
        self._cache_ttl = 300  # 5 minutes cache TTL

        # Load environment variables from .env file
        self._load_environment()
    
    def _load_environment(self):
        """Load environment variables from .env file"""
        try:
            if self.ENV_FILE.exists():
                load_dotenv(self.ENV_FILE)
                self._log_message("✅ Environment loaded from .env file")
            else:
                self._log_message("⚠️ .env file not found, using system environment")
        except Exception as e:
            self._log_message(f"❌ Error loading .env file: {e}")
    
    def _log_message(self, message: str, level: str = "INFO"):
        """Log message with anti-spam protection"""
        current_time = time.time()
        message_key = f"{level}_{message[:50]}"
        
        if (message_key not in self._last_warning_messages or 
            current_time - self._last_warning_messages[message_key] > self._message_cooldown):
            print(f"[{self.service_name}] {message}")
            self._last_warning_messages[message_key] = current_time
    
    def _get_env_value(self, key: str, default: Any = None, required: bool = False) -> Any:
        """Secure environment variable retrieval with validation"""
        try:
            value = os.getenv(key, default)
            
            if required and not value:
                self._log_message(f"❌ Required configuration missing: {key}", "ERROR")
                raise ValueError(f"Required environment variable {key} is not set")
            
            if value is None and default is None:
                self._log_message(f"⚠️ Configuration not found: {key}", "WARNING")
            
            return value
        except Exception as e:
            self._log_message(f"❌ Error retrieving {key}: {e}", "ERROR")
            return default
    
    # ============================================================================
    # 🤖 GEMINI AI API CONFIGURATION
    # ============================================================================
    
    def get_gemini_api_key(self) -> str:
        """Get Gemini AI API key"""
        return self._get_env_value("GEMINI_API_KEY", required=True)
    
    def get_gemini_config(self) -> Dict[str, Any]:
        """Get complete Gemini AI configuration"""
        return {
            "api_key": self.get_gemini_api_key(),
            "model": self._get_env_value("GEMINI_MODEL", "gemini-1.5-flash"),
            "temperature": float(self._get_env_value("GEMINI_TEMPERATURE", "0.7")),
            "max_tokens": int(self._get_env_value("GEMINI_MAX_TOKENS", "8192")),
            "timeout": int(self._get_env_value("GEMINI_TIMEOUT", "30"))
        }
    
    # ============================================================================
    # 🗄️ MONGODB ATLAS DATABASE CONFIGURATION
    # ============================================================================
    
    def get_mongodb_uri(self) -> str:
        """Get MongoDB Atlas connection URI"""
        # Try both URI formats for compatibility
        uri = self._get_env_value("MONGODB_URI") or self._get_env_value("MONGODB_CONNECTION_STRING")
        if not uri:
            raise ValueError("MongoDB URI not configured")
        return uri
    
    def get_mongodb_database(self) -> str:
        """Get MongoDB database name"""
        return self._get_env_value("MONGODB_DATABASE", "deeplica-dev")
    
    def get_mongodb_config(self) -> Dict[str, Any]:
        """Get complete MongoDB configuration"""
        return {
            "uri": self.get_mongodb_uri(),
            "database": self.get_mongodb_database(),
            "use_mock": self._get_env_value("USE_MOCK_DATABASE", "false").lower() == "true",
            "force_mock": self._get_env_value("FORCE_MOCK_DATABASE", "false").lower() == "true",
            "use_real": self._get_env_value("USE_REAL_DATABASE", "true").lower() == "true",
            "timeout": int(self._get_env_value("MONGODB_TIMEOUT", "5000"))
        }
    
    # ============================================================================
    # 📞 TWILIO PHONE SERVICE CONFIGURATION
    # ============================================================================
    
    def get_twilio_account_sid(self) -> str:
        """Get Twilio Account SID"""
        return self._get_env_value("TWILIO_ACCOUNT_SID", required=True)
    
    def get_twilio_auth_token(self) -> str:
        """Get Twilio Auth Token"""
        return self._get_env_value("TWILIO_AUTH_TOKEN", required=True)
    
    def get_twilio_phone_number(self) -> str:
        """Get Twilio Phone Number"""
        return self._get_env_value("TWILIO_PHONE_NUMBER", required=True)
    
    def get_twilio_config(self) -> Dict[str, Any]:
        """Get complete Twilio configuration"""
        return {
            "account_sid": self.get_twilio_account_sid(),
            "auth_token": self.get_twilio_auth_token(),
            "phone_number": self.get_twilio_phone_number(),
            "webhook_url": self._get_env_value("TWILIO_WEBHOOK_URL", ""),
            "timeout": int(self._get_env_value("TWILIO_TIMEOUT", "30"))
        }
    
    # ============================================================================
    # 🌐 NGROK TUNNEL SERVICE CONFIGURATION
    # ============================================================================
    
    def get_ngrok_api_key(self) -> str:
        """Get Ngrok API key"""
        return self._get_env_value("NGROK_API_KEY", required=True)
    
    def get_ngrok_webhook_url(self) -> str:
        """Get current Ngrok webhook URL"""
        return self._get_env_value("TWILIO_WEBHOOK_URL", "")
    
    def get_ngrok_config(self) -> Dict[str, Any]:
        """Get complete Ngrok configuration"""
        return {
            "api_key": self.get_ngrok_api_key(),
            "webhook_url": self.get_ngrok_webhook_url(),
            "tunnel_port": self._get_env_value("NGROK_TUNNEL_PORT", "8080"),
            "api_port": self._get_env_value("NGROK_API_PORT", "4040"),
            "timeout": int(self._get_env_value("NGROK_TIMEOUT", "10"))
        }
    
    # ============================================================================
    # 🌐 SERVICE URLS AND ENDPOINTS
    # ============================================================================
    
    def get_service_url(self, service: str) -> str:
        """Get service URL with fallback to port manager"""
        try:
            from shared.port_manager import get_service_port, get_localhost
            
            # Service URL mappings
            url_mappings = {
                "backend": self._get_env_value("BACKEND_URL", f"http://{get_localhost()}:{get_service_port('backend')}"),
                "dispatcher": self._get_env_value("DISPATCHER_URL", f"http://{get_localhost()}:{get_service_port('dispatcher')}"),
                "dialogue": self._get_env_value("DIALOGUE_AGENT_URL", f"http://{get_localhost()}:{get_service_port('dialogue')}"),
                "planner": self._get_env_value("PLANNER_AGENT_URL", f"http://{get_localhost()}:{get_service_port('planner')}"),
                "phone": self._get_env_value("PHONE_AGENT_URL", f"http://{get_localhost()}:{get_service_port('phone')}"),
                "web-chat": f"http://{get_localhost()}:{get_service_port('web-chat')}",
                "watchdog": f"http://{get_localhost()}:{get_service_port('watchdog')}"
            }
            
            return url_mappings.get(service, f"http://{get_localhost()}:{get_service_port(service)}")
        except Exception as e:
            self._log_message(f"❌ Error getting service URL for {service}: {e}", "ERROR")
            return f"http://localhost:8000"  # Fallback
    
    # ============================================================================
    # ⚙️ SYSTEM CONFIGURATION PARAMETERS
    # ============================================================================
    
    def get_system_config(self) -> Dict[str, Any]:
        """Get system-wide configuration parameters"""
        return {
            "environment": self._get_env_value("ENVIRONMENT", "local"),
            "debug": self._get_env_value("DEBUG", "false").lower() == "true",
            "wait_for_backend": self._get_env_value("WAIT_FOR_BACKEND", "true").lower() == "true",
            "backend_ready_timeout": int(self._get_env_value("BACKEND_READY_TIMEOUT", "120")),
            "service_startup_timeout": int(self._get_env_value("SERVICE_STARTUP_TIMEOUT", "30")),
            "health_check_timeout": int(self._get_env_value("HEALTH_CHECK_TIMEOUT", "10")),
            "default_host": self._get_env_value("DEFAULT_HOST", "0.0.0.0"),
            "external_host": self._get_env_value("EXTERNAL_HOST", "0.0.0.0"),
            "web_host": self._get_env_value("WEB_HOST", "0.0.0.0")
        }
    
    # ============================================================================
    # 🔄 CONFIGURATION UPDATE METHODS
    # ============================================================================
    
    def update_webhook_url(self, new_url: str) -> bool:
        """Update webhook URL in environment"""
        try:
            # Update environment variable
            os.environ["TWILIO_WEBHOOK_URL"] = new_url
            
            # Update .env file
            if self.ENV_FILE.exists():
                with open(self.ENV_FILE, 'r') as f:
                    lines = f.readlines()
                
                updated = False
                for i, line in enumerate(lines):
                    if line.startswith("TWILIO_WEBHOOK_URL="):
                        lines[i] = f"TWILIO_WEBHOOK_URL={new_url}\n"
                        updated = True
                        break
                
                if not updated:
                    lines.append(f"TWILIO_WEBHOOK_URL={new_url}\n")
                
                with open(self.ENV_FILE, 'w') as f:
                    f.writelines(lines)
                
                self._log_message(f"✅ Webhook URL updated: {new_url}")
                return True
            
        except Exception as e:
            self._log_message(f"❌ Error updating webhook URL: {e}", "ERROR")
            return False
    
    def get_all_external_configs(self) -> Dict[str, Dict[str, Any]]:
        """Get all external service configurations for admin interface"""
        return {
            "gemini": self.get_gemini_config(),
            "mongodb": self.get_mongodb_config(),
            "twilio": self.get_twilio_config(),
            "ngrok": self.get_ngrok_config(),
            "system": self.get_system_config()
        }

# ============================================================================
# 🔑 GLOBAL API MANAGER INSTANCE AND CONVENIENCE FUNCTIONS
# ============================================================================

# Global instance for efficient access
_api_manager = None

def get_api_manager() -> APIManager:
    """Get global API manager instance"""
    global _api_manager
    if _api_manager is None:
        _api_manager = APIManager()
    return _api_manager

# Convenience functions for common operations
def get_gemini_api_key() -> str:
    """Get Gemini API key"""
    return get_api_manager().get_gemini_api_key()

def get_gemini_config() -> Dict[str, Any]:
    """Get Gemini configuration"""
    return get_api_manager().get_gemini_config()

def get_mongodb_config() -> Dict[str, Any]:
    """Get MongoDB configuration"""
    return get_api_manager().get_mongodb_config()

def get_twilio_config() -> Dict[str, Any]:
    """Get Twilio configuration"""
    return get_api_manager().get_twilio_config()

def get_ngrok_config() -> Dict[str, Any]:
    """Get Ngrok configuration"""
    return get_api_manager().get_ngrok_config()

def get_service_url(service: str) -> str:
    """Get service URL"""
    return get_api_manager().get_service_url(service)

def get_system_config() -> Dict[str, Any]:
    """Get system configuration"""
    return get_api_manager().get_system_config()

def update_webhook_url(new_url: str) -> bool:
    """Update webhook URL"""
    return get_api_manager().update_webhook_url(new_url)

if __name__ == "__main__":
    # Test the API manager
    print("🔑 DEEPLICA API MANAGER TEST")
    print("=" * 50)
    
    api_mgr = APIManager()
    
    try:
        print("🤖 Gemini Config:", api_mgr.get_gemini_config())
        print("🗄️ MongoDB Config:", api_mgr.get_mongodb_config())
        print("📞 Twilio Config:", api_mgr.get_twilio_config())
        print("🌐 Ngrok Config:", api_mgr.get_ngrok_config())
        print("⚙️ System Config:", api_mgr.get_system_config())
        print("\n✅ API Manager test completed successfully!")
    except Exception as e:
        print(f"❌ API Manager test failed: {e}")
