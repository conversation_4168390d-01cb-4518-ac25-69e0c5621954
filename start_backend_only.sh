#!/bin/bash
# =============================================================================
# DEEPLICA SERVICE SCRIPT - ALL PORTS MANAGED DYNAMICALLY
# =============================================================================
# ALL ports are assigned by shared/port_manager.py - NO HARDCODED PORTS
# External services adapt to assigned ports through configuration
# =============================================================================
echo "🚀 Starting Backend API ONLY (core service)"
echo "📋 Backend provides: Database, LLM, Core APIs"
echo "⚠️  Start other microservices manually after backend is ready"
echo ""
cd backend
PORT = get_service_port("backend") python3 -m app.main
