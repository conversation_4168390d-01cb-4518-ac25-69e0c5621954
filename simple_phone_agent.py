#!/usr/bin/env python3
"""
Simple Phone Agent Service
A working phone agent that can handle phone call tasks from the Dispatcher.
"""

import os
import sys
import json
import asyncio
import uvicorn
from datetime import datetime
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add project root to path
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, PROJECT_ROOT)

app = FastAPI(title="Simple Phone Agent", description="Handles phone call tasks", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class MissionContext(BaseModel):
    mission_id: str
    user_input: str
    title: str = ""
    description: str = ""
    priority: str = "normal"
    status: str
    created_at: str
    started_at: str = None
    progress: dict = {}

class TaskRequest(BaseModel):
    task_id: str
    mission_id: str
    task_type: str
    task_data: dict
    mission_context: MissionContext
    callback_url: str
    context: dict = {}

class TaskResponse(BaseModel):
    task_id: str
    status: str
    message: str
    result: dict = {}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "Simple Phone Agent",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }

@app.post("/execute", response_model=TaskResponse)
async def execute_task(request: TaskRequest):
    """Execute a phone call task"""
    try:
        print(f"📞 Phone Agent: Received task {request.task_id}")
        print(f"   Type: {request.task_type}")
        print(f"   Mission: {request.mission_id}")
        print(f"   Task Data: {request.task_data}")
        print(f"   User Input: {request.mission_context.user_input}")

        if request.task_type != "phone_call":
            raise HTTPException(status_code=400, detail=f"Unsupported task type: {request.task_type}")

        # Extract phone number and message from task data and mission context
        task_description = request.task_data.get("description", "")
        user_input = request.mission_context.user_input

        # Try to extract phone number from user input or task description
        import re
        phone_number = None

        # Look for phone number in user input first
        phone_match = re.search(r'\+?\d{10,15}', user_input)
        if phone_match:
            phone_number = phone_match.group()

        # If not found, look in task description
        if not phone_number and task_description:
            phone_match = re.search(r'\+?\d{10,15}', task_description)
            if phone_match:
                phone_number = phone_match.group()

        # Extract message from user input
        message = "Hello from DEEPLICA! I love you! This is a test call to verify the phone system is working correctly."
        if "say" in user_input.lower():
            # Try to extract what to say
            say_match = re.search(r'say\s+(.+)', user_input.lower())
            if say_match:
                message = f"Hello from DEEPLICA! {say_match.group(1).strip()}"
        
        if not phone_number:
            return TaskResponse(
                task_id=request.task_id,
                status="failed",
                message="No phone number found in task",
                result={"error": "Missing phone number"}
            )
        
        # Make the actual phone call using Twilio
        call_result = await make_twilio_call(phone_number, message)

        # Prepare response
        if call_result["success"]:
            print(f"✅ Phone call successful: {call_result['call_sid']}")
            response = TaskResponse(
                task_id=request.task_id,
                status="completed",
                message=f"Phone call completed successfully to {phone_number}",
                result={
                    "phone_number": phone_number,
                    "call_sid": call_result["call_sid"],
                    "call_status": call_result["status"],
                    "message": message,
                    "timestamp": datetime.now().isoformat()
                }
            )
        else:
            print(f"❌ Phone call failed: {call_result['error']}")
            response = TaskResponse(
                task_id=request.task_id,
                status="failed",
                message=f"Phone call failed: {call_result['error']}",
                result={
                    "phone_number": phone_number,
                    "error": call_result["error"],
                    "message": message,
                    "timestamp": datetime.now().isoformat()
                }
            )

        # Send callback to Dispatcher
        await send_callback_to_dispatcher(request, response)

        return response
        
    except Exception as e:
        print(f"❌ Phone Agent error: {e}")
        return TaskResponse(
            task_id=request.task_id,
            status="failed",
            message=f"Phone agent error: {str(e)}",
            result={"error": str(e)}
        )

async def send_callback_to_dispatcher(request: TaskRequest, response: TaskResponse):
    """Send completion callback to the Dispatcher"""
    try:
        import httpx

        callback_data = {
            "task_id": request.task_id,
            "mission_id": request.mission_id,
            "status": response.status,
            "result": response.result,
            "error_message": response.message if response.status == "failed" else None
        }

        print(f"📤 Sending callback to Dispatcher: {request.callback_url}")
        print(f"   Status: {response.status}")

        async with httpx.AsyncClient() as client:
            callback_response = await client.post(
                request.callback_url,
                json=callback_data,
                timeout=10.0
            )
            callback_response.raise_for_status()
            print(f"✅ Callback sent successfully to Dispatcher")

    except Exception as e:
        print(f"❌ Failed to send callback to Dispatcher: {e}")
        # Don't raise - the task was still completed

async def make_twilio_call(phone_number: str, message: str):
    """Make a phone call using Twilio"""
    try:
        # Check if Twilio credentials are available
        twilio_sid = os.getenv("TWILIO_ACCOUNT_SID")
        twilio_token = os.getenv("TWILIO_AUTH_TOKEN")
        twilio_phone = os.getenv("TWILIO_PHONE_NUMBER")
        
        if not all([twilio_sid, twilio_token, twilio_phone]):
            return {
                "success": False,
                "error": "Twilio credentials not configured"
            }
        
        from twilio.rest import Client
        client = Client(twilio_sid, twilio_token)
        
        # Create TwiML for the call
        twiml = f'<Response><Say>{message}</Say></Response>'
        
        # Make the call
        call = client.calls.create(
            twiml=twiml,
            to=phone_number,
            from_=twilio_phone
        )
        
        return {
            "success": True,
            "call_sid": call.sid,
            "status": call.status
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

if __name__ == "__main__":
    print("🚀 Starting Simple Phone Agent...")
    print("📞 Ready to handle phone call tasks")
    
    # Run on port 8004 (Phone Agent port)
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8004,
        log_level="info"
    )
