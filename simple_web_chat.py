#!/usr/bin/env python3
"""
Simplified DeepChat Web Server - Core functionality only
"""

import os
import sys
import json
import asyncio
import time
from datetime import datetime
from typing import Dict

import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
import httpx

# Simple connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        
    async def connect(self, websocket: WebSocket, user_id: str, username: str):
        connection_id = f"conn_{int(time.time())}_{len(self.active_connections)}"
        self.active_connections[connection_id] = websocket
        print(f"🔗 Connected: {username} ({connection_id})")
        return connection_id
        
    async def disconnect(self, connection_id: str):
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]
            print(f"🔌 Disconnected: {connection_id}")
            
    async def send_personal_message(self, message: str, connection_id: str):
        if connection_id in self.active_connections:
            try:
                websocket = self.active_connections[connection_id]
                await websocket.send_text(message)
                return True
            except Exception as e:
                await self.disconnect(connection_id)
                print(f"❌ Failed to send message: {e}")
                return False
        return False

# Simple Deeplica client
class DeepplicaClient:
    def __init__(self):
        self.cli_url = "http://localhost:8003"  # Default CLI port
        
    async def send_message(self, message: str, username: str) -> str:
        """Send message to Deeplica CLI"""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.post(
                    f"{self.cli_url}/chat",
                    json={
                        "user": username,
                        "message": message,
                        "sender": "user"
                    }
                )
                if response.status_code == 200:
                    result = response.json()
                    return result.get("response", "I received your message!")
                else:
                    return "I'm having trouble processing your request right now."
        except Exception as e:
            print(f"❌ Error communicating with Deeplica: {e}")
            return "I'm currently offline. Please try again later."

# Create FastAPI app
app = FastAPI(title="Simple DeepChat", version="1.0.0")

# Setup templates
templates = Jinja2Templates(directory="web_chat/templates")

# Create instances
manager = ConnectionManager()
deeplica_client = DeepplicaClient()

@app.get("/", response_class=HTMLResponse)
async def root():
    return HTMLResponse("""
    <html>
        <head><title>DeepChat</title></head>
        <body>
            <h1>DeepChat is Running!</h1>
            <p><a href="/chat">Go to Chat</a></p>
        </body>
    </html>
    """)

@app.get("/chat", response_class=HTMLResponse)
async def chat_page(request: Request):
    """Simple chat page"""
    user = {
        "username": "Guest",
        "user_id": f"guest_{int(time.time())}",
        "full_name": "Guest User"
    }
    session_id = f"session_{int(time.time())}"
    
    return templates.TemplateResponse("simple_chat.html", {
        "request": request,
        "user": user,
        "session_id": session_id
    })

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for chat"""
    connection_id = None
    user = {
        "username": "Guest",
        "user_id": f"guest_{int(time.time())}",
        "full_name": "Guest User"
    }
    
    try:
        # Accept connection
        await websocket.accept()
        print(f"✅ WebSocket accepted for {user['username']}")
        connection_id = await manager.connect(websocket, user["user_id"], user["username"])
        
        # Send connection confirmation
        connection_msg = {
            "type": "connection",
            "status": "connected",
            "user": user["username"],
            "timestamp": datetime.now().isoformat()
        }
        await manager.send_personal_message(json.dumps(connection_msg), connection_id)
        
        # Message handling loop
        while True:
            try:
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                if message_data.get("type") == "message":
                    user_message = message_data.get("content", "").strip()
                    if not user_message:
                        continue
                        
                    print(f"💬 Received: {user_message}")
                    
                    # Echo user message back
                    user_msg = {
                        "type": "message",
                        "sender": "user",
                        "content": user_message,
                        "timestamp": datetime.now().isoformat()
                    }
                    await manager.send_personal_message(json.dumps(user_msg), connection_id)
                    
                    # Send typing indicator
                    typing_msg = {"type": "typing", "sender": "deeplica"}
                    await manager.send_personal_message(json.dumps(typing_msg), connection_id)
                    
                    # Get Deeplica response
                    deeplica_response = await deeplica_client.send_message(user_message, user["username"])
                    
                    # Send Deeplica response
                    response_msg = {
                        "type": "message",
                        "sender": "deeplica",
                        "content": deeplica_response,
                        "timestamp": datetime.now().isoformat()
                    }
                    await manager.send_personal_message(json.dumps(response_msg), connection_id)
                    
            except Exception as e:
                print(f"❌ Error in message loop: {e}")
                break
                
    except WebSocketDisconnect:
        if connection_id:
            await manager.disconnect(connection_id)
        print(f"🔌 WebSocket disconnected: {user['username']}")
    except Exception as e:
        if connection_id:
            await manager.disconnect(connection_id)
        print(f"❌ WebSocket error: {e}")

if __name__ == "__main__":
    print("🚀 Starting Simple DeepChat...")
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")
