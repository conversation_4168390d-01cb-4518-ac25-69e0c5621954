"""
Phone Agent - Handles phone call tasks to get answers to specific questions.
"""

import asyncio
import sys
import os
import time
from typing import Dict, Any, Optional, List, Union
from shared.unified_logging import get_phone_logger
from shared.port_manager import get_service_port

# Initialize unified logger
logger = get_phone_logger()
import httpx

from .models import PhoneCallConfig, CallState, CallStatus, CallLanguage
from .twilio_service import TwilioService
from .llm_service import BackendLLMClient

# Add project root to path for shared models
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared_models.communication import TaskResponse


class CircuitBreaker:
    """
    🛡️ PREVENTIVE MEASURE: Circuit breaker to prevent phone calls when system is unstable.

    This prevents the system from calling users during system failures.
    """

    def __init__(self, failure_threshold: int = 3, recovery_timeout: int = 300):
        self.failure_threshold = failure_threshold  # Number of failures before opening circuit
        self.recovery_timeout = recovery_timeout    # Seconds to wait before trying again
        self.failure_count = 0
        self.last_failure_time = 0
        self.state = "CLOSED"  # CLOSED (normal), OPEN (blocking calls), HALF_OPEN (testing)

    def record_success(self):
        """Record a successful operation"""
        self.failure_count = 0
        self.state = "CLOSED"
        logger.debug("circuit closed", module="CIRCUIT-BREAKER", routine="Success recorded")

    def record_failure(self):
        """Record a failed operation"""
        self.failure_count += 1
        self.last_failure_time = time.time()

        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"
            logger.warning(f"Circuit opened after {self.failure_count} failures - blocking phone calls", module="circuit_breaker", routine="record_failure")
        else:
            logger.warning(f"Failure recorded ({self.failure_count}/{self.failure_threshold})", module="circuit_breaker", routine="record_failure")

    def can_execute(self) -> bool:
        """Check if operation can be executed"""
        if self.state == "CLOSED":
            return True

        if self.state == "OPEN":
            # Check if recovery timeout has passed
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = "HALF_OPEN"
                logger.info("open - testing system recovery", module="CIRCUIT-BREAKER", routine="Circuit half")
                return True
            else:
                remaining = self.recovery_timeout - (time.time() - self.last_failure_time)
                logger.warning("blocking calls for {remaining:.0f} more seconds", module="CIRCUIT-BREAKER", routine="Circuit open")
                return False

        if self.state == "HALF_OPEN":
            # Allow one test call
            return True

        return False

    def reset(self):
        """Reset circuit breaker to closed state - for testing/debugging"""
        self.failure_count = 0
        self.last_failure_time = 0
        self.state = "CLOSED"
        logger.info("calls enabled", module="CIRCUIT-BREAKER", routine="Circuit breaker manually reset")

    def get_status(self) -> dict:
        """Get current circuit breaker status"""
        remaining_time = 0
        if self.state == "OPEN":
            remaining_time = max(0, self.recovery_timeout - (time.time() - self.last_failure_time))

        return {
            "state": self.state,
            "failure_count": self.failure_count,
            "failure_threshold": self.failure_threshold,
            "recovery_timeout": self.recovery_timeout,
            "remaining_recovery_time": remaining_time,
            "can_execute": self.can_execute()
        }


class PhoneAgent:
    """Agent that makes phone calls to get answers to specific questions"""
    
    def __init__(self, llm_service: BackendLLMClient, twilio_service: TwilioService):
        """Initialize the phone agent"""
        self.llm_service = llm_service
        self.twilio_service = twilio_service
        # 🛡️ PREVENTIVE MEASURE 4: Circuit breaker to prevent calls during system instability
        self.circuit_breaker = CircuitBreaker(failure_threshold=3, recovery_timeout=300)
        self.http_client = httpx.AsyncClient()
        
        logger.info("📞 Phone Agent initialized", module="PHONE-AGENT", routine="PhoneAgent.__init__()")
        logger.debug("📊 Configuration: llm_service={type(llm_service).__name__}, twilio_service={type(twilio_service).__name__}", module="PHONE-AGENT", routine="PhoneAgent.__init__()")
    
    async def execute_task_async(self, request) -> None:
        """
        Execute a phone call task asynchronously and callback to dispatcher.
        This is the main entry point for the microservice.
        """
        logger.info("📞 Executing phone call task: {request.task_id}", module="PHONE-AGENT", routine="execute_task_async()")
        logger.debug("📊 Request details: task_id={request.task_id}, task_type={request.task_type}", module="PHONE-AGENT", routine="execute_task_async()")
        logger.debug("📋 Task data: {request.task_data}", module="PHONE-AGENT", routine="execute_task_async()")

        try:
            # Execute the phone call task
            logger.debug("🔄 Starting phone call execution...", module="PHONE-AGENT", routine="execute_task_async()")
            result = await self._execute_phone_call_task(request)
            logger.debug("✅ Phone call execution completed: {result}", module="PHONE-AGENT", routine="execute_task_async()")

            # Determine completion status
            if result.get("call_status") == "failed":
                status = "failed"
                error_message = result.get("error_message")
                logger.error("❌ Phone call failed: {error_message}", module="PHONE-AGENT", routine="execute_task_async()")
            else:
                status = "completed"
                error_message = None

            # Send callback to dispatcher
            await self._send_callback(request, status, result, error_message)
            
            logger.info("[PHONE-AGENT:execute_task_async", module="main", routine="unknown")
            
        except Exception as e:
            logger.error(f"❌ Failed to execute phone call task {request.task_id}: {e}", module="PHONE-AGENT", routine="execute_task_async")

            # 🛡️ BULLETPROOF: Send failure callback but don't crash if it fails
            try:
                await self._send_callback(
                    request,
                    "failed",
                    {"call_status": "failed", "error_message": str(e)},
                    error_message=str(e)
                )
            except Exception as callback_error:
                logger.error(f"❌ Failed to send failure callback (service continues): {callback_error}", module="PHONE-AGENT", routine="execute_task_async")

            # 🛡️ CRITICAL: DO NOT re-raise exception - service must continue running
            logger.info(f"🛡️ Phone service continues despite task failure - bulletproof design", module="PHONE-AGENT", routine="execute_task_async")
    
    async def _execute_phone_call_task(self, request) -> Dict[str, Any]:
        """Execute the actual phone call task logic"""
        logger.debug("🔄 Starting phone call task execution", module="PHONE-AGENT", routine="_execute_phone_call_task()")
        logger.debug("📊 Request: task_id={request.task_id}, mission_id={request.mission_id}", module="PHONE-AGENT", routine="_execute_phone_call_task()")

        try:
            # 🛡️ PREVENTIVE MEASURE 1: Circuit breaker check
            if not self.circuit_breaker.can_execute():
                logger.warning("⚠️ Circuit breaker open - aborting call to prevent error calls", module="PHONE-AGENT", routine="_execute_phone_call_task()")
                return {
                    "call_status": "failed",
                    "error_message": "Circuit breaker open - call aborted to prevent error notifications during system instability",
                    "conversation_transcript": [],
                    "extracted_answer": None,
                    "question_answered": False,
                    "preventive_abort": True  # Flag to indicate this was a preventive abort
                }

            # 🛡️ PREVENTIVE MEASURE 2: System health check before making calls
            if not await self._check_system_health():
                logger.warning("⚠️ System health check failed - aborting call to prevent error calls", module="PHONE-AGENT", routine="_execute_phone_call_task()")
                self.circuit_breaker.record_failure()  # Record health check failure
                return {
                    "call_status": "failed",
                    "error_message": "System health check failed - call aborted to prevent error notifications",
                    "conversation_transcript": [],
                    "extracted_answer": None,
                    "question_answered": False,
                    "preventive_abort": True  # Flag to indicate this was a preventive abort
                }

            # Parse phone call configuration from task data
            logger.debug("📋 Parsing call configuration from task data...", module="PHONE-AGENT", routine="_execute_phone_call_task()")
            logger.debug("📊 Task data: {request.task_data}", module="PHONE-AGENT", routine="_execute_phone_call_task()")
            call_config = self._parse_call_config(request.task_data)
            logger.debug("✅ Call config parsed: phone={call_config.phone_number}, contact={call_config.contact_name}", module="PHONE-AGENT", routine="_execute_phone_call_task()")

            # Create call state
            logger.debug("📋 Creating call state...", module="PHONE-AGENT", routine="_execute_phone_call_task()")
            call_state = CallState(
                task_id=request.task_id,
                mission_id=request.mission_id,
                call_config=call_config,
                callback_url=request.callback_url
            )
            logger.debug("✅ Call state created: task_id={call_state.task_id}", module="PHONE-AGENT", routine="_execute_phone_call_task()")

            # Initiate the phone call
            logger.debug("📞 Initiating phone call to {call_config.phone_number}...", module="PHONE-AGENT", routine="_execute_phone_call_task()")
            call_sid = await self.twilio_service.initiate_call(call_state)

            logger.info("📞 Phone call initiated: {call_sid}", module="PHONE-AGENT", routine="_execute_phone_call_task()")

            # Wait for call to complete or timeout
            logger.debug("⏳ Monitoring call completion...", module="PHONE-AGENT", routine="_execute_phone_call_task()")
            result = await self._monitor_call_completion(call_state)
            logger.debug("✅ Call monitoring completed: {result}", module="PHONE-AGENT", routine="_execute_phone_call_task()")

            # 🛡️ Record success/failure in circuit breaker
            if result.get("call_status") == "completed":
                self.circuit_breaker.record_success()
            else:
                self.circuit_breaker.record_failure()

            return result

        except Exception as e:
            logger.error("❌ Error executing phone call task: {e}", module="PHONE-AGENT", routine="_execute_phone_call_task()")
            logger.error("🔍 Exception details: type={type(e).__name__}, args={e.args}", module="PHONE-AGENT", routine="_execute_phone_call_task()")
            import traceback
            logger.error("📋 Traceback: {traceback.format_exc()}", module="PHONE-AGENT", routine="_execute_phone_call_task()")

            # 🛡️ Record failure in circuit breaker
            self.circuit_breaker.record_failure()

            return {
                "call_status": "failed",
                "error_message": str(e),
                "conversation_transcript": [],
                "extracted_answer": None,
                "question_answered": False
            }

    async def _check_system_health(self) -> bool:
        """
        🛡️ PREVENTIVE MEASURE: Check system health before making phone calls.

        This prevents the system from calling users just to tell them about errors.
        Includes comprehensive external service checks with auto-recovery attempts.

        Returns:
            True if system is healthy enough to make calls, False otherwise
        """
        try:
            logger.debug("🔍 Comprehensive system health check before phone call...", module="PHONE-AGENT", routine="_check_system_health()")

            # Check 1: Backend API connectivity with bulletproof error handling
            try:
                import httpx
                # 🛡️ BULLETPROOF: Use shorter timeout to avoid hanging
                timeout = httpx.Timeout(3.0, connect=1.0, read=2.0)
                async with httpx.AsyncClient(timeout=timeout) as client:
                    from shared.port_manager import get_service_port, get_service_host
                    backend_url = f"http://{get_service_host('backend')}:{get_service_port('backend')}"
                    response = await client.get(f"{backend_url}/health")
                    if response.status_code != 200:
                        logger.warning(f"⚠️ Backend API unhealthy: {response.status_code}", module="PHONE-AGENT", routine="_check_system_health()")
                        return False
                    # Also check if database is ready
                    try:
                        health_data = response.json()
                        if not health_data.get("database_ready", False):
                            logger.warning("⚠️ Backend database not ready", module="PHONE-AGENT", routine="_check_system_health()")
                            return False
                    except (ValueError, TypeError, KeyError) as json_error:
                        logger.warning(f"⚠️ Backend health response invalid JSON: {json_error}", module="PHONE-AGENT", routine="_check_system_health()")
                        return False
            except (httpx.ConnectError, httpx.TimeoutException, httpx.ReadTimeout) as e:
                logger.warning(f"⚠️ Backend API connection failed: {e}", module="PHONE-AGENT", routine="_check_system_health()")
                return False
            except Exception as e:
                logger.warning(f"⚠️ Backend API check failed: {e}", module="PHONE-AGENT", routine="_check_system_health()")
                return False

            # Check 2: Dispatcher connectivity
            try:
                async with httpx.AsyncClient(timeout=5.0) as client:
                    response = await client.get(f"http://localhost:{get_service_port('dispatcher')}/health")
                    if response.status_code != 200:
                        logger.warning("⚠️ Dispatcher unhealthy: {response.status_code}", module="PHONE-AGENT", routine="_check_system_health()")
                        return False
            except Exception as e:
                logger.warning("⚠️ Dispatcher unreachable: {e}", module="PHONE-AGENT", routine="_check_system_health()")
                return False

            # Check 3: Twilio service health and connectivity
            if not self.twilio_service:
                logger.warning("⚠️ Twilio service not initialized", module="PHONE-AGENT", routine="_check_system_health()")
                return False

            # Test actual Twilio connection
            try:
                from twilio.rest import Client
                import os
                twilio_sid = os.getenv('TWILIO_ACCOUNT_SID')
                twilio_token = os.getenv('TWILIO_AUTH_TOKEN')
                if not twilio_sid or not twilio_token:
                    logger.warning("⚠️ Twilio credentials not configured", module="PHONE-AGENT", routine="_check_system_health()")
                    return False

                client = Client(twilio_sid, twilio_token)
                # Test connection by fetching account info
                account = client.api.accounts(twilio_sid).fetch()
                logger.debug("✅ Twilio connection verified", module="PHONE-AGENT", routine="_check_system_health()")
            except Exception as e:
                logger.warning("⚠️ Twilio connection failed: {e}", module="PHONE-AGENT", routine="_check_system_health()")
                return False

            # Check 4: ngrok tunnel status and webhook accessibility
            try:
                # Check if ngrok is running
                async with httpx.AsyncClient(timeout=3.0) as client:
                    response = await client.get(f"http://localhost:{get_service_port('ngrok-api')}/api/tunnels")
                    if response.status_code != 200:
                        logger.warning("⚠️ ngrok not running", module="PHONE-AGENT", routine="_check_system_health()")
                        return False

                    tunnels_data = response.json()
                    tunnels = tunnels_data.get('tunnels', [])
                    if not tunnels:
                        logger.warning("⚠️ No ngrok tunnels found", module="PHONE-AGENT", routine="_check_system_health()")
                        return False

                    # Get the HTTPS tunnel URL
                    tunnel_url = None
                    for tunnel in tunnels:
                        if tunnel.get('proto') == 'https':
                            tunnel_url = tunnel.get('public_url')
                            break

                    if not tunnel_url:
                        logger.warning("⚠️ No HTTPS ngrok tunnel found", module="PHONE-AGENT", routine="_check_system_health()")
                        return False

                    # Test webhook accessibility through ngrok
                    webhook_health_url = f"{tunnel_url}/health"
                    response = await client.get(webhook_health_url, timeout=5.0)
                    if response.status_code != 200:
                        logger.warning("⚠️ Webhook not accessible through ngrok: {response.status_code}", module="PHONE-AGENT", routine="_check_system_health()")
                        return False

                    logger.debug("✅ ngrok tunnel and webhook accessibility verified: {tunnel_url}", module="PHONE-AGENT", routine="_check_system_health()")

            except Exception as e:
                logger.debug("🔍 ngrok/webhook check failed (service continues running): {e}", module="PHONE-AGENT", routine="_check_system_health()")
                # Don't fail health check due to ngrok - service should continue running
                # return False  # REMOVED - service should be resilient to ngrok unavailability

            # Check 5: LLM service connectivity
            try:
                if hasattr(self, 'llm_service') and self.llm_service:
                    # Test LLM service with a simple request
                    test_response = await self.llm_service.generate_conversation_response(
                        question="Test",
                        context="Health check",
                        conversation_so_far="",
                        is_first_turn=True
                    )
                    if not test_response:
                        logger.warning("⚠️ LLM service not responding", module="PHONE-AGENT", routine="_check_system_health()")
                        return False
                    logger.debug("✅ LLM service verified", module="PHONE-AGENT", routine="_check_system_health()")
                else:
                    logger.warning("⚠️ LLM service not initialized", module="PHONE-AGENT", routine="_check_system_health()")
                    return False
            except Exception as e:
                logger.warning("⚠️ LLM service check failed: {e}", module="PHONE-AGENT", routine="_check_system_health()")
                return False

            logger.info("✅ Comprehensive system health check passed - ALL EXTERNAL SERVICES READY", module="PHONE-AGENT", routine="_check_system_health()")
            return True

        except Exception as e:
            logger.error("❌ Health check failed: {e}", module="PHONE-AGENT", routine="_check_system_health()")
            return False
    
    def _parse_call_config(self, task_data: Dict[str, Any]) -> PhoneCallConfig:
        """Parse phone call configuration from task data"""

        # Required fields - check both top level and nested context
        phone_number = task_data.get("phone_number")
        contact_name = task_data.get("contact_name", "Unknown")
        context = task_data.get("context", "")
        question = task_data.get("question")
        conversation_script = task_data.get("conversation_script")

        # If phone_number not found at top level, check nested context
        if not phone_number and isinstance(context, dict):
            phone_number = context.get("phone_number")
            if not contact_name or contact_name == "Unknown":
                contact_name = context.get("contact_name", "Unknown")
            if not question:
                question = context.get("question")
            if not conversation_script:
                conversation_script = context.get("conversation_script")
            # Update context to be the nested context string if available
            if "context" in context:
                context = context["context"]

        if not phone_number:
            raise ValueError("phone_number is required for phone call tasks")

        # 🛡️ BULLETPROOF: Check for parameter resolution errors and invalid formats
        if phone_number.startswith("INVALID_PHONE_NUMBER:"):
            invalid_number = phone_number.split(":", 1)[1]
            error_msg = f"❌ INVALID PHONE NUMBER: {invalid_number}"
            logger.error(f"{error_msg}", module="PHONE-AGENT", routine="_parse_call_config")
            print(f"\n{error_msg}")
            # [PHONE-AGENT:_parse_call_config] SYSTEM | ❌ The phone number '{invalid_number}' has an invalid format.
            # [PHONE-AGENT:_parse_call_config] SYSTEM | ❌ Phone numbers must be in international format (e.g., +123456789")
            raise ValueError(f"Invalid phone number format: {invalid_number}")

        if phone_number == "PHONE_NUMBER_NOT_FOUND":
            error_msg = "❌ PHONE NUMBER NOT FOUND"
            logger.error(f"{error_msg}", module="PHONE-AGENT", routine="_parse_call_config")
            print(f"\n{error_msg}")
            # [PHONE-AGENT:_parse_call_config] SYSTEM | ❌ Could not find a phone number to call.
            # [PHONE-AGENT:_parse_call_config] SYSTEM | ❌ Please provide a phone number in your request or ensure previous tasks collected one.
            raise ValueError("Phone number not found")

        # 🛡️ BULLETPROOF: Handle "user_will_provide" and similar placeholder values
        if phone_number.lower() in ["user_will_provide", "user will provide", "tbd", "to be determined", "pending", "unknown"]:
            error_msg = f"❌ PLACEHOLDER PHONE NUMBER: {phone_number} - requires user input"
            logger.error(f"{error_msg}", module="PHONE-AGENT", routine="_parse_call_config")
            print(f"\n{error_msg}")
            # [PHONE-AGENT:_parse_call_config] SYSTEM | ❌ Phone number is a placeholder that requires user input.
            # [PHONE-AGENT:_parse_call_config] SYSTEM | ❌ Please provide an actual phone number to call.
            raise ValueError(f"Invalid phone number format: {phone_number}")

        # Validate phone number format
        if not self._validate_phone_number(phone_number):
            error_msg = f"❌ INVALID PHONE NUMBER FORMAT: {phone_number}"
            logger.error("error_msg", module="PHONE-AGENT", routine="_parse_call_config")
            print(f"\n{error_msg}")
            # 15 digit")
            raise ValueError(f"Invalid phone number format: {phone_number}")

        # For script-based calls, question is optional
        if not question and not conversation_script:
            raise ValueError("Either 'question' or 'conversation_script' is required for phone call tasks")
        
        # Optional fields with defaults
        language_str = task_data.get("language", "en")
        try:
            language = CallLanguage(language_str)
        except ValueError:
            logger.warning("⚠️ Invalid language '{language_str}', using English", module="PHONE-AGENT", routine="_parse_call_config")
            language = CallLanguage.ENGLISH
        
        voice = task_data.get("voice")
        max_duration = task_data.get("max_duration_minutes", 5)
        
        return PhoneCallConfig(
            phone_number=phone_number,
            contact_name=contact_name,
            context=context,
            question=question or "Say the conversation script",
            language=language,
            voice=voice,
            max_duration_minutes=max_duration,
            conversation_script=conversation_script
        )

    def _validate_phone_number(self, phone_number: str) -> bool:
        """Validate phone number format"""
        import re

        if not phone_number:
            return False

        # Remove all non-digit characters except +
        cleaned = re.sub(r'[^\d+]', '', phone_number)

        # Must start with + for international format
        if not cleaned.startswith('+'):
            return False

        # Must have between 10-15 digits after the +
        digits_only = cleaned[1:]  # Remove the +
        if not digits_only.isdigit():
            return False

        if len(digits_only) < 10 or len(digits_only) > 15:
            return False

        return True
    
    async def _monitor_call_completion(self, call_state: CallState) -> Dict[str, Any]:
        """Monitor call until completion and return final result"""
        
        # Wait for call to complete (with timeout)
        max_wait_seconds = (call_state.call_config.max_duration_minutes * 60) + 30  # Extra buffer
        wait_interval = 2  # Check every 2 seconds
        elapsed = 0
        
        while elapsed < max_wait_seconds:
            current_state = self.twilio_service.get_call_state(call_state.task_id)
            
            if not current_state:
                # Call was removed from active calls (completed or failed)
                break
            
            if current_state.call_status in [CallStatus.COMPLETED, CallStatus.FAILED, 
                                           CallStatus.NO_ANSWER, CallStatus.BUSY, 
                                           CallStatus.DISCONNECTED]:
                break
            
            await asyncio.sleep(wait_interval)
            elapsed += wait_interval
        
        # Get final call result
        if call_state.task_id in self.twilio_service.active_calls:
            # Call still active - force end due to timeout
            await self.twilio_service.end_call(call_state.task_id, "monitoring_timeout")
        
        # Analyze conversation and build result
        return await self._build_final_result(call_state)
    
    async def _build_final_result(self, call_state: CallState) -> Dict[str, Any]:
        """Build final result with LLM analysis of conversation"""

        # Build conversation transcript string
        transcript_lines = []
        for turn in call_state.conversation:
            transcript_lines.append(f"{turn.speaker}: {turn.text}")
        transcript_str = "\n".join(transcript_lines)

        extracted_answer = None
        question_answered = False
        confirmation_received = False

        # Analyze conversation with LLM if we have transcript
        if transcript_str.strip():
            try:
                analysis = await self.llm_service.analyze_conversation(
                    question=call_state.call_config.question,
                    conversation_transcript=transcript_str,
                    context=call_state.call_config.context
                )

                if analysis.get("content"):
                    content = analysis["content"]
                    if isinstance(content, dict):
                        extracted_answer = content.get("extracted_answer")
                        question_answered = content.get("question_answered", False)
                        confirmation_received = content.get("confirmation_received", False)

                logger.info("🧠 LLM analysis: answered={question_answered}, confirmation={confirmation_received}, answer='{extracted_answer}'", module="PHONE-AGENT", routine="_build_final_result")

            except Exception as e:
                logger.error("❌ Error analyzing conversation: {e}", module="PHONE-AGENT", routine="_build_final_result")
        
        # Build call result (pass call_state since it may have been removed from active_calls)
        call_result = self.twilio_service.build_call_result(
            call_state.task_id,
            extracted_answer=extracted_answer,
            question_answered=question_answered,
            call_state=call_state
        )
        
        return {
            "call_status": call_result.call_status.value,
            "duration_seconds": call_result.duration_seconds,
            "conversation_transcript": [turn.model_dump() for turn in call_result.conversation_transcript],
            "extracted_answer": call_result.extracted_answer,
            "question_answered": call_result.question_answered,
            "confirmation_received": confirmation_received,
            "twilio_call_sid": call_result.twilio_call_sid,
            "disconnect_reason": call_result.disconnect_reason,
            "error_message": call_result.error_message
        }


    async def _send_callback(
        self,
        request,
        status: str,
        result: Dict[str, Any],
        error_message: Optional[str] = None
    ) -> None:
        """Send completion callback to dispatcher with bulletproof error handling"""

        callback_data = TaskResponse(
            task_id=request.task_id,
            mission_id=request.mission_id,
            status=status,
            result=result,
            error_message=error_message,
            suggested_tasks=None  # Phone agent doesn't suggest new tasks for now
        )

        # 🛡️ BULLETPROOF CALLBACK - Multiple retry attempts with exponential backoff
        max_retries = 5
        base_delay = 1.0

        for attempt in range(max_retries):
            try:
                logger.debug(f"📤 Sending callback attempt {attempt + 1}/{max_retries} to {request.callback_url}",
                           module="PHONE-AGENT", routine="_send_callback")

                # Use bulletproof timeout settings
                timeout = httpx.Timeout(30.0, connect=5.0, read=15.0, write=10.0)

                response = await self.http_client.post(
                    request.callback_url,
                    json=callback_data.model_dump(),
                    timeout=timeout
                )
                response.raise_for_status()

                logger.info(f"✅ Callback sent successfully to dispatcher: {request.task_id} (attempt {attempt + 1})",
                          module="PHONE-AGENT", routine="_send_callback")
                return  # Success - exit retry loop

            except httpx.HTTPStatusError as e:
                if e.response.status_code == 500:
                    logger.warning(f"⚠️ Backend API 500 error on attempt {attempt + 1}: {e} - will retry",
                                 module="PHONE-AGENT", routine="_send_callback")
                elif e.response.status_code in [502, 503, 504]:
                    logger.warning(f"⚠️ Backend API temporary error {e.response.status_code} on attempt {attempt + 1} - will retry",
                                 module="PHONE-AGENT", routine="_send_callback")
                else:
                    logger.error(f"❌ Backend API permanent error {e.response.status_code}: {e} - not retrying",
                               module="PHONE-AGENT", routine="_send_callback")
                    break  # Don't retry for permanent errors

            except (httpx.ConnectError, httpx.TimeoutException, httpx.ReadTimeout) as e:
                logger.warning(f"⚠️ Network error on attempt {attempt + 1}: {e} - will retry",
                             module="PHONE-AGENT", routine="_send_callback")

            except httpx.HTTPError as e:
                logger.warning(f"⚠️ HTTP error on attempt {attempt + 1}: {e} - will retry",
                             module="PHONE-AGENT", routine="_send_callback")

            except Exception as e:
                logger.warning(f"⚠️ Unexpected error on attempt {attempt + 1}: {e} - will retry",
                             module="PHONE-AGENT", routine="_send_callback")

            # Wait before retry with exponential backoff
            if attempt < max_retries - 1:
                delay = base_delay * (2 ** attempt)
                logger.debug(f"⏳ Waiting {delay}s before retry...", module="PHONE-AGENT", routine="_send_callback")
                await asyncio.sleep(delay)

        # 🛡️ BULLETPROOF: If all retries failed, log but DON'T crash the service
        logger.error(f"❌ All {max_retries} callback attempts failed for task {request.task_id} - service continues",
                   module="PHONE-AGENT", routine="_send_callback")

        # Store failed callback for later retry (optional enhancement)
        try:
            failed_callback = {
                "task_id": request.task_id,
                "callback_url": request.callback_url,
                "callback_data": callback_data.model_dump(),
                "timestamp": time.time(),
                "attempts": max_retries
            }
            logger.info(f"📝 Stored failed callback for potential retry: {failed_callback}",
                      module="PHONE-AGENT", routine="_send_callback")
        except Exception as store_error:
            logger.debug(f"Failed to store callback info: {store_error}", module="PHONE-AGENT", routine="_send_callback")

        # 🛡️ CRITICAL: DO NOT raise exception - service must continue running
        logger.info("🛡️ Phone service continues despite callback failure - bulletproof design",
                  module="PHONE-AGENT", routine="_send_callback")
