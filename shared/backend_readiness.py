"""
Backend Readiness Checker - Ensures microservices wait for backend to be fully operational

This module provides utilities for microservices to wait for the backend API to be
fully ready before starting their own initialization. It checks the /ready endpoint
and ensures all backend services (database, LLM, etc.) are operational.
"""

import asyncio
import os
import time
import httpx
from typing import Optional, Dict, Any
from shared.unified_logging import get_logger
from shared.port_manager import get_service_port

# Initialize unified logger
logger = get_logger("SHARED")


class BackendReadinessChecker:
    """
    Utility class to check if the backend API is fully ready for communications.
    
    This checker validates that:
    1. Backend API is responding
    2. Database is connected and ready
    3. LLM service is initialized
    4. All core services are operational
    """
    
    def __init__(self, backend_url: str = f"http://localhost:{get_service_port('backend')}", service_name: str = "microservice"):
        self.backend_url = backend_url.rstrip('/')
        self.service_name = service_name
        self.ready_endpoint = f"{self.backend_url}/ready"
        self.health_endpoint = f"{self.backend_url}/health"
        
    async def wait_for_backend_ready(
        self, 
        timeout_seconds: int = 300,
        check_interval: float = 2.0,
        log_interval: int = 15
    ) -> bool:
        """
        Wait for the backend API to be fully ready for communications.
        
        Args:
            timeout_seconds: Maximum time to wait (default: 5 minutes)
            check_interval: Time between checks in seconds (default: 2 seconds)
            log_interval: How often to log progress (every N attempts, default: 15)
            
        Returns:
            True if backend is ready, False if timeout reached
        """
        start_time = time.time()
        attempt = 0
        
        logger.info(f"[{self.service_name.upper()}] ⏳ Waiting for Backend API to be FULLY READY at {self.backend_url}")
        logger.info(f"[{self.service_name.upper()}] 🔄 Will check /ready endpoint every {check_interval}s for up to {timeout_seconds}s")
        logger.info(f"[{self.service_name.upper()}] 💪 This service will NEVER crash - it will wait as long as needed!")
        
        while True:
            attempt += 1
            elapsed = time.time() - start_time
            
            # Check timeout
            if elapsed > timeout_seconds:
                logger.error(f"[{self.service_name.upper()}] ❌ Timeout waiting for backend readiness after {timeout_seconds}s")
                return False
            
            try:
                # Check the /ready endpoint first (most comprehensive)
                ready_status = await self._check_ready_endpoint()
                if ready_status:
                    logger.info(f"[{self.service_name.upper()}] ✅ Backend API is FULLY READY! (attempt {attempt}, elapsed: {elapsed:.1f}s)")
                    return True
                    
                # If /ready fails, check basic health
                health_status = await self._check_health_endpoint()
                if health_status and health_status.get("database_ready", False):
                    logger.info(f"[{self.service_name.upper()}] ✅ Backend API is READY via health check! (attempt {attempt}, elapsed: {elapsed:.1f}s)")
                    return True
                
                # Log progress periodically with anti-spam
                if attempt % log_interval == 0:
                    from shared.unified_logging import should_log_status_change
                    status_key = f"backend_wait_{attempt//log_interval}"
                    if should_log_status_change(self.service_name.upper(), "backend_wait", status_key):
                        logger.info(f"[{self.service_name.upper()}] ⏳ Still waiting for backend readiness... (attempt {attempt}, elapsed: {elapsed:.1f}s)")
                        if health_status:
                            db_ready = health_status.get("database_ready", False)
                            logger.info(f"[{self.service_name.upper()}] 📊 Backend status: API responding, database_ready={db_ready}")
                
            except Exception as e:
                # Log connection errors less frequently to avoid spam
                if attempt % log_interval == 0:
                    from shared.unified_logging import should_log_status_change
                    if should_log_status_change(self.service_name.upper(), "backend_error", str(type(e).__name__)):
                        logger.debug(f"[{self.service_name.upper()}] 🔄 Backend not yet available: {e} (attempt {attempt})")
            
            # Wait before next check
            await asyncio.sleep(check_interval)
    
    async def _check_ready_endpoint(self) -> bool:
        """Check the /ready endpoint - most comprehensive readiness check"""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(self.ready_endpoint)
                
                if response.status_code == 200:
                    data = response.json()
                    return data.get("status") == "ready"
                else:
                    return False
                    
        except Exception:
            return False
    
    async def _check_health_endpoint(self) -> Optional[Dict[str, Any]]:
        """Check the /health endpoint - fallback health check"""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(self.health_endpoint)
                
                if response.status_code == 200:
                    return response.json()
                else:
                    return None
                    
        except Exception:
            return None
    
    def should_wait_for_backend(self) -> bool:
        """
        Check if this service should wait for backend readiness based on environment variables.
        
        Returns True if WAIT_FOR_BACKEND environment variable is set to 'true'
        """
        return os.getenv("WAIT_FOR_BACKEND", "false").lower() == "true"
    
    def get_timeout_from_env(self) -> int:
        """Get backend readiness timeout from environment variable"""
        try:
            return int(os.getenv("BACKEND_READINESS_TIMEOUT", "300"))
        except ValueError:
            return 300


def wait_for_backend_ready_sync(
    backend_url: str = f"http://localhost:{get_service_port('backend')}",
    service_name: str = "microservice",
    timeout_seconds: int = 300
) -> bool:
    """
    Synchronous wrapper for backend readiness checking.
    
    Args:
        backend_url: Backend API URL
        service_name: Name of the calling service (for logging)
        timeout_seconds: Maximum time to wait
        
    Returns:
        True if backend is ready, False if timeout
    """
    checker = BackendReadinessChecker(backend_url, service_name)
    return asyncio.run(checker.wait_for_backend_ready(timeout_seconds))


async def ensure_backend_ready_before_startup(
    service_name: str,
    backend_url: str = f"http://localhost:{get_service_port('backend')}"
) -> bool:
    """
    Ensure backend is ready before allowing service startup.
    
    This function should be called at the beginning of each microservice's startup
    process when WAIT_FOR_BACKEND environment variable is set.
    
    Args:
        service_name: Name of the calling service
        backend_url: Backend API URL
        
    Returns:
        True if backend is ready or waiting is disabled, False on timeout
    """
    checker = BackendReadinessChecker(backend_url, service_name)
    
    # Check if we should wait
    if not checker.should_wait_for_backend():
        logger.info(f"[{service_name.upper()}] 🚀 WAIT_FOR_BACKEND not enabled - starting immediately")
        return True
    
    # Wait for backend readiness
    timeout = checker.get_timeout_from_env()
    logger.info(f"[{service_name.upper()}] 🎯 WAIT_FOR_BACKEND enabled - ensuring backend is ready first")
    
    ready = await checker.wait_for_backend_ready(timeout)
    if ready:
        logger.info(f"[{service_name.upper()}] ✅ Backend readiness confirmed - proceeding with service startup")
        return True
    else:
        logger.error(f"[{service_name.upper()}] ❌ Backend readiness timeout - service will start anyway to avoid hanging")
        return False


# Convenience function for immediate use
async def wait_for_backend(service_name: str) -> bool:
    """Simple convenience function to wait for backend readiness"""
    return await ensure_backend_ready_before_startup(service_name)
