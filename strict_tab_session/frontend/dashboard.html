<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 Secure Dashboard - Strict Tab Session</title>
    
    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-bottom: 1px solid rgba(0, 212, 255, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            color: #00d4ff;
            font-size: 24px;
        }
        
        .header .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .logout-btn {
            background: linear-gradient(45deg, #ff4757, #ff3742);
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            color: white;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .logout-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 71, 87, 0.4);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .security-status {
            background: rgba(46, 204, 113, 0.1);
            border: 1px solid rgba(46, 204, 113, 0.3);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .security-status h2 {
            color: #2ecc71;
            margin-bottom: 15px;
            font-size: 28px;
        }
        
        .security-status p {
            color: #ccc;
            font-size: 16px;
        }
        
        .session-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .info-card {
            background: rgba(255, 255, 255, 0.1);
            -webkit-backdrop-filter: blur(10px); /* Safari support */
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 25px;
        }
        
        .info-card h3 {
            color: #00d4ff;
            margin-bottom: 20px;
            font-size: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            color: #ccc;
            font-weight: 500;
        }
        
        .info-value {
            color: #00d4ff;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            word-break: break-all;
            max-width: 60%;
            text-align: right;
        }
        
        .security-tests {
            background: rgba(255, 71, 87, 0.1);
            border: 1px solid rgba(255, 71, 87, 0.3);
            border-radius: 15px;
            padding: 30px;
        }
        
        .security-tests h3 {
            color: #ff4757;
            margin-bottom: 20px;
            font-size: 20px;
        }
        
        .test-button {
            background: linear-gradient(45deg, #ff4757, #ff3742);
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            color: white;
            cursor: pointer;
            font-weight: bold;
            margin: 10px 10px 10px 0;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 71, 87, 0.4);
        }
        
        .test-results {
            margin-top: 20px;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }

        .security-test-description {
            color: #ccc;
            margin-bottom: 20px;
        }

        .test-result-placeholder {
            color: #00d4ff;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online {
            background: #2ecc71;
            box-shadow: 0 0 10px rgba(46, 204, 113, 0.5);
        }
        
        .status-offline {
            background: #ff4757;
            box-shadow: 0 0 10px rgba(255, 71, 87, 0.5);
        }
        
        .refresh-btn {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            color: white;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .refresh-btn:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔐 Secure Dashboard</h1>
        <div class="user-info">
            <span id="userInfo">Loading...</span>
            <button type="button" class="logout-btn" onclick="logout()">🚪 Logout</button>
        </div>
    </div>
    
    <div class="container">
        <div class="security-status">
            <h2>🛡️ Security Status: Active</h2>
            <p>Your session is protected by strict single-tab enforcement</p>
        </div>
        
        <div class="session-info">
            <div class="info-card">
                <h3>🔑 Session Information</h3>
                <div class="info-item">
                    <span class="info-label">Username:</span>
                    <span class="info-value" id="username">{{ session_info.username if session_info else 'Loading...' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Tab ID:</span>
                    <span class="info-value" id="tabId">Loading...</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Session Token:</span>
                    <span class="info-value" id="sessionToken">Loading...</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Created:</span>
                    <span class="info-value" id="createdAt">{{ session_info.created_at if session_info else 'Loading...' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Last Activity:</span>
                    <span class="info-value" id="lastActivity">{{ session_info.last_activity if session_info else 'Loading...' }}</span>
                </div>
            </div>
            
            <div class="info-card">
                <h3>📊 Security Metrics</h3>
                <div class="info-item">
                    <span class="info-label">Validation Count:</span>
                    <span class="info-value" id="validationCount">{{ session_info.validation_count if session_info else '0' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Security Violations:</span>
                    <span class="info-value" id="securityViolations">{{ session_info.security_violations if session_info else '0' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Session Status:</span>
                    <span class="info-value">
                        <span class="status-indicator status-online"></span>
                        Active
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">IP Address:</span>
                    <span class="info-value" id="ipAddress">{{ session_info.ip_address if session_info else 'Loading...' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">
                        Auto-Refresh:
                        <button type="button" class="refresh-btn" onclick="toggleAutoRefresh()" id="autoRefreshBtn">Enable</button>
                    </span>
                    <span class="info-value" id="autoRefreshStatus">Disabled</span>
                </div>
            </div>
        </div>
        
        <div class="security-tests">
            <h3>🧪 Security Tests</h3>
            <p class="security-test-description">
                Test the strict single-tab session enforcement by attempting to violate security policies:
            </p>
            
            <button type="button" class="test-button" onclick="testNewTab()">
                🆕 Open New Tab (Should Fail)
            </button>

            <button type="button" class="test-button" onclick="testSessionValidation()">
                ✅ Validate Current Session
            </button>

            <button type="button" class="test-button" onclick="testInvalidTabId()">
                🚫 Send Invalid Tab ID
            </button>

            <button type="button" class="test-button" onclick="testRateLimit()">
                ⚡ Test Rate Limiting
            </button>
            
            <div class="test-results" id="testResults">
                <div class="test-result-placeholder">🔍 Security test results will appear here...</div>
            </div>
        </div>
    </div>
    
    <!-- Load Tab Security Manager -->
    <script src="/static/tab_security.js"></script>
    
    <script>
        let autoRefreshInterval = null;
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                const tabManager = window.getStrictTabSessionManager();
                if (tabManager && tabManager.isInitialized) {
                    document.getElementById('tabId').textContent = 
                        tabManager.getTabId().substring(0, 16) + '...';
                    
                    // Load session info
                    loadSessionInfo();
                } else {
                    addTestResult('❌ Tab security manager not initialized', 'error');
                }
            }, 500);
        });
        
        async function loadSessionInfo() {
            try {
                const tabManager = window.getStrictTabSessionManager();
                const response = await fetch('/api/session-info', {
                    headers: {
                        'X-Tab-ID': tabManager.getTabId(),
                        'X-Session-Token': tabManager.sessionToken
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    updateSessionDisplay(data.session_info);
                } else {
                    addTestResult('❌ Failed to load session info', 'error');
                }
            } catch (error) {
                addTestResult('❌ Error loading session info: ' + error.message, 'error');
            }
        }
        
        function updateSessionDisplay(sessionInfo) {
            if (sessionInfo) {
                document.getElementById('username').textContent = sessionInfo.username || 'Unknown';
                document.getElementById('sessionToken').textContent = sessionInfo.session_token || 'Unknown';
                document.getElementById('validationCount').textContent = sessionInfo.validation_count || '0';
                document.getElementById('securityViolations').textContent = sessionInfo.security_violations || '0';
                document.getElementById('ipAddress').textContent = sessionInfo.ip_address || 'Unknown';
                
                if (sessionInfo.created_at) {
                    document.getElementById('createdAt').textContent = 
                        new Date(sessionInfo.created_at).toLocaleString();
                }
                
                if (sessionInfo.last_activity) {
                    document.getElementById('lastActivity').textContent = 
                        new Date(sessionInfo.last_activity).toLocaleString();
                }
            }
        }
        
        async function logout() {
            try {
                const tabManager = window.getStrictTabSessionManager();
                const response = await fetch('/api/logout', {
                    method: 'POST',
                    headers: {
                        'X-Tab-ID': tabManager.getTabId(),
                        'X-Session-Token': tabManager.sessionToken
                    }
                });
                
                if (response.ok) {
                    window.location.href = '/login';
                } else {
                    addTestResult('❌ Logout failed', 'error');
                }
            } catch (error) {
                addTestResult('❌ Logout error: ' + error.message, 'error');
            }
        }
        
        function testNewTab() {
            addTestResult('🧪 Testing new tab detection...', 'info');
            
            // Open new tab/window - this should trigger security violation
            const newWindow = window.open(window.location.href, '_blank');
            
            setTimeout(() => {
                if (newWindow) {
                    newWindow.close();
                }
                addTestResult('✅ New tab test completed - check for security violations', 'success');
            }, 3000);
        }
        
        async function testSessionValidation() {
            try {
                addTestResult('🧪 Testing session validation...', 'info');
                
                const tabManager = window.getStrictTabSessionManager();
                const response = await fetch('/api/validate-session', {
                    method: 'POST',
                    headers: {
                        'X-Tab-ID': tabManager.getTabId(),
                        'X-Session-Token': tabManager.sessionToken
                    }
                });
                
                const result = await response.json();
                
                if (result.valid) {
                    addTestResult('✅ Session validation passed', 'success');
                } else {
                    addTestResult('❌ Session validation failed: ' + result.reason, 'error');
                }
            } catch (error) {
                addTestResult('❌ Validation test error: ' + error.message, 'error');
            }
        }
        
        async function testInvalidTabId() {
            try {
                addTestResult('🧪 Testing invalid Tab ID...', 'info');
                
                const tabManager = window.getStrictTabSessionManager();
                const response = await fetch('/api/validate-session', {
                    method: 'POST',
                    headers: {
                        'X-Tab-ID': 'invalid-tab-id-12345',
                        'X-Session-Token': tabManager.sessionToken
                    }
                });
                
                if (response.status === 401) {
                    addTestResult('✅ Invalid Tab ID correctly rejected', 'success');
                } else {
                    addTestResult('❌ Invalid Tab ID was accepted (security issue!)', 'error');
                }
            } catch (error) {
                addTestResult('❌ Invalid Tab ID test error: ' + error.message, 'error');
            }
        }
        
        async function testRateLimit() {
            addTestResult('🧪 Testing rate limiting...', 'info');
            
            const tabManager = window.getStrictTabSessionManager();
            let successCount = 0;
            let failCount = 0;
            
            // Send multiple rapid requests
            for (let i = 0; i < 10; i++) {
                try {
                    const response = await fetch('/api/validate-session', {
                        method: 'POST',
                        headers: {
                            'X-Tab-ID': tabManager.getTabId(),
                            'X-Session-Token': tabManager.sessionToken
                        }
                    });
                    
                    if (response.ok) {
                        successCount++;
                    } else if (response.status === 429) {
                        failCount++;
                    }
                } catch (error) {
                    failCount++;
                }
            }
            
            addTestResult(`✅ Rate limit test: ${successCount} success, ${failCount} blocked`, 'success');
        }
        
        function addTestResult(message, type) {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff4757' : type === 'success' ? '#2ecc71' : '#00d4ff';
            
            resultsDiv.innerHTML += `<div style="color: ${color}; margin-bottom: 5px;">[${timestamp}] ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        function toggleAutoRefresh() {
            const btn = document.getElementById('autoRefreshBtn');
            const status = document.getElementById('autoRefreshStatus');
            
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                btn.textContent = 'Enable';
                status.textContent = 'Disabled';
            } else {
                autoRefreshInterval = setInterval(loadSessionInfo, 5000);
                btn.textContent = 'Disable';
                status.textContent = 'Every 5s';
            }
        }
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        });
    </script>
</body>
</html>
