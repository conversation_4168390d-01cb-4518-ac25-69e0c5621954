#!/usr/bin/env python3
"""
🚀 START DEEPLICA INSTRUCTIONS
Instructions for starting each microservice in its own VS Code terminal
"""

import time
from datetime import datetime

def show_instructions():
    """Show step-by-step instructions for starting all services"""
    
    print("🚀 START DEEPLICA - MANUAL TERMINAL SETUP")
    print("=" * 70)
    print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 Each microservice MUST run in its own separate VS Code terminal")
    print("📋 Follow these steps to start all services properly:")
    print("=" * 70)
    
    services = [
        {
            "name": "🐕 Watchdog",
            "steps": [
                "Press Ctrl+Shift+` (or Cmd+Shift+` on Mac) to open new terminal",
                "Run: python3 watchdog/main.py",
                "Wait for 'WATCHDOG log server started on port 8005'"
            ]
        },
        {
            "name": "🌐 Backend API",
            "steps": [
                "Press Ctrl+Shift+` (or Cmd+Shift+` on Mac) to open new terminal",
                "Run: cd backend && python3 -m app.main",
                "Wait for 'Uvicorn running on http://0.0.0.0:8000'"
            ]
        },
        {
            "name": "🎯 Dispatcher",
            "steps": [
                "Press Ctrl+Shift+` (or Cmd+Shift+` on Mac) to open new terminal",
                "Run: cd dispatcher && python3 -m app.main",
                "Wait for 'Uvicorn running on http://0.0.0.0:8001'"
            ]
        },
        {
            "name": "💬 Dialogue Agent",
            "steps": [
                "Press Ctrl+Shift+` (or Cmd+Shift+` on Mac) to open new terminal",
                "Run: cd agents/dialogue && python3 -m app.main",
                "Wait for 'Uvicorn running on http://0.0.0.0:8002'"
            ]
        },
        {
            "name": "🧠 Planner Agent",
            "steps": [
                "Press Ctrl+Shift+` (or Cmd+Shift+` on Mac) to open new terminal",
                "Run: cd agents/planner && python3 -m app.main",
                "Wait for 'Uvicorn running on http://0.0.0.0:8003'"
            ]
        },
        {
            "name": "📞 Phone Agent",
            "steps": [
                "Press Ctrl+Shift+` (or Cmd+Shift+` on Mac) to open new terminal",
                "Run: cd agents/phone && python3 -m app.main",
                "Wait for 'Uvicorn running on http://0.0.0.0:8004'"
            ]
        },
        {
            "name": "🖥️ CLI Terminal",
            "steps": [
                "Press Ctrl+Shift+` (or Cmd+Shift+` on Mac) to open new terminal",
                "Run: python3 cli/main.py",
                "Wait for CLI interface to appear"
            ]
        }
    ]
    
    for i, service in enumerate(services, 1):
        print(f"\n📋 STEP {i}: {service['name']}")
        print("-" * 50)
        for step_num, step in enumerate(service['steps'], 1):
            print(f"  {step_num}. {step}")
        
        if i < len(services):
            print(f"\n⏳ Wait for {service['name']} to fully start before proceeding...")
    
    print("\n" + "=" * 70)
    print("🎉 WHEN ALL SERVICES ARE RUNNING:")
    print("✅ You should have 7 separate VS Code terminal tabs")
    print("✅ Each service running in its own dedicated terminal")
    print("✅ All services showing 'running' or 'started' messages")
    print("🛑 Use F5 → 'STOP DEEPLICA' to stop all services")
    print("=" * 70)
    
    print("\n🔧 ALTERNATIVE: Use individual launch configurations")
    print("💡 You can also start each service individually:")
    print("   1. Press F5 in VS Code")
    print("   2. Select the specific service you want to start:")
    print("      - 🐕 Watchdog")
    print("      - 🌐 Backend API")
    print("      - 🎯 Dispatcher")
    print("      - 💬 Dialogue Agent")
    print("      - 🧠 Planner Agent")
    print("      - 📞 Phone Agent")
    print("      - 🖥️ CLI Terminal")
    print("   3. Each will start in its own terminal")
    print("   4. Repeat for all services you need")
    
    print("\n🎯 RECOMMENDED STARTUP ORDER:")
    print("   1. 🐕 Watchdog (FIRST - registry service)")
    print("   2. 🌐 Backend API (SECOND - core backend)")
    print("   3. 🎯 Dispatcher (THIRD - data management)")
    print("   4. 💬 Dialogue Agent")
    print("   5. 🧠 Planner Agent")
    print("   6. 📞 Phone Agent")
    print("   7. 🖥️ CLI Terminal (LAST)")
    
    print(f"\n⏰ Instructions displayed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🚀 Ready to start your Deeplica microservices!")

def main():
    """Main function"""
    try:
        show_instructions()
        
        # Keep the terminal open so user can read instructions
        print(f"\n💡 This terminal will close in 60 seconds...")
        print(f"📋 Use the instructions above to start each service")
        
        for i in range(60, 0, -10):
            print(f"⏳ Closing in {i} seconds... (Press Ctrl+C to close now)")
            time.sleep(10)
        
        print("✅ Instructions complete!")
        
    except KeyboardInterrupt:
        print(f"\n✅ Instructions closed by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    main()
