#!/usr/bin/env python3
"""
Start the backend API without ngrok for testing
This bypasses ngrok issues and starts all microservices
"""

import os
import sys
import subprocess
import time
from shared.port_manager import get_service_port

# Add project root to path for shared modules
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.port_manager import DeepLicaPortManager

def cleanup_ports():
    """Clean up any existing processes on our ports"""
    # [BACKEND-API:cleanup_ports] CLEANUP | 🧹 Cleaning up existing processes...
    
    # Get all ports from central port manager
    port_manager = DeepLicaPortManager()
    ports = list(port_manager.OFFICIAL_PORTS.values())
    
    for port in ports:
        try:
            result = subprocess.run(
                ["lsof", "-ti", f": {port}"],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                for pid in pids:
                    if pid.strip():
                        try:
                            subprocess.run(["kill", "-9", pid.strip()], capture_output=True)
                            # [BACKEND-API:cleanup_ports] SYSTEM | 🔪 Killed process {pid} on port {port}
                        except:
                            pass
        except:
            pass
    
    # Kill any lingering processes
    try:
        subprocess.run(["pkill", "-f", "python3.*app.main"], capture_output=True)
        subprocess.run(["pkill", "-f", "ngrok"], capture_output=True)
    except:
        pass
    
    time.sleep(2)
    # [BACKEND-API:cleanup_ports] SUCCESS | ✅ Cleanup completed

def start_backend():
    """Start the backend API with ngrok disabled"""
    # [BACKEND-API:start_backend] STARTUP | 🚀 Starting Backend API (ngrok disable...")
    
    # Set environment variable to disable ngrok
    env = os.environ.copy()
    env["SKIP_NGROK"] = "true"
    env["PORT"] = str(port_manager.get_service_port("backend"))
    
    # Start backend
    process = subprocess.Popen(
        [sys.executable, "-m", "app.main"],
        cwd="/Users/<USER>/Documents/prototype/backend",
        env=env
    )
    
    return process

def main():
    # [BACKEND-API:main] STARTUP | 🔧 BACKEND STARTUP (NO NGRO")
    # [BACKEND-API:main] SYSTEM | =" * 4
    
    # Clean up first
    cleanup_ports()
    
    # Start backend
    backend_process = start_backend()
    
    # [BACKEND-API:main] SYSTEM | ✅ Backend started (PID: {backend_process.pid")
    # [BACKEND-API:main] SYSTEM | 📊 Monitor at: http://localhost:8888/health
    # [BACKEND-API:main] SYSTEM | 📋 System status: http://localhost:8888/system/status
    print("")
    # [BACKEND-API:main] SYSTEM | ⚠️  Note: Phone calls will not work without ngrok
    # [BACKEND-API:main] SYSTEM | 🔄 Press Ctrl+C to stop
    
    try:
        backend_process.wait()
    except KeyboardInterrupt:
        print("\n🛑 Stopping backend...")
        backend_process.terminate()
        backend_process.wait()
        # [BACKEND-API:main] SYSTEM | ✅ Backend stopped

if __name__ == "__main__":
    main()
