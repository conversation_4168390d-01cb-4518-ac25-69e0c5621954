#!/usr/bin/env python3
"""
🔌 Test Enhanced Port Manager with Mandatory Conflict Detection
Test the improved DHCP-like port allocation with mandatory port checking
"""

import sys
import os
import time
import subprocess

# Add shared directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'shared'))

# Import the enhanced port manager
from port_manager import DeepLicaPortManager

def test_mandatory_port_checking():
    """Test that port manager ALWAYS checks if ports are actually free"""
    print("🔌 Testing Enhanced Port Manager with Mandatory Conflict Detection")
    print("=" * 70)
    
    # Create a fresh port manager instance
    pm = DeepLicaPortManager()
    
    print("\n🧹 Cleaning up any existing leases...")
    # Clear existing leases for clean test
    pm.port_leases = {}
    pm.allocated_ports = set()
    pm._save_port_leases()
    
    print("\n🔍 Testing MANDATORY port availability checking...")
    
    # Test 1: Normal allocation (port should be free)
    print("\n📞 Test 1: Allocating port for 'phone' (should get reserved port 8004)")
    phone_port = pm.allocate_port('phone')
    print(f"✅ Phone Agent allocated port: {phone_port}")
    
    # Test 2: Simulate port conflict by starting a dummy process on the port
    print(f"\n🚨 Test 2: Creating conflict by starting dummy process on port {phone_port}")
    
    # Start a simple HTTP server on the allocated port to create conflict
    dummy_process = subprocess.Popen([
        'python3', '-c', 
        f'import http.server; import socketserver; '
        f'handler = http.server.SimpleHTTPRequestHandler; '
        f'httpd = socketserver.TCPServer(("", {phone_port}), handler); '
        f'print("Dummy server started on port {phone_port}"); '
        f'httpd.serve_forever()'
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    # Wait for dummy server to start
    time.sleep(2)
    
    print(f"✅ Dummy process started on port {phone_port} (PID: {dummy_process.pid})")
    
    # Test 3: Try to allocate the same port again - should detect conflict and resolve
    print(f"\n🔧 Test 3: Trying to allocate port for 'phone' again (should detect and resolve conflict)")
    
    # Clear the lease to force re-allocation
    if 'phone' in pm.port_leases:
        del pm.port_leases['phone']
    
    phone_port_2 = pm.allocate_port('phone')
    print(f"✅ Phone Agent allocated port after conflict resolution: {phone_port_2}")
    
    # Test 4: Verify the dummy process was killed
    print(f"\n🔍 Test 4: Verifying dummy process was killed...")
    try:
        dummy_process.wait(timeout=1)
        print(f"✅ Dummy process was terminated (return code: {dummy_process.returncode})")
    except subprocess.TimeoutExpired:
        print(f"⚠️ Dummy process still running - killing it manually")
        dummy_process.kill()
        dummy_process.wait()
    
    # Test 5: Test planner allocation
    print(f"\n🧠 Test 5: Allocating port for 'planner' (should get reserved port 8003)")
    planner_port = pm.allocate_port('planner')
    print(f"✅ Planner Agent allocated port: {planner_port}")
    
    # Test 6: Verify no conflicts between services
    print(f"\n🚨 Test 6: Verifying no conflicts between services...")
    if phone_port_2 == planner_port:
        print(f"❌ CONFLICT: Both services got the same port {phone_port_2}")
    else:
        print(f"✅ No conflicts: Phone={phone_port_2}, Planner={planner_port}")
    
    # Test 7: Show final port assignments
    print(f"\n📋 Final Port Assignments:")
    lease_info = pm.get_port_lease_info('phone')
    print(f"  Phone Agent: {lease_info}")
    lease_info = pm.get_port_lease_info('planner')
    print(f"  Planner Agent: {lease_info}")
    
    print(f"\n🎉 Enhanced Port Manager Test Completed!")
    
    # Expected results
    print(f"\n🎯 Expected Results:")
    print(f"  ✅ Phone Agent should get port 8004 (reserved)")
    print(f"  ✅ Conflict should be detected and resolved automatically")
    print(f"  ✅ Dummy process should be killed")
    print(f"  ✅ Planner Agent should get port 8003 (reserved)")
    print(f"  ✅ No conflicts between different services")

def test_conflict_resolution():
    """Test the aggressive conflict resolution"""
    print("\n" + "=" * 70)
    print("🔧 Testing Aggressive Conflict Resolution")
    print("=" * 70)
    
    pm = DeepLicaPortManager()
    
    # Test the new conflict resolution methods
    print("\n🔍 Testing _get_processes_on_port method...")
    processes = pm._get_processes_on_port(8888)  # Backend port
    print(f"Processes on port 8888: {len(processes)}")
    for proc in processes:
        print(f"  PID {proc['pid']}: {proc['command'][:50]}...")
    
    print(f"\n✅ Conflict resolution methods are working!")

if __name__ == "__main__":
    test_mandatory_port_checking()
    test_conflict_resolution()
