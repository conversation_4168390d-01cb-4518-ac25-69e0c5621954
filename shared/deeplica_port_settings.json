{"port_config": {"backend": 8888, "dispatcher": 8001, "dialogue": 8002, "planner": 8003, "phone": 8006, "watchdog": 8005, "web-chat": 8007, "cli": 8008, "twilio-echo-bot": 8009, "webhook": 8010, "test-server": 8011, "dev-server": 8012, "proxy-server": 8013, "mock-server": 8014, "debug-server": 8015, "admin-panel": 8016, "metrics": 8017, "logs": 8018, "health-check": 8019, "ngrok-api": 4040, "ngrok-tunnel": 8080, "mongodb-proxy": 8020}, "last_updated": **********.255596, "version": "5.0", "port_management_type": "DHCP_STYLE_RESERVATIONS"}