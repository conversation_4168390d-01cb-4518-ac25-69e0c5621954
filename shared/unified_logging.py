"""
Unified logging utility for DEEPLICA services
Provides standardized logging format: time - [LEVEL] - Svc: [service], Mod: [module], Cod: [routine], msg: [message]
"""

import logging
import sys
from datetime import datetime
from typing import Optional
import os


class UnifiedFormatter(logging.Formatter):
    """Custom formatter for unified DEEPLICA logging format"""
    
    def __init__(self, service_name: str):
        self.service_name = service_name
        super().__init__()
    
    def format(self, record):
        # Get timestamp in required format
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Extract module and routine information
        module_name = record.module if hasattr(record, 'module') else record.name.split('.')[-1]
        routine_name = record.funcName if record.funcName != '<module>' else 'main'
        
        # Get custom fields if provided
        if hasattr(record, 'deeplica_module'):
            module_name = record.deeplica_module
        if hasattr(record, 'deeplica_routine'):
            routine_name = record.deeplica_routine
        
        # Format the message
        level = record.levelname
        message = record.getMessage()
        
        # Build unified format
        formatted_msg = f"{timestamp} - [{level}] - Svc: {self.service_name}, Mod: {module_name}, Cod: {routine_name}, msg: {message}"
        
        # Add exception info if present
        if record.exc_info:
            formatted_msg += f"\n{self.formatException(record.exc_info)}"
        
        return formatted_msg


class DeepLicaLogger:
    """Unified logger for DEEPLICA services"""
    
    def __init__(self, service_name: str, log_level: str = "INFO"):
        self.service_name = service_name
        self.logger = logging.getLogger(f"deeplica.{service_name.lower()}")
        
        # Clear any existing handlers
        self.logger.handlers.clear()
        
        # Set log level
        level = getattr(logging, log_level.upper(), logging.INFO)
        self.logger.setLevel(level)
        
        # Create console handler with unified formatter
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(level)
        
        formatter = UnifiedFormatter(service_name)
        console_handler.setFormatter(formatter)
        
        self.logger.addHandler(console_handler)
        
        # Prevent propagation to avoid duplicate messages
        self.logger.propagate = False
    
    def debug(self, message: str, module: str = None, routine: str = None):
        """Log debug message"""
        extra = {}
        if module:
            extra['deeplica_module'] = module
        if routine:
            extra['deeplica_routine'] = routine
        self.logger.debug(message, extra=extra)
    
    def info(self, message: str, module: str = None, routine: str = None):
        """Log info message"""
        extra = {}
        if module:
            extra['deeplica_module'] = module
        if routine:
            extra['deeplica_routine'] = routine
        self.logger.info(message, extra=extra)
    
    def warning(self, message: str, module: str = None, routine: str = None):
        """Log warning message"""
        extra = {}
        if module:
            extra['deeplica_module'] = module
        if routine:
            extra['deeplica_routine'] = routine
        self.logger.warning(message, extra=extra)
    
    def error(self, message: str, module: str = None, routine: str = None, exc_info: bool = False):
        """Log error message"""
        extra = {}
        if module:
            extra['deeplica_module'] = module
        if routine:
            extra['deeplica_routine'] = routine
        self.logger.error(message, extra=extra, exc_info=exc_info)
    
    def critical(self, message: str, module: str = None, routine: str = None, exc_info: bool = False):
        """Log critical message"""
        extra = {}
        if module:
            extra['deeplica_module'] = module
        if routine:
            extra['deeplica_routine'] = routine
        self.logger.critical(message, extra=extra, exc_info=exc_info)

    def info_on_change(self, message: str, operation: str, status: str, module: str = None, routine: str = None):
        """Log info message only when status changes"""
        if should_log_status_change(self.service_name, operation, status):
            self.info(message, module, routine)

    def debug_on_change(self, message: str, operation: str, status: str, module: str = None, routine: str = None):
        """Log debug message only when status changes"""
        if should_log_status_change(self.service_name, operation, status):
            self.debug(message, module, routine)

    def warning_on_change(self, message: str, operation: str, status: str, module: str = None, routine: str = None):
        """Log warning message only when status changes"""
        if should_log_status_change(self.service_name, operation, status):
            self.warning(message, module, routine)

    def error_always(self, message: str, module: str = None, routine: str = None, exc_info: bool = False):
        """Log error message always (errors are always logged regardless of repetition)"""
        self.error(message, module, routine, exc_info)


# Global logger instances for each service
_loggers = {}


def get_logger(service_name: str, log_level: str = None) -> DeepLicaLogger:
    """Get or create a logger for a service"""
    if service_name not in _loggers:
        if log_level is None:
            log_level = os.getenv("LOG_LEVEL", "INFO")
        _loggers[service_name] = DeepLicaLogger(service_name, log_level)
    return _loggers[service_name]


# Convenience functions for common services
def get_backend_logger() -> DeepLicaLogger:
    return get_logger("BACKEND-API")


def get_dispatcher_logger() -> DeepLicaLogger:
    return get_logger("DISPATCHER")


def get_planner_logger() -> DeepLicaLogger:
    return get_logger("PLANNER-AGENT")


def get_dialogue_logger() -> DeepLicaLogger:
    return get_logger("DIALOGUE-AGENT")


def get_phone_logger() -> DeepLicaLogger:
    return get_logger("PHONE-AGENT")


def get_watchdog_logger() -> DeepLicaLogger:
    return get_logger("WATCHDOG")


def get_webchat_logger() -> DeepLicaLogger:
    return get_logger("WEB-CHAT")


def get_cli_logger() -> DeepLicaLogger:
    return get_logger("CLI-TERMINAL")


# Status change tracking for smart logging
_status_cache = {}


class StatusChangeTracker:
    """Tracks status changes to avoid repetitive logging"""

    def __init__(self):
        self.status_cache = {}

    def should_log(self, key: str, status: str, force_log: bool = False) -> bool:
        """
        Check if we should log this status message.
        Only logs if status changed or force_log is True.
        """
        if force_log:
            return True

        previous_status = self.status_cache.get(key)
        if previous_status != status:
            self.status_cache[key] = status
            return True
        return False

    def clear_status(self, key: str):
        """Clear cached status for a key"""
        self.status_cache.pop(key, None)


# Global status tracker
_global_status_tracker = StatusChangeTracker()


def should_log_status_change(service: str, operation: str, status: str, force_log: bool = False) -> bool:
    """
    Global function to check if a status should be logged.
    Only logs when status changes or on errors.

    Args:
        service: Service name (e.g., "BACKEND-API")
        operation: Operation name (e.g., "health_check", "database_connection")
        status: Current status (e.g., "healthy", "failed", "connecting")
        force_log: Force logging regardless of previous status

    Returns:
        True if should log, False if should suppress
    """
    key = f"{service}:{operation}"
    return _global_status_tracker.should_log(key, status, force_log)


# Emergency logging function for critical system messages
def emergency_log(message: str, service: str = "SYSTEM"):
    """Emergency logging when normal logging fails"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"{timestamp} - [ERROR] - Svc: {service}, Mod: emergency, Cod: emergency_log, msg: {message}", file=sys.stderr)
