#!/usr/bin/env python3
"""
🔗 WEBHOOK SERVER FOR TWILIO
Dedicated webhook server for handling Twilio callbacks
"""

import os
import sys
from datetime import datetime
from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import PlainTextResponse
import uvicorn
import logging

# Add project root to path for shared modules
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.port_manager import get_service_port, get_localhost

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Deeplica Webhook Server", version="1.0.0")

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "Deeplica Webhook Server",
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "endpoints": [
            "/twilio/voice",
            "/twilio/sms", 
            "/twilio/status",
            "/health"
        ]
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "webhook-server",
        "timestamp": datetime.now().isoformat()
    }

@app.post("/twilio/voice")
async def twilio_voice_webhook(request: Request):
    """Handle Twilio voice webhooks"""
    try:
        # Get form data from Twilio
        form_data = await request.form()
        
        logger.info(f"📞 Twilio voice webhook received:")
        for key, value in form_data.items():
            logger.info(f"  {key}: {value}")
        
        # Basic TwiML response for voice calls
        twiml_response = """<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="alice">Hello from Deeplica! Your call has been received and is being processed.</Say>
    <Pause length="1"/>
    <Say voice="alice">Please hold while we connect you to our AI assistant.</Say>
</Response>"""
        
        return PlainTextResponse(content=twiml_response, media_type="application/xml")
        
    except Exception as e:
        logger.error(f"❌ Error processing voice webhook: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/twilio/sms")
async def twilio_sms_webhook(request: Request):
    """Handle Twilio SMS webhooks"""
    try:
        # Get form data from Twilio
        form_data = await request.form()
        
        logger.info(f"📱 Twilio SMS webhook received:")
        for key, value in form_data.items():
            logger.info(f"  {key}: {value}")
        
        # Basic TwiML response for SMS
        twiml_response = """<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Message>Thank you for your message! Deeplica AI is processing your request.</Message>
</Response>"""
        
        return PlainTextResponse(content=twiml_response, media_type="application/xml")
        
    except Exception as e:
        logger.error(f"❌ Error processing SMS webhook: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/twilio/status")
async def twilio_status_webhook(request: Request):
    """Handle Twilio status callbacks"""
    try:
        # Get form data from Twilio
        form_data = await request.form()
        
        logger.info(f"📊 Twilio status webhook received:")
        for key, value in form_data.items():
            logger.info(f"  {key}: {value}")
        
        # Just acknowledge the status update
        return {"status": "received"}
        
    except Exception as e:
        logger.error(f"❌ Error processing status webhook: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

def main():
    """Main function to run the webhook server"""
    server_port = get_service_port('webhook')
    
    print("🔗 DEEPLICA WEBHOOK SERVER")
    print("=" * 50)
    print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 Starting webhook server on port {server_port}")
    print("🔗 Endpoints available:")
    print(f"  📞 Voice: http://{get_localhost()}:{server_port}/twilio/voice")
    print(f"  📱 SMS: http://{get_localhost()}:{server_port}/twilio/sms")
    print(f"  📊 Status: http://{get_localhost()}:{server_port}/twilio/status")
    print(f"  ❤️ Health: http://{get_localhost()}:{server_port}/health")
    print("=" * 50)
    
    try:
        uvicorn.run(
            app,
            host=get_service_host(),
            port=server_port,
            log_level="info",
            access_log=True
        )
    except Exception as e:
        logger.error(f"❌ Failed to start webhook server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
