#!/usr/bin/env python3
"""
DEEPLICA Enhanced Startup Script with Auto-Recovery
Starts all services in correct order with proper port management and health checks.
"""

import os
import sys
import time
import subprocess
import requests
import json
from pathlib import Path
from shared.port_manager import get_service_port

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Service startup configuration with dependencies and health checks
SERVICES = {
    "watchdog": {
        "port": get_service_port("watchdog"),
        "command": "python3 main.py",
        "directory": "watchdog",
        "dependencies": [],
        "health_endpoint": "/health",
        "startup_delay": 2,
        "critical": True
    },
    "backend": {
        "port": "get_service_port('backend')",  # Will be evaluated at runtime
        "command": "python3 -m app.main",
        "directory": "backend",
        "dependencies": ["watchdog"],
        "health_endpoint": "/health",
        "startup_delay": 5,
        "critical": True
    },
    "dispatcher": {
        "port": get_service_port("dispatcher"),
        "command": "python3 -m app.main",
        "directory": "dispatcher",
        "dependencies": ["backend"],
        "health_endpoint": "/health",
        "startup_delay": 3,
        "critical": True
    },
    "web-chat": {
        "port": get_service_port("web-chat"),
        "command": "python3 main.py",
        "directory": "web_chat",
        "dependencies": ["backend"],
        "health_endpoint": "/health",
        "startup_delay": 3,
        "critical": False
    },
    "dialogue": {
        "port": get_service_port("dialogue"),
        "command": "python3 -m app.main",
        "directory": "agents/dialogue",
        "dependencies": ["backend", "dispatcher"],
        "health_endpoint": "/health",
        "startup_delay": 4,
        "critical": False
    },
    "planner": {
        "port": get_service_port("planner"),
        "command": "python3 -m app.main",
        "directory": "agents/planner",
        "dependencies": ["backend", "dispatcher"],
        "health_endpoint": "/health",
        "startup_delay": 4,
        "critical": False
    },
    "phone": {
        "port": get_service_port("phone"),
        "command": "python3 -m app.main",
        "directory": "agents/phone",
        "dependencies": ["backend", "dispatcher"],
        "health_endpoint": "/health",
        "startup_delay": 5,
        "critical": False
    }
}

def check_port_available(port):
    """Check if a port is available"""
    try:
        result = subprocess.run(
            ["lsof", "-ti", f":{port}"],
            capture_output=True,
            text=True
        )
        return result.returncode != 0
    except Exception:
        return True

def kill_port_process(port):
    """Kill process using specified port"""
    try:
        result = subprocess.run(
            ["lsof", "-ti", f":{port}"],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0 and result.stdout.strip():
            pids = result.stdout.strip().split('\n')
            for pid in pids:
                if pid:
                    print(f"🔪 Killing process {pid} on port {port}")
                    subprocess.run(["kill", "-9", pid], capture_output=True)
            time.sleep(2)
            return True
        return False
    except Exception as e:
        print(f"❌ Error killing process on port {port}: {e}")
        return False

def check_service_health(service_name, port, endpoint="/health", timeout=5):
    """Check if a service is healthy"""
    try:
        url = f"http://localhost:{port}{endpoint}"
        response = requests.get(url, timeout=timeout)
        return response.status_code == 200
    except Exception:
        return False

def wait_for_dependencies(service_name, dependencies, max_wait=60):
    """Wait for service dependencies to be ready"""
    if not dependencies:
        return True
    
    print(f"⏳ Waiting for {service_name} dependencies: {', '.join(dependencies)}")
    
    start_time = time.time()
    while time.time() - start_time < max_wait:
        all_ready = True
        for dep in dependencies:
            if dep in SERVICES:
                dep_port = SERVICES[dep]["port"]
                dep_endpoint = SERVICES[dep]["health_endpoint"]
                if not check_service_health(dep, dep_port, dep_endpoint):
                    all_ready = False
                    break
        
        if all_ready:
            print(f"✅ All dependencies ready for {service_name}")
            return True
        
        time.sleep(2)
    
    print(f"⚠️  Timeout waiting for {service_name} dependencies")
    return False

def start_service(service_name, config):
    """Start a single service"""
    print(f"\n🚀 Starting {service_name}...")
    
    # Check if port is available
    port = config["port"]
    if not check_port_available(port):
        print(f"⚠️  Port {port} is in use, clearing...")
        kill_port_process(port)
        
        if not check_port_available(port):
            print(f"❌ Failed to clear port {port} for {service_name}")
            return False
    
    # Wait for dependencies
    if not wait_for_dependencies(service_name, config["dependencies"]):
        if config["critical"]:
            print(f"❌ Critical service {service_name} dependencies not ready")
            return False
        else:
            print(f"⚠️  Starting {service_name} without all dependencies ready")
    
    # Start the service
    try:
        working_dir = project_root / config["directory"]
        command = config["command"].split()
        
        print(f"📂 Working directory: {working_dir}")
        print(f"🔧 Command: {' '.join(command)}")
        
        process = subprocess.Popen(
            command,
            cwd=working_dir,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            start_new_session=True
        )
        
        # Wait for startup
        time.sleep(config["startup_delay"])
        
        # Check if service is healthy
        max_health_checks = 10
        for i in range(max_health_checks):
            if check_service_health(service_name, port, config["health_endpoint"]):
                print(f"✅ {service_name} started successfully on port {port}")
                return True
            
            if i < max_health_checks - 1:
                print(f"⏳ Waiting for {service_name} to be ready... ({i+1}/{max_health_checks})")
                time.sleep(3)
        
        print(f"❌ {service_name} failed to start or become healthy")
        return False
        
    except Exception as e:
        print(f"❌ Error starting {service_name}: {e}")
        return False

def start_external_services():
    """Start external services like ngrok"""
    print("\n🌐 Starting external services...")
    
    # Start ngrok for phone agent
    try:
        print("🚇 Starting ngrok tunnel for phone agent...")
        
        # Kill existing ngrok processes
        subprocess.run(["pkill", "-f", "ngrok"], capture_output=True)
        time.sleep(2)
        
        # Start ngrok
        ngrok_process = subprocess.Popen(
            ["ngrok", "http", str(get_service_port("phone")), "--log=stdout"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            start_new_session=True
        )
        
        # Wait for ngrok to start
        time.sleep(5)
        
        # Get ngrok URL
        try:
            response = requests.get("http://localhost:get_service_port("ngrok-api")/api/tunnels", timeout=5)
            if response.status_code == 200:
                data = response.json()
                tunnels = data.get("tunnels", [])
                for tunnel in tunnels:
                    if tunnel.get("proto") == "https":
                        ngrok_url = tunnel.get("public_url")
                        print(f"✅ ngrok tunnel started: {ngrok_url}")
                        
                        # Update phone agent webhook
                        try:
                            webhook_response = requests.post(
                                "http://localhost:get_service_port("phone")/update_webhook",
                                json={"webhook_url": ngrok_url},
                                timeout=10
                            )
                            if webhook_response.status_code == 200:
                                print(f"✅ Phone agent webhook updated")
                            else:
                                print(f"⚠️  Failed to update phone agent webhook")
                        except Exception:
                            print(f"⚠️  Could not update phone agent webhook (service may not be ready)")
                        
                        return True
            
            print("⚠️  ngrok started but no tunnel URL found")
            return False
            
        except Exception as e:
            print(f"⚠️  Could not get ngrok tunnel URL: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to start ngrok: {e}")
        return False

def verify_system_health():
    """Verify all services are running and communicating"""
    print("\n🔍 Verifying system health...")
    
    all_healthy = True
    for service_name, config in SERVICES.items():
        port = config["port"]
        endpoint = config["health_endpoint"]
        
        if check_service_health(service_name, port, endpoint):
            print(f"  ✅ {service_name} ({port}) - Healthy")
        else:
            print(f"  ❌ {service_name} ({port}) - Not responding")
            if config["critical"]:
                all_healthy = False
    
    return all_healthy

def main():
    """Main startup function"""
    print("🚀 DEEPLICA Enhanced Startup with Auto-Recovery")
    print("=" * 50)
    
    # Start services in dependency order
    started_services = []
    failed_services = []
    
    for service_name, config in SERVICES.items():
        success = start_service(service_name, config)
        if success:
            started_services.append(service_name)
        else:
            failed_services.append(service_name)
            if config["critical"]:
                print(f"💥 Critical service {service_name} failed to start!")
    
    # Start external services
    start_external_services()
    
    # Verify system health
    print(f"\n📊 Startup Summary:")
    print(f"  ✅ Started: {len(started_services)} services")
    print(f"  ❌ Failed: {len(failed_services)} services")
    
    if failed_services:
        print(f"  Failed services: {', '.join(failed_services)}")
    
    # Final health check
    if verify_system_health():
        print("\n🎉 DEEPLICA startup completed successfully!")
        print("🌐 Access the admin interface at: http://localhost:get_service_port("web-chat")/admin")
    else:
        print("\n⚠️  DEEPLICA startup completed with some issues")
        print("🔧 Check the logs and try restarting failed services")

if __name__ == "__main__":
    main()
