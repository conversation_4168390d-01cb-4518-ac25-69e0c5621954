{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "🚀 START ALL DEEPLICA SERVICES",
            "dependsOrder": "sequence",
            "dependsOn": [
                "🐕 Start Watchdog",
                "🌐 Start Backend API", 
                "🎯 Start Dispatcher",
                "💬 Start Dialogue Agent",
                "🧠 Start Planner Agent",
                "📞 Start Phone Agent",
                "🖥️ Start CLI Terminal"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "new",
                "showReuseMessage": true,
                "clear": false
            }
        },
        {
            "label": "🐕 Start Watchdog",
            "type": "shell",
            "command": "python3",
            "args": ["watchdog/main.py"],
            "options": {
                "cwd": "${workspaceFolder}",
                "env": {
                    "PYTHONPATH": "${workspaceFolder}",
                    "SERVICE_NAME": "WATCHDOG"
                }
            },
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "new",
                "showReuseMessage": false,
                "clear": true
            },
            "runOptions": {
                "runOn": "default"
            }
        },
        {
            "label": "🌐 Start Backend API",
            "type": "shell",
            "command": "python3",
            "args": ["-m", "app.main"],
            "options": {
                "cwd": "${workspaceFolder}/backend",
                "env": {
                    "PYTHONPATH": "${workspaceFolder}",
                    "PORT": "8888",  // Backend port - matches port_manager.py
                    "SERVICE_NAME": "BACKEND-API"
                }
            },
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "new",
                "showReuseMessage": false,
                "clear": true
            },
            "runOptions": {
                "runOn": "default"
            }
        },
        {
            "label": "🎯 Start Dispatcher",
            "type": "shell",
            "command": "python3",
            "args": ["-m", "app.main"],
            "options": {
                "cwd": "${workspaceFolder}/dispatcher",
                "env": {
                    "PYTHONPATH": "${workspaceFolder}",
                    "PORT": "8001",
                    "WAIT_FOR_BACKEND": "true",
                    "SERVICE_NAME": "DISPATCHER"
                }
            },
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "new",
                "showReuseMessage": false,
                "clear": true
            },
            "runOptions": {
                "runOn": "default"
            }
        },
        {
            "label": "💬 Start Dialogue Agent",
            "type": "shell",
            "command": "python3",
            "args": ["-m", "app.main"],
            "options": {
                "cwd": "${workspaceFolder}/agents/dialogue",
                "env": {
                    "PYTHONPATH": "${workspaceFolder}",
                    "PORT": "8002",
                    "WAIT_FOR_BACKEND": "true",
                    "SERVICE_NAME": "DIALOGUE-AGENT"
                }
            },
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "new",
                "showReuseMessage": false,
                "clear": true
            },
            "runOptions": {
                "runOn": "default"
            }
        },
        {
            "label": "🧠 Start Planner Agent",
            "type": "shell",
            "command": "python3",
            "args": ["-m", "app.main"],
            "options": {
                "cwd": "${workspaceFolder}/agents/planner",
                "env": {
                    "PYTHONPATH": "${workspaceFolder}",
                    "PORT": "8003",
                    "WAIT_FOR_BACKEND": "true",
                    "SERVICE_NAME": "PLANNER-AGENT"
                }
            },
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "new",
                "showReuseMessage": false,
                "clear": true
            },
            "runOptions": {
                "runOn": "default"
            }
        },
        {
            "label": "📞 Start Phone Agent",
            "type": "shell",
            "command": "python3",
            "args": ["-m", "app.main"],
            "options": {
                "cwd": "${workspaceFolder}/agents/phone",
                "env": {
                    "PYTHONPATH": "${workspaceFolder}",
                    "PORT": "8004",
                    "WAIT_FOR_BACKEND": "true",
                    "SERVICE_NAME": "PHONE-AGENT"
                }
            },
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "new",
                "showReuseMessage": false,
                "clear": true
            },
            "runOptions": {
                "runOn": "default"
            }
        },
        {
            "label": "🖥️ Start CLI Terminal",
            "type": "shell",
            "command": "python3",
            "args": ["cli/main.py"],
            "options": {
                "cwd": "${workspaceFolder}",
                "env": {
                    "PYTHONPATH": "${workspaceFolder}",
                    "SERVICE_NAME": "CLI-TERMINAL"
                }
            },
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "new",
                "showReuseMessage": false,
                "clear": true
            },
            "runOptions": {
                "runOn": "default"
            }
        }
    ]
}
