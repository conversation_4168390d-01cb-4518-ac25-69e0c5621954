#!/usr/bin/env python3
"""
🔌 Port Cache System
Efficient port management with local caching to reduce port manager calls.
"""

import time
from typing import Dict, Optional
from shared.port_manager import get_service_port, get_service_host


class PortCache:
    """Local port cache to reduce port manager calls"""
    
    def __init__(self, service_name: str, cache_duration: int = 300):
        """
        Initialize port cache
        
        Args:
            service_name: Name of the service using this cache
            cache_duration: How long to cache ports in seconds (default: 5 minutes)
        """
        self.service_name = service_name
        self.cache_duration = cache_duration
        self._port_cache: Dict[str, tuple] = {}  # service_name -> (port, timestamp)
        self._host_cache: Optional[tuple] = None  # (host, timestamp)
        
    def get_port(self, service_name: str) -> int:
        """Get port for a service with caching"""
        current_time = time.time()
        
        # Check cache first
        if service_name in self._port_cache:
            port, timestamp = self._port_cache[service_name]
            if current_time - timestamp < self.cache_duration:
                return port
        
        # Cache miss or expired - fetch from port manager
        try:
            port = get_service_port(service_name)
            self._port_cache[service_name] = (port, current_time)
            return port
        except Exception as e:
            # If port manager fails, try to use cached value even if expired
            if service_name in self._port_cache:
                port, _ = self._port_cache[service_name]
                return port
            raise e
    
    def get_host(self) -> str:
        """Get service host with caching"""
        current_time = time.time()
        
        # Check cache first
        if self._host_cache:
            host, timestamp = self._host_cache
            if current_time - timestamp < self.cache_duration:
                return host
        
        # Cache miss or expired - fetch from port manager
        try:
            host = get_service_host()
            self._host_cache = (host, current_time)
            return host
        except Exception as e:
            # If port manager fails, try to use cached value even if expired
            if self._host_cache:
                host, _ = self._host_cache
                return host
            raise e
    
    def get_own_port(self) -> int:
        """Get this service's own port"""
        return self.get_port(self.service_name)
    
    def preload_common_ports(self):
        """Preload commonly used ports to cache"""
        common_services = [
            "backend", "dispatcher", "dialogue", "planner", 
            "phone", "web-chat", "cli", "watchdog"
        ]
        
        for service in common_services:
            try:
                self.get_port(service)
            except Exception:
                # Ignore errors during preloading
                pass
    
    def clear_cache(self):
        """Clear all cached ports"""
        self._port_cache.clear()
        self._host_cache = None
    
    def get_cache_stats(self) -> Dict:
        """Get cache statistics"""
        current_time = time.time()
        valid_entries = 0
        expired_entries = 0
        
        for _, (_, timestamp) in self._port_cache.items():
            if current_time - timestamp < self.cache_duration:
                valid_entries += 1
            else:
                expired_entries += 1
        
        return {
            "total_entries": len(self._port_cache),
            "valid_entries": valid_entries,
            "expired_entries": expired_entries,
            "host_cached": self._host_cache is not None,
            "cache_duration": self.cache_duration
        }


# Global cache instances for common use
_service_caches: Dict[str, PortCache] = {}


def get_port_cache(service_name: str) -> PortCache:
    """Get or create a port cache for a service"""
    if service_name not in _service_caches:
        _service_caches[service_name] = PortCache(service_name)
    return _service_caches[service_name]


def get_cached_port(service_name: str, requesting_service: str = "unknown") -> int:
    """Get a port using the cache system"""
    cache = get_port_cache(requesting_service)
    return cache.get_port(service_name)


def get_cached_host(requesting_service: str = "unknown") -> str:
    """Get host using the cache system"""
    cache = get_port_cache(requesting_service)
    return cache.get_host()


def preload_all_ports(requesting_service: str = "unknown"):
    """Preload all common ports for a service"""
    cache = get_port_cache(requesting_service)
    cache.preload_common_ports()


def clear_all_caches():
    """Clear all port caches"""
    for cache in _service_caches.values():
        cache.clear_cache()
