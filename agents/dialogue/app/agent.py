"""
Dialogue Agent - Handles user interaction tasks.
Adapted from backend DialogueAgent for microservice architecture.
"""

import asyncio
import httpx
from typing import Dict, Any, Optional, List
import sys
import os

# Add project root to path for shared modules
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.unified_logging import get_dialogue_logger

# Initialize unified logger
logger = get_dialogue_logger()

from .models import TaskRequest, TaskResponse
from .llm_service import BackendLLMClient


class DialogueAgent:
    """
    Agent responsible for task execution and user communication.
    
    Handles dialogue tasks and provides responses to users based on
    task prompts and mission context.
    """
    
    def __init__(self, llm_service: BackendLLMClient):
        self.llm_service = llm_service
        self.agent_name = "dialogue_agent"
        self.http_client = httpx.AsyncClient(timeout=30.0)
    
    async def execute_task_async(self, request: TaskRequest) -> None:
        """
        Execute a task asynchronously and callback to dispatcher.
        This is the main entry point for the microservice.
        """
        logger.info("💬 Executing task: {request.task_id} ({request.task_type})", module="DIALOGUE-AGENT", routine="execute_task_async")
        
        try:
            # Execute the task
            result = await self._execute_task(request)
            
            # Determine completion status
            if result.get("requires_user_input", False):
                status = "requires_user_input"
            else:
                status = "completed"
            
            # Send callback to dispatcher
            await self._send_callback(request, status, result)
            
            logger.info("✅ Task {request.task_id} executed successfully", module="DIALOGUE-AGENT", routine="execute_task_async")
            
        except Exception as e:
            logger.error("❌ Failed to execute task {request.task_id}: {e}", module="DIALOGUE-AGENT", routine="execute_task_async")
            
            # Send failure callback
            await self._send_callback(
                request, 
                "failed", 
                {}, 
                error_message=str(e)
            )
    
    async def _execute_task(self, request: TaskRequest) -> Dict[str, Any]:
        """Execute the actual task logic"""
        
        # Check if this is a continuation with user response
        is_continuation = request.task_data.get("continuation", False)
        user_response = request.task_data.get("user_response")
        
        # Build context for task execution
        context = self._build_task_context(request, user_response)
        
        # Generate response based on task type
        if request.task_type in ["dialogue", "user_interaction", "conversation"]:
            result = await self._handle_dialogue_task(request, context)
        elif request.task_type == "information_gathering":
            result = await self._handle_information_gathering_task(request, context)
        else:
            # For v0, treat other task types as dialogue
            result = await self._handle_dialogue_task(request, context)
        
        return result
    
    def _build_task_context(
        self,
        request: TaskRequest,
        user_response: Optional[str] = None
    ) -> Dict[str, Any]:
        """Build comprehensive context for task execution"""

        task_data = request.task_data

        # Use unified mission context
        mission_context = request.mission_context

        # Extract conversation history from mission progress
        conversation_history = mission_context.progress.get("conversation_history", [])

        context = {
            "mission": {
                "id": request.mission_id,
                "title": mission_context.title,
                "description": mission_context.description,
                "user_input": mission_context.user_input,
                "priority": mission_context.priority,
                "status": mission_context.status,
                "progress": mission_context.progress
            },
            "task": {
                "id": request.task_id,
                "type": request.task_type,
                "description": task_data.get("description", ""),
                "prompt": task_data.get("prompt", "")
            },
            "user_response": user_response,
            "context": task_data.get("context", {}),
            "continuation": request.task_data.get("continuation", False),
            "conversation_history": conversation_history
        }

        return context

    def _build_conversation_summary(self, conversation_history: List[dict]) -> str:
        """Build a comprehensive summary of previous conversation exchanges"""
        if not conversation_history:
            return "CONVERSATION HISTORY:\nNo previous conversations in this mission."

        summary_parts = ["CONVERSATION HISTORY:"]

        # First, collect all user responses for a comprehensive summary
        all_user_responses = []
        for exchange in conversation_history:
            result = exchange.get("result", {})
            user_response = result.get("user_response")
            if user_response:
                all_user_responses.append(user_response)

        # If we have user responses, show them prominently first
        if all_user_responses:
            summary_parts.append("\nUSER PROVIDED INFORMATION:")
            for i, response in enumerate(all_user_responses, 1):
                # Preserve more content for user responses since they contain key information
                if len(response) > 800:
                    response = response[:800] + "..."
                summary_parts.append(f"{i}. {response}")
            summary_parts.append("")

        # Then show the detailed exchange history
        summary_parts.append("DETAILED EXCHANGE HISTORY:")
        for i, exchange in enumerate(conversation_history, 1):
            result = exchange.get("result", {})
            agent_message = result.get("message", "No message recorded")
            user_response = result.get("user_response")
            status = exchange.get("status", "unknown")

            # Use generous truncation for agent messages
            if len(agent_message) > 600:
                agent_message = agent_message[:600] + "..."

            # Add status indicator for clarity
            status_indicator = "✅" if status == "done" else "💬"

            summary_parts.append(f"{i}. {status_indicator} Exchange:")
            summary_parts.append(f"   Agent: {agent_message}")

            # Include user response if available
            if user_response:
                if len(user_response) > 500:
                    user_response = user_response[:500] + "..."
                summary_parts.append(f"   User: {user_response}")

            # Add a separator between exchanges
            if i < len(conversation_history):
                summary_parts.append("")

        return "\n".join(summary_parts)



    async def _handle_dialogue_task(self, request: TaskRequest, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle dialogue tasks with user interaction"""
        
        system_prompt = """You are a helpful AI assistant executing a specific task within a larger mission.

BALANCED APPROACH:
1. GATHER ESSENTIAL INFORMATION - Ask for key details needed to help effectively
2. AVOID EXCESSIVE QUESTIONING - Don't ask for minor details or preferences that don't significantly impact the outcome
3. USE CONVERSATION HISTORY - Don't repeat questions already answered
4. MAKE REASONABLE ASSUMPTIONS - Fill in minor gaps with sensible defaults
5. PROGRESS EFFICIENTLY - Move toward solutions rather than perfect information gathering

QUESTION GUIDELINES:
- Ask for information that significantly impacts the solution quality
- Group related questions together rather than asking them one by one
- Provide reasonable defaults or options when asking for preferences
- Skip questions about minor details that don't change the core approach
- Use conversation history to avoid repeating previous questions

RESPONSE FORMAT:
- For essential information: Ask clearly and explain why it's needed
- For recommendations: Provide specific, actionable suggestions with reasonable assumptions
- For options: Present choices rather than asking open-ended preference questions

Remember: Balance information gathering with progress. Ask what matters, assume what doesn't."""
        
        # Build conversation history summary
        conversation_summary = self._build_conversation_summary(context.get("conversation_history", []))

        # Build the prompt with context
        if context.get("continuation") and context.get("user_response"):
            # This is a continuation with user response
            user_prompt = f"""MISSION CONTEXT:
Original Request: "{context['mission'].get('user_input', 'N/A')}"
Mission: {context['mission'].get('title', 'N/A')}
Description: {context['mission'].get('description', 'N/A')}

{conversation_summary}

CURRENT TASK:
Task: {context['task']['description']}
Instructions: {context['task']['prompt']}

USER RESPONSE:
{context['user_response']}

BALANCED ANALYSIS (for your reasoning):
Analyze this step by step:
1. What information did the user just provide?
2. What information do I already have from previous conversations? (Review the CONVERSATION HISTORY section carefully)
3. What are the key pieces of information I still need to provide a good solution?
4. Can I group any needed questions together efficiently?
5. What reasonable assumptions can I make for less critical details?

CRITICAL: Before asking any questions, carefully review the CONVERSATION HISTORY above to see what information has already been provided by the user. DO NOT ask for information that was already given in previous exchanges.

APPROACH: Gather essential information efficiently, then provide solutions. Avoid asking about minor preferences that don't significantly impact the outcome.

Now process the user's response and either provide a solution or ask for essential missing information (grouped together when possible). Do NOT repeat questions from your analysis - ask each question only once."""
        else:
            # This is the initial task execution
            user_prompt = f"""MISSION CONTEXT:
Original Request: "{context['mission'].get('user_input', 'N/A')}"
Mission: {context['mission'].get('title', 'N/A')}
Description: {context['mission'].get('description', 'N/A')}

{conversation_summary}

CURRENT TASK:
Task: {context['task']['description']}
Instructions: {context['task']['prompt']}

EFFICIENT EXECUTION (for your reasoning):
Evaluate this step by step:
1. What is the specific goal of this task?
2. What information do I already have from the original request and previous conversations? (Review the CONVERSATION HISTORY section carefully)
3. What key information do I need to provide a quality solution?
4. Can I group any necessary questions together?
5. What assumptions can I make for less critical details?

CRITICAL: Before asking any questions, carefully review the CONVERSATION HISTORY above to see what information has already been provided by the user. DO NOT ask for information that was already given in previous exchanges.

BALANCED APPROACH: Gather essential information efficiently, then deliver solutions. Ask for what significantly impacts the outcome, assume reasonable defaults for the rest.

Now execute the task with efficient information gathering and solution delivery. Do NOT repeat questions from your analysis - ask each question only once."""
        
        response = await self.llm_service.generate_response(
            prompt=user_prompt,
            system_prompt=system_prompt,
            temperature=0.7,
            max_tokens=1000
        )
        
        # Build result with user response included for conversation history
        result = {
            "response_type": "dialogue",
            "message": response["content"],
            "requires_user_input": self._requires_user_input(response["content"]),
            "task_status": "done",
            "llm_usage": response.get("usage", {}),
            "context_used": {
                "has_user_response": context['user_response'] is not None,
                "is_continuation": context.get("continuation", False)
            }
        }

        # Include user response in result for conversation history persistence
        if context.get("continuation") and context.get("user_response"):
            result["user_response"] = context["user_response"]

        return result

    async def _handle_information_gathering_task(
        self,
        request: TaskRequest,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Handle information gathering tasks"""

        system_prompt = """You are an information gathering specialist. Your job is to collect, organize, and summarize information needed for the mission.

INSTRUCTIONS:
1. Analyze what information is needed based on the task
2. Use available context from previous interactions
3. Identify what additional information might be needed
4. Organize findings clearly
5. Suggest next steps for information collection

Focus on being thorough and systematic in your information gathering approach."""

        user_prompt = f"""INFORMATION GATHERING TASK:
Task: {context['task']['description']}
Instructions: {context['task']['prompt']}

MISSION CONTEXT:
Original Request: "{context['mission'].get('user_input', 'N/A')}"
Mission: {context['mission'].get('title', 'N/A')}

USER INPUT (if any):
{context['user_response'] or 'No additional user input provided.'}

ANALYSIS REQUIRED:
1. What information do I already have?
2. What information is still needed?
3. How can I organize this information effectively?
4. What are the next steps for gathering missing information?

Provide your information gathering analysis and recommendations."""

        response = await self.llm_service.generate_response(
            prompt=user_prompt,
            system_prompt=system_prompt,
            temperature=0.5,
            max_tokens=800
        )

        return {
            "response_type": "information_gathering",
            "analysis": response["content"],
            "information_collected": self._extract_information_points(response["content"]),
            "next_steps": self._extract_next_steps(response["content"]),
            "task_status": "done",
            "llm_usage": response.get("usage", {})
        }

    def _requires_user_input(self, response: str) -> bool:
        """
        Determine if the response requires user input.
        Balanced approach - detect real questions while avoiding false positives.
        """
        response_lower = response.lower()

        # Strong indicators that definitely require user input
        direct_question_indicators = [
            "could you tell me",
            "can you tell me",
            "please tell me",
            "i need to know",
            "what would you like",
            "which do you prefer",
            "do you want me to",
            "should i",
            "would you prefer",
            "could you provide",
            "can you provide"
        ]

        # Check for direct question indicators
        if any(indicator in response_lower for indicator in direct_question_indicators):
            return True

        # Check for question marks with some context
        if "?" in response:
            # Exclude purely informational or rhetorical questions
            informational_patterns = [
                "what is",
                "what are",
                "how does",
                "why is",
                "here's what",
                "this is what",
                "what we know",
                "what i found",
                "did you know"
            ]

            # If it contains informational patterns, likely not requiring input
            if any(pattern in response_lower for pattern in informational_patterns):
                return False

            # Look for question words that typically require user response
            question_words = ["what", "which", "how", "when", "where", "who"]
            if any(word in response_lower for word in question_words):
                return True

        return False

    def _extract_information_points(self, analysis: str) -> List[str]:
        """Extract key information points from analysis"""
        # Simple extraction - look for bullet points or numbered items
        lines = analysis.split('\n')
        info_points = []

        for line in lines:
            line = line.strip()
            if line.startswith(('-', '*', '•')) or (len(line) > 0 and line[0].isdigit()):
                info_points.append(line)

        return info_points[:5]  # Limit to top 5 points

    def _extract_next_steps(self, analysis: str) -> List[str]:
        """Extract next steps from analysis"""
        # Look for sections mentioning next steps, recommendations, etc.
        lines = analysis.split('\n')
        next_steps = []

        in_next_steps_section = False
        for line in lines:
            line = line.strip()

            if any(keyword in line.lower() for keyword in ['next step', 'recommend', 'suggest', 'should']):
                in_next_steps_section = True
                if line.startswith(('-', '*', '•')) or (len(line) > 0 and line[0].isdigit()):
                    next_steps.append(line)
            elif in_next_steps_section and (line.startswith(('-', '*', '•')) or (len(line) > 0 and line[0].isdigit())):
                next_steps.append(line)
            elif in_next_steps_section and line == '':
                in_next_steps_section = False

        return next_steps[:3]  # Limit to top 3 next steps

    async def _send_callback(
        self,
        request: TaskRequest,
        status: str,
        result: Dict[str, Any],
        error_message: Optional[str] = None
    ) -> None:
        """Send completion callback to dispatcher"""

        # Extract suggested tasks from result if present (for future use)
        suggested_tasks = result.get("suggested_tasks", [])

        callback_data = TaskResponse(
            task_id=request.task_id,
            mission_id=request.mission_id,
            status=status,
            result=result,
            error_message=error_message,
            suggested_tasks=suggested_tasks if suggested_tasks else None
        )

        try:
            response = await self.http_client.post(
                request.callback_url,
                json=callback_data.model_dump(),
                timeout=10.0
            )
            response.raise_for_status()

            logger.info("✅ Callback sent to dispatcher: {request.task_id}", module="DIALOGUE-AGENT", routine="_send_callback")

        except Exception as e:
            logger.error("❌ Failed to send callback to dispatcher: {e}", module="DIALOGUE-AGENT", routine="_send_callback")
            # Don't raise - we don't want to fail the task because of callback issues
