#!/usr/bin/env python3
"""
🌐 Deeplica Web Chat Server
ChatGPT-like web interface for communicating with Deeplica
"""

import os
import sys
import json
import uuid
import asyncio
import logging
import webbrowser
import threading
import time
from contextlib import asynccontextmanager
from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Dict, List, Optional
from pathlib import Path

import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Request, Form
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, RedirectResponse, FileResponse
from fastapi.exception_handlers import http_exception_handler
from starlette.exceptions import HTTPException as Star<PERSON>HTTPException
from security_manager import security_manager
import httpx
from dotenv import load_dotenv

# Add project root to path for imports
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.unified_logging import get_webchat_logger
from shared.port_manager import get_service_port

# Initialize unified logger
logger = get_webchat_logger()
PROJECT_ROOT = Path(__file__).parent.parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

# Load environment variables from .env file
load_dotenv(PROJECT_ROOT / ".env")

from shared.port_manager import ensure_service_port_free, get_service_port, get_service_host, get_localhost, get_service_url

# Setup logging - suppress routine INFO messages
logging.basicConfig(level=logging.WARNING)
# Note: Keep using unified logger from line 38

# Suppress uvicorn access logs for health checks and routine requests
uvicorn_logger = logging.getLogger("uvicorn.access")
uvicorn_logger.setLevel(logging.WARNING)

# Custom filter to suppress health check logs
class HealthCheckFilter(logging.Filter):
    def filter(self, record):
        # Suppress health check requests and other routine requests
        if hasattr(record, 'getMessage'):
            message = record.getMessage()
            if any(pattern in message for pattern in [
                'GET /health',
                'GET /favicon.ico',
                'WebSocket /ws',
                '200 OK',
                '404 Not Found'
            ]):
                return False
        return True

# Apply filter to uvicorn access logger
uvicorn_logger.addFilter(HealthCheckFilter())

# Enhanced Watchdog integration
class WatchdogClient:
    def __init__(self):
        self.watchdog_url = f"http://{get_localhost()}:{get_service_port('watchdog')}"
        self.service_name = "WEB-CHAT"
        self.last_health_check = datetime.now()

    async def send_log(self, message: str, level: str = "INFO", module: str = "main"):
        """Send log message to watchdog"""
        try:
            async with httpx.AsyncClient() as client:
                await client.post(
                    f"{self.watchdog_url}/log",
                    json={
                        "service": self.service_name,
                        "module": module,
                        "level": level,
                        "message": message,
                        "timestamp": datetime.now().isoformat()
                    },
                    timeout=2.0
                )
        except Exception:
            # Silently fail if watchdog is not available
            pass

    async def send_health_status(self, status: str, details: dict = None):
        """Send health status to watchdog"""
        try:
            async with httpx.AsyncClient() as client:
                await client.post(
                    f"{self.watchdog_url}/health_status",
                    json={
                        "service": self.service_name,
                        "status": status,
                        "details": details or {},
                        "timestamp": datetime.now().isoformat()
                    },
                    timeout=2.0
                )
                self.last_health_check = datetime.now()
        except Exception:
            pass

    async def register_service(self):
        """Register service with watchdog"""
        try:
            async with httpx.AsyncClient() as client:
                await client.post(
                    f"{self.watchdog_url}/register",
                    json={
                        "service": self.service_name,
                        "port": get_service_port("web-chat"),
                        "health_endpoint": "/health",
                        "description": "Web Chat Interface for Deeplica",
                        "timestamp": datetime.now().isoformat()
                    },
                    timeout=2.0
                )
        except Exception:
            pass

watchdog_client = WatchdogClient()

# Backward compatibility
async def send_to_watchdog(message: str, level: str = "INFO"):
    """Send important messages to watchdog (backward compatibility)"""
    await watchdog_client.send_log(message, level)

# Browser auto-launch functionality
class BrowserLauncher:
    def __init__(self):
        self.web_chat_url = f"http://{get_localhost()}:{get_service_port('web-chat')}"
        self.backend_url = f"http://{get_localhost()}:{get_service_port('backend')}"
        self.cli_url = f"http://{get_localhost()}:{get_service_port('cli')}"
        self.max_wait_time = 60  # Maximum wait time in seconds
        self.check_interval = 2  # Check every 2 seconds

    async def check_service_health(self, url: str, service_name: str) -> bool:
        """Check if a service is healthy and ready"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{url}/health", timeout=5.0)
                if response.status_code == 200:
                    print(f"🌐 [WEB-CHAT] ✅ {service_name} is ready")
                    return True
                else:
                    print(f"🌐 [WEB-CHAT] ⚠️ {service_name} returned status {response.status_code}")
                    return False
        except Exception as e:
            print(f"🌐 [WEB-CHAT] ⏳ Waiting for {service_name}... ({str(e)[:50]})")
            return False

    async def wait_for_deeplica_ready(self) -> bool:
        """Wait for Deeplica backend services to be ready"""
        print(f"🌐 [WEB-CHAT] 🔍 Checking if Deeplica is ready for user communication...")

        start_time = time.time()

        while (time.time() - start_time) < self.max_wait_time:
            try:
                # Check CLI Terminal (optional - nice to have but not required)
                cli_ready = await self.check_service_health(self.cli_url, "CLI Terminal")
                if cli_ready:
                    print(f"🌐 [WEB-CHAT] ✅ CLI Terminal is ready")

                # Check Backend API (required for core functionality)
                backend_ready = await self.check_service_health(self.backend_url, "Backend API")
                if backend_ready:
                    print(f"🌐 [WEB-CHAT] ✅ Backend API is ready")

                # Backend API is sufficient for browser launch
                if backend_ready:
                    print(f"🌐 [WEB-CHAT] ✅ Deeplica is ready for user communication!")
                    return True

                # Wait before next check
                await asyncio.sleep(self.check_interval)

            except Exception as e:
                print(f"🌐 [WEB-CHAT] ⚠️ Error checking Deeplica readiness: {e}")
                await asyncio.sleep(self.check_interval)

        print(f"🌐 [WEB-CHAT] ⏰ Timeout waiting for Deeplica to be ready")
        return False

    def open_browser_to_login(self):
        """Open browser to the web chat login page"""
        try:
            login_url = f"{self.web_chat_url}/login"
            print(f"🌐 [WEB-CHAT] 🚀 Opening browser to: {login_url}")

            # Try to open browser
            success = webbrowser.open(login_url)

            if success:
                print(f"🌐 [WEB-CHAT] ✅ Browser opened successfully!")
                print(f"🌐 [WEB-CHAT] 👤 Demo credentials: admin/admin123 or demo/demo123")
                print(f"🌐 [WEB-CHAT] 💬 Ready for user communication!")
            else:
                print(f"🌐 [WEB-CHAT] ⚠️ Could not open browser automatically")
                print(f"🌐 [WEB-CHAT] 🔗 Please manually open: {login_url}")

        except Exception as e:
            print(f"🌐 [WEB-CHAT] ❌ Error opening browser: {e}")
            print(f"🌐 [WEB-CHAT] 🔗 Please manually open: {self.web_chat_url}/login")

    async def launch_browser_when_ready(self):
        """Wait for Deeplica to be ready, then launch browser"""
        print(f"🌐 launch initiated...")

        # Wait for Deeplica to be ready
        deeplica_ready = await self.wait_for_deeplica_ready()

        if deeplica_ready:
            # Small delay to ensure web chat is fully ready
            await asyncio.sleep(2)

            # Open browser in a separate thread to avoid blocking
            browser_thread = threading.Thread(
                target=self.open_browser_to_login,
                daemon=True
            )
            browser_thread.start()

            await watchdog_client.send_log("Browser auto-launched for user communication", "INFO", "browser_launcher")
        else:
            print(f"🌐 [WEB-CHAT] ⚠️ Deeplica not fully ready, but launching browser anyway...")
            print(f"🌐 [WEB-CHAT] 💡 Web Chat is running and ready for use!")

            # Launch browser anyway after a short delay
            await asyncio.sleep(3)
            browser_thread = threading.Thread(
                target=self.open_browser_to_login,
                daemon=True
            )
            browser_thread.start()

            await watchdog_client.send_log("Browser auto-launched (fallback mode)", "INFO", "browser_launcher")

browser_launcher = BrowserLauncher()

# CLI Terminal integration
async def send_to_cli_terminal(user: str, message: str, sender: str = "user"):
    """Send chat messages to CLI terminal in chat format"""
    try:
        async with httpx.AsyncClient() as client:
            await client.post(
                f"http://{get_localhost()}:{get_service_port('cli')}/chat",
                json={
                    "message": message,
                    "user": user,
                    "sender": sender,
                    "timestamp": datetime.now().isoformat()
                },
                timeout=1.0
            )
    except Exception:
        # Silently fail if CLI terminal is not available
        pass

# Add current directory to path for local imports
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# Import user service
from user_service import user_service
from shared_models.user import UserRole, UserPermission, UserCreate, UserUpdate

# Health monitoring task
async def health_monitoring_task():
    """Periodic health monitoring and reporting to watchdog"""
    while True:
        try:
            # Collect health metrics
            connection_stats = manager.get_connection_stats()
            health_details = {
                "active_connections": connection_stats["total_connections"],
                "active_users": connection_stats["unique_users"],
                "average_session_duration": connection_stats["average_session_duration"],
                "total_messages": connection_stats["total_messages"],
                "uptime_seconds": (datetime.now() - app.state.start_time).total_seconds() if hasattr(app.state, 'start_time') else 0,
                "memory_usage": "N/A",  # Could add psutil for memory monitoring
                "last_activity": app.state.last_activity.isoformat() if hasattr(app.state, 'last_activity') else None,
                "deeplica_client_failures": deeplica_client.circuit_breaker_failures,
                "circuit_breaker_open": deeplica_client.is_circuit_breaker_open()
            }

            # Determine health status
            status = "healthy"
            if len(manager.active_connections) == 0:
                status = "idle"
            elif len(manager.active_connections) > 100:  # Arbitrary threshold
                status = "busy"

            # Send to watchdog
            await watchdog_client.send_health_status(status, health_details)

        except Exception as e:
            await watchdog_client.send_log(f"Health monitoring error: {e}", "ERROR", "health_monitor")

        # Wait 30 seconds before next check
        await asyncio.sleep(30)

# FastAPI app with lifespan management
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    app.state.start_time = datetime.now()
    app.state.last_activity = datetime.now()

    # Initialize user service
    await user_service.connect()

    # Register with watchdog
    await watchdog_client.register_service()
    await watchdog_client.send_log("Web Chat service starting up", "INFO", "startup")

    # Start health monitoring task
    health_task = asyncio.create_task(health_monitoring_task())
    app.state.health_task = health_task

    # Start browser auto-launch task
    browser_task = asyncio.create_task(browser_launcher.launch_browser_when_ready())
    app.state.browser_task = browser_task

    yield

    # Shutdown
    await watchdog_client.send_log("Web Chat service shutting down", "INFO", "shutdown")
    if hasattr(app.state, 'health_task'):
        app.state.health_task.cancel()
    if hasattr(app.state, 'browser_task'):
        app.state.browser_task.cancel()

    # Close HTTP clients
    await deeplica_client.close()
    await watchdog_client.close() if hasattr(watchdog_client, 'close') else None

app = FastAPI(
    title="Deeplica Web Chat",
    version="1.0.0",
    description="Web-based chat interface for Deeplica",
    lifespan=lifespan
)

# Custom exception handler for authentication redirects
@app.exception_handler(HTTPException)
async def custom_http_exception_handler(request: Request, exc: HTTPException):
    """Handle authentication exceptions with proper redirects"""
    if exc.detail == "redirect_unauthorized":
        return RedirectResponse(url="/unauthorized", status_code=307)

    # For other HTTP exceptions, use default handler
    return await http_exception_handler(request, exc)

# 404 handler - redirect all invalid URLs to unauthorized page
@app.exception_handler(StarletteHTTPException)
async def custom_404_handler(request: Request, exc: StarletteHTTPException):
    """Handle 404 errors by redirecting to unauthorized page"""
    if exc.status_code == 404:
        # Log the invalid URL attempt for security monitoring
        asyncio.create_task(send_to_watchdog(f"Invalid URL access attempt: {request.url.path}"))
        return RedirectResponse(url="/unauthorized", status_code=307)

    # For other Starlette HTTP exceptions, use default handler
    return await http_exception_handler(request, exc)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Static files and templates
static_dir = Path(__file__).parent / "static"
media_pool_dir = Path(__file__).parent / "media_pool"
templates_dir = Path(__file__).parent / "templates"

static_dir.mkdir(exist_ok=True)
media_pool_dir.mkdir(exist_ok=True)
templates_dir.mkdir(exist_ok=True)

app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
app.mount("/media", StaticFiles(directory=str(media_pool_dir)), name="media")
templates = Jinja2Templates(directory=str(templates_dir))
# Disable template caching for development
templates.env.cache = {}

# Session storage (simple in-memory for demo)
user_sessions = {}

# Active WebSocket connections
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_sessions: Dict[str, str] = {}  # user_id -> username
        self.user_rate_limits: Dict[str, List[float]] = {}  # user_id -> list of message timestamps
        self.rate_limit_window = 60  # seconds
        self.rate_limit_max_messages = 30  # max messages per window
        self.connection_metadata: Dict[str, Dict] = {}  # user_id -> metadata

    async def connect(self, websocket: WebSocket, user_id: str, username: str):
        connection_id = str(uuid.uuid4())
        self.active_connections[connection_id] = websocket
        self.user_sessions[connection_id] = username
        self.connection_metadata[connection_id] = {
            "connected_at": datetime.now(),
            "last_activity": datetime.now(),
            "message_count": 0
        }

        # Initialize rate limiting for user
        if username not in self.user_rate_limits:
            self.user_rate_limits[username] = []

        logger.info("User {username} connected with ID {connection_id}", module="main", routine="unknown")
        return connection_id

    async def disconnect(self, connection_id: str):
        if connection_id in self.active_connections:
            username = self.user_sessions.get(connection_id, "unknown")
            del self.active_connections[connection_id]
            if connection_id in self.user_sessions:
                del self.user_sessions[connection_id]
            if connection_id in self.connection_metadata:
                del self.connection_metadata[connection_id]
            logger.info("User {username} disconnected", module="main", routine="unknown")

    def check_rate_limit(self, username: str) -> bool:
        """Check if user is within rate limits"""
        now = datetime.now()
        user_messages = self.user_rate_limits.get(username, [])

        # Remove old messages outside the window
        cutoff_time = now - timedelta(seconds=self.rate_limit_window)
        user_messages = [msg_time for msg_time in user_messages if msg_time > cutoff_time]
        self.user_rate_limits[username] = user_messages

        # Check if under limit
        return len(user_messages) < self.rate_limit_max_messages

    def record_message(self, username: str, connection_id: str):
        """Record a message for rate limiting and activity tracking"""
        if username not in self.user_rate_limits:
            self.user_rate_limits[username] = []
        self.user_rate_limits[username].append(datetime.now())

        # Update connection metadata
        if connection_id in self.connection_metadata:
            self.connection_metadata[connection_id]["last_activity"] = datetime.now()
            self.connection_metadata[connection_id]["message_count"] += 1

    async def send_personal_message(self, message: str, connection_id: str):
        """Send message to specific connection with error handling"""
        if connection_id in self.active_connections:
            websocket = self.active_connections[connection_id]
            try:
                await websocket.send_text(message)
                return True
            except Exception as e:
                # Connection is broken, clean it up
                await self.disconnect(connection_id)
                await watchdog_client.send_log(f"Failed to send message to {connection_id}: {e}", "WARNING", "connection_manager")
                return False
        return False

    async def broadcast_to_user(self, message: str, username: str):
        """Send message to all connections for a specific user"""
        sent_count = 0
        connections_to_remove = []

        for connection_id, user in self.user_sessions.items():
            if user == username:
                success = await self.send_personal_message(message, connection_id)
                if success:
                    sent_count += 1
                else:
                    connections_to_remove.append(connection_id)

        # Clean up failed connections
        for connection_id in connections_to_remove:
            await self.disconnect(connection_id)

        return sent_count

    async def broadcast(self, message: str):
        """Broadcast message to all active connections"""
        sent_count = 0
        connections_to_remove = []

        for connection_id in list(self.active_connections.keys()):
            success = await self.send_personal_message(message, connection_id)
            if success:
                sent_count += 1
            else:
                connections_to_remove.append(connection_id)

        # Clean up failed connections
        for connection_id in connections_to_remove:
            await self.disconnect(connection_id)

        return sent_count

    def get_connection_stats(self) -> Dict:
        """Get connection statistics for monitoring"""
        now = datetime.now()
        active_users = set(self.user_sessions.values())

        # Calculate average session duration
        total_duration = 0
        for metadata in self.connection_metadata.values():
            duration = (now - metadata["connected_at"]).total_seconds()
            total_duration += duration

        avg_duration = total_duration / len(self.connection_metadata) if self.connection_metadata else 0

        return {
            "total_connections": len(self.active_connections),
            "unique_users": len(active_users),
            "average_session_duration": avg_duration,
            "total_messages": sum(meta["message_count"] for meta in self.connection_metadata.values())
        }

manager = ConnectionManager()

# Authentication functions using user service
async def authenticate_user(username: str, password: str):
    """Authenticate user with case-insensitive username"""
    return await user_service.authenticate_user(username, password)

async def create_session(user) -> str:
    """Create session for authenticated user"""
    return await user_service.create_session(user)

async def get_user_from_session(session_id: str):
    """Get user from session ID"""
    return await user_service.get_user_from_session(session_id)

async def require_secure_authentication(request: Request, page_type: str) -> tuple[str, dict, dict]:
    """
    🔐 BULLETPROOF AUTHENTICATION with encrypted tokens
    Returns (session_id, user, security_tokens) if authenticated, raises redirect if not.
    """
    # Check for secure tokens in URL parameters
    access_token = request.query_params.get("access_token")
    page_token = request.query_params.get("page_token")

    # Also check session cookie as fallback
    session_id = request.cookies.get("session_id")

    if access_token and page_token and session_id:
        # Verify secure token-based access
        user = await get_user_from_session(session_id)
        if user:
            # Verify token correlation
            if security_manager.verify_secure_access(
                access_token, page_token, user.user_id, session_id, page_type
            ):
                # Generate new tokens for continued access
                new_tokens = security_manager.generate_secure_tokens(
                    user.user_id, session_id, page_type
                )
                return session_id, user, new_tokens

    # Fallback to basic session check for initial access
    if session_id:
        user = await get_user_from_session(session_id)
        if user:
            # Generate initial secure tokens
            tokens = security_manager.generate_secure_tokens(
                user.user_id, session_id, page_type
            )
            return session_id, user, tokens

    # No valid authentication - redirect to unauthorized page
    raise HTTPException(status_code=401, detail="redirect_unauthorized")

async def require_authentication(request: Request) -> tuple[str, dict]:
    """
    Legacy authentication function for backward compatibility
    """
    session_id = request.cookies.get("session_id")

    if not session_id:
        raise HTTPException(status_code=401, detail="redirect_unauthorized")

    user = await get_user_from_session(session_id)
    if not user:
        raise HTTPException(status_code=401, detail="redirect_unauthorized")

    return session_id, user

async def check_single_session_per_user(user_id: str, current_session_id: str) -> bool:
    """
    Check if this is the only active session for the user.
    Returns True if valid, False if another session exists.
    """
    active_session = await user_service.get_active_session_for_user(user_id)
    return active_session == current_session_id

# Routes
@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """Smart redirect based on authentication status"""
    session_id = request.cookies.get("session_id")

    if session_id:
        user = await get_user_from_session(session_id)
        if user and await check_single_session_per_user(user.user_id, session_id):
            # Valid authenticated user with single session - go to chat
            return RedirectResponse(url="/chat")

    # Not authenticated or invalid session - go to login
    return RedirectResponse(url="/login")

@app.get("/unauthorized", response_class=HTMLResponse)
async def unauthorized_page(request: Request):
    """Unauthorized access page - styled redirect to login"""
    return templates.TemplateResponse("unauthorized.html", {"request": request})

@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    """Login page"""
    return templates.TemplateResponse("login.html", {"request": request})

@app.post("/login")
async def login(request: Request):
    """Handle login with case-insensitive username"""
    form = await request.form()
    username = form.get("username")
    password = form.get("password")

    user = await authenticate_user(username, password)
    if not user:
        return templates.TemplateResponse("login.html", {
            "request": request,
            "error": "Invalid username or password"
        })

    session_id = await create_session(user)
    # Send to watchdog instead of console
    asyncio.create_task(send_to_watchdog(f"User {user.username} logged in, session: {session_id}"))

    response = RedirectResponse(url="/chat", status_code=303)
    response.set_cookie(
        key="session_id",
        value=session_id,
        httponly=False,  # Allow JavaScript access
        secure=False,    # Allow HTTP (not just HTTPS)
        samesite="lax"   # Allow cross-site requests
    )
    return response

@app.get("/chat", response_class=HTMLResponse)
async def chat_page(request: Request):
    """🔐 BULLETPROOF SECURE CHAT - Token-based authentication"""
    session_id, user, security_tokens = await require_secure_authentication(request, "chat")

    # Verify this is the only active session for this user
    if not await check_single_session_per_user(user.user_id, session_id):
        await user_service.delete_session(session_id)
        raise HTTPException(status_code=401, detail="redirect_unauthorized")

    # Generate HTML correlation token
    html_token = security_manager.generate_page_html_token(
        security_tokens['access_token'],
        security_tokens['page_token']
    )

    # Log secure access
    asyncio.create_task(send_to_watchdog(f"🔐 SECURE DeepChat access: {user.username} (encrypted tokens)"))

    # Build secure URL with tokens
    secure_url = f"/chat?access_token={security_tokens['access_token']}&page_token={security_tokens['page_token']}"

    return templates.TemplateResponse("chat_new.html", {
        "request": request,
        "user": user,
        "session_id": session_id,
        "security_tokens": security_tokens,
        "html_token": html_token,
        "secure_url": secure_url
    })

@app.get("/chat-old", response_class=HTMLResponse)
async def chat_old_page(request: Request):
    """Original complex chat interface (for debugging) - Secure access"""
    session_id, user = await require_authentication(request)

    # Verify this is the only active session for this user
    if not await check_single_session_per_user(user.user_id, session_id):
        await user_service.delete_session(session_id)
        raise HTTPException(status_code=401, detail="redirect_unauthorized")

    return templates.TemplateResponse("chat.html", {
        "request": request,
        "user": user,
        "session_id": session_id
    })

@app.get("/test-websocket", response_class=HTMLResponse)
async def test_websocket_page(request: Request):
    """WebSocket test page - Secure access"""
    session_id, user = await require_authentication(request)

    # Verify this is the only active session for this user
    if not await check_single_session_per_user(user.user_id, session_id):
        await user_service.delete_session(session_id)
        raise HTTPException(status_code=401, detail="redirect_unauthorized")

    return templates.TemplateResponse("test_websocket.html", {
        "request": request,
        "user": user,
        "session_id": session_id
    })

@app.get("/simple-chat", response_class=HTMLResponse)
async def simple_chat_page(request: Request):
    """Simple chat page - Secure access"""
    session_id, user = await require_authentication(request)

    # Verify this is the only active session for this user
    if not await check_single_session_per_user(user.user_id, session_id):
        await user_service.delete_session(session_id)
        raise HTTPException(status_code=401, detail="redirect_unauthorized")

    return templates.TemplateResponse("simple_chat.html", {
        "request": request,
        "user": user,
        "session_id": session_id
    })

@app.post("/logout")
async def logout(request: Request):
    """Logout"""
    session_id = request.cookies.get("session_id")
    if session_id:
        await user_service.delete_session(session_id)

    response = RedirectResponse(url="/login")
    response.delete_cookie(key="session_id")
    return response

# Admin routes
async def require_secure_admin(request: Request) -> tuple[dict, dict]:
    """🔐 BULLETPROOF ADMIN AUTHENTICATION with encrypted tokens"""
    session_id, user, security_tokens = await require_secure_authentication(request, "admin")

    # Verify single session per user
    if not await check_single_session_per_user(user.user_id, session_id):
        await user_service.delete_session(session_id)
        raise HTTPException(status_code=401, detail="redirect_unauthorized")

    if user.role != UserRole.ADMIN:
        # Not admin - redirect to unauthorized page
        raise HTTPException(status_code=403, detail="redirect_unauthorized")

    return user, security_tokens

async def require_admin(request: Request):
    """Legacy admin authentication for backward compatibility"""
    session_id = request.cookies.get("session_id")
    if not session_id:
        raise HTTPException(status_code=401, detail="redirect_unauthorized")

    user = await get_user_from_session(session_id)
    if not user:
        raise HTTPException(status_code=401, detail="redirect_unauthorized")

    if not await check_single_session_per_user(user.user_id, session_id):
        await user_service.delete_session(session_id)
        raise HTTPException(status_code=401, detail="redirect_unauthorized")

    if user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="redirect_unauthorized")

    return user

@app.get("/api/admin/debug/current-user")
async def debug_current_user(request: Request):
    """Debug endpoint to check current user info"""
    session_id = request.cookies.get("session_id")
    if not session_id:
        return {"authenticated": False, "message": "No session found"}

    try:
        user = await get_user_from_session(session_id)
        if not user:
            return {"authenticated": False, "message": "Invalid session"}

        return {
            "authenticated": True,
            "user_id": user.user_id,
            "username": user.username,
            "role": user.role,
            "is_admin": user.role == UserRole.ADMIN,
            "permissions": user.permissions
        }
    except Exception as e:
        return {"authenticated": False, "error": str(e)}

@app.get("/admin", response_class=HTMLResponse)
async def admin_page(request: Request):
    """🔐 BULLETPROOF SECURE ADMIN - Token-based authentication"""
    try:
        user, security_tokens = await require_secure_admin(request)

        # Generate HTML correlation token
        html_token = security_manager.generate_page_html_token(
            security_tokens['access_token'],
            security_tokens['page_token']
        )

        # Build secure URL with tokens
        secure_url = f"/admin?access_token={security_tokens['access_token']}&page_token={security_tokens['page_token']}"

        # Create response with cache-busting headers
        response = templates.TemplateResponse("admin.html", {
            "request": request,
            "user": user,
            "security_tokens": security_tokens,
            "html_token": html_token,
            "secure_url": secure_url
        })

        # Add cache-busting headers to ensure fresh content every time
        response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate, max-age=0"
        response.headers["Pragma"] = "no-cache"
        response.headers["Expires"] = "0"
        response.headers["Last-Modified"] = "Thu, 01 Jan 1970 00:00:00 GMT"

        return response
    except HTTPException as e:
        if e.detail == "redirect_unauthorized":
            return RedirectResponse(url="/unauthorized", status_code=307)
        elif e.status_code == 401:
            return RedirectResponse(url="/unauthorized", status_code=307)
        else:
            return RedirectResponse(url="/unauthorized", status_code=307)

@app.get("/api/admin/users")
async def get_users(request: Request):
    """Get all users (admin only)"""
    await require_admin(request)

    try:
        # Ensure user service is connected
        if user_service.users_collection is None and not hasattr(user_service, '_mock_users'):
            await user_service.connect()

        users = await user_service.get_all_users()
        users_data = []
        for user in users:
            users_data.append({
                "user_id": user.user_id,
                "username": user.username,
                "email": user.email,
                "mobile_phone": user.mobile_phone,
                "full_name": user.full_name,
                "display_name": user.display_name,
                "role": user.role,
                "status": user.status,
                "permissions": user.permissions,
                "created_at": user.created_at.isoformat(),
                "last_login": user.last_login.isoformat() if user.last_login else None,
                "login_count": user.login_count
            })

        return {"users": users_data}
    except Exception as e:
        logger.error(f"Failed to get users: {e}", module="WEB-CHAT", routine="get_users")
        raise HTTPException(status_code=500, detail=f"Failed to fetch users: {str(e)}")

@app.post("/api/admin/users")
async def create_user_admin(request: Request):
    """Create new user (admin only)"""
    await require_admin(request)

    try:
        data = await request.json()

        # Ensure user service is connected
        if user_service.users_collection is None and not hasattr(user_service, '_mock_users'):
            await user_service.connect()

        user_create = UserCreate(**data)
        user_response = await user_service.create_user(user_create)
        return {"success": True, "user": user_response.model_dump()}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to create user: {e}", module="WEB-CHAT", routine="create_user_admin")
        raise HTTPException(status_code=500, detail=f"Failed to create user: {str(e)}")

@app.get("/api/admin/users/{user_id}")
async def get_user_admin(request: Request, user_id: str):
    """Get single user (admin only)"""
    await require_admin(request)

    try:
        # Ensure user service is connected
        if user_service.users_collection is None and not hasattr(user_service, '_mock_users'):
            await user_service.connect()

        user = await user_service.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        return {
            "user_id": user.user_id,
            "username": user.username,
            "email": user.email,
            "mobile_phone": user.mobile_phone,
            "full_name": user.full_name,
            "display_name": user.display_name,
            "role": user.role,
            "status": user.status,
            "permissions": user.permissions,
            "password_hash": user.password_hash,  # Include password hash for admin debugging
            "created_at": user.created_at.isoformat(),
            "last_login": user.last_login.isoformat() if user.last_login else None,
            "login_count": user.login_count
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get user: {e}", module="WEB-CHAT", routine="get_user_admin")
        raise HTTPException(status_code=500, detail=f"Failed to fetch user: {str(e)}")

@app.put("/api/admin/users/{user_id}")
async def update_user_admin(request: Request, user_id: str):
    """Update user (admin only)"""
    await require_admin(request)

    try:
        data = await request.json()

        # Ensure user service is connected
        if user_service.users_collection is None and not hasattr(user_service, '_mock_users'):
            await user_service.connect()

        user_update = UserUpdate(**data)
        updated_user = await user_service.update_user(user_id, user_update)

        if not updated_user:
            raise HTTPException(status_code=404, detail="User not found")

        return {"success": True, "user": updated_user.model_dump()}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update user: {e}", module="WEB-CHAT", routine="update_user_admin")
        raise HTTPException(status_code=500, detail=f"Failed to update user: {str(e)}")

@app.delete("/api/admin/users/{user_id}")
async def delete_user_admin(request: Request, user_id: str):
    """Delete user (admin only)"""
    admin_user = await require_admin(request)

    # Prevent admin from deleting themselves
    if user_id == admin_user.user_id:
        raise HTTPException(status_code=400, detail="Cannot delete your own account")

    try:
        # Ensure user service is connected
        if user_service.users_collection is None and not hasattr(user_service, '_mock_users'):
            await user_service.connect()

        success = await user_service.delete_user(user_id)

        if not success:
            raise HTTPException(status_code=404, detail="User not found")

        return {"success": True, "message": "User deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete user: {e}", module="WEB-CHAT", routine="delete_user_admin")
        raise HTTPException(status_code=500, detail=f"Failed to delete user: {str(e)}")

@app.post("/api/admin/users/{user_id}/restore")
async def restore_user(request: Request, user_id: str):
    """Restore user from database (admin only)"""
    await require_admin(request)

    try:
        # Ensure user service is connected
        if user_service.users_collection is None and not hasattr(user_service, '_mock_users'):
            await user_service.connect()

        # Get user from database
        user = await user_service.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # If user is already active, no need to restore
        if user.status == UserStatus.ACTIVE:
            return {"success": True, "message": "User is already active", "user": user.model_dump()}

        # Restore user by setting status to active
        user_update = UserUpdate(status=UserStatus.ACTIVE)
        updated_user = await user_service.update_user(user_id, user_update)

        if not updated_user:
            raise HTTPException(status_code=404, detail="Failed to restore user")

        return {"success": True, "message": "User restored successfully", "user": updated_user.model_dump()}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to restore user: {e}", module="WEB-CHAT", routine="restore_user")
        raise HTTPException(status_code=500, detail=f"Failed to restore user: {str(e)}")

@app.post("/api/admin/users/refresh")
async def refresh_users_from_db(request: Request):
    """Refresh users from database (admin only)"""
    await require_admin(request)

    try:
        # Force reconnection to database
        await user_service.connect()

        # Get all users from database
        users = await user_service.get_all_users()
        users_data = []
        for user in users:
            users_data.append({
                "user_id": user.user_id,
                "username": user.username,
                "email": user.email,
                "mobile_phone": user.mobile_phone,
                "full_name": user.full_name,
                "display_name": user.display_name,
                "role": user.role,
                "status": user.status,
                "permissions": user.permissions,
                "created_at": user.created_at.isoformat() if user.created_at else None,
                "last_login": user.last_login.isoformat() if user.last_login else None,
                "login_count": user.login_count
            })

        return {"success": True, "users": users_data, "message": f"Refreshed {len(users_data)} users from database"}

    except Exception as e:
        logger.error(f"Failed to refresh users: {e}", module="WEB-CHAT", routine="refresh_users_from_db")
        raise HTTPException(status_code=500, detail=f"Failed to refresh users: {str(e)}")


# ========================================
# EXTERNAL SERVICES MANAGEMENT
# ========================================

@app.get("/api/admin/external-services")
async def get_external_services(request: Request):
    """Get external services configuration (admin only)"""
    await require_admin(request)

    try:
        # Load external services configuration
        external_services = {
            'mongodb': {
                'name': 'MongoDB Atlas',
                'description': 'Primary database for DEEPLICA system',
                'enabled': True,
                'connection_string': os.getenv('MONGODB_CONNECTION_STRING', ''),
                'database_name': 'deeplica-dev'
            },
            'gemini': {
                'name': 'Google Gemini API',
                'description': 'AI language model for dialogue and planning',
                'enabled': True,
                'api_key': os.getenv('GEMINI_API_KEY', ''),
                'model': 'gemini-1.5-flash'
            },
            'twilio': {
                'name': 'Twilio Service',
                'description': 'Phone call and SMS services',
                'enabled': True,
                'account_sid': os.getenv('TWILIO_ACCOUNT_SID', ''),
                'auth_token': os.getenv('TWILIO_AUTH_TOKEN', ''),
                'phone_number': os.getenv('TWILIO_PHONE_NUMBER', '+**********'),
                'webhook_url': os.getenv('TWILIO_WEBHOOK_URL', 'https://your-ngrok-url.ngrok-free.app')
            },
            'ngrok': {
                'name': 'ngrok Service',
                'description': 'Secure tunneling for webhooks',
                'enabled': True,
                'auth_token': os.getenv('NGROK_API_KEY', ''),
                'tunnel_port': get_service_port("backend"),
                'api_port': get_service_port("ngrok-api")
            }
        }

        return {
            "success": True,
            "services": external_services
        }
    except Exception as e:
        logger.error(f"Failed to get external services: {e}", module="WEB-CHAT", routine="get_external_services")
        raise HTTPException(status_code=500, detail=f"Failed to get external services: {str(e)}")


@app.post("/api/admin/external-services")
async def save_external_services(request: Request):
    """Save external services configuration (admin only)"""
    await require_admin(request)

    try:
        data = await request.json()
        # Implementation for saving external services
        # This would update .env files and database settings

        return {
            "success": True,
            "message": "External services configuration saved"
        }
    except Exception as e:
        logger.error(f"Failed to save external services: {e}", module="WEB-CHAT", routine="save_external_services")
        raise HTTPException(status_code=500, detail=f"Failed to save external services: {str(e)}")


@app.post("/api/admin/factory-reset")
async def factory_reset(request: Request):
    """Reset all settings to factory defaults (admin only)"""
    await require_admin(request)

    try:
        # Implementation for factory reset
        # This would reset all settings to defaults

        return {
            "success": True,
            "message": "Factory reset completed"
        }
    except Exception as e:
        logger.error(f"Failed to perform factory reset: {e}", module="WEB-CHAT", routine="factory_reset")
        raise HTTPException(status_code=500, detail=f"Failed to perform factory reset: {str(e)}")

# ========================================
# HOST SETTINGS MANAGEMENT (ADMIN ONLY)
# ========================================

@app.get("/admin/host-settings")
async def get_host_settings(request: Request):
    """Get current host settings (admin only)"""
    await require_admin(request)

    try:
        from shared.system_settings import get_system_host_config
        host_config = await get_system_host_config()

        return {
            "default_host": host_config.get("default_host", "0.0.0.0"),
            "localhost": host_config.get("localhost", "127.0.0.1"),
            "external_host": host_config.get("external_host", "0.0.0.0"),
            "available_hosts": {
                "0.0.0.0": "All Interfaces (External Access)",
                "127.0.0.1": "Localhost Only (Local Access)"
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get host settings: {str(e)}")

@app.post("/admin/host-settings")
async def update_host_settings(request: Request):
    """Update host settings (admin only)"""
    await require_admin(request)

    try:
        data = await request.json()
        new_host = data.get("default_host")

        if not new_host:
            raise HTTPException(status_code=400, detail="default_host is required")

        # Validate host value (allow custom IPs as well)
        import re
        ip_pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
        if not re.match(ip_pattern, new_host):
            raise HTTPException(status_code=400, detail="Invalid IP address format")

        # Update the host setting in database
        from shared.system_settings import update_system_host_config
        host_config = {
            "default_host": new_host,
            "localhost": "127.0.0.1",  # Keep localhost constant
            "external_host": new_host if new_host != "127.0.0.1" else "0.0.0.0"
        }

        success = await update_system_host_config(host_config)

        if not success:
            raise HTTPException(status_code=500, detail="Failed to save host settings to database")

        logger.info(f"Host settings updated to {new_host} by admin", module="WEB-CHAT", routine="update_host_settings")

        return {
            "success": True,
            "message": f"Host settings updated to {new_host}",
            "default_host": new_host,
            "note": "Services will use the new host configuration on next restart"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update host settings: {str(e)}")

# ========================================
# PORT SETTINGS MANAGEMENT (ADMIN ONLY)
# ========================================

@app.get("/admin/port-settings")
async def get_port_settings(request: Request):
    """Get current port settings with friendly names (admin only)"""
    await require_admin(request)

    try:
        from shared.system_settings import get_system_port_config
        from shared.port_manager import is_port_free

        port_config = await get_system_port_config()

        # Service friendly names mapping - ALL SERVICES THAT USE PORTS
        service_names = {
            # Core Services
            "backend": "🌐 Backend API",
            "dispatcher": "🎯 Dispatcher Service",
            "dialogue": "💬 Dialogue Agent",
            "planner": "🧠 Planner Agent",
            "phone": "📞 Phone Agent",

            # System Services
            "watchdog": "🐕 Watchdog Monitor",

            # Web Services
            "web-chat": "💻 Web Chat Interface",
            "cli": "🖥️ CLI Terminal",

            # External Services
            "twilio-echo-bot": "📱 Twilio Echo Bot",
            "webhook": "🔗 Webhook Server",

            # Development & Utility Services
            "ngrok-api": "🌐 ngrok API Dashboard",
            "ngrok-tunnel": "🚇 ngrok Tunnel Port",
            "test-server": "🧪 Test Server",
            "dev-server": "⚙️ Development Server",
            "proxy-server": "🔄 Proxy Server"
        }

        # Add status information and friendly names for each port
        ports_with_status = {}
        for service, port in port_config.items():
            status = "Free" if is_port_free(port) else "In Use"
            is_readonly = (service == "backend")  # Backend API is read-only

            ports_with_status[service] = {
                "port": port,
                "status": status,
                "friendly_name": service_names.get(service, service.replace("-", " ").title()),
                "readonly": is_readonly,
                "description": "Constant port - cannot be changed" if is_readonly else "Configurable port"
            }

        return {
            "ports": ports_with_status,
            "message": "Port settings retrieved successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get port settings: {str(e)}")

@app.post("/admin/port-settings")
async def update_port_settings(request: Request):
    """Update port settings (admin only)"""
    await require_admin(request)

    try:
        data = await request.json()
        ports = data.get("ports", {})

        if not ports:
            raise HTTPException(status_code=400, detail="ports configuration is required")

        # Validate port values
        for service, port in ports.items():
            if not isinstance(port, int) or port < 1024 or port > 65535:
                raise HTTPException(status_code=400, detail=f"Invalid port {port} for service {service}. Must be between 1024-65535")

        # Update the port settings
        from shared.system_settings import update_system_port_config
        success = await update_system_port_config(ports)

        if not success:
            raise HTTPException(status_code=500, detail="Failed to save port settings to database")

        logger.info(f"Port settings updated by admin", module="WEB-CHAT", routine="update_port_settings")

        return {
            "success": True,
            "message": "Port settings updated successfully",
            "ports": ports,
            "note": "Services will use the new port configuration on next restart"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update port settings: {str(e)}")

@app.post("/admin/port-settings/reset")
async def reset_port_settings(request: Request):
    """Reset port settings to defaults (admin only)"""
    await require_admin(request)

    try:
        from shared.system_settings import SystemSettingsManager

        settings_manager = SystemSettingsManager()
        default_ports = settings_manager.default_settings["port_config"]

        # Update with default ports
        from shared.system_settings import update_system_port_config
        success = await update_system_port_config(default_ports)

        if not success:
            raise HTTPException(status_code=500, detail="Failed to reset port settings in database")

        logger.info("Port settings reset to defaults by admin", module="WEB-CHAT", routine="reset_port_settings")

        return {
            "success": True,
            "message": "Port settings reset to default values",
            "ports": default_ports
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to reset port settings: {str(e)}")

@app.get("/admin/check-port/{port}")
async def check_port_availability(port: int, request: Request):
    """Check if a port is available (admin only)"""
    await require_admin(request)

    try:
        from shared.port_manager import is_port_free

        available = is_port_free(port)

        return {
            "port": port,
            "available": available,
            "status": "Free" if available else "In Use"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to check port availability: {str(e)}")

@app.post("/admin/factory-reset")
async def factory_reset_settings(request: Request):
    """Reset all system settings to factory defaults (admin only)"""
    await require_admin(request)

    try:
        from shared.system_settings import reset_system_to_factory_defaults
        from shared.port_manager import reset_to_factory_defaults

        # Reset system settings in database
        db_success = await reset_system_to_factory_defaults()

        # Reset local backup file
        reset_to_factory_defaults()

        if not db_success:
            logger.warning("Database reset failed, but local backup was reset", module="WEB-CHAT", routine="factory_reset_settings")

        logger.info("System settings reset to factory defaults by admin", module="WEB-CHAT", routine="factory_reset_settings")

        return {
            "success": True,
            "message": "System settings reset to factory defaults. Please restart Deeplica for changes to take effect.",
            "database_reset": db_success,
            "local_backup_reset": True,
            "restart_required": True
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to reset to factory defaults: {str(e)}", module="WEB-CHAT", routine="factory_reset_settings")
        raise HTTPException(status_code=500, detail=f"Failed to reset to factory defaults: {str(e)}")

# ========================================
# EXTERNAL SERVICES SETTINGS (ADMIN ONLY)
# ========================================

@app.get("/admin/external-services")
async def get_external_services(request: Request):
    """Get external services configuration (admin only)"""
    await require_admin(request)

    try:
        from shared.api_manager import get_api_manager

        # Get all external configurations through API manager
        api_manager = get_api_manager()
        external_services = api_manager.get_all_external_configs()

        # Return real values - no masking for admin interface
        # Admin users need access to real connection strings and API keys
        return {
            "external_services": external_services,
            "message": "External services configuration retrieved successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get external services: {str(e)}")

@app.post("/admin/external-services")
async def update_external_services(request: Request, services_data: dict):
    """Update external services configuration (admin only)"""
    await require_admin(request)

    try:
        # NOTE: API Manager enforces .env as the only source
        # Web admin can view configurations but updates must be done through .env file
        # This ensures consistency and prevents configuration drift

        external_services = services_data.get("external_services", {})

        # Validate required fields for each service
        validation_errors = []

        for service_id, config in external_services.items():
            if service_id == "mongodb":
                if config.get("enabled") and not config.get("uri"):
                    validation_errors.append("MongoDB URI is required when enabled")
            elif service_id == "gemini":
                if config.get("enabled") and not config.get("api_key"):
                    validation_errors.append("Gemini API key is required when enabled")
            elif service_id == "twilio":
                if config.get("enabled"):
                    required_fields = ["account_sid", "auth_token", "phone_number"]
                    missing = [field for field in required_fields if not config.get(field)]
                    if missing:
                        validation_errors.append(f"Twilio missing required fields: {', '.join(missing)}")
            elif service_id == "ngrok":
                if config.get("enabled") and not config.get("api_key"):
                    validation_errors.append("Ngrok API key is required when enabled")

        if validation_errors:
            raise HTTPException(status_code=400, detail=f"Validation errors: {'; '.join(validation_errors)}")

        # For now, return success but inform admin about .env requirement
        success = True

        if not success:
            raise HTTPException(status_code=500, detail="Failed to update external services in database")

        logger.info("External services configuration updated by admin", module="WEB-CHAT", routine="update_external_services")

        return {
            "success": True,
            "message": "Configuration validated successfully. To apply changes, update the .env file and restart services. API Manager enforces .env as the single source of truth.",
            "restart_required": True,
            "note": "API Manager Policy: All external service configurations must be managed through .env file only"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update external services: {str(e)}", module="WEB-CHAT", routine="update_external_services")
        raise HTTPException(status_code=500, detail=f"Failed to update external services: {str(e)}")

@app.post("/admin/test-external-service/{service_id}")
async def test_external_service(service_id: str, request: Request):
    """Test external service connection (admin only)"""
    await require_admin(request)

    try:
        from shared.system_settings import get_external_services_config

        external_services = await get_external_services_config()
        service_config = external_services.get(service_id)

        if not service_config:
            raise HTTPException(status_code=404, detail=f"Service {service_id} not found")

        if not service_config.get("enabled"):
            return {"success": False, "message": f"{service_config.get('name', service_id)} is disabled"}

        # Test connection based on service type
        test_result = await _test_service_connection(service_id, service_config)

        return {
            "success": test_result["success"],
            "message": test_result["message"],
            "details": test_result.get("details", {})
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to test service {service_id}: {str(e)}", module="WEB-CHAT", routine="test_external_service")
        raise HTTPException(status_code=500, detail=f"Failed to test service: {str(e)}")

async def _test_service_connection(service_id: str, config: dict) -> dict:
    """Test connection to external service"""
    try:
        if service_id == "mongodb":
            # Test MongoDB connection
            import motor.motor_asyncio
            client = motor.motor_asyncio.AsyncIOMotorClient(config["connection_string"])
            await client.admin.command('ping')
            await client.close()
            return {"success": True, "message": "MongoDB connection successful"}

        elif service_id == "gemini":
            # Test Gemini API
            import google.generativeai as genai
            genai.configure(api_key=config["api_key"])
            model = genai.GenerativeModel(config.get("model", "gemini-1.5-flash"))
            response = model.generate_content("Test connection")
            return {"success": True, "message": "Gemini API connection successful"}

        elif service_id == "twilio":
            # Test Twilio API
            from twilio.rest import Client
            client = Client(config["account_sid"], config["auth_token"])
            account = client.api.accounts(config["account_sid"]).fetch()
            return {"success": True, "message": f"Twilio connection successful: {account.friendly_name}"}

        elif service_id == "ngrok":
            # Test ngrok API
            import requests
            ngrok_port = config.get('api_port', get_service_port("ngrok-api"))
            response = requests.get(f"http://localhost:{ngrok_port}/api/tunnels", timeout=5)
            if response.status_code == 200:
                tunnels = response.json().get("tunnels", [])
                return {"success": True, "message": f"ngrok API accessible, {len(tunnels)} active tunnels"}
            else:
                return {"success": False, "message": "ngrok API not accessible"}
        else:
            return {"success": False, "message": f"Unknown service type: {service_id}"}

    except Exception as e:
        return {"success": False, "message": f"Connection test failed: {str(e)}"}

@app.get("/health")
async def health_check():
    """Enhanced health check endpoint with detailed metrics"""
    connection_stats = manager.get_connection_stats()

    # Determine overall health status
    status = "healthy"
    if deeplica_client.is_circuit_breaker_open():
        status = "degraded"
    elif connection_stats["total_connections"] == 0:
        status = "idle"
    elif connection_stats["total_connections"] > 50:  # Arbitrary threshold
        status = "busy"

    return {
        "status": status,
        "service": "Web Chat Interface",
        "version": "1.0.0",
        "port": get_service_port("web-chat"),
        "uptime_seconds": (datetime.now() - app.state.start_time).total_seconds() if hasattr(app.state, 'start_time') else 0,
        "connections": {
            "active": connection_stats["total_connections"],
            "unique_users": connection_stats["unique_users"],
            "average_session_duration": connection_stats["average_session_duration"],
            "total_messages": connection_stats["total_messages"]
        },
        "backend": {
            "deeplica_failures": deeplica_client.circuit_breaker_failures,
            "circuit_breaker_open": deeplica_client.is_circuit_breaker_open()
        },
        "last_activity": app.state.last_activity.isoformat() if hasattr(app.state, 'last_activity') else None,
        "browser_auto_launch": "enabled"
    }

@app.get("/avatar")
async def get_avatar():
    """Serve the Deeplica avatar video"""
    try:
        video_path = PROJECT_ROOT / "agents" / "media_pool" / "Deeplica Avatar.mp4"
        if video_path.exists():
            return FileResponse(
                path=str(video_path),
                media_type="video/mp4",
                headers={"Cache-Control": "public, max-age=3600"}
            )
        else:
            raise HTTPException(status_code=404, detail="Avatar video not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error serving avatar: {str(e)}")

@app.post("/launch_browser")
async def launch_browser_manually():
    """Manually trigger browser launch"""
    try:
        # Launch browser in background task
        browser_task = asyncio.create_task(browser_launcher.launch_browser_when_ready())

        return {
            "status": "success",
            "message": "Browser launch initiated",
            "url": browser_launcher.web_chat_url + "/login"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Failed to launch browser: {str(e)}"
        }

# Enhanced Deeplica Communication with connection pooling
class DeepplicaClient:
    def __init__(self):
        self.cli_url = f"http://{get_localhost()}:{get_service_port('cli')}"  # CLI Terminal port
        self.client = None
        self.retry_attempts = 3
        self.retry_delay = 1.0
        self.circuit_breaker_failures = 0
        self.circuit_breaker_threshold = 5
        self.circuit_breaker_reset_time = 60
        self.last_failure_time = None

    async def get_client(self):
        """Get or create HTTP client with connection pooling"""
        if self.client is None:
            self.client = httpx.AsyncClient(
                timeout=httpx.Timeout(30.0, connect=5.0),
                limits=httpx.Limits(max_keepalive_connections=5, max_connections=10)
            )
        return self.client

    async def close(self):
        """Close HTTP client"""
        if self.client:
            await self.client.aclose()
            self.client = None

    def is_circuit_breaker_open(self) -> bool:
        """Check if circuit breaker is open"""
        if self.circuit_breaker_failures >= self.circuit_breaker_threshold:
            if self.last_failure_time and (datetime.now() - self.last_failure_time).seconds < self.circuit_breaker_reset_time:
                return True
            else:
                # Reset circuit breaker
                self.circuit_breaker_failures = 0
                self.last_failure_time = None
        return False

    async def send_message(self, message: str, username: str) -> str:
        """Send message to Deeplica CLI with retry logic and circuit breaker"""
        if self.is_circuit_breaker_open():
            await watchdog_client.send_log("Circuit breaker open, rejecting request", "WARNING", "deeplica_client")
            return "Service temporarily unavailable. Please try again in a moment."

        for attempt in range(self.retry_attempts):
            try:
                client = await self.get_client()
                response = await client.post(
                    f"{self.cli_url}/chat",
                    json={"message": message, "user": username},
                    timeout=30.0
                )

                if response.status_code == 200:
                    # Reset circuit breaker on success
                    self.circuit_breaker_failures = 0
                    self.last_failure_time = None
                    return response.json().get("response", "No response from Deeplica")
                else:
                    raise httpx.HTTPStatusError(f"HTTP {response.status_code}", request=response.request, response=response)

            except Exception as e:
                self.circuit_breaker_failures += 1
                self.last_failure_time = datetime.now()

                await watchdog_client.send_log(f"Deeplica client error (attempt {attempt + 1}): {e}", "ERROR", "deeplica_client")

                if attempt < self.retry_attempts - 1:
                    await asyncio.sleep(self.retry_delay * (attempt + 1))  # Exponential backoff
                else:
                    logger.error("Error communicating with Deeplica after {self.retry_attempts} attempts: {e}", module="main", routine="unknown")
                    return f"I'm having trouble connecting to my backend service. Please try again later."

        return "Service temporarily unavailable. Please try again."

deeplica_client = DeepplicaClient()

# WebSocket endpoint
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time chat with improved stability"""
    connection_id = None
    user = None

    try:
        # Get session from query params
        session_id = websocket.query_params.get("session_id")
        print(f"🔗 WebSocket connection attempt with session: {session_id}")
        print(f"🔍 Query params: {dict(websocket.query_params)}")
        print(f"🔍 Available sessions: {list(user_sessions.keys())}")

        if not session_id:
            print(f"❌ WebSocket rejected: No session provided")
            await websocket.accept()  # Must accept before closing
            await websocket.close(code=1008, reason="No session provided")
            return

        user = await get_user_from_session(session_id)
        if not user:
            print(f"❌ WebSocket rejected: Invalid session {session_id}")
            await websocket.accept()  # Must accept before closing
            await websocket.close(code=1008, reason="Invalid session")
            return

        # Verify this is the only active session for this user
        if not await check_single_session_per_user(user.user_id, session_id):
            print(f"❌ WebSocket rejected: Multiple sessions detected for user {user.username}")
            await websocket.accept()  # Must accept before closing
            await websocket.close(code=1008, reason="Multiple sessions not allowed")
            return

        # Accept WebSocket connection
        await websocket.accept()
        print(f"✅ WebSocket accepted for user: {user.username} (secure single session)")
        connection_id = await manager.connect(websocket, user.username, user.username)

        # Update activity tracking
        if hasattr(app.state, 'last_activity'):
            app.state.last_activity = datetime.now()

        # Send to watchdog and log
        await watchdog_client.send_log(f"WebSocket connected: {user.username}", "INFO", "websocket")
        print(f"🔗 WebSocket connected: {user.username} (session: {session_id})")

        # Send connection confirmation
        connection_msg = {
            "type": "connection",
            "status": "connected",
            "user": user.username,
            "timestamp": datetime.now().isoformat()
        }
        await manager.send_personal_message(json.dumps(connection_msg), connection_id)

        # Welcome message is now handled by static HTML template
        # No need to send duplicate welcome message via WebSocket
        print(f"🔗 WebSocket connected for {user.username}")

        # Message handling loop with heartbeat
        last_heartbeat = datetime.now()

        while True:
            try:
                # Wait for message with timeout for heartbeat
                data = await asyncio.wait_for(websocket.receive_text(), timeout=30.0)
                message_data = json.loads(data)

                # Handle different message types
                if message_data.get("type") == "ping":
                    # Respond to ping with pong
                    pong_msg = {
                        "type": "pong",
                        "timestamp": datetime.now().isoformat()
                    }
                    await manager.send_personal_message(json.dumps(pong_msg), connection_id)
                    last_heartbeat = datetime.now()

                elif message_data.get("type") == "message":
                    user_message = message_data.get("content", "").strip()
                    if not user_message:
                        continue

                    # Check rate limiting
                    if not manager.check_rate_limit(user.username):
                        rate_limit_msg = {
                            "type": "error",
                            "message": "Rate limit exceeded. Please slow down.",
                            "timestamp": datetime.now().isoformat()
                        }
                        await manager.send_personal_message(json.dumps(rate_limit_msg), connection_id)
                        await watchdog_client.send_log(f"Rate limit exceeded for user {user.username}", "WARNING", "rate_limiter")
                        continue

                    # Record message for rate limiting and activity tracking
                    manager.record_message(user.username, connection_id)

                    # Update global activity tracking
                    if hasattr(app.state, 'last_activity'):
                        app.state.last_activity = datetime.now()

                    print(f"💬 Received message from {user.username}: {user_message}")

                    # Forward user message to CLI terminal
                    await send_to_cli_terminal(user.username, user_message, "user")

                    # Echo user message back
                    user_msg = {
                        "type": "message",
                        "sender": "user",
                        "content": user_message,
                        "timestamp": datetime.now().isoformat()
                    }
                    await manager.send_personal_message(json.dumps(user_msg), connection_id)
                    print(f"📤 User message echoed back to {user.username}")

                    # Send typing indicator
                    typing_msg = {
                        "type": "typing",
                        "sender": "deeplica"
                    }
                    await manager.send_personal_message(json.dumps(typing_msg), connection_id)

                    # Get response from Deeplica
                    deeplica_response = await deeplica_client.send_message(user_message, user.username)

                    # Send Deeplica response
                    response_msg = {
                        "type": "message",
                        "sender": "deeplica",
                        "content": deeplica_response,
                        "timestamp": datetime.now().isoformat()
                    }
                    await manager.send_personal_message(json.dumps(response_msg), connection_id)
                    last_heartbeat = datetime.now()

            except asyncio.TimeoutError:
                # Send heartbeat if no activity
                if (datetime.now() - last_heartbeat).seconds > 25:
                    heartbeat_msg = {
                        "type": "heartbeat",
                        "timestamp": datetime.now().isoformat()
                    }
                    await manager.send_personal_message(json.dumps(heartbeat_msg), connection_id)
                    last_heartbeat = datetime.now()
                continue

    except WebSocketDisconnect:
        if connection_id:
            await manager.disconnect(connection_id)
        if user:
            print(f"🔌 WebSocket disconnected: {user.username}")
            await send_to_watchdog(f"WebSocket disconnected: {user.username}")
    except Exception as e:
        if connection_id:
            await manager.disconnect(connection_id)
        if user:
            print(f"❌ WebSocket error for {user.username}: {e}")
            await send_to_watchdog(f"WebSocket error for {user.username}: {e}", "ERROR")
        else:
            print(f"❌ WebSocket error: {e}")
            await send_to_watchdog(f"WebSocket error: {e}", "ERROR")

# ========================================
# CATCH-ALL ROUTE FOR SECURITY
# ========================================

@app.get("/{full_path:path}")
async def catch_all_invalid_routes(request: Request, full_path: str):
    """
    Catch-all route for any invalid/non-existing URLs.
    Redirects to unauthorized page for security consistency.
    """
    # Log the invalid URL attempt for security monitoring
    asyncio.create_task(send_to_watchdog(f"Invalid URL access: {request.url.path} from {request.client.host if request.client else 'unknown'}"))

    # Redirect to unauthorized page
    return RedirectResponse(url="/unauthorized", status_code=307)

def main():
    """Main function to run the web chat server"""
    print("🌐 Starting Deeplica Web Chat Server...")

    # Ensure port is free using centralized port manager
    try:
        server_port = ensure_service_port_free("WEB-CHAT", force=True)
        print(f"📍 Server will be available at: http://{get_localhost()}:{server_port}")
        print("👤 Demo credentials: admin/admin123 or demo/demo123")
        print("🚀 Browser will auto-launch when Deeplica is ready")
        print("🔇 Routine INFO messages suppressed (health checks, etc.)")
    except Exception as e:
        print(f"❌ Failed to secure port for Web Chat: {e}")
        print("💡 Will retry with different port...")
        # Try to get any available port instead of exiting
        try:
            import socket
            sock = socket.socket()
            sock.bind(('', 0))
            server_port = sock.getsockname()[1]
            sock.close()
            print(f"🔄 Using alternative port: {server_port}")
        except:
            server_port = get_service_port("web-chat")  # fallback to port manager
            print(f"🔄 Using fallback port: {server_port}")
        # DO NOT EXIT - microservices should never exit themselves
        # sys.exit(1)  # REMOVED

    uvicorn.run(
        "main:app",
        host=get_service_host("web-chat"),
        port=server_port,
        reload=True,
        log_level="warning",  # Suppress INFO messages
        access_log=False      # Disable access logging
    )

if __name__ == "__main__":
    try:
        # Set distinctive process name for easy identification
        try:
            import setproctitle
            setproctitle.setproctitle("DEEPLICA-WEB-CHAT")
            print("✅ Process name set to: DEEPLICA-WEB-CHAT")
        except ImportError:
            print("⚠️ setproctitle not available - process name unchanged")

        # Set terminal title
        service_name = os.getenv("SERVICE_NAME", "WEB-CHAT")
        print(f"\033]0;💬 {service_name}\007", end="")  # xterm title
        print(f"\033]2;💬 {service_name}\007", end="")  # window title
        print(f"\033]1;💬 {service_name}\007", end="")  # icon title

        print("\n" + "="*80)
        print(f"💬 {service_name} TERMINAL")
        print("="*80 + "\n")

        # Run the main function
        main()

    except KeyboardInterrupt:
        print("🛑 Web Chat shutdown requested by user")
    except Exception as e:
        print(f"💥 Web Chat crashed: {e}")
        import traceback
        print(f"Stack trace:\n{traceback.format_exc()}")
        print("🔄 MICROSERVICE WILL RESTART - NOT EXITING")
        # DO NOT EXIT - let orchestrator restart this microservice
        # sys.exit(1)  # REMOVED - microservices should never exit themselves
