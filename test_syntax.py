#!/usr/bin/env python3
"""
Test file to verify the syntax fix is working
"""

def get_service_port(service):
    return 8080

def test_syntax():
    config = {'api_port': 9000}
    
    # This is the corrected syntax that should work
    ngrok_port = config.get('api_port', get_service_port("ngrok-api"))
    response_url = f"http://localhost:{ngrok_port}/api/tunnels"
    
    print(f"✅ Syntax test passed!")
    print(f"URL: {response_url}")
    return True

if __name__ == "__main__":
    test_syntax()
