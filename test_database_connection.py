#!/usr/bin/env python3
"""
Test MongoDB connection to diagnose database issues
"""

import os
import asyncio
import sys
from dotenv import load_dotenv
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError

# Load environment variables
load_dotenv()

async def test_mongodb_connection():
    """Test MongoDB connection with detailed error reporting"""

    connection_string = os.getenv("MONGODB_CONNECTION_STRING")
    database_name = os.getenv("MONGODB_DATABASE")
    use_mock = os.getenv("USE_MOCK_DATABASE", "false").lower() == "true"
    force_mock = os.getenv("FORCE_MOCK_DATABASE", "false").lower() == "true"

    print("🔍 MONGODB CONNECTION TEST")
    print("=" * 50)
    print(f"📋 Database Name: {database_name}")
    print(f"🔗 Connection String: {connection_string[:50] if connection_string else 'None'}...")
    print(f"🎭 Mock Mode: {use_mock or force_mock}")
    print()

    # Check for real database mode
    if not use_mock and not force_mock and connection_string and connection_string.startswith("mongodb"):
        print("🏭 REAL DATABASE MODE DETECTED - Using MongoDB Atlas")
        print("🔗 Testing real MongoDB Atlas connection...")
    elif force_mock or use_mock or (connection_string and connection_string.startswith("mock://")):
        print("❌ MOCK MODE DETECTED - But real database is required!")
        print("💡 Please update .env to use real MongoDB Atlas connection")
        return False

    if not connection_string:
        print("❌ ERROR: MONGODB_CONNECTION_STRING not found in environment")
        return False

    if not database_name:
        print("❌ ERROR: MONGODB_DATABASE not found in environment")
        return False
    
    try:
        print("🔌 Attempting to connect to MongoDB...")
        
        # Create client with shorter timeout for testing
        client = AsyncIOMotorClient(
            connection_string,
            serverSelectionTimeoutMS=10000,  # 10 seconds
            connectTimeoutMS=10000,
            socketTimeoutMS=10000
        )
        
        # Test the connection
        print("⏳ Testing server selection...")
        await client.admin.command('ping')
        
        print("✅ Successfully connected to MongoDB!")
        
        # Test database access
        database = client[database_name]
        print(f"📊 Accessing database: {database_name}")
        
        # List collections to verify access
        collections = await database.list_collection_names()
        print(f"📁 Found {len(collections)} collections: {collections}")
        
        # Close connection
        client.close()
        print("🔌 Connection closed successfully")
        
        return True
        
    except ServerSelectionTimeoutError as e:
        print(f"❌ SERVER SELECTION TIMEOUT: {e}")
        print("💡 This usually means:")
        print("   - MongoDB Atlas cluster is paused/stopped")
        print("   - Network connectivity issues")
        print("   - Incorrect connection string")
        print("   - IP whitelist restrictions")
        return False
        
    except ConnectionFailure as e:
        print(f"❌ CONNECTION FAILURE: {e}")
        print("💡 This usually means:")
        print("   - Invalid credentials")
        print("   - Database server is down")
        print("   - Network firewall blocking connection")
        return False
        
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {e}")
        print(f"🔍 Error type: {type(e).__name__}")
        return False

async def test_dns_resolution():
    """Test DNS resolution for MongoDB Atlas"""
    import socket
    
    print("\n🌐 DNS RESOLUTION TEST")
    print("=" * 50)
    
    connection_string = os.getenv("MONGODB_CONNECTION_STRING")
    if not connection_string:
        print("❌ No connection string to test")
        return False
    
    try:
        # Extract hostname from connection string
        if "mongodb+srv://" in connection_string:
            # Extract hostname from SRV connection string
            parts = connection_string.split("@")
            if len(parts) > 1:
                hostname_part = parts[1].split("/")[0].split("?")[0]
                print(f"🔍 Testing DNS resolution for: {hostname_part}")
                
                # Test DNS resolution
                ip_addresses = socket.getaddrinfo(hostname_part, 27017)
                print(f"✅ DNS resolution successful!")
                print(f"📍 Resolved to {len(ip_addresses)} addresses")
                for addr in ip_addresses[:3]:  # Show first 3
                    print(f"   - {addr[4][0]}")
                return True
        else:
            print("⚠️ Not an SRV connection string, skipping DNS test")
            return True
            
    except socket.gaierror as e:
        print(f"❌ DNS RESOLUTION FAILED: {e}")
        print("💡 This means the hostname cannot be resolved")
        print("   - Check internet connection")
        print("   - Verify MongoDB Atlas cluster hostname")
        return False
    except Exception as e:
        print(f"❌ DNS TEST ERROR: {e}")
        return False

async def main():
    """Run all database tests"""
    print("🧪 DEEPLICA DATABASE DIAGNOSTICS")
    print("=" * 60)
    
    # Test DNS resolution first
    dns_ok = await test_dns_resolution()
    
    # Test MongoDB connection
    db_ok = await test_mongodb_connection()
    
    print("\n📊 TEST SUMMARY")
    print("=" * 50)
    print(f"🌐 DNS Resolution: {'✅ PASS' if dns_ok else '❌ FAIL'}")
    print(f"🔌 MongoDB Connection: {'✅ PASS' if db_ok else '❌ FAIL'}")
    
    if dns_ok and db_ok:
        print("\n🎉 All tests passed! Database should work correctly.")
        return True
    else:
        print("\n⚠️ Some tests failed. Check the errors above.")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test crashed: {e}")
        sys.exit(1)
