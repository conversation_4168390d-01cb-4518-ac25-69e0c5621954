#!/usr/bin/env python3
"""
🧪 TEST ALL IMPORTS AND BASIC FUNCTIONALITY
Verifies that all services can be imported and basic functionality works
"""

import sys
import os
import subprocess
import time

def test_import(module_name, working_dir=None, description=""):
    """Test importing a module"""
    print(f"🧪 Testing {description or module_name}...")
    
    if working_dir:
        cmd = f"cd {working_dir} && python3 -c 'import {module_name}; print(\"✅ {description or module_name} import successful\")'"
    else:
        cmd = f"python3 -c 'import {module_name}; print(\"✅ {description or module_name} import successful\")'"
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ {description or module_name} - OK")
            return True
        else:
            print(f"❌ {description or module_name} - FAILED")
            print(f"   Error: {result.stderr.strip()}")
            return False
    except subprocess.TimeoutExpired:
        print(f"❌ {description or module_name} - TIMEOUT")
        return False
    except Exception as e:
        print(f"❌ {description or module_name} - EXCEPTION: {e}")
        return False

def test_service_registration():
    """Test the service registration system"""
    print("\n🧪 Testing Service Registration System...")
    
    cmd = """python3 -c "
import sys
sys.path.append('.')
from shared.process_registry import register_service
try:
    registry = register_service(
        service_name='IMPORT-TEST',
        port=9999,
        terminal_name='Import Test',
        mandatory=False
    )
    print('✅ Service registration system works')
except Exception as e:
    print(f'❌ Service registration failed: {e}')
    sys.exit(1)
" """
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
        if result.returncode == 0 and "✅" in result.stdout:
            print("✅ Service Registration System - OK")
            return True
        else:
            print("❌ Service Registration System - FAILED")
            print(f"   Output: {result.stdout.strip()}")
            print(f"   Error: {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"❌ Service Registration System - EXCEPTION: {e}")
        return False

def main():
    """Run all import tests"""
    print("🧪 DEEPLICA IMPORT AND FUNCTIONALITY TEST")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 0
    
    # Test core modules
    test_cases = [
        # Core services
        ("stop_deeplica_service.main", None, "Stop Deeplica Service"),
        ("shared.process_registry", None, "Process Registry"),
        ("cli.main", None, "CLI Main"),
        ("cli.terminal_ui", None, "CLI Terminal UI"),
        ("orchestrator.main", None, "Orchestrator"),
        ("watchdog.main", None, "Watchdog"),
        
        # Services that need to run from their own directory
        ("app.main", "backend", "Backend API"),
        ("app.main", "dispatcher", "Dispatcher"),
        ("app.main", "agents/dialogue", "Dialogue Agent"),
        ("app.main", "agents/planner", "Planner Agent"),
        ("app.main", "agents/phone", "Phone Agent"),
    ]
    
    for module, working_dir, description in test_cases:
        total_tests += 1
        if test_import(module, working_dir, description):
            tests_passed += 1
    
    # Test service registration system
    total_tests += 1
    if test_service_registration():
        tests_passed += 1
    
    # Test orchestrator configuration
    print("\n🧪 Testing Orchestrator Configuration...")
    total_tests += 1
    try:
        cmd = """python3 -c "
import orchestrator.main
orch = orchestrator.main.BulletproofDeepplicaOrchestrator()
services = list(orch.services.keys())
expected = ['stop_service', 'backend', 'dispatcher', 'dialogue', 'planner', 'phone', 'cli', 'watchdog']
if all(svc in services for svc in expected):
    print('✅ All expected services configured')
else:
    print(f'❌ Missing services. Expected: {expected}, Got: {services}')
    exit(1)
" """
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
        if result.returncode == 0 and "✅" in result.stdout:
            print("✅ Orchestrator Configuration - OK")
            tests_passed += 1
        else:
            print("❌ Orchestrator Configuration - FAILED")
            print(f"   Output: {result.stdout.strip()}")
    except Exception as e:
        print(f"❌ Orchestrator Configuration - EXCEPTION: {e}")
    
    # Summary
    print(f"\n🎯 TEST RESULTS: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 ALL IMPORT TESTS PASSED!")
        print("✅ System is ready for startup")
        return True
    else:
        print("❌ Some import tests failed")
        print("🔧 Fix the errors above before starting the system")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
