<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .log {
            background: #f5f5f5;
            border: 1px solid #ddd;
            padding: 10px;
            height: 400px;
            overflow-y: auto;
            margin: 20px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        input {
            padding: 10px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 5px;
            width: 300px;
        }
    </style>
</head>
<body>
    <h1>🔗 WebSocket Connection Test</h1>
    <p>Session ID: <strong>{{ session_id }}</strong></p>
    <p>User: <strong>{{ user.username }}</strong></p>
    
    <div>
        <button onclick="connectWebSocket()">Connect</button>
        <button onclick="disconnectWebSocket()">Disconnect</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>
    
    <div>
        <input type="text" id="messageInput" placeholder="Type a message..." onkeypress="handleKeyPress(event)">
        <button onclick="sendMessage()">Send Message</button>
    </div>
    
    <div id="log" class="log">Ready to test WebSocket connection...\n</div>

    <script>
        const sessionId = '{{ session_id }}';
        const username = '{{ user.username }}';
        let ws = null;
        let isConnected = false;

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function connectWebSocket() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('❌ Already connected');
                return;
            }

            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws?session_id=${sessionId}`;

            log(`🔗 Connecting to: ${wsUrl}`);
            log(`📋 Session ID: ${sessionId}`);
            log(`👤 Username: ${username}`);

            try {
                ws = new WebSocket(wsUrl);

                ws.onopen = function(event) {
                    log('✅ WebSocket connected successfully!');
                    isConnected = true;
                };

                ws.onmessage = function(event) {
                    log(`📨 Received: ${event.data}`);
                    try {
                        const data = JSON.parse(event.data);
                        if (data.type === 'connection' && data.status === 'connected') {
                            log('🎉 Connection confirmed by server');
                        }
                    } catch (e) {
                        log(`⚠️ Could not parse message as JSON: ${e}`);
                    }
                };

                ws.onclose = function(event) {
                    log(`🔴 WebSocket closed. Code: ${event.code}, Reason: ${event.reason}`);
                    isConnected = false;
                };

                ws.onerror = function(error) {
                    log(`❌ WebSocket error: ${error}`);
                    isConnected = false;
                };

            } catch (error) {
                log(`❌ Error creating WebSocket: ${error}`);
            }
        }

        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                log('🔌 Disconnecting WebSocket...');
            } else {
                log('❌ No WebSocket to disconnect');
            }
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message) {
                log('❌ Please enter a message');
                return;
            }

            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('❌ WebSocket not connected');
                return;
            }

            const data = {
                type: 'message',
                content: message
            };

            try {
                ws.send(JSON.stringify(data));
                log(`📤 Sent: ${message}`);
                input.value = '';
            } catch (error) {
                log(`❌ Error sending message: ${error}`);
            }
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        function clearLog() {
            document.getElementById('log').textContent = 'Log cleared...\n';
        }

        // Auto-connect on page load
        log('🚀 Page loaded, auto-connecting...');
        connectWebSocket();
    </script>
</body>
</html>
