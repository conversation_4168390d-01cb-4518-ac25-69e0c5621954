"""
LLM service for AI agent interactions.
Provides abstraction layer over different LLM providers (Gemini, OpenAI).
"""

import os
import json
from typing import Dict, Any, Optional
from enum import Enum
import asyncio
from shared.unified_logging import get_backend_logger
from shared.api_manager import get_gemini_config, get_system_config

# Initialize unified logger
logger = get_backend_logger()

try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    logger.warning("Google Generative AI not available", module="BACKEND-API", routine="unknown")

# OpenAI removed - using Gemini only
OPENAI_AVAILABLE = False


class LLMProvider(str, Enum):
    """Supported LLM providers"""
    GEMINI = "gemini"
    # OPENAI removed - using Gemini only


class LLMService:
    """
    Unified LLM service supporting multiple providers.
    
    Provides a consistent interface for different LLM providers
    with automatic fallback and retry mechanisms.
    """
    
    def __init__(self):
        self.primary_provider = LLMProvider.GEMINI
        self.fallback_provider = None  # No fallback - using Gemini only

        # Get system config through API manager
        system_config = get_system_config()
        self.max_retries = int(system_config.get("llm_retry_attempts", 3))

        # Initialize providers
        self._init_gemini()
        self._init_openai()  # Still call but it does nothing
    
    def _init_gemini(self) -> None:
        """Initialize Gemini API"""
        if not GEMINI_AVAILABLE:
            logger.warning("🤖 Gemini API not available", module="BACKEND-API", routine="_init_gemini")
            return

        try:
            # Get Gemini config through API manager
            gemini_config = get_gemini_config()
            api_key = gemini_config["api_key"]
        except Exception as e:
            logger.warning(f"🤖 Gemini API configuration error: {e}", module="BACKEND-API", routine="_init_gemini")
            return

        try:
            genai.configure(api_key=api_key)

            # Try different model names in order of preference
            model_names = [
                'models/gemini-1.5-flash',
                'models/gemini-1.5-pro',
                'models/gemini-1.5-flash-latest',
                'models/gemini-1.5-pro-latest',
                'models/gemini-2.0-flash'
            ]

            for model_name in model_names:
                try:
                    self.gemini_model = genai.GenerativeModel(model_name)
                    self.gemini_model_name = model_name  # Store the model name
                    logger.info(f"✅ Gemini API initialized with model: {model_name}", module="BACKEND-API", routine="_init_gemini")
                    break
                except Exception as model_error:
                    logger.debug("Model {model_name} not available: {model_error}", module="BACKEND-API", routine="_init_gemini")
                    continue
            else:
                raise Exception("No available Gemini models found")
        except Exception as e:
            logger.warning("⚠️ Gemini initialization failed: {e}", module="BACKEND-API", routine="_init_gemini")
            logger.info("💡 This might be due to missing Google Cloud credentials for local development", module="BACKEND-API", routine="_init_gemini")
            logger.info("default login", module="BACKEND-API:_init_gemini", routine="SYSTEM | 💡 Try: gcloud auth application")
    
    def _init_openai(self) -> None:
        """OpenAI removed - using Gemini only"""
        logger.warning("🤖 OpenAI API not available", module="BACKEND-API", routine="_init_openai")
        self.openai_client = None
    
    async def generate_response(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        response_format: Optional[str] = None,
        provider: Optional[LLMProvider] = None
    ) -> Dict[str, Any]:
        """
        Generate a response using the specified or default LLM provider.
        
        Args:
            prompt: The main prompt for the LLM
            system_prompt: Optional system prompt for context
            temperature: Creativity level (0.0 to 1.0)
            max_tokens: Maximum tokens in response
            response_format: Expected format ("json", "text")
            provider: Specific provider to use
            
        Returns:
            Dictionary containing response and metadata
        """
        target_provider = provider or self.primary_provider
        
        for attempt in range(self.max_retries):
            try:
                logger.debug("🤖 Generating response with {target_provider} (attempt {attempt + 1})", module="BACKEND-API", routine="generate_response")
                
                if target_provider == LLMProvider.GEMINI:
                    result = await self._generate_gemini_response(
                        prompt, system_prompt, temperature, max_tokens, response_format
                    )
                elif target_provider == LLMProvider.OPENAI:
                    result = await self._generate_openai_response(
                        prompt, system_prompt, temperature, max_tokens, response_format
                    )
                else:
                    raise ValueError(f"Unsupported provider: {target_provider}")
                
                logger.info("✅ Generated response with {target_provider}", module="BACKEND-API", routine="generate_response")
                return result
                
            except Exception as e:
                logger.warning("⚠️ {target_provider} failed (attempt {attempt + 1}): {e}", module="BACKEND-API", routine="generate_response")
                
                # Try fallback provider on last attempt
                if attempt == self.max_retries - 1 and target_provider != self.fallback_provider:
                    logger.info("🔄 Falling back to {self.fallback_provider}", module="BACKEND-API", routine="generate_response")
                    target_provider = self.fallback_provider
                    continue
                
                # Wait before retry
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(2 ** attempt)  # Exponential backoff
        
        raise Exception(f"All LLM providers failed after {self.max_retries} attempts")
    
    async def _generate_gemini_response(
        self,
        prompt: str,
        system_prompt: Optional[str],
        temperature: float,
        max_tokens: Optional[int],
        response_format: Optional[str] = None
    ) -> Dict[str, Any]:
        """Generate response using Gemini"""
        if not hasattr(self, 'gemini_model'):
            raise Exception("Gemini not initialized")
        
        # Combine system prompt and user prompt
        full_prompt = prompt
        if system_prompt:
            full_prompt = f"{system_prompt}\n\n{prompt}"
        
        # Add JSON format instruction if needed
        if response_format == "json":
            full_prompt += "\n\nPlease respond with valid JSON only."
        
        # Configure generation parameters
        generation_config = genai.types.GenerationConfig(
            temperature=temperature,
            max_output_tokens=max_tokens or 2048,
        )
        
        # Generate response
        response = await asyncio.to_thread(
            self.gemini_model.generate_content,
            full_prompt,
            generation_config=generation_config
        )

        content = response.text
        logger.debug("Raw Gemini response: {content[:200]}...", module="main", routine="unknown")  # Log first 200 chars
        
        # Parse JSON if requested
        if response_format == "json":
            try:
                content = json.loads(content)
            except json.JSONDecodeError as e:
                logger.warning("⚠️ Failed to parse JSON response: {e}", module="BACKEND-API", routine="_generate_gemini_response")
                # Try to extract JSON from the response
                content = self._extract_json_from_text(content)

        # Handle usage metadata safely
        usage_info = {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}
        try:
            if hasattr(response, 'usage_metadata') and response.usage_metadata:
                usage_info = {
                    "prompt_tokens": getattr(response.usage_metadata, 'prompt_token_count', 0),
                    "completion_tokens": getattr(response.usage_metadata, 'candidates_token_count', 0),
                    "total_tokens": getattr(response.usage_metadata, 'total_token_count', 0)
                }
        except Exception as e:
            logger.debug("Could not extract usage metadata: {e}", module="BACKEND-API", routine="_generate_gemini_response")

        return {
            "content": content,
            "provider": "gemini",
            "model": getattr(self, 'gemini_model_name', 'gemini-unknown'),
            "usage": usage_info
        }
    
    async def _generate_openai_response(
        self,
        prompt: str,
        system_prompt: Optional[str],
        temperature: float,
        max_tokens: Optional[int],
        response_format: Optional[str] = None
    ) -> Dict[str, Any]:
        """Generate response using OpenAI"""
        if not OPENAI_AVAILABLE:
            raise Exception("OpenAI not available")
        
        # Prepare messages
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        # Configure parameters
        params = {
            "model": "gpt-3.5-turbo",
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens or 2048,
        }
        
        if response_format == "json":
            params["response_format"] = {"type": "json_object"}
            # Add JSON instruction to prompt
            if not any("json" in msg["content"].lower() for msg in messages):
                messages[-1]["content"] += "\n\nPlease respond with valid JSON."
        
        # Generate response
        response = await asyncio.to_thread(
            openai.ChatCompletion.create,
            **params
        )
        
        content = response.choices[0].message.content
        
        # Parse JSON if requested
        if response_format == "json":
            try:
                content = json.loads(content)
            except json.JSONDecodeError as e:
                logger.warning("⚠️ Failed to parse JSON response: {e}", module="BACKEND-API", routine="_generate_openai_response")
                content = self._extract_json_from_text(content)
        
        return {
            "content": content,
            "provider": "openai",
            "model": response.model,
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens
            }
        }
    
    def _extract_json_from_text(self, text: str) -> Dict[str, Any]:
        """Try to extract JSON from text response"""
        if not text or not text.strip():
            return {"response": "Empty response", "format_error": True}

        try:
            # First try to parse the entire text as JSON
            return json.loads(text.strip())
        except json.JSONDecodeError:
            pass

        try:
            # Look for JSON-like content between braces
            start = text.find('{')
            end = text.rfind('}') + 1

            if start != -1 and end > start:
                json_str = text[start:end]
                return json.loads(json_str)
        except json.JSONDecodeError:
            pass

        try:
            # Look for JSON in code blocks
            import re
            json_match = re.search(r'```(?:json)?\s*(\{.*?\})\s*```', text, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(1))
        except (json.JSONDecodeError, AttributeError):
            pass

        # Fallback: return text wrapped in a structure
        logger.warning("Could not extract JSON from response: {text[:100]}...", module="main", routine="unknown")
        return {"response": text, "format_error": True}
    

    

