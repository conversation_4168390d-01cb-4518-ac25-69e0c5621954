# 🚀 VS Code Separate Integrated Terminals Guide

## 📋 **Overview**

Each Deeplica microservice now runs in its **own separate VS Code integrated terminal tab**, giving you complete isolation and easy monitoring within the IDE.

## 🎯 **Launch Methods**

### **🚀 Method 1: Compound Launch (Recommended)**

1. Open VS Code Debug panel (`Cmd+Shift+D`)
2. Select `🚀 ALL SERVICES (Separate VS Code Terminals)`
3. Click the green play button

**✅ Result:** All 6 services launch simultaneously, each in its own VS Code terminal tab.

### **🚀 Method 2: Individual Service Launch**

Launch services one by one for debugging:

1. Open VS Code Debug panel (`Cmd+Shift+D`)
2. Select individual service:
   - `🌐 Backend API (Separate Terminal)`
   - `🎯 Dispatcher (Separate Terminal)`
   - `💬 Dialogue Agent (Separate Terminal)`
   - `🧠 Planner Agent (Separate Terminal)`
   - `📞 Phone Agent (Separate Terminal)`
   - `🖥️ CLI Terminal (Separate Terminal)`
3. Click the green play button

### **🚀 Method 3: Development Compounds**

- `🔧 Core Services Only` - Just Backend + Dispatcher
- `🤖 Agents Only` - Just the 3 agent services
- `🎬 Orchestrator` - Single orchestrator approach

## 📺 **Separate VS Code Terminal Tabs**

Each service gets its own integrated terminal tab in VS Code:

| **Service** | **Terminal Tab Name** | **Process Name** | **Port** |
|-------------|----------------------|------------------|----------|
| **🌐 Backend API** | `🌐 Backend API (Separate Terminal)` | `DEEPLICA-BACKEND-API` | 8000 |
| **🎯 Dispatcher** | `🎯 Dispatcher (Separate Terminal)` | `DEEPLICA-DISPATCHER` | 8001 |
| **💬 Dialogue Agent** | `💬 Dialogue Agent (Separate Terminal)` | `DEEPLICA-DIALOGUE-AGENT` | 8002 |
| **🧠 Planner Agent** | `🧠 Planner Agent (Separate Terminal)` | `DEEPLICA-PLANNER-AGENT` | 8003 |
| **📞 Phone Agent** | `📞 Phone Agent (Separate Terminal)` | `DEEPLICA-PHONE-AGENT` | 8004 |
| **🖥️ CLI Terminal** | `🖥️ CLI Terminal (Separate Terminal)` | `DEEPLICA-CLI-TERMINAL` | - |

## 🔄 **Startup Behavior**

### **Automatic Dependency Management:**
- **🌐 Backend API** starts immediately
- **🎯 Dispatcher, 💬 Dialogue, 🧠 Planner, 📞 Phone** wait for backend (`WAIT_FOR_BACKEND=true`)
- **🖥️ CLI Terminal** waits for backend

### **Each Service:**
- ✅ Gets its own VS Code integrated terminal tab
- ✅ Has distinctive process name for system monitoring
- ✅ Includes all error prevention measures (Phone Agent)
- ✅ Proper environment variables and Python path

## 🔍 **Monitoring Services**

### **VS Code Terminal Tabs:**
- Click between terminal tabs to view each service
- Each service has isolated logs and output
- Easy to spot errors or issues per service
- Terminal tabs are clearly labeled with service names

### **System Process Monitoring:**
```bash
# View all Deeplica services
ps aux | grep DEEPLICA | grep -v grep

# View specific service
ps aux | grep DEEPLICA-PHONE-AGENT | grep -v grep
```

### **Service Health Checks:**
```bash
# Backend API
curl http://localhost:8000/api/v1/health

# Phone Agent (with error prevention)
curl http://localhost:8004/health
```

## 🛑 **Stopping Services**

### **Method 1: VS Code Debug Panel**
1. Open Debug panel (`Cmd+Shift+D`)
2. Click the stop button (square icon) next to running configurations
3. Or use "Stop All" if you launched a compound

### **Method 2: Terminal Tab Controls**
1. Click on individual terminal tabs
2. Press `Ctrl+C` in each terminal to stop the service
3. Or close the terminal tab

### **Method 3: Command Line**
```bash
# Stop all services
./stop_all_services.sh

# Kill specific service
pkill DEEPLICA-PHONE-AGENT
```

## 🛡️ **Phone Agent Protection**

The Phone Agent terminal includes all error prevention measures:
- ✅ **Error calls disabled** (`DISABLE_ERROR_CALLS=true`)
- ✅ **Circuit breaker** - Automatic failure detection
- ✅ **Health checks** - System stability monitoring
- ✅ **Preventive abortion** - Stops calls during system issues
- ✅ **Silent error handling** - No more error calls to users

## 🔧 **Debugging Benefits**

### **Individual Terminal Isolation:**
- Each service has its own terminal tab
- No mixed output from multiple services
- Easy to identify which service has issues
- Clear separation of logs and errors

### **VS Code Integration:**
- Full VS Code debugging capabilities per service
- Breakpoints work in each service independently
- Integrated terminal with VS Code features
- Easy copy/paste and terminal management

### **Professional Development:**
- Named terminal tabs for easy identification
- Process names visible in system monitoring
- Individual start/stop control per service
- Clean development environment

## 🎯 **Usage Scenarios**

### **Full System Development:**
1. Use `🚀 ALL SERVICES (Separate VS Code Terminals)`
2. All services start in separate terminal tabs
3. Monitor each service independently

### **Backend Development:**
1. Use `🔧 Core Services Only`
2. Just Backend + Dispatcher in separate terminals
3. Focus on core functionality

### **Agent Development:**
1. Start Backend manually: `🌐 Backend API (Separate Terminal)`
2. Use `🤖 Agents Only` for agent services
3. Develop and test agents independently

### **Individual Service Debugging:**
1. Start services one by one
2. Debug specific service issues
3. Full control over startup sequence

## 🔍 **Troubleshooting**

### **If Services Don't Start:**
1. Check individual terminal tabs for error messages
2. Verify Backend API starts first and is ready
3. Check VS Code Debug Console for launch errors

### **If Terminal Tabs Don't Appear:**
1. Ensure VS Code is up to date
2. Check that debugpy extension is installed
3. Verify launch.json syntax is correct

### **If Services Can't Connect:**
1. Check Backend API terminal for startup errors
2. Verify MongoDB Atlas connection
3. Check port availability: `lsof -i :8000`

## 🎉 **Result**

You now have:
- ✅ **6 separate VS Code integrated terminal tabs** - one for each microservice
- ✅ **Named terminal tabs** - easy identification within VS Code
- ✅ **Distinctive process names** - clear system monitoring
- ✅ **Proper dependency management** - services wait for backend
- ✅ **Complete error prevention** - phone agent protection
- ✅ **Professional VS Code development environment**
- ✅ **Individual debugging capabilities** per service
- ✅ **Clean terminal isolation** - no mixed output

## 🚀 **Quick Start:**

1. **Open VS Code Debug Panel:** `Cmd+Shift+D`
2. **Select:** `🚀 ALL SERVICES (Separate VS Code Terminals)`
3. **Click:** Green play button
4. **Monitor:** Each service in its own terminal tab
5. **Debug:** Individual services as needed

**Each Deeplica microservice now runs in its own dedicated VS Code integrated terminal tab for maximum clarity and control within your IDE!** 🎉
