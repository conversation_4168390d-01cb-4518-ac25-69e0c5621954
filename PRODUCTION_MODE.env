
# =============================================================================
# ALL PORTS ARE MANAGED DYNAMICALLY BY shared/port_manager.py
# =============================================================================
# NO CONSTANT PORTS - ALL ports including Backend API are configurable
# External services will adapt to assigned ports through configuration
# =============================================================================

# DEEPLICA PRODUCTION MODE CONFIGURATION
# This file ensures DEEPLICA runs in production mode with real MongoDB Atlas

# PRODUCTION MODE - REAL MONGODB ATLAS DATABASE
USE_MOCK_DATABASE=false
FORCE_MOCK_DATABASE=false
USE_REAL_DATABASE=true
MONGODB_CONNECTION_STRING=mongodb+srv://deeplica-db:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0
MONGODB_DATABASE=deeplica-dev

# PRODUCTION MODE INDICATORS
PRODUCTION_MODE=true
ENVIRONMENT=production
DEBUG=false

# Required: Gemini API Key for LLM services
GEMINI_API_KEY=AIzaSyAJEa_NQQ6xFS3zQYfwg6BRzwS9ibr3Lwg

# ============================================================================
# PORT CONFIGURATION - CENTRALLY MANAGED
# ============================================================================
# IMPORTANT: All ports are managed by shared/port_manager.py
# 
# ALL PORTS ARE CONFIGURABLE (can be changed via admin interface):
#   - Backend API: configurable (default 8888)
#   - Twilio Echo Bot: configurable (default 8009)
#   - Webhook Server: configurable (default 8010)
#   - Ngrok API: configurable (default 4040)
#   - Ngrok Tunnel: configurable (default 8080)
#   - All service ports are dynamically assigned by port_manager.py
#
# To change configurable ports:
#   1. Use the admin interface in the web chat
#   2. Or modify shared/deeplica_port_settings.json
#   3. Restart all services for changes to take effect
# ============================================================================
# Service Port Configuration (Centrally Managed)
# NO HARDCODED PORTS - All ports are managed by the port manager
BACKEND_API_PORT=8888
DISPATCHER_PORT=8001
DIALOGUE_AGENT_PORT=8002
PLANNER_AGENT_PORT=8003
PHONE_AGENT_PORT=8004
WATCHDOG_PORT=8005
WEB_CHAT_PORT=8007
CLI_TERMINAL_PORT=8008
TWILIO_ECHO_BOT_PORT=${get_service_port("twilio-echo-bot")}
WEBHOOK_SERVER_PORT=${get_service_port("webhook-server")}

# External Service Ports
NGROK_API_PORT=${get_service_port("ngrok-api")}
NGROK_TUNNEL_PORT=${get_service_port("ngrok-tunnel")}

# Development/Test Ports
TEST_SERVER_PORT=8011
DEV_SERVER_PORT=8012
PROXY_SERVER_PORT=8013

# Host Configuration
DEFAULT_HOST=0.0.0.0
EXTERNAL_HOST=0.0.0.0
WEB_HOST=0.0.0.0

# Service Startup Configuration
WAIT_FOR_BACKEND=true
BACKEND_READY_TIMEOUT=120
SERVICE_STARTUP_TIMEOUT=30
HEALTH_CHECK_TIMEOUT=10

# Service URLs for local development (non-Docker)
DISPATCHER_URL=http://localhost:8001  # Port managed by port_manager.py
DIALOGUE_AGENT_URL=http://localhost:8002  # Port managed by port_manager.py
PLANNER_AGENT_URL=http://localhost:8003  # Port managed by port_manager.py
PHONE_AGENT_URL=http://localhost:8004  # Port managed by port_manager.py
BACKEND_URL=http://localhost:{get_service_port("backend")}  # CONSTANT PORT - never changes

# Server Configuration
HOST=0.0.0.0
PORT = get_service_port("backend")

# PRODUCTION MODE - External services with real credentials
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********
NGROK_API_KEY=your_ngrok_api_key
TWILIO_WEBHOOK_URL=https://your-domain.ngrok-free.app
