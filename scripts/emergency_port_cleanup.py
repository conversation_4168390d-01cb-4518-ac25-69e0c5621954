#!/usr/bin/env python3
"""
Emergency Port Cleanup Utility for DEEPLICA
Aggressively cleans up all DEEPLICA service ports when services are stuck
"""

import subprocess
import signal
import time
import os
import sys

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.port_manager import get_service_port

def get_all_deeplica_ports():
    """Get all DEEPLICA service ports"""
    services = ['backend', 'dispatcher', 'dialogue', 'planner', 'phone', 'watchdog', 'web-chat', 'cli']
    ports = {}
    
    for service in services:
        try:
            port = get_service_port(service)
            ports[service] = port
        except Exception as e:
            print(f"❌ Could not get port for {service}: {e}")
    
    return ports

def find_processes_on_port(port):
    """Find all processes using a specific port"""
    try:
        result = subprocess.run(['lsof', '-ti', f':{port}'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0 and result.stdout.strip():
            return [pid.strip() for pid in result.stdout.strip().split('\n') if pid.strip()]
        return []
    except Exception as e:
        print(f"❌ Error finding processes on port {port}: {e}")
        return []

def get_process_info(pid):
    """Get detailed process information"""
    try:
        result = subprocess.run(['ps', '-p', pid, '-o', 'pid,ppid,command'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            if len(lines) > 1:
                return lines[1]  # Skip header
        return f"PID {pid} (info unavailable)"
    except Exception:
        return f"PID {pid} (info unavailable)"

def kill_process_aggressive(pid):
    """Aggressively kill a process"""
    try:
        pid_int = int(pid)
        # Don't kill ourselves
        if pid_int == os.getpid():
            print(f"⚠️ Skipping self (PID {pid_int})")
            return False
        
        print(f"🔪 Killing PID {pid_int}")
        
        # Try SIGTERM first
        try:
            os.kill(pid_int, signal.SIGTERM)
            time.sleep(1)
        except ProcessLookupError:
            print(f"✅ PID {pid_int} already terminated")
            return True
        
        # Then SIGKILL if still running
        try:
            os.kill(pid_int, signal.SIGKILL)
            print(f"💀 Force killed PID {pid_int}")
            return True
        except ProcessLookupError:
            print(f"✅ PID {pid_int} terminated")
            return True
            
    except (ValueError, PermissionError) as e:
        print(f"❌ Could not kill PID {pid}: {e}")
        return False

def emergency_cleanup():
    """Perform emergency cleanup of all DEEPLICA ports"""
    print("🚨 DEEPLICA Emergency Port Cleanup")
    print("=" * 50)
    
    # Get all service ports
    ports = get_all_deeplica_ports()
    if not ports:
        print("❌ Could not get service ports")
        return False
    
    print(f"🎯 Found {len(ports)} DEEPLICA services:")
    for service, port in ports.items():
        print(f"   {service:15} -> port {port}")
    
    print("\n🔍 Scanning for processes using DEEPLICA ports...")
    
    all_processes = {}
    for service, port in ports.items():
        pids = find_processes_on_port(port)
        if pids:
            all_processes[port] = {'service': service, 'pids': pids}
            print(f"⚠️ Port {port} ({service}): {len(pids)} process(es)")
            for pid in pids:
                info = get_process_info(pid)
                print(f"   📋 {info}")
    
    if not all_processes:
        print("✅ No processes found using DEEPLICA ports")
        return True
    
    print(f"\n🔥 Found processes on {len(all_processes)} port(s)")
    
    # Ask for confirmation
    response = input("\n❓ Kill all processes using DEEPLICA ports? (y/N): ").strip().lower()
    if response != 'y':
        print("❌ Cleanup cancelled")
        return False
    
    print("\n💀 Killing processes...")
    
    success_count = 0
    total_count = 0
    
    for port, data in all_processes.items():
        service = data['service']
        pids = data['pids']
        
        print(f"\n🎯 Cleaning port {port} ({service}):")
        
        for pid in pids:
            total_count += 1
            if kill_process_aggressive(pid):
                success_count += 1
        
        # Wait for port to be released
        time.sleep(2)
        
        # Verify port is free
        remaining_pids = find_processes_on_port(port)
        if remaining_pids:
            print(f"⚠️ Port {port} still has {len(remaining_pids)} process(es)")
        else:
            print(f"✅ Port {port} is now free")
    
    print(f"\n📊 Cleanup Summary:")
    print(f"   Killed: {success_count}/{total_count} processes")
    
    if success_count == total_count:
        print("✅ Emergency cleanup completed successfully")
        return True
    else:
        print("⚠️ Some processes could not be killed")
        return False

if __name__ == "__main__":
    try:
        success = emergency_cleanup()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ Cleanup interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
