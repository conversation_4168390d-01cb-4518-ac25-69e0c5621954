<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Management Test</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.3);
            padding: 30px;
            border-radius: 15px;
            border: 2px solid #00d4ff;
        }
        .test-button {
            background: linear-gradient(45deg, #00d4ff, #0066ff);
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }
        .test-button:hover {
            background: linear-gradient(45deg, #0066ff, #00d4ff);
        }
        .log {
            background: rgba(0, 0, 0, 0.5);
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success { background: rgba(46, 204, 113, 0.3); }
        .status.warning { background: rgba(241, 196, 15, 0.3); }
        .status.error { background: rgba(231, 76, 60, 0.3); }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 DEEPLICA Session Management Test</h1>
        <p>This page tests the single session enforcement system.</p>
        
        <div class="status" id="status">Initializing...</div>
        
        <div>
            <button class="test-button" onclick="openChatWindow()">Open DeepChat Window</button>
            <button class="test-button" onclick="openAdminWindow()">Open Admin Window</button>
            <button class="test-button" onclick="openMultipleChat()">Open Multiple Chat Windows</button>
            <button class="test-button" onclick="clearSessions()">Clear All Sessions</button>
        </div>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        let logElement = document.getElementById('log');
        let statusElement = document.getElementById('status');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logElement.innerHTML += logEntry + '\n';
            logElement.scrollTop = logElement.scrollHeight;
            
            if (type === 'error') {
                statusElement.textContent = message;
                statusElement.className = 'status error';
            } else if (type === 'warning') {
                statusElement.textContent = message;
                statusElement.className = 'status warning';
            } else if (type === 'success') {
                statusElement.textContent = message;
                statusElement.className = 'status success';
            }
        }
        
        function openChatWindow() {
            log('🚀 Opening DeepChat window...');
            const chatWindow = window.open('http://localhost:8007/chat', '_blank');
            
            setTimeout(() => {
                if (chatWindow && !chatWindow.closed) {
                    log('✅ DeepChat window opened successfully', 'success');
                } else {
                    log('❌ Failed to open DeepChat window', 'error');
                }
            }, 1000);
        }
        
        function openAdminWindow() {
            log('🔧 Opening Admin window...');
            const adminWindow = window.open('http://localhost:8007/admin', '_blank');
            
            setTimeout(() => {
                if (adminWindow && !adminWindow.closed) {
                    log('✅ Admin window opened successfully', 'success');
                } else {
                    log('❌ Failed to open Admin window', 'error');
                }
            }, 1000);
        }
        
        function openMultipleChat() {
            log('🔥 Testing multiple chat windows...');
            
            for (let i = 1; i <= 3; i++) {
                setTimeout(() => {
                    log(`🚀 Opening chat window ${i}/3...`);
                    window.open('http://localhost:8007/chat', '_blank');
                }, i * 500);
            }
            
            setTimeout(() => {
                log('⚠️ Multiple windows opened - session management should handle conflicts', 'warning');
            }, 2000);
        }
        
        function clearSessions() {
            log('🧹 Clearing all session data...');
            
            // Clear chat sessions
            localStorage.removeItem('deeplica_chat_session');
            localStorage.removeItem('deeplica_tab_registry');
            
            // Clear admin sessions
            localStorage.removeItem('deeplica_admin_session');
            localStorage.removeItem('deeplica_admin_tab_registry');
            
            log('✅ All session data cleared', 'success');
        }
        
        function checkSessions() {
            const chatSession = localStorage.getItem('deeplica_chat_session');
            const adminSession = localStorage.getItem('deeplica_admin_session');
            const chatTabs = localStorage.getItem('deeplica_tab_registry');
            const adminTabs = localStorage.getItem('deeplica_admin_tab_registry');
            
            let status = '📊 Session Status: ';
            
            if (chatSession) {
                status += `Chat: ${chatSession.substring(0, 20)}... `;
            }
            
            if (adminSession) {
                status += `Admin: ${adminSession.substring(0, 20)}... `;
            }
            
            if (chatTabs) {
                const tabs = JSON.parse(chatTabs);
                status += `(${tabs.length} chat tabs) `;
            }
            
            if (adminTabs) {
                const tabs = JSON.parse(adminTabs);
                status += `(${tabs.length} admin tabs) `;
            }
            
            if (!chatSession && !adminSession) {
                status += 'No active sessions';
            }
            
            log(status);
        }
        
        // Initialize
        log('🔒 Session Management Test initialized');
        log('💡 Use the buttons above to test session enforcement');
        checkSessions();
        
        // Check sessions periodically
        setInterval(checkSessions, 5000);
    </script>
</body>
</html>
