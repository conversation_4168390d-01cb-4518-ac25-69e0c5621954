#!/usr/bin/env python3
"""
End-to-end test for the scenario:
1. Call +972547000430 and ask for a number
2. Call +972547000430 to tell that number

This test simulates the complete flow through planner, dispatcher, and phone agent.
"""

import asyncio
import sys
import os
import json
from unittest.mock import AsyncMock, MagicMock

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from agents.planner.app.agent import PlannerAgent
from dispatcher.app.orchestrator import MissionOrchestrator
from dispatcher.app.parameter_resolver import ParameterResolver
from shared_models import Task, TaskStatus, Mission, MissionStatus


async def test_end_to_end_scenario():
    """Test the complete scenario end-to-end"""
    
    print("🚀 Testing end-to-end scenario: call for number, then call to tell number")
    
    # Step 1: Test planner creates correct tasks
    print("\n📋 Step 1: Testing planner task creation...")
    
    # Mock LLM service for planner
    mock_llm_service = AsyncMock()
    
    # Expected planner response for our scenario
    planner_response = {
        "tasks": [
            {
                "task_id": "call_ask_number",
                "task_type": "phone_call",
                "description": "Call to ask for a number",
                "prompt": "Call the provided number to ask for a number",
                "phone_number": "+972547000430",
                "question": "Hi, can you give me a number?",
                "context": "Need to get a number from the contact for use in a follow-up call.",
                "contact_name": "Contact",
                "parent_tasks": [],
                "child_tasks": ["call_tell_number"]
            },
            {
                "task_id": "call_tell_number",
                "task_type": "phone_call",
                "description": "Call to tell the number received",
                "prompt": "Call to inform about the number received from previous call",
                "phone_number": "+972547000430",
                "question": "The number is {{task:MISSION_ID_call_ask_number:extracted_answer}}",
                "context": "Tell the contact the number that was received from the previous call.",
                "contact_name": "Contact",
                "parent_tasks": ["call_ask_number"],
                "child_tasks": []
            }
        ]
    }
    
    mock_llm_service.generate_response.return_value = {"content": planner_response}
    
    # Create planner agent
    planner = PlannerAgent(mock_llm_service)
    
    # Test planner task creation
    user_input = "call +972547000430 and ask for a number, then call +972547000430 to tell that number"
    mission_id = "test_mission_123"

    # Create mock request for planner
    from shared_models.communication import TaskRequest, MissionContext

    mission_context = MissionContext(
        mission_id=mission_id,
        user_input=user_input,
        description="Test mission for number exchange",
        status="in_progress",
        created_at="2025-01-01T10:00:00Z"
    )

    mock_request = TaskRequest(
        task_id="planner_task_123",
        mission_id=mission_id,
        task_type="planning",
        task_data={"description": "Plan the mission"},
        callback_url="http://test/callback",
        mission_context=mission_context
    )

    try:
        # Execute planning task
        result = await planner._execute_planning_task(mock_request)
        task_suggestions = result.get("suggested_tasks", [])
        print(f"✅ Planner created {len(task_suggestions)} tasks")
        
        # Verify task structure
        if len(task_suggestions) >= 2:
            first_task = task_suggestions[0]
            second_task = task_suggestions[1]

            print(f"📞 First task: {first_task.task_id} - {first_task.description}")
            print(f"📞 Second task: {second_task.task_id} - {second_task.description}")

            # Check if second task has placeholder
            second_task_context = second_task.context
            has_placeholder = "{{task:" in json.dumps(second_task_context)
            print(f"🔍 Second task has placeholder: {has_placeholder}")

            if has_placeholder:
                print(f"📋 Second task context: {second_task_context}")
        else:
            print(f"❌ Expected 2 tasks, got {len(task_suggestions)}")
            return False
        
    except Exception as e:
        print(f"❌ Planner failed: {e}")
        return False
    
    # Step 2: Test parameter resolution
    print("\n🔧 Step 2: Testing parameter resolution...")
    
    # Mock database service
    mock_db_service = AsyncMock()
    
    # Mock first task completion result
    completed_first_task = Task(
        task_id="test_mission_123_call_ask_number",
        mission_id="test_mission_123",
        task_type="phone_call",
        description="Call to ask for a number",
        prompt="Call to ask for a number",
        status=TaskStatus.DONE,
        result={
            "call_status": "completed",
            "duration_seconds": 30,
            "conversation_transcript": [
                {"timestamp": "2025-01-01T10:00:00", "speaker": "agent", "text": "Hi, can you give me a number?"},
                {"timestamp": "2025-01-01T10:00:15", "speaker": "human", "text": "Sure, the number is 42"}
            ],
            "extracted_answer": "42",
            "question_answered": True,
            "twilio_call_sid": "CA123456789",
            "disconnect_reason": "completed-via-hangup",
            "error_message": None
        }
    )
    
    mock_db_service.get_task.return_value = completed_first_task
    
    # Create parameter resolver
    resolver = ParameterResolver(mock_db_service)
    
    # Test resolving second task parameters
    second_task_data = {
        "phone_number": "+972547000430",
        "contact_name": "Contact",
        "context": "Tell the contact the number that was received from the previous call.",
        "question": "The number is {{task:test_mission_123_call_ask_number:extracted_answer}}",
        "language": "en",
        "max_duration_minutes": 5
    }
    
    try:
        resolved_data = await resolver.resolve_task_parameters(second_task_data, "test_mission_123")
        print(f"✅ Parameter resolution successful")
        print(f"📋 Original question: {second_task_data['question']}")
        print(f"📋 Resolved question: {resolved_data['question']}")
        
        expected_question = "The number is 42"
        if resolved_data["question"] == expected_question:
            print("✅ SUCCESS: Placeholder resolved correctly!")
        else:
            print(f"❌ FAILED: Expected '{expected_question}', got '{resolved_data['question']}'")
            return False
            
    except Exception as e:
        print(f"❌ Parameter resolution failed: {e}")
        return False
    
    # Step 3: Test the issue might be in task data structure
    print("\n🔍 Step 3: Analyzing task data structure...")
    
    # Check how the planner formats the task context
    if task_suggestions:
        second_task = task_suggestions[1]
        print(f"📋 Second task full context: {second_task.context}")
        
        # Check if the placeholder is in the right place
        if isinstance(second_task.context, dict):
            question_field = second_task.context.get("question", "")
            print(f"📋 Question field in context: {question_field}")
            
            if "{{task:" in question_field:
                print("✅ Placeholder found in question field")
            else:
                print("❌ No placeholder in question field - this might be the issue!")
                
                # Check other fields
                for key, value in second_task.context.items():
                    if isinstance(value, str) and "{{task:" in value:
                        print(f"🔍 Found placeholder in field '{key}': {value}")
    
    print("\n🎉 End-to-end test completed!")
    return True


async def main():
    """Run the test"""
    print("🧪 Starting end-to-end scenario test...")
    
    success = await test_end_to_end_scenario()
    
    if success:
        print("\n✅ Test completed successfully!")
        return 0
    else:
        print("\n❌ Test failed!")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
