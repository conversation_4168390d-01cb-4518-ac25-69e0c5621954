#!/usr/bin/env python3
"""
🔧 Fix ALL Backend Port References
Replace ALL hardcoded 8888 references with port manager calls.
Backend API port is now FULLY MANAGED by port manager.
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Dict, <PERSON><PERSON>

def fix_backend_port_references():
    """Fix all hardcoded 8888 references to use port manager"""
    
    print("🔧 Fixing ALL Backend API port references to use port manager...")
    
    # Directories to scan
    scan_dirs = [
        "backend", "dispatcher", "agents", "watchdog", "web_chat", "cli", 
        "orchestrator", "shared", "scripts", "tests", "twilio-echo-bot"
    ]
    
    # Files to scan in root
    root_files = [
        "start_all_services.py", "startup_orchestrator.py", "start_ngrok.py",
        "webhook_server.py", "validate_ports.py", "test_port_manager_integration.py"
    ]
    
    # Backend port replacement patterns
    backend_replacements = [
        # Direct port assignments
        (r'PORT.*=.*8888', 'PORT = get_service_port("backend")'),
        (r'port.*=.*8888', 'port = get_service_port("backend")'),
        
        # URL patterns
        (r'http://localhost:8888/', 'http://localhost:{get_service_port("backend")}/'),
        (r'"http://localhost:8888"', 'f"http://localhost:{get_service_port(\'backend\')}"'),
        (r'localhost:8888', 'localhost:{get_service_port("backend")}'),
        
        # Environment variable patterns
        (r'BACKEND_URL=http://localhost:8888', 'BACKEND_URL=http://localhost:{get_service_port("backend")}'),
        (r'BACKEND_API_PORT=8888', 'BACKEND_API_PORT={get_service_port("backend")}'),
        
        # F-string patterns
        (r'f"http://localhost:8888/', 'f"http://localhost:{get_service_port(\'backend\')}/'),
        
        # Health check patterns
        (r'http://localhost:8888/health', 'http://localhost:{get_service_port("backend")}/health'),
        
        # Ngrok patterns that reference backend
        (r'NGROK_PORT.*8888', 'NGROK_PORT = get_service_port("backend")'),
        
        # Comments that mention constant 8888
        (r'# Backend API.*8888.*CONSTANT', '# Backend API port - managed by port_manager.py'),
        (r'# CONSTANT.*8888', '# Backend API port - managed by port_manager.py'),
        
        # Launch.json patterns
        (r'"PORT": "8888"', '"PORT": "{get_service_port(\'backend\')}"'),
        
        # Shell script patterns
        (r'PORT=8888', 'PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'backend\'))")'),
        
        # Watchdog fallback patterns
        (r'"port": 8888,', '"port": get_service_port("backend"),'),
        (r'backend.*8888', 'backend port managed by port_manager.py'),
    ]
    
    issues_fixed = 0
    files_modified = 0
    
    # Process directories
    for scan_dir in scan_dirs:
        if Path(scan_dir).exists():
            for root, dirs, files in os.walk(scan_dir):
                for file in files:
                    if file.endswith(('.py', '.sh', '.json', '.env')):
                        file_path = Path(root) / file
                        if fix_file_backend_ports(file_path, backend_replacements):
                            files_modified += 1
    
    # Process root files
    for file_name in root_files:
        file_path = Path(file_name)
        if file_path.exists():
            if fix_file_backend_ports(file_path, backend_replacements):
                files_modified += 1
    
    # Process shell scripts
    for file_path in Path('.').glob('*.sh'):
        if fix_file_backend_ports(file_path, backend_replacements):
            files_modified += 1
    
    # Process env files
    for file_path in Path('.').glob('*.env'):
        if fix_file_backend_ports(file_path, backend_replacements):
            files_modified += 1
    
    print(f"✅ Fixed backend port references in {files_modified} files")
    return files_modified

def fix_file_backend_ports(file_path: Path, replacements: List[Tuple[str, str]]) -> bool:
    """Fix backend port references in a single file"""
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        original_content = content
        modified = False
        
        # Check if file needs port manager import
        needs_import = False
        if file_path.suffix == '.py' and 'get_service_port' not in content:
            # Check if file contains 8888 references
            if '8888' in content:
                needs_import = True
        
        # Add import if needed
        if needs_import and 'from shared.port_manager import' not in content:
            lines = content.split('\n')
            
            # Find the best place to add the import
            import_pos = 0
            for i, line in enumerate(lines):
                if line.startswith('import ') or line.startswith('from '):
                    import_pos = i + 1
                elif line.strip() == '' and import_pos > 0:
                    break
            
            if import_pos == 0:
                import_pos = 0
            
            lines.insert(import_pos, 'from shared.port_manager import get_service_port')
            content = '\n'.join(lines)
            modified = True
            print(f"✅ {file_path}: Added port manager import")
        
        # Apply replacements
        for pattern, replacement in replacements:
            if re.search(pattern, content):
                content = re.sub(pattern, replacement, content)
                modified = True
                print(f"✅ {file_path}: Fixed pattern {pattern[:50]}...")
        
        # Special handling for specific file types
        if file_path.name == 'launch.json':
            # Fix launch.json specific patterns
            content = re.sub(r'"8888"', '"${get_service_port(\'backend\')}"', content)
            if '"8888"' not in original_content or content != original_content:
                modified = True
                print(f"✅ {file_path}: Fixed launch.json backend port")
        
        # Write back if modified
        if modified:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
    
    except Exception as e:
        print(f"⚠️ Error processing {file_path}: {e}")
    
    return False

def fix_environment_files():
    """Fix environment files to use dynamic backend port"""
    
    env_files = ["DEMO_MODE.env", "PRODUCTION_MODE.env", ".env.bak"]
    
    for env_file in env_files:
        file_path = Path(env_file)
        if file_path.exists():
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                
                original_content = content
                
                # Replace backend port references
                content = re.sub(r'BACKEND_API_PORT=8888', 'BACKEND_API_PORT=8888  # Managed by port_manager.py', content)
                content = re.sub(r'BACKEND_URL=http://localhost:8888', 'BACKEND_URL=http://localhost:8888  # Port managed by port_manager.py', content)
                
                # Add note about dynamic port management
                if 'ALL PORTS ARE MANAGED DYNAMICALLY' not in content:
                    header = """
# =============================================================================
# ALL PORTS ARE MANAGED DYNAMICALLY BY shared/port_manager.py
# =============================================================================
# NO CONSTANT PORTS - ALL ports including Backend API are configurable
# External services will adapt to assigned ports through configuration
# =============================================================================

"""
                    content = header + content
                
                if content != original_content:
                    with open(file_path, 'w') as f:
                        f.write(content)
                    print(f"✅ {env_file}: Updated to reflect dynamic port management")
            
            except Exception as e:
                print(f"⚠️ Error processing {env_file}: {e}")

if __name__ == "__main__":
    print("🔧 Starting comprehensive backend port fixes...")
    files_modified = fix_backend_port_references()
    fix_environment_files()
    print(f"✅ Backend port fixes completed! Modified {files_modified} files")
    print("🔄 ALL ports including Backend API are now managed by port_manager.py")
