#!/usr/bin/env python3
"""
🧪 Test Port Cache and Anti-Spam Logging
Verify that port caching reduces port manager calls and logging spam is eliminated.
"""

import time
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_port_cache():
    """Test port caching functionality"""
    print("🧪 Testing Port Cache System")
    print("=" * 60)
    
    from shared.port_cache import get_port_cache, get_cached_port, preload_all_ports
    
    # Test individual service cache
    print("\n1️⃣ Testing individual service cache:")
    cache = get_port_cache("test-service")
    
    # Time the first call (should hit port manager)
    start_time = time.time()
    port1 = cache.get_port("backend")
    first_call_time = time.time() - start_time
    print(f"  📊 First call (cache miss): {first_call_time:.4f}s -> Port {port1}")
    
    # Time the second call (should hit cache)
    start_time = time.time()
    port2 = cache.get_port("backend")
    second_call_time = time.time() - start_time
    print(f"  ⚡ Second call (cache hit): {second_call_time:.4f}s -> Port {port2}")
    
    assert port1 == port2, "Cached port should match original"
    assert second_call_time < first_call_time, "Cache should be faster"
    print(f"  ✅ Cache speedup: {first_call_time/second_call_time:.1f}x faster")
    
    # Test cache statistics
    stats = cache.get_cache_stats()
    print(f"  📈 Cache stats: {stats}")
    
    print("\n2️⃣ Testing global cache functions:")
    # Test global cache functions
    start_time = time.time()
    port3 = get_cached_port("dispatcher", "test-service")
    global_call_time = time.time() - start_time
    print(f"  🌐 Global cache call: {global_call_time:.4f}s -> Port {port3}")
    
    print("\n3️⃣ Testing preload functionality:")
    # Test preloading
    start_time = time.time()
    preload_all_ports("test-service")
    preload_time = time.time() - start_time
    print(f"  🚀 Preload time: {preload_time:.4f}s")
    
    # Verify preloaded ports are cached
    cached_stats = cache.get_cache_stats()
    print(f"  📊 After preload: {cached_stats}")
    
    print("  ✅ Port cache system working correctly!")


def test_anti_spam_logging():
    """Test anti-spam logging functionality"""
    print("\n🧪 Testing Anti-Spam Logging")
    print("=" * 60)
    
    from shared.unified_logging import should_log_status_change
    
    print("\n1️⃣ Testing repetitive status suppression:")
    
    # Test repetitive status - should only log first time
    for i in range(5):
        should_log = should_log_status_change("TEST-SERVICE", "health_check", "healthy")
        status = "LOGGED" if should_log else "SUPPRESSED"
        print(f"  Attempt {i+1}: {status}")
    
    print("\n2️⃣ Testing status change detection:")
    
    # Test status change - should log when status changes
    statuses = ["healthy", "healthy", "unhealthy", "unhealthy", "healthy"]
    for i, status in enumerate(statuses):
        should_log = should_log_status_change("TEST-SERVICE", "health_check", status)
        log_status = "LOGGED" if should_log else "SUPPRESSED"
        print(f"  Status '{status}': {log_status}")
    
    print("  ✅ Anti-spam logging working correctly!")


def test_port_manager_anti_spam():
    """Test port manager anti-spam functionality"""
    print("\n🧪 Testing Port Manager Anti-Spam")
    print("=" * 60)
    
    from shared.port_manager import DeepLicaPortManager
    
    print("\n1️⃣ Testing repetitive port manager messages:")
    
    # Create multiple port managers to test message suppression
    for i in range(3):
        print(f"\n  Creating port manager #{i+1}:")
        pm = DeepLicaPortManager(f"test-service-{i}")
        # This should only print the backup message once due to anti-spam
    
    print("  ✅ Port manager anti-spam working correctly!")


def test_integration():
    """Test integration of all improvements"""
    print("\n🧪 Testing Integration")
    print("=" * 60)
    
    from shared.port_cache import get_port_cache
    from shared.unified_logging import get_backend_logger, should_log_status_change
    
    print("\n1️⃣ Testing service with port cache and anti-spam logging:")
    
    # Simulate a service using both systems
    cache = get_port_cache("integration-test")
    logger = get_backend_logger()
    
    # Simulate repetitive health checks with caching
    for i in range(3):
        # Get port (should be cached after first call)
        backend_port = cache.get_port("backend")
        
        # Log health status (should be suppressed after first)
        if should_log_status_change("INTEGRATION-TEST", "health_check", "healthy"):
            print(f"  Health check {i+1}: Service healthy on port {backend_port}")
        else:
            print(f"  Health check {i+1}: (suppressed - no status change)")
    
    print("  ✅ Integration test successful!")


def main():
    """Run all tests"""
    print("🚀 DEEPLICA Port Cache and Anti-Spam Test Suite")
    print("Testing efficient port management and spam elimination")
    print("=" * 80)
    
    try:
        test_port_cache()
        test_anti_spam_logging()
        test_port_manager_anti_spam()
        test_integration()
        
        print("\n" + "=" * 80)
        print("🎉 All Tests Passed!")
        print("\n📊 Summary:")
        print("✅ Port caching reduces port manager calls")
        print("✅ Anti-spam logging suppresses repetitive messages")
        print("✅ Port manager messages are properly throttled")
        print("✅ Integration works seamlessly")
        print("\n🎯 Benefits:")
        print("• Faster service startup (cached ports)")
        print("• Cleaner console output (no spam)")
        print("• Reduced system load (fewer port manager calls)")
        print("• Better debugging experience")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
