#!/usr/bin/env python3
"""
🧪 TWILIO TEST CALL SCRIPT
Direct test of Twilio integration with ngrok
"""

import os
import sys
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv
load_dotenv()

def test_twilio_call():
    """Test Twilio call to +************"""
    try:
        from twilio.rest import Client
        
        # Get Twilio credentials from environment
        account_sid = os.getenv('TWILIO_ACCOUNT_SID')
        auth_token = os.getenv('TWILIO_AUTH_TOKEN')
        twilio_phone = os.getenv('TWILIO_PHONE_NUMBER')
        
        if not all([account_sid, auth_token, twilio_phone]):
            print("❌ Missing Twilio credentials in .env file")
            return False
        
        print("📞 TWILIO TEST CALL")
        print("=" * 50)
        print(f"From: {twilio_phone}")
        print(f"To: +************")
        print(f"Webhook: https://326177bd5f13.ngrok-free.app/webhook/voice")
        print()
        
        # Initialize Twilio client
        client = Client(account_sid, auth_token)
        
        # Create the call
        print("🚀 Initiating call...")
        call = client.calls.create(
            to='+************',
            from_=twilio_phone,
            url='https://326177bd5f13.ngrok-free.app/webhook/voice',
            timeout=30,
            method='POST'
        )
        
        print(f"✅ Call initiated successfully!")
        print(f"📋 Call SID: {call.sid}")
        print(f"📋 Call Status: {call.status}")
        print()
        print("📱 The call should now be connecting to +************")
        print("🎤 When answered, wait 3 seconds then say 'Eran' 5 times")
        print()
        
        # Monitor call status
        print("📊 Monitoring call status...")
        for i in range(60):  # Monitor for 60 seconds
            call = client.calls(call.sid).fetch()
            print(f"⏱️  {i+1:2d}s - Status: {call.status}")
            
            if call.status in ['completed', 'failed', 'busy', 'no-answer']:
                break
                
            time.sleep(1)
        
        # Final status
        call = client.calls(call.sid).fetch()
        print()
        print(f"🏁 Final call status: {call.status}")
        print(f"📞 Duration: {call.duration} seconds" if call.duration else "📞 Duration: N/A")
        
        return True
        
    except Exception as e:
        print(f"❌ Error making Twilio call: {e}")
        return False

def create_test_webhook():
    """Create a simple test webhook response"""
    webhook_response = '''<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="alice" language="en-US">
        Hello Eran! This is a test call from DEEPLICA. 
        Please wait 3 seconds and then say your name Eran 5 times for testing.
        Testing... one... two... three... Now please say Eran 5 times.
    </Say>
    <Pause length="10"/>
    <Say voice="alice" language="en-US">
        Thank you for testing DEEPLICA. This call will now end. Goodbye!
    </Say>
</Response>'''
    
    print("📝 Test webhook TwiML response:")
    print(webhook_response)
    print()
    return webhook_response

if __name__ == "__main__":
    print("🧪 DEEPLICA TWILIO TEST")
    print("=" * 50)
    
    # Show test webhook
    create_test_webhook()
    
    # Make test call
    success = test_twilio_call()
    
    if success:
        print("✅ Twilio test completed successfully!")
    else:
        print("❌ Twilio test failed!")
