#!/usr/bin/env python3
"""
🚀 Deeplica Separate Terminal Launcher

This script launches each Deeplica microservice in its own separate terminal window.
Each service gets its own dedicated terminal for easy monitoring and management.
"""

import subprocess
import sys
import time
import os
import platform
from pathlib import Path
from shared.port_manager import get_service_port

# Get the project root directory
PROJECT_ROOT = Path(__file__).parent.absolute()

# Service configurations
SERVICES = [
    {
        "name": "🌐 DEEPLICA-BACKEND-API",
        "command": ["python3", "-m", "app.main"],
        "cwd": PROJECT_ROOT / "backend",
        "port": get_service_port("backend"),
        "wait_for": None,  # Backend starts first
        "env": {"PYTHONPATH": str(PROJECT_ROOT), "PORT": str(get_service_port("backend"))}
    },
    {
        "name": "🎯 DEEPLICA-DISPATCHER",
        "command": ["python3", "-m", "app.main"],
        "cwd": PROJECT_ROOT / "dispatcher",
        "port": get_service_port("dispatcher"),
        "wait_for": "backend",  # Wait for backend
        "env": {"PYTHONPATH": str(PROJECT_ROOT), "PORT": str(get_service_port("dispatcher")), "WAIT_FOR_BACKEND": "true"}
    },
    {
        "name": "💬 DEEPLICA-DIALOGUE-AGENT",
        "command": ["python3", "-m", "app.main"],
        "cwd": PROJECT_ROOT / "agents" / "dialogue",
        "port": get_service_port("dialogue"),
        "wait_for": "backend",  # Wait for backend
        "env": {"PYTHONPATH": str(PROJECT_ROOT), "PORT": str(get_service_port("dialogue")), "WAIT_FOR_BACKEND": "true"}
    },
    {
        "name": "🧠 DEEPLICA-PLANNER-AGENT",
        "command": ["python3", "-m", "app.main"],
        "cwd": PROJECT_ROOT / "agents" / "planner",
        "port": get_service_port("planner"),
        "wait_for": "backend",  # Wait for backend
        "env": {"PYTHONPATH": str(PROJECT_ROOT), "PORT": str(get_service_port("planner")), "WAIT_FOR_BACKEND": "true"}
    },
    {
        "name": "📞 DEEPLICA-PHONE-AGENT",
        "command": ["python3", "-m", "app.main"],
        "cwd": PROJECT_ROOT / "agents" / "phone",
        "port": get_service_port("phone"),
        "wait_for": "backend",  # Wait for backend
        "env": {"PYTHONPATH": str(PROJECT_ROOT), "PORT": str(get_service_port("phone")), "WAIT_FOR_BACKEND": "true"}
    },
    {
        "name": "🖥️ DEEPLICA-CLI-TERMINAL",
        "command": ["python3", "terminal_ui.py"],
        "cwd": PROJECT_ROOT / "cli",
        "port": None,
        "wait_for": "all_services",  # Wait for all services
        "env": {"PYTHONPATH": str(PROJECT_ROOT), "WAIT_FOR_BACKEND": "true"}
    }
]

def get_terminal_command(service_name, command, cwd, env):
    """Get the appropriate terminal command for the current platform"""
    system = platform.system()
    
    # Prepare environment variables
    env_vars = os.environ.copy()
    env_vars.update(env)
    env_string = " ".join([f'{k}="{v}"' for k, v in env.items()])
    
    # Prepare the full command
    full_command = " ".join(command)
    cd_and_run = f"cd '{cwd}' && {env_string} {full_command}"
    
    if system == "Darwin":  # macOS
        # Use Terminal.app to open a new window
        applescript = f'''
        tell application "Terminal"
            do script "echo '🚀 Starting {service_name}' && {cd_and_run}"
            set custom title of front window to "{service_name}"
        end tell
        '''
        return ["osascript", "-e", applescript]
    
    elif system == "Linux":
        # Try different terminal emulators
        terminals = ["gnome-terminal", "konsole", "xterm", "terminator"]
        for terminal in terminals:
            if subprocess.run(["which", terminal], capture_output=True).returncode == 0:
                if terminal == "gnome-terminal":
                    return [terminal, "--title", service_name, "--", "bash", "-c", 
                           f"echo '🚀 Starting {service_name}' && {cd_and_run}; exec bash"]
                elif terminal == "konsole":
                    return [terminal, "--title", service_name, "-e", "bash", "-c",
                           f"echo '🚀 Starting {service_name}' && {cd_and_run}; exec bash"]
                elif terminal == "xterm":
                    return [terminal, "-title", service_name, "-e", "bash", "-c",
                           f"echo '🚀 Starting {service_name}' && {cd_and_run}; exec bash"]
                elif terminal == "terminator":
                    return [terminal, "--title", service_name, "-e", 
                           f"bash -c \"echo '🚀 Starting {service_name}' && {cd_and_run}; exec bash\""]
        
        # Fallback to xterm
        return ["xterm", "-title", service_name, "-e", "bash", "-c",
               f"echo '🚀 Starting {service_name}' && {cd_and_run}; exec bash"]
    
    elif system == "Windows":
        # Use Windows Terminal or cmd
        return ["cmd", "/c", "start", "cmd", "/k", 
               f"title {service_name} && echo 🚀 Starting {service_name} && cd /d {cwd} && {full_command}"]
    
    else:
        raise RuntimeError(f"Unsupported platform: {system}")

def wait_for_backend():
    """Wait for backend to be ready"""
    import requests
    import time
    
    # [SYSTEM:wait_for_backend] SUCCESS | ⏳ Waiting for Backend API to be ready...
    for attempt in range(60):  # Wait up to 2 minutes
        try:
            from shared.port_manager import get_service_port
            backend_port = get_service_port("backend")
            response = requests.get(f"http://localhost:{backend_port}/api/v1/health", timeout=2)
            if response.status_code == 200:
                # [SYSTEM:wait_for_backend] SUCCESS | ✅ Backend API is ready!
                return True
        except:
            pass
        time.sleep(2)
    
    # [SYSTEM:wait_for_backend] ERROR | ❌ Backend API failed to start within 2 minutes
    return False

def wait_for_all_services():
    """Wait for all core services to be ready"""
    import requests
    
    services_to_check = [
        ("Backend API", f"http://localhost:{get_service_port('backend')}/health"),
        ("Dispatcher", f"http://localhost:{get_service_port('dispatcher')}/health"),
        ("Dialogue Agent", f"http://localhost:{get_service_port('dialogue')}/health"),
        ("Planner Agent", f"http://localhost:{get_service_port('planner')}/health"),
        ("Phone Agent", f"http://localhost:{get_service_port('phone')}/health"),
    ]
    
    # [SYSTEM:wait_for_all_services] SUCCESS | ⏳ Waiting for all services to be ready...
    for name, url in services_to_check:
        for attempt in range(30):  # Wait up to 1 minute per service
            try:
                response = requests.get(url, timeout=2)
                if response.status_code == 200:
                    # [SYSTEM:wait_for_all_services] SUCCESS | ✅ {name} is ready!
                    break
            except:
                pass
            time.sleep(2)
        else:
            # [SYSTEM:wait_for_all_services] SUCCESS | ⚠️ {name} not ready, continuing anyway...
            pass
    
    # [SYSTEM:wait_for_all_services] SUCCESS | ✅ All services check completed!

def main():
    """Launch all services in separate terminals"""
    # [SYSTEM:main] SYSTEM | 🚀 Launching Deeplica microservices in separate terminals...
    # [SYSTEM:main] SYSTEM | 📁 Project root: {PROJECT_ROOT}
    
    launched_processes = []
    
    try:
        for service in SERVICES:
            print(f"\n🚀 Launching {service['name']}...")
            
            # Handle wait conditions
            if service['wait_for'] == 'backend':
                if not wait_for_backend():
                    # [SYSTEM:main] SUCCESS | ❌ Skipping {service['name']} due to backend not ready
                    continue
                time.sleep(2)  # Small delay between services
            elif service['wait_for'] == 'all_services':
                wait_for_all_services()
                time.sleep(3)  # Longer delay before CLI
            
            # Get terminal command
            terminal_cmd = get_terminal_command(
                service['name'], 
                service['command'], 
                service['cwd'], 
                service['env']
            )
            
            # Launch in separate terminal
            process = subprocess.Popen(terminal_cmd)
            launched_processes.append((service['name'], process))
            
            # [SYSTEM:main] SYSTEM | ✅ {service['name']} launched in separate terminal (PID: {process.pid")
            
            # Small delay between launches
            time.sleep(1)
        
        print(f"\n🎉 All {len(launched_processes)} services launched in separate terminals!")
        print("\n📋 Launched services:")
        for name, process in launched_processes:
            # [SYSTEM:main] SYSTEM |   ✅ {name} (PID: {process.pid")
        
            pass
        print("\n🔍 To monitor services:")
        # [SYSTEM:main] SYSTEM |   • Check each terminal window for service logs
        # [SYSTEM:main] SYSTEM |   • Use 'ps aux | grep DEEPLICA' to see running processes
        # [SYSTEM:main] SYSTEM |   • Each service has its own dedicated terminal
        
        print("\n👋 Press Ctrl+C to exit this launcher (services will continue running)")
        
        # Keep launcher running
        while True:
            time.sleep(10)
            
    except KeyboardInterrupt:
        print("\n👋 Launcher stopped. Services continue running in their terminals.")
        # [SYSTEM:main] SYSTEM | 💡 To stop services, close their individual terminal windows or use 'pkill DEEPLICA-*'

if __name__ == "__main__":
    main()
