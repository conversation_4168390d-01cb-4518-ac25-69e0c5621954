"""
Communication models for inter-service communication.
Used for HTTP requests and responses between microservices.
"""

from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field


class MissionContext(BaseModel):
    """Complete mission context provided to all agents"""
    mission_id: str = Field(..., description="Mission identifier")
    user_input: str = Field(..., description="Original user request")
    title: str = Field(default="", description="Mission title")
    description: str = Field(default="", description="Mission description")
    priority: str = Field(default="normal", description="Mission priority")
    status: str = Field(..., description="Current mission status")
    created_at: str = Field(..., description="Mission creation timestamp")
    started_at: Optional[str] = Field(None, description="Mission start timestamp")
    progress: Dict[str, Any] = Field(default_factory=dict, description="Mission progress data")


class TaskSuggestion(BaseModel):
    """Suggestion for a new task to be created"""
    task_id: str = Field(..., description="Suggested task identifier")
    task_type: str = Field(..., description="Type of task to create")
    description: str = Field(..., description="Task description")
    prompt: str = Field(..., description="Task prompt for execution")
    parent_tasks: List[str] = Field(default_factory=list, description="Parent task dependencies")
    child_tasks: List[str] = Field(default_factory=list, description="Child task relationships")
    assigned_agent: Optional[str] = Field(None, description="Preferred agent for execution")
    context: Dict[str, Any] = Field(default_factory=dict, description="Task-specific context")
    has_placeholders: bool = Field(default=False, description="Whether task contains parameter placeholders")


class TaskRequest(BaseModel):
    """Request from dispatcher to agent services to execute a task"""
    task_id: str = Field(..., description="Unique task identifier")
    mission_id: str = Field(..., description="Mission identifier")
    task_type: str = Field(..., description="Type of task to execute")
    task_data: Dict[str, Any] = Field(..., description="Task-specific data")
    mission_context: MissionContext = Field(..., description="Complete mission context")
    callback_url: str = Field(..., description="URL for completion callback")
    context: Dict[str, Any] = Field(default_factory=dict, description="Additional context")


class TaskResponse(BaseModel):
    """Response sent back from agent services to dispatcher"""
    task_id: str = Field(..., description="Task identifier")
    mission_id: str = Field(..., description="Mission identifier")
    status: str = Field(..., description="Completion status")
    result: Dict[str, Any] = Field(default_factory=dict, description="Task result")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    suggested_tasks: Optional[List[TaskSuggestion]] = Field(None, description="New tasks suggested by agent")
