#!/usr/bin/env python3
"""
🧹 DEEPLICA DUPLICATE PROCESS CLEANER
Safely removes duplicate DEEPLICA processes
"""

import os
import sys
import psutil
import signal
import time
from pathlib import Path
from typing import Dict, List, Set
from dataclasses import dataclass

# Add project root to path
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

from shared.unified_logging import get_logger

logger = get_logger("DUPLICATE-CLEANER")

@dataclass
class ProcessInfo:
    """Process information"""
    pid: int
    name: str
    service_name: str
    cmdline: List[str]
    create_time: float
    is_current: bool = False

class DuplicateCleaner:
    """Clean duplicate DEEPLICA processes"""
    
    def __init__(self):
        self.services = {
            "WATCHDOG": ["DEEPLICA-WATCHDOG", "watchdog/main.py", "watchdog.main"],
            "BACKEND-API": ["DEEPLICA-BACKEND-API", "backend.app.main", "backend/app/main.py"],
            "DISPATCHER": ["DEEPLICA-DISPATCHER", "dispatcher.app.main", "dispatcher/app/main.py"],
            "DIALOGUE-AGENT": ["DEEPLICA-DIALOGUE-AGENT", "agents.dialogue.app.main", "agents/dialogue/app/main.py"],
            "PLANNER-AGENT": ["DEEPLICA-PLANNER-AGENT", "agents.planner.app.main", "agents/planner/app/main.py"],
            "PHONE-AGENT": ["DEEPLICA-PHONE-AGENT", "agents.phone.app.main", "agents/phone/app/main.py"],
            "CLI-TERMINAL": ["DEEPLICA-CLI-TERMINAL", "cli/main.py", "cli.main"],
            "WEB-CHAT": ["DEEPLICA-WEB-CHAT", "web_chat/main.py", "web_chat.main"]
        }
        self.current_pid = os.getpid()
        
    def find_duplicate_processes(self) -> Dict[str, List[ProcessInfo]]:
        """Find all duplicate DEEPLICA processes"""
        duplicates = {}
        
        for service_name, identifiers in self.services.items():
            processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
                try:
                    cmdline_str = ' '.join(proc.info['cmdline'] or [])
                    
                    # Check if this process matches any service identifier
                    for identifier in identifiers:
                        if identifier in cmdline_str:
                            process_info = ProcessInfo(
                                pid=proc.info['pid'],
                                name=proc.info['name'],
                                service_name=service_name,
                                cmdline=proc.info['cmdline'] or [],
                                create_time=proc.info['create_time'],
                                is_current=(proc.info['pid'] == self.current_pid)
                            )
                            processes.append(process_info)
                            break
                            
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
            
            # Only include services with duplicates (more than 1 instance)
            if len(processes) > 1:
                # Sort by creation time (oldest first)
                processes.sort(key=lambda p: p.create_time)
                duplicates[service_name] = processes
                
        return duplicates
    
    def kill_process_safely(self, pid: int, service_name: str) -> bool:
        """Safely kill a process"""
        try:
            process = psutil.Process(pid)
            
            # Double-check it's a DEEPLICA process
            cmdline_str = ' '.join(process.cmdline())
            is_deeplica = any(identifier in cmdline_str for identifier in self.services.get(service_name, []))
            
            if not is_deeplica:
                logger.warning(f"⚠️ Process {pid} doesn't appear to be {service_name}, skipping")
                return False
            
            logger.info(f"🔪 Terminating {service_name} process {pid}")
            
            # Try graceful termination first
            process.terminate()
            
            # Wait up to 5 seconds for graceful shutdown
            try:
                process.wait(timeout=5)
                logger.info(f"✅ Process {pid} terminated gracefully")
                return True
            except psutil.TimeoutExpired:
                # Force kill if graceful termination failed
                logger.warning(f"⚡ Force killing process {pid}")
                process.kill()
                process.wait(timeout=2)
                logger.info(f"✅ Process {pid} force killed")
                return True
                
        except psutil.NoSuchProcess:
            logger.info(f"✅ Process {pid} already terminated")
            return True
        except psutil.AccessDenied:
            logger.error(f"❌ Access denied killing process {pid}")
            return False
        except Exception as e:
            logger.error(f"❌ Error killing process {pid}: {e}")
            return False
    
    def clean_duplicates(self, dry_run: bool = False) -> Dict[str, int]:
        """Clean duplicate processes"""
        duplicates = self.find_duplicate_processes()
        results = {}
        
        if not duplicates:
            logger.info("✅ No duplicate processes found")
            return results
        
        logger.info(f"🔍 Found duplicates in {len(duplicates)} services")
        
        for service_name, processes in duplicates.items():
            logger.info(f"\n🔧 {service_name}: {len(processes)} instances")
            
            # Keep the newest process, kill older ones
            processes_to_kill = processes[:-1]  # All except the last (newest)
            keep_process = processes[-1]
            
            logger.info(f"   ✅ Keeping: PID {keep_process.pid} (newest)")
            
            killed_count = 0
            for proc in processes_to_kill:
                if proc.is_current:
                    logger.warning(f"   🛡️ Skipping current process: PID {proc.pid}")
                    continue
                    
                logger.info(f"   🔪 Will kill: PID {proc.pid} (older)")
                
                if not dry_run:
                    if self.kill_process_safely(proc.pid, service_name):
                        killed_count += 1
                        time.sleep(0.5)  # Brief pause between kills
                else:
                    killed_count += 1
            
            results[service_name] = killed_count
            logger.info(f"   📊 {service_name}: {'Would kill' if dry_run else 'Killed'} {killed_count} duplicates")
        
        return results
    
    def generate_report(self) -> str:
        """Generate duplicate process report"""
        duplicates = self.find_duplicate_processes()
        
        report = []
        report.append("🧹 DEEPLICA DUPLICATE PROCESS REPORT")
        report.append("=" * 60)
        
        if not duplicates:
            report.append("✅ No duplicate processes found")
            return "\n".join(report)
        
        total_duplicates = sum(len(procs) - 1 for procs in duplicates.values())
        report.append(f"🔍 Found {total_duplicates} duplicate processes across {len(duplicates)} services")
        report.append("")
        
        for service_name, processes in duplicates.items():
            report.append(f"🔧 {service_name}: {len(processes)} instances")
            
            for i, proc in enumerate(processes):
                status = "NEWEST" if i == len(processes) - 1 else "DUPLICATE"
                current_marker = " (CURRENT)" if proc.is_current else ""
                create_time = time.strftime("%H:%M:%S", time.localtime(proc.create_time))
                
                report.append(f"   {i+1}. PID {proc.pid} - {status}{current_marker} - Started: {create_time}")
            
            report.append("")
        
        return "\n".join(report)

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Clean DEEPLICA duplicate processes")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be done without actually killing processes")
    parser.add_argument("--report-only", action="store_true", help="Only show report, don't clean")
    
    args = parser.parse_args()
    
    cleaner = DuplicateCleaner()
    
    print("🧹 DEEPLICA DUPLICATE PROCESS CLEANER")
    print("=" * 50)
    
    # Always show report first
    report = cleaner.generate_report()
    print(report)
    
    if args.report_only:
        return
    
    if args.dry_run:
        print("\n🧪 DRY RUN MODE - No processes will be killed")
    
    # Clean duplicates
    print(f"\n{'🧪 DRY RUN:' if args.dry_run else '🧹 CLEANING:'} Removing duplicate processes...")
    results = cleaner.clean_duplicates(dry_run=args.dry_run)
    
    if results:
        total_killed = sum(results.values())
        print(f"\n📊 SUMMARY: {'Would kill' if args.dry_run else 'Killed'} {total_killed} duplicate processes")
        for service, count in results.items():
            print(f"   {service}: {count}")
    else:
        print("\n✅ No duplicates to clean")

if __name__ == "__main__":
    main()
