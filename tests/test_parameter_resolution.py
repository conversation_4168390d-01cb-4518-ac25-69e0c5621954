"""
Test Parameter Resolution System

Tests the parameter resolution functionality for task placeholders.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock
import sys
import os

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from dispatcher.app.parameter_resolver import ParameterResolver
from shared_models import Task, TaskStatus


class TestParameterResolver:
    """Test cases for parameter resolution"""
    
    @pytest.fixture
    def mock_db_service(self):
        """Mock database service"""
        db_service = AsyncMock()
        return db_service
    
    @pytest.fixture
    def resolver(self, mock_db_service):
        """Parameter resolver instance"""
        return ParameterResolver(mock_db_service)
    
    def test_placeholder_detection(self, resolver):
        """Test placeholder detection in data"""

        # Test data with placeholders (using full task IDs)
        data_with_placeholders = {
            "phone_number": "{{task:mission_123_call_eran:result.extracted_answer}}",
            "message": "Call {{task:mission_123_get_info:result.contact_name}} about the meeting"
        }

        # Test data without placeholders
        data_without_placeholders = {
            "phone_number": "+1234567890",
            "message": "Call John about the meeting"
        }

        assert resolver.has_placeholders(data_with_placeholders) == True
        assert resolver.has_placeholders(data_without_placeholders) == False
    
    def test_find_placeholders(self, resolver):
        """Test finding placeholders in nested data"""

        data = {
            "phone_number": "{{task:mission_123_call_eran:result.extracted_answer}}",
            "context": {
                "message": "Tell {{task:mission_123_get_name:result.name}} about the event"
            },
            "list_field": [
                "normal_value",
                "{{task:mission_123_task1:result.value}}"
            ]
        }

        placeholders = resolver._find_placeholders(data)

        assert len(placeholders) == 3

        # Check first placeholder
        assert placeholders[0]["task_id"] == "mission_123_call_eran"
        assert placeholders[0]["result_path"] == "result.extracted_answer"
        assert placeholders[0]["data_path"] == "phone_number"

        # Check nested placeholder
        assert placeholders[1]["task_id"] == "mission_123_get_name"
        assert placeholders[1]["result_path"] == "result.name"
        assert placeholders[1]["data_path"] == "context.message"

        # Check list placeholder
        assert placeholders[2]["task_id"] == "mission_123_task1"
        assert placeholders[2]["result_path"] == "result.value"
        assert placeholders[2]["data_path"] == "list_field[1]"
    
    @pytest.mark.asyncio
    async def test_resolve_simple_placeholder(self, resolver, mock_db_service):
        """Test resolving a simple placeholder"""

        # Mock task with result (using full task ID)
        mock_task = Task(
            task_id="mission_1_call_eran",
            mission_id="mission_1",
            task_type="phone_call",
            description="Call Eran",
            prompt="Call Eran",
            status=TaskStatus.DONE,
            result={
                "extracted_answer": "+972541234567",
                "question_answered": True
            }
        )

        mock_db_service.get_task.return_value = mock_task

        # Test data with placeholder (using full task ID)
        task_data = {
            "phone_number": "{{task:mission_1_call_eran:result.extracted_answer}}",
            "contact_name": "Oren"
        }

        # Resolve parameters
        resolved_data = await resolver.resolve_task_parameters(task_data, "mission_1")

        # Verify resolution
        assert resolved_data["phone_number"] == "+972541234567"
        assert resolved_data["contact_name"] == "Oren"  # Unchanged

        # Verify database call
        mock_db_service.get_task.assert_called_once_with("mission_1_call_eran")
    
    @pytest.mark.asyncio
    async def test_resolve_multiple_placeholders(self, resolver, mock_db_service):
        """Test resolving multiple placeholders"""

        # Mock tasks with results (using full task IDs)
        task1 = Task(
            task_id="mission_1_get_phone",
            mission_id="mission_1",
            task_type="phone_call",
            description="Get phone",
            prompt="Get phone",
            status=TaskStatus.DONE,
            result={"extracted_answer": "+1234567890"}
        )

        task2 = Task(
            task_id="mission_1_get_name",
            mission_id="mission_1",
            task_type="phone_call",
            description="Get name",
            prompt="Get name",
            status=TaskStatus.DONE,
            result={"extracted_answer": "John Doe"}
        )

        # Configure mock to return different tasks
        def get_task_side_effect(task_id):
            if task_id == "mission_1_get_phone":
                return task1
            elif task_id == "mission_1_get_name":
                return task2
            return None

        mock_db_service.get_task.side_effect = get_task_side_effect

        # Test data with multiple placeholders (using full task IDs)
        task_data = {
            "phone_number": "{{task:mission_1_get_phone:result.extracted_answer}}",
            "message": "Call {{task:mission_1_get_name:result.extracted_answer}} about the meeting"
        }

        # Resolve parameters
        resolved_data = await resolver.resolve_task_parameters(task_data, "mission_1")

        # Verify resolution
        assert resolved_data["phone_number"] == "+1234567890"
        assert resolved_data["message"] == "Call John Doe about the meeting"
    
    @pytest.mark.asyncio
    async def test_resolve_placeholder_error_cases(self, resolver, mock_db_service):
        """Test error cases in placeholder resolution"""

        task_data = {
            "phone_number": "{{task:mission_1_nonexistent_task:result.value}}"
        }

        # Test task not found
        mock_db_service.get_task.return_value = None

        with pytest.raises(ValueError, match="Referenced task not found"):
            await resolver.resolve_task_parameters(task_data, "mission_1")

        # Test task without result
        incomplete_task = Task(
            task_id="mission_1_incomplete_task",
            mission_id="mission_1",
            task_type="phone_call",
            description="Incomplete",
            prompt="Incomplete",
            status=TaskStatus.IN_PROGRESS,
            result=None
        )

        mock_db_service.get_task.return_value = incomplete_task

        with pytest.raises(ValueError, match="has no result"):
            await resolver.resolve_task_parameters(task_data, "mission_1")
    
    def test_extract_value_by_path(self, resolver):
        """Test extracting values using dot notation paths"""
        
        data = {
            "result": {
                "extracted_answer": "test_value",
                "nested": {
                    "deep_value": "deep_test"
                }
            },
            "simple_field": "simple_value"
        }
        
        # Test simple path
        assert resolver._extract_value_by_path(data, "simple_field") == "simple_value"
        
        # Test nested path
        assert resolver._extract_value_by_path(data, "result.extracted_answer") == "test_value"
        
        # Test deep nested path
        assert resolver._extract_value_by_path(data, "result.nested.deep_value") == "deep_test"
        
        # Test invalid path
        with pytest.raises(KeyError):
            resolver._extract_value_by_path(data, "nonexistent.path")


if __name__ == "__main__":
    pytest.main([__file__])
