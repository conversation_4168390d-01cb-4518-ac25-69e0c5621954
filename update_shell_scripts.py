#!/usr/bin/env python3
"""
🔧 Update Shell Scripts Script
Updates all shell scripts to use port manager references and fix hardcoded ports.
"""

import os
import re
from pathlib import Path
from typing import Dict, List

def update_shell_scripts():
    """Update all shell scripts to reference port manager"""
    
    print("🔧 Updating shell scripts to use centralized port manager...")
    
    # Shell scripts to update
    shell_scripts = [
        "start_all_services.sh",
        "start_orchestrated_system.sh", 
        "stop_all_services.sh",
        "start_ngrok.sh",
        "get_ngrok_url.sh",
        "scripts/start.sh"
    ]
    
    # Port replacement patterns for shell scripts
    shell_replacements = [
        # Add comments explaining port management
        (r'# Start Backend \(port 8000\)', 
         '# Start Backend (port 8888 - CONSTANT)'),
        (r'# Start Dispatcher \(port 8001\)', 
         '# Start Dispatcher (port 8001 - managed by port_manager.py)'),
        (r'# Start Dialogue Agent \(port 8002\)', 
         '# Start Dialogue Agent (port 8002 - managed by port_manager.py)'),
        (r'# Start Planner Agent \(port 8003\)', 
         '# Start Planner Agent (port 8003 - managed by port_manager.py)'),
        (r'# Start Phone Agent \(port 8004\)', 
         '# Start Phone Agent (port 8004 - managed by port_manager.py)'),
        
        # Fix incorrect port assignments in start_all_services.sh
        (r'PORT=8003 python3 -m app\.main.*# Phone Agent restart', 
         'PORT=8004 python3 -m app.main &  # Phone Agent restart - correct port'),
        (r'wait_for_service "http://localhost:8003/health" "Phone Agent \(restarted\)"', 
         'wait_for_service "http://localhost:8004/health" "Phone Agent (restarted)"'),
        
        # Fix health check URLs that have wrong ports
        (r'echo "Planner:.*http://localhost:8002/health', 
         'echo "Planner:   $(curl -s http://localhost:8003/health'),
        (r'echo "Phone:.*http://localhost:8003/health', 
         'echo "Phone:     $(curl -s http://localhost:8004/health'),
        (r'echo "Dialogue:.*http://localhost:8004/health', 
         'echo "Dialogue:  $(curl -s http://localhost:8002/health'),
        
        # Fix service URL display
        (r'echo "   Planner:.*http://localhost:8002"', 
         'echo "   Planner:    http://localhost:8003"'),
        (r'echo "   Phone:.*http://localhost:8003"', 
         'echo "   Phone:      http://localhost:8004"'),
        (r'echo "   Dialogue:.*http://localhost:8004"', 
         'echo "   Dialogue:   http://localhost:8002"'),
        
        # Add port manager comments to PORT assignments
        (r'PORT=8001 python3', 
         'PORT=8001 python3  # Port managed by port_manager.py'),
        (r'PORT=8002 python3', 
         'PORT=8002 python3  # Port managed by port_manager.py'),
        (r'PORT=8003 python3', 
         'PORT=8003 python3  # Port managed by port_manager.py'),
        (r'PORT=8004 python3', 
         'PORT=8004 python3  # Port managed by port_manager.py'),
        
        # Add comments to ngrok commands
        (r'ngrok http 8004', 
         'ngrok http 8004  # Phone Agent port - managed by port_manager.py'),
        
        # Add comments to health check URLs
        (r'http://localhost:8888/health', 
         'http://localhost:8888/health  # Backend API - CONSTANT port'),
        (r'http://localhost:8001/health', 
         'http://localhost:8001/health  # Dispatcher - managed by port_manager.py'),
        (r'http://localhost:8002/health', 
         'http://localhost:8002/health  # Dialogue Agent - managed by port_manager.py'),
        (r'http://localhost:8003/health', 
         'http://localhost:8003/health  # Planner Agent - managed by port_manager.py'),
        (r'http://localhost:8004/health', 
         'http://localhost:8004/health  # Phone Agent - managed by port_manager.py'),
    ]
    
    # Process each shell script
    for script_path in shell_scripts:
        full_path = Path(script_path)
        if not full_path.exists():
            print(f"⚠️ File not found: {script_path}")
            continue
            
        try:
            with open(full_path, 'r') as f:
                content = f.read()
            
            original_content = content
            modified = False
            
            # Add header comment if not present
            if "PORT MANAGEMENT" not in content and script_path.endswith('.sh'):
                header_comment = """#!/bin/bash
# =============================================================================
# DEEPLICA SERVICE STARTUP SCRIPT
# =============================================================================
# NOTE: All ports are managed by shared/port_manager.py
# - Backend API: 8888 (CONSTANT - never changes)
# - Other services: configurable via port manager
# =============================================================================

"""
                if content.startswith('#!/bin/bash'):
                    # Replace the shebang line with our header
                    lines = content.split('\n')
                    lines[0] = header_comment.strip()
                    content = '\n'.join(lines)
                    modified = True
                    print(f"✅ {script_path}: Added port management header")
            
            # Apply shell script replacements
            for pattern, replacement in shell_replacements:
                if re.search(pattern, content):
                    content = re.sub(pattern, replacement, content)
                    modified = True
                    print(f"✅ {script_path}: Updated pattern {pattern[:50]}...")
            
            # Write back if modified
            if modified:
                with open(full_path, 'w') as f:
                    f.write(content)
                print(f"✅ {script_path}: Updated successfully")
            else:
                print(f"ℹ️ {script_path}: No changes needed")
                
        except Exception as e:
            print(f"❌ Error processing {script_path}: {e}")

def create_port_validation_script():
    """Create a script to validate all port assignments"""
    
    validation_script = """#!/usr/bin/env python3
\"\"\"
🔍 Port Validation Script
Validates that all services are using correct ports from the port manager.
\"\"\"

import sys
import os

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.port_manager import get_service_port, get_all_ports, is_constant_port

def validate_ports():
    \"\"\"Validate all port assignments\"\"\"
    print("🔍 Validating DEEPLICA port assignments...")
    print("=" * 60)
    
    all_ports = get_all_ports()
    
    print("\\n📋 Current Port Assignments:")
    for service, port in all_ports.items():
        constant = "CONSTANT" if is_constant_port(service.lower()) else "configurable"
        print(f"  {service:20} {port:5} ({constant})")
    
    print("\\n✅ Port validation completed!")
    print("\\n💡 To change configurable ports:")
    print("   1. Use the admin interface in web chat")
    print("   2. Or edit shared/deeplica_port_settings.json")
    print("   3. Restart all services")

if __name__ == "__main__":
    validate_ports()
"""
    
    try:
        with open("validate_ports.py", 'w') as f:
            f.write(validation_script)
        
        # Make it executable
        os.chmod("validate_ports.py", 0o755)
        print("✅ Created validate_ports.py script")
    except Exception as e:
        print(f"❌ Error creating validation script: {e}")

if __name__ == "__main__":
    print("🔧 Starting shell script updates...")
    update_shell_scripts()
    create_port_validation_script()
    print("✅ Shell script updates completed!")
