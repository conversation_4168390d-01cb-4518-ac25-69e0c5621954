#!/usr/bin/env python3
"""
🔍 VERBOSE START DEEPLICA - Comprehensive Debugging Orchestrator

This script provides ultra-verbose debugging for the DEEPLICA startup process
to identify exactly why services are crashing and fix the issues.

Features:
- Real-time process monitoring with detailed output capture
- Service dependency verification
- Port conflict detection and resolution
- Environment validation
- Crash analysis with stack traces
- Automatic service restart with exponential backoff
- Health check monitoring
- Resource usage tracking
"""

import asyncio
import os
import sys
import time
import subprocess
import signal
import json
import psutil
import threading
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from pathlib import Path

# Add project root to path
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, PROJECT_ROOT)

from shared.port_manager import get_service_port, ensure_service_port_free, cleanup_all_ports
from shared.unified_logging import get_logger

# Initialize logger
logger = get_logger("VERBOSE-ORCHESTRATOR")

class ServiceState(Enum):
    NOT_STARTED = "not_started"
    STARTING = "starting"
    RUNNING = "running"
    FAILED = "failed"
    CRASHED = "crashed"
    STOPPED = "stopped"

@dataclass
class ServiceConfig:
    name: str
    display_name: str
    directory: str
    module: str
    port: int
    depends_on: List[str]
    health_check_url: Optional[str] = None
    startup_timeout: int = 60
    restart_count: int = 0
    max_restarts: int = 5

class VerboseDeepplicaOrchestrator:
    """Ultra-verbose orchestrator with comprehensive debugging"""
    
    def __init__(self):
        self.services: Dict[str, ServiceConfig] = {}
        self.processes: Dict[str, subprocess.Popen] = {}
        self.service_states: Dict[str, ServiceState] = {}
        self.service_outputs: Dict[str, List[str]] = {}
        self.shutdown_requested = False
        self.monitoring_tasks: Dict[str, asyncio.Task] = {}
        
        # Configure services
        self._configure_services()
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("🔍 VERBOSE ORCHESTRATOR INITIALIZED")
    
    def _configure_services(self):
        """Configure all services with verbose debugging"""
        logger.info("📋 CONFIGURING SERVICES...")
        
        # Backend API - Core service
        backend_port = get_service_port("BACKEND-API")
        self.services["backend"] = ServiceConfig(
            name="backend",
            display_name="🌐 Backend API",
            directory="backend",
            module="app.main",
            port=backend_port,
            depends_on=[],
            health_check_url=f"http://localhost:{backend_port}/health",
            startup_timeout=120  # Give backend more time to start
        )
        
        # Dispatcher
        dispatcher_port = get_service_port("DISPATCHER")
        self.services["dispatcher"] = ServiceConfig(
            name="dispatcher",
            display_name="🎯 Dispatcher",
            directory="dispatcher",
            module="app.main",
            port=dispatcher_port,
            depends_on=["backend"],
            health_check_url=f"http://localhost:{dispatcher_port}/health",
            startup_timeout=90  # Give dispatcher time to wait for backend
        )
        
        # Web Chat
        webchat_port = get_service_port("WEB-CHAT")
        self.services["webchat"] = ServiceConfig(
            name="webchat",
            display_name="💬 Web Chat",
            directory="web_chat",
            module="main",
            port=webchat_port,
            depends_on=["backend"],
            health_check_url=f"http://localhost:{webchat_port}/health"
        )
        
        # Watchdog
        watchdog_port = get_service_port("WATCHDOG")
        self.services["watchdog"] = ServiceConfig(
            name="watchdog",
            display_name="🐕 Watchdog",
            directory="watchdog",
            module="main",
            port=watchdog_port,
            depends_on=["backend"],
            health_check_url=f"http://localhost:{watchdog_port}/health"
        )
        
        # Initialize states
        for service_name in self.services:
            self.service_states[service_name] = ServiceState.NOT_STARTED
            self.service_outputs[service_name] = []
        
        logger.info(f"✅ CONFIGURED {len(self.services)} SERVICES")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"🛑 RECEIVED SIGNAL {signum} - SHUTTING DOWN")
        self.shutdown_requested = True
    
    async def start_all_services(self) -> bool:
        """Start all services with verbose debugging"""
        logger.info("🚀 STARTING VERBOSE DEEPLICA ORCHESTRATOR")
        logger.info("=" * 80)
        
        try:
            # Phase 1: Environment validation
            await self._validate_environment()
            
            # Phase 2: Port cleanup
            await self._cleanup_ports()
            
            # Phase 3: Start services in order
            startup_order = ["backend", "dispatcher", "webchat", "watchdog"]
            
            for service_name in startup_order:
                if self.shutdown_requested:
                    logger.warning("🛑 SHUTDOWN REQUESTED - ABORTING STARTUP")
                    return False
                
                success = await self._start_service_verbose(service_name)
                if not success:
                    logger.error(f"❌ FAILED TO START {service_name} - ABORTING")
                    await self._shutdown_all_services()
                    return False
                
                # Wait between services
                await asyncio.sleep(2)
            
            logger.info("🎉 ALL SERVICES STARTED SUCCESSFULLY!")
            
            # Monitor services
            await self._monitor_all_services()
            
            return True
            
        except Exception as e:
            logger.error(f"💥 ORCHESTRATOR CRASHED: {e}")
            import traceback
            logger.error(f"STACK TRACE:\n{traceback.format_exc()}")
            return False
    
    async def _validate_environment(self):
        """Validate environment and dependencies"""
        logger.info("🔍 VALIDATING ENVIRONMENT...")
        
        # Check Python version
        python_version = sys.version_info
        logger.info(f"🐍 Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # Check project structure
        for service_name, service in self.services.items():
            service_dir = Path(PROJECT_ROOT) / service.directory
            if not service_dir.exists():
                logger.error(f"❌ SERVICE DIRECTORY NOT FOUND: {service_dir}")
            else:
                logger.info(f"✅ Service directory exists: {service_dir}")
        
        # Check .env file
        env_file = Path(PROJECT_ROOT) / ".env"
        if env_file.exists():
            logger.info("✅ .env file found")
        else:
            logger.warning("⚠️ .env file not found")
        
        # Check available memory
        memory = psutil.virtual_memory()
        logger.info(f"💾 Available memory: {memory.available / 1024 / 1024:.1f} MB")
        
        # Check disk space
        disk = psutil.disk_usage(PROJECT_ROOT)
        logger.info(f"💽 Available disk space: {disk.free / 1024 / 1024 / 1024:.1f} GB")
        
        logger.info("✅ ENVIRONMENT VALIDATION COMPLETE")
    
    async def _cleanup_ports(self):
        """Clean up ports with verbose logging"""
        logger.info("🧹 CLEANING UP PORTS...")
        
        for service_name, service in self.services.items():
            if service.port > 0:
                logger.info(f"🔍 Checking port {service.port} for {service.display_name}")
                
                # Check if port is in use
                if not ensure_service_port_free(service_name):
                    logger.warning(f"⚠️ Port {service.port} was in use - cleaned up")
                else:
                    logger.info(f"✅ Port {service.port} is free")
        
        logger.info("✅ PORT CLEANUP COMPLETE")
    
    async def _start_service_verbose(self, service_name: str) -> bool:
        """Start service with ultra-verbose debugging"""
        service = self.services[service_name]
        logger.info(f"🚀 STARTING {service.display_name}")
        logger.info("-" * 60)
        
        try:
            # Check dependencies
            for dep in service.depends_on:
                if self.service_states[dep] != ServiceState.RUNNING:
                    logger.error(f"❌ DEPENDENCY {dep} NOT RUNNING")
                    return False
                logger.info(f"✅ Dependency {dep} is running")
            
            # Set service state
            self.service_states[service_name] = ServiceState.STARTING
            
            # Build command
            cmd = self._build_service_command(service)
            logger.info(f"📝 Command: {' '.join(cmd)}")
            
            # Set environment
            env = self._build_service_environment(service)
            logger.info(f"🌍 Environment variables: {len(env)} set")
            
            # Set working directory
            cwd = Path(PROJECT_ROOT) / service.directory
            logger.info(f"📁 Working directory: {cwd}")
            
            # Launch process
            logger.info(f"🎬 LAUNCHING PROCESS...")
            process = subprocess.Popen(
                cmd,
                cwd=str(cwd),
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1
            )
            
            self.processes[service_name] = process
            logger.info(f"✅ Process launched with PID: {process.pid}")
            
            # Start output monitoring
            self._start_output_monitoring(service_name, process)
            
            # Wait for startup
            await self._wait_for_service_startup(service_name)
            
            # Verify health
            if service.health_check_url:
                healthy = await self._check_service_health(service_name)
                if not healthy:
                    logger.error(f"❌ HEALTH CHECK FAILED for {service.display_name}")
                    return False
            
            self.service_states[service_name] = ServiceState.RUNNING
            logger.info(f"🎉 {service.display_name} STARTED SUCCESSFULLY!")
            return True
            
        except Exception as e:
            logger.error(f"💥 EXCEPTION STARTING {service.display_name}: {e}")
            import traceback
            logger.error(f"STACK TRACE:\n{traceback.format_exc()}")
            self.service_states[service_name] = ServiceState.FAILED
            return False

    def _build_service_command(self, service: ServiceConfig) -> List[str]:
        """Build command to start service"""
        if service.name == "backend":
            return [sys.executable, "app/main.py"]
        elif service.name == "dispatcher":
            # Use module execution to avoid relative import issues
            return [sys.executable, "-m", "app.main"]
        elif service.name == "webchat":
            return [sys.executable, "main.py"]
        elif service.name == "watchdog":
            return [sys.executable, "main.py"]
        else:
            return [sys.executable, "-m", service.module]

    def _build_service_environment(self, service: ServiceConfig) -> Dict[str, str]:
        """Build environment for service"""
        env = os.environ.copy()
        env["PYTHONPATH"] = PROJECT_ROOT
        env["SERVICE_NAME"] = service.name.upper().replace("_", "-")
        env["USE_MOCK_DATABASE"] = "true"
        env["WAIT_FOR_BACKEND"] = "false"  # Orchestrator handles dependencies
        return env

    def _start_output_monitoring(self, service_name: str, process: subprocess.Popen):
        """Start monitoring service output in background thread"""
        def monitor_output():
            try:
                for line in iter(process.stdout.readline, ''):
                    if line:
                        line = line.strip()
                        self.service_outputs[service_name].append(line)
                        logger.info(f"[{service_name.upper()}] {line}")

                        # Check for crash indicators
                        if any(keyword in line.lower() for keyword in ['error', 'exception', 'traceback', 'failed']):
                            logger.warning(f"⚠️ POTENTIAL ISSUE in {service_name}: {line}")
            except Exception as e:
                logger.error(f"❌ OUTPUT MONITORING ERROR for {service_name}: {e}")

        thread = threading.Thread(target=monitor_output, daemon=True)
        thread.start()

    async def _wait_for_service_startup(self, service_name: str):
        """Wait for service to start up"""
        service = self.services[service_name]
        logger.info(f"⏳ Waiting for {service.display_name} to start...")

        for i in range(service.startup_timeout):
            # Check if process is still running
            process = self.processes.get(service_name)
            if process and process.poll() is not None:
                # For services that should run indefinitely, exit code 0 during startup is an error
                # But for some services, they might exit normally after initialization
                if service_name in ["backend", "dispatcher", "webchat", "watchdog"]:
                    logger.error(f"❌ LONG-RUNNING SERVICE DIED during startup: exit code {process.returncode}")
                    # Show recent output
                    recent_output = self.service_outputs[service_name][-10:]
                    for line in recent_output:
                        logger.error(f"OUTPUT: {line}")
                    raise Exception(f"Long-running service died with exit code {process.returncode}")
                else:
                    logger.info(f"✅ Service {service_name} completed with exit code {process.returncode}")
                    break

            # For services with health checks, try to verify they're responding
            if service.health_check_url and i > 5:  # Wait at least 5 seconds before health checks
                try:
                    import aiohttp
                    async with aiohttp.ClientSession() as session:
                        async with session.get(service.health_check_url, timeout=2) as response:
                            if response.status == 200:
                                logger.info(f"✅ {service.display_name} health check passed - service is ready!")
                                return
                except:
                    pass  # Health check failed, continue waiting

            await asyncio.sleep(1)

            if i % 10 == 0 and i > 0:
                logger.info(f"⏳ Still waiting for {service.display_name}... ({i}s)")

        logger.info(f"✅ {service.display_name} startup wait complete")

    async def _check_service_health(self, service_name: str) -> bool:
        """Check service health with detailed logging"""
        service = self.services[service_name]
        if not service.health_check_url:
            return True

        logger.info(f"🩺 HEALTH CHECK: {service.health_check_url}")

        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.get(service.health_check_url, timeout=10) as response:
                    if response.status == 200:
                        logger.info(f"✅ HEALTH CHECK PASSED for {service.display_name}")
                        return True
                    else:
                        logger.error(f"❌ HEALTH CHECK FAILED: HTTP {response.status}")
                        return False
        except Exception as e:
            logger.error(f"❌ HEALTH CHECK ERROR: {e}")
            return False

    async def _monitor_all_services(self):
        """Monitor all services for crashes"""
        logger.info("👁️ STARTING SERVICE MONITORING...")

        while not self.shutdown_requested:
            for service_name, process in self.processes.items():
                if process.poll() is not None:
                    logger.error(f"💥 SERVICE CRASHED: {service_name} (exit code: {process.returncode})")

                    # Show crash output
                    recent_output = self.service_outputs[service_name][-20:]
                    logger.error(f"CRASH OUTPUT for {service_name}:")
                    for line in recent_output:
                        logger.error(f"  {line}")

                    # Attempt restart
                    await self._restart_service(service_name)

            await asyncio.sleep(5)

    async def _restart_service(self, service_name: str):
        """Restart a crashed service"""
        service = self.services[service_name]
        service.restart_count += 1

        if service.restart_count > service.max_restarts:
            logger.error(f"❌ MAX RESTARTS EXCEEDED for {service.display_name}")
            self.service_states[service_name] = ServiceState.FAILED
            return

        logger.info(f"🔄 RESTARTING {service.display_name} (attempt {service.restart_count})")

        # Clean up old process
        if service_name in self.processes:
            del self.processes[service_name]

        # Wait before restart
        await asyncio.sleep(min(service.restart_count * 2, 10))

        # Restart service
        success = await self._start_service_verbose(service_name)
        if success:
            logger.info(f"✅ RESTART SUCCESSFUL for {service.display_name}")
        else:
            logger.error(f"❌ RESTART FAILED for {service.display_name}")

    async def _shutdown_all_services(self):
        """Shutdown all services"""
        logger.info("🛑 SHUTTING DOWN ALL SERVICES...")

        for service_name, process in self.processes.items():
            try:
                logger.info(f"🛑 Stopping {service_name}...")
                process.terminate()

                # Wait for graceful shutdown
                try:
                    process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    logger.warning(f"⚠️ Force killing {service_name}")
                    process.kill()
                    process.wait()

                logger.info(f"✅ {service_name} stopped")
            except Exception as e:
                logger.error(f"❌ Error stopping {service_name}: {e}")

        logger.info("✅ ALL SERVICES STOPPED")

async def main():
    """Main function"""
    orchestrator = VerboseDeepplicaOrchestrator()

    try:
        success = await orchestrator.start_all_services()
        if success:
            logger.info("🎉 DEEPLICA STARTED SUCCESSFULLY!")
        else:
            logger.error("❌ DEEPLICA STARTUP FAILED!")
            return 1
    except KeyboardInterrupt:
        logger.info("🛑 INTERRUPTED BY USER")
    except Exception as e:
        logger.error(f"💥 ORCHESTRATOR ERROR: {e}")
        import traceback
        logger.error(f"STACK TRACE:\n{traceback.format_exc()}")
        return 1
    finally:
        await orchestrator._shutdown_all_services()

    return 0

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(result)
    except Exception as e:
        print(f"💥 FATAL ERROR: {e}")
        sys.exit(1)
