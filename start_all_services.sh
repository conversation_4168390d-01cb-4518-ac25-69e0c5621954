#!/bin/bash
# =============================================================================
# DEEPLICA SERVICE SCRIPT - ALL PORTS MANAGED DYNAMICALLY
# =============================================================================
# ALL ports are assigned by shared/port_manager.py - NO HARDCODED PORTS
# External services adapt to assigned ports through configuration
# =============================================================================
# =============================================================================
# DEEPLICA SERVICE STARTUP SCRIPT
# =============================================================================
# NOTE: All ports are managed by shared/port_manager.py
# - Backend API: 8888 (CONSTANT - never changes)
# - Other services: configurable via port manager
# =============================================================================

# Deeplica v0 - Start All Microservices
# This script starts all microservices in the correct order with proper dependency management

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')] ✅${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[$(date '+%H:%M:%S')] ⚠️${NC} $1"
}

print_error() {
    echo -e "${RED}[$(date '+%H:%M:%S')] ❌${NC} $1"
}

# Function to check if a service is running
check_service() {
    local port=$1
    local service_name=$2

    if curl -s "http://localhost:${port}/health" > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to wait for a service to be ready
wait_for_service() {
    local port=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1

    print_status "Waiting for ${service_name} to be ready on port ${port}..."

    while [ $attempt -le $max_attempts ]; do
        if check_service $port "$service_name"; then
            print_success "${service_name} is ready!"
            return 0
        fi

        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done

    print_error "${service_name} failed to start within $((max_attempts * 2)) seconds"
    return 1
}

# Kill any existing processes on our ports
echo "🧹 Cleaning up existing processes..."
check_port 8888
check_port 8001
check_port 8002
check_port 8003
check_port 8004

# Start Backend (port 8888 - CONSTANT)
echo "🔧 Starting Backend Service (port 8000)..."
cd backend
python3 -m app.main &
BACKEND_PID=$!
cd ..
echo "   Backend PID: $BACKEND_PID"

# Wait for backend to be ready
wait_for_service "http://localhost:{get_service_port("backend")}/health  # Backend API - CONSTANT port" "Backend"

# Start Dispatcher (port 8001 - managed by port_manager.py)
echo "🎯 Starting Dispatcher Service (port 8001)..."
cd dispatcher
PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('dispatcher')") python3  # Port dynamically assigned by port_manager.py -m app.main &
DISPATCHER_PID=$!
cd ..
echo "   Dispatcher PID: $DISPATCHER_PID"

# Wait for dispatcher to be ready
wait_for_service "http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('dispatcher')")/health  # Dispatcher - managed by port_manager.py" "Dispatcher"

# Start Dialogue Agent (port 8002 - managed by port_manager.py)
echo "💬 Starting Dialogue Agent (port 8002)..."
cd agents/dialogue
PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('dialogue')") python3  # Port dynamically assigned by port_manager.py -m app.main &
DIALOGUE_PID=$!
cd ../..
echo "   Dialogue PID: $DIALOGUE_PID"

# Wait for dialogue agent to be ready
wait_for_service "http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('dialogue')")/health  # Dialogue Agent - managed by port_manager.py" "Dialogue Agent"

# Start Planner Agent (port 8003 - managed by port_manager.py)
echo "🧠 Starting Planner Agent (port 8003)..."
cd agents/planner
PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('planner')") python3  # Port dynamically assigned by port_manager.py -m app.main &
PLANNER_PID=$!
cd ../..
echo "   Planner PID: $PLANNER_PID"

# Wait for planner to be ready
wait_for_service "http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('planner')")/health  # Planner Agent - managed by port_manager.py" "Planner Agent"

# Start Phone Agent (port 8004 - managed by port_manager.py)
echo "📞 Starting Phone Agent (port 8004)..."
cd agents/phone
PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('phone')") python3  # Port dynamically assigned by port_manager.py -m app.main &
PHONE_PID=$!
cd ../..
echo "   Dialogue PID: $DIALOGUE_PID"

# Wait for dialogue agent to be ready
wait_for_service "http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('dialogue')")/health  # Dialogue Agent - managed by port_manager.py" "Dialogue Agent"

# Wait for phone agent to be ready
wait_for_service "http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('phone')")/health  # Phone Agent - managed by port_manager.py" "Phone Agent"

# Start ngrok tunnel for phone agent webhooks
echo "🌐 Starting ngrok tunnel for phone webhooks..."
ngrok http 8004  # Phone Agent port - managed by port_manager.py &
NGROK_PID=$!
echo "   ngrok PID: $NGROK_PID"

# Wait a moment for ngrok to establish tunnel
sleep 5

# Get ngrok URL and update .env file
echo "🔗 Getting ngrok URL..."
NGROK_URL=$(curl -s http://localhost:${get_service_port("ngrok-api")}/api/tunnels | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    for tunnel in data['tunnels']:
        if tunnel['proto'] == 'https':
            print(tunnel['public_url'])
            break
except:
    pass
")

if [ -n "$NGROK_URL" ]; then
    echo "✅ ngrok URL: $NGROK_URL"
    # Update .env file with new webhook URL
    sed -i.bak "s|TWILIO_WEBHOOK_URL=.*|TWILIO_WEBHOOK_URL=$NGROK_URL|" .env
    echo "✅ Updated .env file with new webhook URL"
    
    # Restart phone agent to pick up new webhook URL
    echo "🔄 Restarting phone agent with new webhook URL..."
    kill $PHONE_PID
    sleep 2
    cd agents/phone
    PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('planner')") python3  # Port dynamically assigned by port_manager.py -m app.main &
    PHONE_PID=$!
    cd ../..
    
    # Wait for phone agent to be ready again
    wait_for_service "http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('phone')")/health  # Phone Agent - managed by port_manager.py" "Phone Agent (restarted)"
else
    echo "⚠️  Could not get ngrok URL, phone webhooks may not work"
fi

# Final health check
echo "🏥 Final health check..."
echo "Backend:   $(curl -s http://localhost:{get_service_port("backend")}/health  # Backend API - CONSTANT port 2>/dev/null || echo 'DOWN')"
echo "Dispatcher: $(curl -s http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('dispatcher')")/health  # Dispatcher - managed by port_manager.py 2>/dev/null || echo 'DOWN')"
echo "Planner:   $(curl -s http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('planner')")/health  # Planner Agent - managed by port_manager.py 2>/dev/null || echo 'DOWN')"
echo "Phone:     $(curl -s http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('phone')")/health  # Phone Agent - managed by port_manager.py 2>/dev/null || echo 'DOWN')"
echo "Dialogue:  $(curl -s http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('dialogue')")/health  # Dialogue Agent - managed by port_manager.py 2>/dev/null || echo 'DOWN')"

# Save PIDs for cleanup
echo "💾 Saving process IDs..."
cat > .service_pids << EOF
BACKEND_PID=$BACKEND_PID
DISPATCHER_PID=$DISPATCHER_PID
PLANNER_PID=$PLANNER_PID
PHONE_PID=$PHONE_PID
DIALOGUE_PID=$DIALOGUE_PID
NGROK_PID=$NGROK_PID
EOF

echo ""
echo "🎉 All services started successfully!"
echo ""
echo "📋 Service URLs:"
echo "   Backend:    http://localhost:{get_service_port("backend")}"
echo "   Dispatcher: http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('dispatcher')")"
echo "   Planner:    http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('planner')")"
echo "   Phone:      http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('phone')")"
echo "   Dialogue:   http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('dialogue')")"
echo "   Webhook:    $NGROK_URL"
echo ""
echo "🛑 To stop all services, run: ./stop_all_services.sh"
echo ""
echo "✅ System ready for phone call missions!"
