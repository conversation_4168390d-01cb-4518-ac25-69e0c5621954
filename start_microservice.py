#!/usr/bin/env python3
"""
Universal microservice starter that waits for backend API to be ready
Usage: python3 start_microservice.py <service_name> <port>
Example: python3 start_microservice.py dispatcher get_service_port("dispatcher")
"""

import sys
import time
import requests
import subprocess
import os
from pathlib import Path

# Add project root to path for shared modules
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.port_manager import get_service_port, get_localhost

def wait_for_backend(timeout=120):
    """Wait for backend API to be completely ready"""
    # [SYSTEM:wait_for_backend] SUCCESS | ⏳ Waiting for Backend API to be ready...
    
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            # Check if backend is ready
            response = requests.get(f"http://{get_localhost()}:{get_service_port('backend')}/ready", timeout=5)
            if response.status_code == 200:
                # [SYSTEM:wait_for_backend] SUCCESS | ✅ Backend API is ready!
                
                # Also check system status to ensure all backend services are loaded
                status_response = requests.get(f"http://{get_localhost()}:{get_service_port('backend')}/system/status", timeout=5)
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    backend_ready = status_data.get("backend_ready", False)
                    if backend_ready:
                        # [SYSTEM:wait_for_backend] SYSTEM | ✅ Backend API is fully operational!
                        return True
                    else:
                        # [SYSTEM:wait_for_backend] SYSTEM | ⏳ Backend API responding but not fully loaded yet...
                        pass
                else:
                    # [SYSTEM:wait_for_backend] SUCCESS | ⏳ Backend API ready but system status not available yet...
                    pass
                    
            else:
                # [SYSTEM:wait_for_backend] SUCCESS | ⏳ Backend API not ready yet (status: {response.status_code")
                
                pass
        except Exception as e:
            # [SYSTEM:wait_for_backend] SUCCESS | ⏳ Backend API not ready yet: {e}
        
            pass
        time.sleep(2)
    
    raise Exception(f"Backend API failed to become ready within {timeout} seconds")

def start_microservice(service_name, port):
    """Start a microservice after backend is ready"""
    # [SYSTEM:start_microservice] STARTUP | 🚀 Starting {service_name} microservice on port {port}
    
    # Wait for backend first
    wait_for_backend()
    
    # Determine service directory and module
    service_dirs = {
        "dispatcher": "dispatcher",
        "dialogue": "dialogue_agent", 
        "planner": "planner_agent",
        "phone": "phone_agent"
    }
    
    if service_name not in service_dirs:
        raise ValueError(f"Unknown service: {service_name}. Available: {list(service_dirs.keys())}")
    
    service_dir = service_dirs[service_name]
    project_root = Path(__file__).parent
    service_path = project_root / service_dir
    
    if not service_path.exists():
        raise FileNotFoundError(f"Service directory not found: {service_path}")
    
    # Set environment
    env = os.environ.copy()
    env["PORT"] = str(port)
    
    # Start the service
    # [SYSTEM:start_microservice] STARTUP | 🔄 Starting {service_name} from {service_path}
    
    try:
        process = subprocess.Popen(
            [sys.executable, "-m", "app.main"],
            cwd=str(service_path),
            env=env
        )
        
        # [SYSTEM:start_microservice] SUCCESS | ✅ {service_name} started successfully (PID: {process.pid")
        # [SYSTEM:start_microservice] SYSTEM | 📊 Monitor at: http://{get_localhost()}:{port}/health
        # [SYSTEM:start_microservice] SYSTEM | 🔄 Press Ctrl+C to stop
        
        # Wait for the process
        process.wait()
        
    except KeyboardInterrupt:
        print(f"\n🛑 Stopping {service_name}...")
        process.terminate()
        process.wait()
        # [SYSTEM:start_microservice] SYSTEM | ✅ {service_name} stopped

def main():
    if len(sys.argv) != 3:
        print("Usage: python3 start_microservice.py <service_name> <port>")
        print("Services: dispatcher, dialogue, planner, phone")
        print("Example: python3 start_microservice.py dispatcher $(get_service_port dispatcher)")
        return 1  # Return instead of sys.exit()

    service_name = sys.argv[1].lower()
    try:
        port = int(sys.argv[2])
    except ValueError:
        print("Error: Port must be a number")
        return 1  # Return instead of sys.exit()

    print(f"🔧 MICROSERVICE STARTER: {service_name.upper()}")
    print("=" * 50)

    try:
        start_microservice(service_name, port)
        return 0
    except Exception as e:
        print(f"❌ Failed to start {service_name}: {e}")
        return 1  # Return instead of sys.exit()

if __name__ == "__main__":
    main()
