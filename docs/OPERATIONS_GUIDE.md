# 🚀 DEEPLICA V3 OPERATIONS GUIDE

## **Complete Operational Procedures for Development & Production**

---

## 🎮 **System Startup**

### **Method 1: VS Code Integrated (Recommended)**
1. Open VS Code in the project root
2. Go to **Run and Debug** panel (Ctrl+Shift+D)
3. Select **🚀 START DEEPLICA (Separate VS Code Terminals)**
4. Click **Start Debugging** (F5)

**Result**: All 7 services start in separate VS Code terminals with full debugging support.

### **Method 2: Command Line Orchestrated**
```bash
# Single terminal with orchestrated startup
python3 orchestrator/main.py
```

### **Method 3: Manual Script**
```bash
# All services in separate terminals
./start_all_services.sh

# Or separate terminals with tmux/screen
./launch_separate_terminals.sh
```

### **Startup Sequence**
1. **Backend API** (8888) - Starts first, establishes database connection
2. **Dispatcher** (8001) - Waits for Backend, then starts
3. **Dialogue Agent** (8002) - Waits for Backend, then starts
4. **Planner Agent** (8003) - Waits for Backend, then starts
5. **Phone Agent** (8004) - Waits for Backend, then starts
6. **Watchdog** (8005) - Starts last, monitors all services
7. **CLI Terminal** - Connects to Backend when ready

---

## 🛑 **System Shutdown**

### **Safe Shutdown (Recommended)**
```bash
# Use VS Code: Stop all debug sessions manually
# OR Command line: Kill processes by port
python3 cleanup_deeplica.py
```

### **Emergency Shutdown**
```bash
# Kill all processes
pkill -f DEEPLICA

# Or force kill specific ports
./scripts/kill_ports.sh 8000 8001 8002 8003 8004 8005
```

### **Shutdown Process**
1. **Stop accepting new requests**
2. **Complete active tasks** (with timeout)
3. **Close database connections**
4. **Stop external services** (ngrok, webhooks)
5. **Clean up temporary files**
6. **Free all ports**

---

## 📊 **Monitoring & Health Checks**

### **Real-time System Status**
```bash
# Check all services
curl http://localhost:8000/health  # Backend
curl http://localhost:8001/health  # Dispatcher
curl http://localhost:8002/health  # Dialogue
curl http://localhost:8003/health  # Planner
curl http://localhost:8004/health  # Phone
curl http://localhost:8005/health  # Watchdog

# Comprehensive system status
curl http://localhost:8005/system_status
```

### **Watchdog Dashboard**
The Watchdog service provides real-time monitoring:
- **Service Health**: All microservices status
- **External Dependencies**: MongoDB, Twilio, ngrok
- **Performance Metrics**: Response times, error rates
- **Auto-Recovery**: Automatic service restart

### **Log Monitoring**
```bash
# Service logs (in VS Code terminals)
# Or check log files if configured
tail -f logs/backend.log
tail -f logs/phone.log
tail -f logs/watchdog.log
```

---

## 🔧 **Configuration Management**

### **Environment Variables (.env)**
```bash
# Core Configuration
ENVIRONMENT=local
DEBUG=true
HOST=0.0.0.0

# Database
MONGODB_CONNECTION_STRING=mongodb+srv://...
MONGODB_DATABASE=deeplica-dev

# LLM Service
GEMINI_API_KEY=your_api_key_here

# Twilio Configuration
TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_PHONE_NUMBER=your_twilio_number
TWILIO_WEBHOOK_URL=https://your-ngrok-url.ngrok-free.app

# Service URLs (auto-configured)
BACKEND_URL=http://localhost:8000
DISPATCHER_URL=http://localhost:8001
DIALOGUE_AGENT_URL=http://localhost:8002
PLANNER_AGENT_URL=http://localhost:8003
PHONE_AGENT_URL=http://localhost:8004
```

### **Dynamic Configuration Updates**
```bash
# Reload phone service configuration
curl -X POST http://localhost:8004/reload_config

# Update ngrok webhook URL automatically
# (Watchdog handles this automatically)
```

---

## 📞 **Phone System Operations**

### **Testing Phone Calls**
```bash
# Comprehensive phone test
python3 phone_test_system.py

# Manual test call
curl -X POST http://localhost:8004/execute \
  -H "Content-Type: application/json" \
  -d '{
    "task_id": "test_call_123",
    "task_type": "phone_call",
    "task_data": {
      "phone_number": "+**********",
      "contact_name": "Test User",
      "question": "This is a test call"
    }
  }'
```

### **ngrok Management**
```bash
# Check ngrok status
curl http://localhost:4040/api/tunnels

# Restart ngrok (automatic via watchdog)
pkill ngrok
ngrok http 8004 &

# Update webhook URL
curl -X POST http://localhost:8004/reload_config
```

### **Call Troubleshooting**
1. **Check ngrok tunnel**: Ensure HTTPS tunnel is active
2. **Verify webhook URL**: Must match current ngrok URL
3. **Test Twilio credentials**: Validate account SID and auth token
4. **Check phone number format**: Must include country code (+**********)

---

## 🗄️ **Database Operations**

### **MongoDB Atlas Management**
```bash
# Check database connectivity
curl http://localhost:8000/health

# View database status via watchdog
curl http://localhost:8005/system_status | jq '.external_services.mongodb'
```

### **Data Backup**
```bash
# MongoDB Atlas automatic backups are enabled
# Manual backup via mongodump (if needed)
mongodump --uri="mongodb+srv://..." --db=deeplica-dev
```

### **Database Migrations**
```bash
# Run database migrations (if implemented)
python3 scripts/migrate_database.py
```

---

## 🔍 **Troubleshooting**

### **Common Issues**

#### **Port Conflicts**
```bash
# Check what's using ports
lsof -i :8000
lsof -i :8001
# ... etc

# Kill processes on specific ports
./scripts/kill_ports.sh 8000 8001 8002 8003 8004 8005
```

#### **Service Won't Start**
1. **Check dependencies**: Ensure Backend API starts first
2. **Verify environment**: Check .env file configuration
3. **Database connectivity**: Ensure MongoDB Atlas is accessible
4. **Port availability**: Ensure ports are not in use

#### **Phone Calls Failing**
1. **ngrok tunnel**: Check if tunnel is active and accessible
2. **Webhook URL**: Ensure it matches current ngrok URL
3. **Twilio credentials**: Verify account SID and auth token
4. **Phone number format**: Must include country code

#### **Database Connection Issues**
1. **Network connectivity**: Check internet connection
2. **Credentials**: Verify MongoDB Atlas connection string
3. **IP whitelist**: Ensure your IP is whitelisted in Atlas
4. **Database exists**: Verify database and collections exist

### **Debug Mode**
```bash
# Enable debug logging
export DEBUG=true

# Run services with verbose output
python3 -m agents.backend.app.main --debug
```

### **Performance Issues**
```bash
# Check system metrics
curl http://localhost:8005/metrics

# Monitor resource usage
top -p $(pgrep -f DEEPLICA)
```

---

## 🚀 **Deployment**

### **Development Environment**
- **Local services**: All on localhost
- **VS Code integration**: Full debugging support
- **Hot reload**: Automatic code reloading

### **Production Considerations**
```bash
# Environment setup
export ENVIRONMENT=production
export DEBUG=false

# Security hardening
# - Use production database
# - Enable authentication
# - Configure firewalls
# - Set up monitoring

# Container deployment
docker-compose up -d

# Kubernetes deployment
kubectl apply -f k8s/
```

---

## 📈 **Performance Optimization**

### **Service Optimization**
- **Connection pooling**: Database and HTTP connections
- **Async processing**: Non-blocking I/O operations
- **Caching**: Strategic caching for frequently accessed data
- **Load balancing**: Multiple service instances

### **Database Optimization**
- **Indexing**: Proper database indexes
- **Query optimization**: Efficient database queries
- **Connection limits**: Appropriate connection pool sizes

### **Monitoring & Alerting**
- **Health checks**: Regular service health verification
- **Performance metrics**: Response times and throughput
- **Error tracking**: Comprehensive error monitoring
- **Alerting**: Automated alerts for critical issues

---

**Next**: See [DEVELOPER_GUIDE.md](DEVELOPER_GUIDE.md) for development procedures
