"""
User models for the Deeplica system.

This module defines the user data structures and authentication models
used across all microservices.
"""

from datetime import datetime
from enum import Enum
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


class UserRole(str, Enum):
    """User roles in the system"""
    ADMIN = "admin"
    USER = "user"
    GUEST = "guest"


class UserStatus(str, Enum):
    """User account status"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING = "pending"


class UserPermission(str, Enum):
    """User permissions"""
    # Chat permissions
    CHAT_ACCESS = "chat_access"
    CHAT_HISTORY = "chat_history"
    
    # Mission permissions
    CREATE_MISSIONS = "create_missions"
    VIEW_MISSIONS = "view_missions"
    MANAGE_MISSIONS = "manage_missions"
    
    # Phone permissions
    PHONE_CALLS = "phone_calls"
    PHONE_HISTORY = "phone_history"
    
    # Admin permissions
    USER_MANAGEMENT = "user_management"
    SYSTEM_SETTINGS = "system_settings"
    VIEW_LOGS = "view_logs"
    SYSTEM_ADMIN = "system_admin"


class User(BaseModel):
    """
    User model for authentication and authorization.
    """
    
    # Core identification
    user_id: str = Field(..., description="Unique identifier for the user")
    username: str = Field(..., description="Username (case insensitive)", min_length=3, max_length=50)
    email: Optional[str] = Field(None, description="User email address")
    mobile_phone: Optional[str] = Field(None, description="User mobile phone number")

    # Authentication
    password_hash: Optional[str] = Field(None, description="Hashed password (case sensitive) - Optional for legacy users")

    # Profile information
    full_name: str = Field(..., description="User's full name", min_length=1, max_length=100)
    display_name: Optional[str] = Field(None, description="Display name for chat", max_length=50)
    
    # Authorization
    role: UserRole = Field(default=UserRole.USER, description="User role")
    status: UserStatus = Field(default=UserStatus.ACTIVE, description="Account status")
    permissions: List[UserPermission] = Field(default_factory=list, description="User permissions")
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.now, description="Account creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.now, description="Last update timestamp")
    last_login: Optional[datetime] = Field(None, description="Last login timestamp")
    login_count: int = Field(default=0, description="Number of logins")
    
    # Settings
    preferences: Dict[str, Any] = Field(default_factory=dict, description="User preferences")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class UserCreate(BaseModel):
    """Model for creating a new user"""
    username: str = Field(..., min_length=3, max_length=50)
    password: str = Field(..., min_length=6, max_length=100)
    email: Optional[str] = None
    mobile_phone: Optional[str] = None
    full_name: str = Field(..., min_length=1, max_length=100)
    display_name: Optional[str] = Field(None, max_length=50)
    role: UserRole = Field(default=UserRole.USER)
    status: UserStatus = Field(default=UserStatus.ACTIVE)
    permissions: List[UserPermission] = Field(default_factory=list)


class UserUpdate(BaseModel):
    """Model for updating user information"""
    new_user_id: Optional[str] = None
    email: Optional[str] = None
    mobile_phone: Optional[str] = None
    full_name: Optional[str] = Field(None, min_length=1, max_length=100)
    display_name: Optional[str] = Field(None, max_length=50)
    role: Optional[UserRole] = None
    status: Optional[UserStatus] = None
    permissions: Optional[List[UserPermission]] = None
    preferences: Optional[Dict[str, Any]] = None
    password: Optional[str] = Field(None, min_length=6, max_length=100)


class UserLogin(BaseModel):
    """Model for user login"""
    username: str = Field(..., min_length=1)
    password: str = Field(..., min_length=1)


class UserSession(BaseModel):
    """Model for user session"""
    session_id: str = Field(..., description="Session identifier")
    user_id: str = Field(..., description="User identifier")
    username: str = Field(..., description="Username")
    created_at: datetime = Field(default_factory=datetime.now)
    expires_at: datetime = Field(..., description="Session expiration")
    last_activity: datetime = Field(default_factory=datetime.now)
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None


class UserResponse(BaseModel):
    """Response model for user operations"""
    user_id: str
    username: str
    email: Optional[str]
    mobile_phone: Optional[str]
    full_name: str
    display_name: Optional[str]
    role: UserRole
    status: UserStatus
    permissions: List[UserPermission]
    created_at: datetime
    last_login: Optional[datetime]
    login_count: int


# Default permissions by role
DEFAULT_PERMISSIONS = {
    UserRole.ADMIN: [
        UserPermission.CHAT_ACCESS,
        UserPermission.CHAT_HISTORY,
        UserPermission.CREATE_MISSIONS,
        UserPermission.VIEW_MISSIONS,
        UserPermission.MANAGE_MISSIONS,
        UserPermission.PHONE_CALLS,
        UserPermission.PHONE_HISTORY,
        UserPermission.USER_MANAGEMENT,
        UserPermission.SYSTEM_SETTINGS,
        UserPermission.VIEW_LOGS,
        UserPermission.SYSTEM_ADMIN,
    ],
    UserRole.USER: [
        UserPermission.CHAT_ACCESS,
        UserPermission.CHAT_HISTORY,
        UserPermission.CREATE_MISSIONS,
        UserPermission.VIEW_MISSIONS,
        UserPermission.PHONE_CALLS,
        UserPermission.PHONE_HISTORY,
    ],
    UserRole.GUEST: [
        UserPermission.CHAT_ACCESS,
    ],
}


def get_default_permissions(role: UserRole) -> List[UserPermission]:
    """Get default permissions for a user role"""
    return DEFAULT_PERMISSIONS.get(role, [])
