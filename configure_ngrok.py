#!/usr/bin/env python3
"""
🌐 Ngrok Configuration Script
Configure ngrok to work with DEEPLICA's dynamic port management.
"""

import os
import sys
import subprocess
import json
from pathlib import Path

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.port_manager import get_service_port

def setup_ngrok():
    """Setup ngrok with proper configuration"""
    
    print("🌐 NGROK CONFIGURATION FOR DEEPLICA")
    print("=" * 50)
    
    # Check if ngrok is installed
    try:
        result = subprocess.run(['ngrok', 'version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Ngrok installed: {result.stdout.strip()}")
        else:
            raise FileNotFoundError()
    except FileNotFoundError:
        print("❌ Ngrok not found")
        print("💡 Install ngrok from: https://ngrok.com/download")
        print("💡 Or use: brew install ngrok (on macOS)")
        return False
    
    # Check if auth token is configured
    try:
        result = subprocess.run(['ngrok', 'config', 'check'], capture_output=True, text=True)
        if result.returncode != 0:
            print("⚠️ Ngrok auth token not configured")
            print("💡 Run: ngrok config add-authtoken YOUR_TOKEN_HERE")
            print("💡 Get your token from: https://dashboard.ngrok.com/get-started/your-authtoken")
            print("💡 Suggested command:")
            print("   ngrok config add-authtoken *************************************************")
            return False
        else:
            print("✅ Ngrok auth token configured")
    except Exception as e:
        print(f"⚠️ Could not check ngrok config: {e}")
    
    # Get dynamic ports from port manager
    try:
        phone_port = get_service_port('phone')
        backend_port = get_service_port('backend')
        ngrok_api_port = get_service_port('ngrok-api')
        
        print(f"\n📋 DEEPLICA Port Assignments:")
        print(f"   📞 Phone Agent: {phone_port}")
        print(f"   🖥️  Backend API: {backend_port}")
        print(f"   🌐 Ngrok API: {ngrok_api_port}")
        
    except Exception as e:
        print(f"❌ Error getting ports from port manager: {e}")
        return False
    
    # Create ngrok configuration
    ngrok_config = {
        "version": "2",
        "authtoken_from_env": True,
        "tunnels": {
            "deeplica-phone": {
                "proto": "http",
                "addr": phone_port,
                "bind_tls": True,
                "inspect": True
            },
            "deeplica-backend": {
                "proto": "http", 
                "addr": backend_port,
                "bind_tls": True,
                "inspect": True
            }
        },
        "web_addr": f"localhost:{ngrok_api_port}",
        "log_level": "info",
        "log_format": "logfmt"
    }
    
    # Write ngrok config
    try:
        config_dir = Path.home() / ".ngrok2"
        config_dir.mkdir(exist_ok=True)
        config_file = config_dir / "ngrok.yml"
        
        with open(config_file, 'w') as f:
            import yaml
            yaml.dump(ngrok_config, f, default_flow_style=False)
        
        print(f"✅ Ngrok config written to: {config_file}")
        
    except ImportError:
        print("⚠️ PyYAML not installed, creating JSON config instead")
        config_file = config_dir / "ngrok.json"
        with open(config_file, 'w') as f:
            json.dump(ngrok_config, f, indent=2)
        print(f"✅ Ngrok config written to: {config_file}")
        
    except Exception as e:
        print(f"❌ Error writing ngrok config: {e}")
        return False
    
    print(f"\n🚀 Ngrok Configuration Complete!")
    print(f"💡 Usage:")
    print(f"   Start phone tunnel: ngrok start deeplica-phone")
    print(f"   Start backend tunnel: ngrok start deeplica-backend")
    print(f"   Start both: ngrok start deeplica-phone deeplica-backend")
    print(f"   Dashboard: http://localhost:{ngrok_api_port}")
    
    return True

def test_ngrok_setup():
    """Test ngrok setup with current port configuration"""
    
    print("\n🧪 Testing ngrok setup...")
    
    try:
        phone_port = get_service_port('phone')
        
        # Test if phone service is running
        import requests
        try:
            response = requests.get(f"http://localhost:{phone_port}/health", timeout=2)
            if response.status_code == 200:
                print(f"✅ Phone service is running on port {phone_port}")
            else:
                print(f"⚠️ Phone service responded with status {response.status_code}")
        except requests.exceptions.RequestException:
            print(f"⚠️ Phone service not responding on port {phone_port}")
            print("💡 Start the phone service before testing ngrok")
        
        # Test ngrok tunnel (dry run)
        print(f"💡 To test ngrok tunnel, run:")
        print(f"   ngrok http {phone_port}")
        print(f"   or")
        print(f"   python3 start_ngrok.py")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing ngrok setup: {e}")
        return False

if __name__ == "__main__":
    print("🌐 Starting ngrok configuration...")
    
    if setup_ngrok():
        test_ngrok_setup()
        print("\n✅ Ngrok configuration completed successfully!")
        print("🔄 Ngrok is now configured to work with DEEPLICA's dynamic port management")
    else:
        print("\n❌ Ngrok configuration failed")
        sys.exit(1)
