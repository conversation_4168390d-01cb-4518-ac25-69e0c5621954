#!/usr/bin/env python3
"""
🔄 DEEPLICA Database Mode Switcher

This script allows switching between demo mode (mock database) and production mode (real MongoDB Atlas).
"""

import os
import sys
import shutil
from pathlib import Path

def switch_to_production_mode():
    """Switch to production mode with real MongoDB Atlas"""
    print("🏭 SWITCHING TO PRODUCTION MODE...")
    print("=" * 50)

    # Backup current .env if it exists
    if os.path.exists(".env"):
        shutil.copy(".env", ".env.backup")
        print("📁 Backed up current .env to .env.backup")

    # Copy production mode configuration
    if os.path.exists("PRODUCTION_MODE.env"):
        shutil.copy("PRODUCTION_MODE.env", ".env")
        print("✅ Copied PRODUCTION_MODE.env to .env")
    else:
        print("❌ PRODUCTION_MODE.env not found!")
        return False

    print("\n🎉 PRODUCTION MODE ACTIVATED!")
    print("✅ Real MongoDB Atlas database enabled")
    print("✅ Full database persistence")
    print("✅ Production-ready configuration")
    print("\nTo start DEEPLICA: python3 START_DEEPLICA.py")
    return True

def switch_to_demo_mode():
    """Switch to demo mode with mock database"""
    print("🎭 SWITCHING TO DEMO MODE...")
    print("=" * 50)

    # Check if demo configuration exists
    if not os.path.exists("DEMO_MODE.env"):
        print("❌ No demo configuration found (DEMO_MODE.env)")
        print("💡 Please create DEMO_MODE.env with mock database configuration")
        return False

    # Backup current .env
    if os.path.exists(".env"):
        shutil.copy(".env", ".env.production.backup")
        print("📁 Backed up production .env to .env.production.backup")

    # Copy demo configuration
    shutil.copy("DEMO_MODE.env", ".env")
    print("✅ Copied DEMO_MODE.env to .env")

    print("\n⚠️ DEMO MODE ACTIVATED!")
    print("🎭 Mock database connection enabled")
    print("⚠️ No data persistence")
    print("⚠️ For development/testing only")
    print("\nTo start DEEPLICA: python3 START_DEEPLICA.py")
    return True

def show_current_mode():
    """Show current database mode"""
    print("🔍 CURRENT DATABASE MODE")
    print("=" * 50)
    
    if not os.path.exists(".env"):
        print("❌ No .env file found")
        return
    
    with open(".env", "r") as f:
        content = f.read()
    
    use_mock = "USE_MOCK_DATABASE=true" in content
    force_mock = "FORCE_MOCK_DATABASE=true" in content
    connection_string = ""
    
    for line in content.split("\n"):
        if line.startswith("MONGODB_CONNECTION_STRING="):
            connection_string = line.split("=", 1)[1]
            break
    
    if force_mock or use_mock or connection_string.startswith("mock://"):
        print("🎭 DEMO MODE ACTIVE")
        print("✅ Mock database enabled")
        print("✅ No external dependencies")
        print(f"🔗 Connection: {connection_string}")
    else:
        print("🏭 PRODUCTION MODE ACTIVE")
        print("🔗 Real MongoDB Atlas connection")
        print("✅ Full database persistence")
        print(f"🔗 Connection: {connection_string[:50]}..." if connection_string else "Not configured")

def main():
    """Main function"""
    if len(sys.argv) != 2:
        print("🔄 DEEPLICA Database Mode Switcher")
        print("=" * 50)
        print("Usage:")
        print("  python3 switch_database_mode.py production # Switch to production mode (default)")
        print("  python3 switch_database_mode.py demo       # Switch to demo mode")
        print("  python3 switch_database_mode.py status     # Show current mode")
        print()
        show_current_mode()
        return 1
    
    mode = sys.argv[1].lower()
    
    if mode == "production":
        success = switch_to_production_mode()
    elif mode == "demo":
        success = switch_to_demo_mode()
    elif mode == "status":
        show_current_mode()
        return 0
    else:
        print(f"❌ Unknown mode: {mode}")
        print("Valid modes: demo, production, status")
        return 1
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
