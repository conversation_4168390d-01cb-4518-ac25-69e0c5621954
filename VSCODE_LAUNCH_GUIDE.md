# 🚀 VS Code Launch Configuration Guide

## 📋 **Overview**

Each Deeplica microservice now runs in its own **named terminal** in VS Code, making it easy to monitor, debug, and manage individual services.

## 🎯 **Launch Options**

### **🎬 Orchestrated Launch (Recommended)**

| **Configuration** | **Description** | **Use Case** |
|-------------------|-----------------|--------------|
| `🎬 START DEEPLICA (Recommended - Orchestrator)` | Single orchestrator manages all services | **Production-like startup** |

**✅ Benefits:**
- ✅ Automatic dependency management
- ✅ Proper startup sequencing
- ✅ Health checks and monitoring
- ✅ Single terminal for orchestrator
- ✅ All services get named terminals

### **🚀 Manual Launch Options**

| **Configuration** | **Description** | **Terminals** |
|-------------------|-----------------|---------------|
| `🚀 All Microservices (Named Terminals - Ordered)` | All services with backend dependency wait | 6 named terminals |
| `🚀 All Microservices (Named Terminals - Simultaneous)` | All services start immediately | 6 named terminals |

### **🔧 Development Options**

| **Configuration** | **Description** | **Use Case** |
|-------------------|-----------------|--------------|
| `🔧 Core Services Only (Backend + Dispatcher)` | Just backend and dispatcher | **Backend development** |
| `🤖 Agents Only (No Backend)` | Just the agent services | **Agent development** |

## 📺 **Named Terminals**

Each service gets its own terminal with a distinctive name:

| **Service** | **Terminal Name** | **Port** |
|-------------|-------------------|----------|
| **🎬 Orchestrator** | `🎬 DEEPLICA-ORCHESTRATOR` | - |
| **🌐 Backend API** | `🌐 DEEPLICA-BACKEND-API` | 8000 |
| **🎯 Dispatcher** | `🎯 DEEPLICA-DISPATCHER` | 8001 |
| **💬 Dialogue Agent** | `💬 DEEPLICA-DIALOGUE-AGENT` | 8002 |
| **🧠 Planner Agent** | `🧠 DEEPLICA-PLANNER-AGENT` | 8003 |
| **📞 Phone Agent** | `📞 DEEPLICA-PHONE-AGENT` | 8004 |
| **🖥️ CLI Terminal** | `🖥️ DEEPLICA-CLI-TERMINAL` | - |

## 🎮 **How to Use**

### **Method 1: VS Code Command Palette**
1. Press `Cmd+Shift+P` (macOS) or `Ctrl+Shift+P` (Windows/Linux)
2. Type "Debug: Select and Start Debugging"
3. Choose your desired configuration

### **Method 2: Debug Panel**
1. Open Debug panel (`Cmd+Shift+D` / `Ctrl+Shift+D`)
2. Select configuration from dropdown
3. Click the green play button

### **Method 3: F5 Quick Start**
1. Press `F5`
2. Select configuration from the list

## 🔧 **Terminal Management**

### **View All Terminals:**
- Click the terminal panel at bottom
- All named terminals will be visible as tabs

### **Switch Between Services:**
- Click terminal tabs to switch between services
- Each service has its own isolated terminal

### **Monitor Specific Service:**
- Click on the service's named terminal
- View logs, errors, and status in real-time

## 🛡️ **Phone Agent Features**

The Phone Agent terminal (`📞 DEEPLICA-PHONE-AGENT`) includes:
- ✅ **Error call prevention** - No more error calls to users
- ✅ **Circuit breaker** - Automatic failure detection
- ✅ **Health checks** - System stability monitoring
- ✅ **Process name** - `DEEPLICA-PHONE-AGENT` in system processes

## 🎯 **Recommended Workflow**

### **For Production Testing:**
1. Use `🎬 START DEEPLICA (Recommended - Orchestrator)`
2. Monitor orchestrator terminal for system status
3. Individual service terminals available for debugging

### **For Development:**
1. Use `🚀 All Microservices (Named Terminals - Ordered)`
2. Each service in its own terminal for easy debugging
3. Backend starts first, others wait for readiness

### **For Debugging Specific Services:**
1. Use individual service configurations
2. Start only the services you need
3. Full control over startup sequence

## 🔍 **Debugging Benefits**

### **Individual Service Monitoring:**
- Each service has dedicated terminal
- Easy to spot errors in specific services
- Clear separation of logs and output

### **Process Management:**
- Each service has distinctive process name
- Easy to identify in system process list
- Targeted killing/restarting of services

### **Development Efficiency:**
- Start only needed services
- Quick iteration on specific components
- Clear visibility into service interactions

## 🚨 **Troubleshooting**

### **If Terminal Names Don't Appear:**
- Restart VS Code
- Ensure you're using VS Code 1.60+ 
- Check that `"terminal"` configuration is properly set

### **If Services Don't Start:**
- Check the individual service terminals for errors
- Verify environment variables are set
- Ensure ports are not already in use

### **If Orchestrator Fails:**
- Check orchestrator terminal for detailed logs
- Verify all dependencies are installed
- Check MongoDB Atlas connection

## 🎉 **Result**

You now have:
- ✅ **Named terminals** for each microservice
- ✅ **Easy service identification** and monitoring
- ✅ **Flexible launch options** for different scenarios
- ✅ **Professional development environment**
- ✅ **Phone error prevention** built-in

**Happy debugging with your organized, named terminal setup!** 🚀
