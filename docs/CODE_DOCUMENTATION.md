# 📝 DEEPLICA V3 CODE DOCUMENTATION

## **Complete Code Reference for Developers**

---

## 🏗️ **Project Structure**

```
deeplica-v3/
├── agents/                     # Microservices
│   ├── backend/               # Backend API Service
│   │   ├── app/
│   │   │   ├── main.py       # FastAPI application entry point
│   │   │   ├── models/       # Pydantic data models
│   │   │   │   ├── mission.py
│   │   │   │   ├── task.py
│   │   │   │   └── response.py
│   │   │   ├── routes/       # API endpoint handlers
│   │   │   │   ├── missions.py
│   │   │   │   ├── health.py
│   │   │   │   └── __init__.py
│   │   │   ├── services/     # Business logic
│   │   │   │   ├── mission_service.py
│   │   │   │   ├── database.py
│   │   │   │   └── __init__.py
│   │   │   └── config.py     # Configuration management
│   │   └── requirements.txt
│   ├── dispatcher/            # Task Orchestration Service
│   │   ├── app/
│   │   │   ├── main.py       # Service entry point
│   │   │   ├── dispatcher.py # Core orchestration logic
│   │   │   ├── task_router.py # Task routing logic
│   │   │   └── models.py     # Data models
│   │   └── requirements.txt
│   ├── dialogue/              # User Interaction Service
│   │   ├── app/
│   │   │   ├── main.py       # Service entry point
│   │   │   ├── dialogue_engine.py # Conversation logic
│   │   │   ├── intent_recognition.py # NLP processing
│   │   │   └── models.py     # Data models
│   │   └── requirements.txt
│   ├── planner/               # Mission Planning Service
│   │   ├── app/
│   │   │   ├── main.py       # Service entry point
│   │   │   ├── planner.py    # Mission planning logic
│   │   │   ├── task_graph.py # Task dependency management
│   │   │   └── models.py     # Data models
│   │   └── requirements.txt
│   ├── phone/                 # Voice Communication Service
│   │   ├── app/
│   │   │   ├── main.py       # Service entry point
│   │   │   ├── twilio_service.py # Twilio integration
│   │   │   ├── call_handler.py # Call management
│   │   │   ├── speech_processor.py # Speech processing
│   │   │   └── models.py     # Data models
│   │   └── requirements.txt
│   └── cli/                   # Command Line Interface
│       ├── terminal_ui.py     # Main CLI application
│       ├── commands/          # CLI command handlers
│       ├── display/           # UI components
│       └── requirements.txt
├── watchdog/                  # System Monitoring Service
│   ├── main.py               # Watchdog entry point
│   ├── monitor.py            # Monitoring logic
│   ├── recovery.py           # Auto-recovery logic
│   └── requirements.txt
├── orchestrator/              # Service Startup Orchestration
│   ├── main.py               # Orchestrator entry point
│   └── service_manager.py    # Service management
├── stop_deeplica/             # Safe System Shutdown
│   ├── main.py               # Stop service entry point
│   └── cleanup.py            # Cleanup utilities
├── shared/                    # Common Libraries
│   ├── models/               # Shared data models
│   │   ├── base.py          # Base model classes
│   │   ├── mission.py       # Mission models
│   │   ├── task.py          # Task models
│   │   └── enums.py         # Enumeration types
│   ├── utils/                # Utility functions
│   │   ├── logging.py       # Logging utilities
│   │   ├── config.py        # Configuration utilities
│   │   ├── http_client.py   # HTTP client utilities
│   │   └── validation.py    # Validation utilities
│   └── config/               # Configuration management
│       ├── settings.py      # Application settings
│       └── database.py      # Database configuration
├── docs/                      # Documentation
├── tests/                     # Test Suites
│   ├── unit/                 # Unit tests
│   ├── integration/          # Integration tests
│   ├── performance/          # Performance tests
│   └── fixtures/             # Test data
├── scripts/                   # Utility Scripts
│   ├── setup.py             # Environment setup
│   ├── deploy.py            # Deployment scripts
│   └── migrate.py           # Database migrations
├── .vscode/                   # VS Code Configuration
│   ├── launch.json          # Debug configurations
│   ├── tasks.json           # Build tasks
│   └── settings.json        # Editor settings
├── requirements.txt           # Main dependencies
├── .env.example              # Environment template
├── .gitignore               # Git ignore rules
└── README.md                # Project documentation
```

---

## 🔧 **Core Service Implementation**

### **Backend API Service**

#### **main.py - Application Entry Point**
```python
"""
Backend API Service - Main Application Entry Point

This module initializes the FastAPI application with all routes,
middleware, and configuration for the Backend API service.
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import uvicorn
import os
import logging

from .routes import missions, health
from .services.database import DatabaseManager
from .config import Settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='[BACKEND-API:%(funcName)s] %(levelname)s | %(message)s'
)
logger = logging.getLogger(__name__)

# Global settings
settings = Settings()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup/shutdown events."""
    # Startup
    logger.info("Starting Backend API service...")
    
    # Initialize database connection
    app.state.db_manager = DatabaseManager(settings.mongodb_connection_string)
    await app.state.db_manager.connect()
    
    logger.info("Backend API service started successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Backend API service...")
    await app.state.db_manager.disconnect()
    logger.info("Backend API service stopped")

# Create FastAPI application
app = FastAPI(
    title="Deeplica V3 Backend API",
    description="Central API gateway and database interface",
    version="3.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(health.router, prefix="", tags=["health"])
app.include_router(missions.router, prefix="/api/v1", tags=["missions"])

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler for unhandled errors."""
    logger.error(f"Unhandled exception: {exc}")
    return HTTPException(status_code=500, detail="Internal server error")

if __name__ == "__main__":
    port = int(os.getenv("PORT", 8000))
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=port,
        reload=True if os.getenv("DEBUG") == "true" else False
    )
```

#### **models/mission.py - Mission Data Models**
```python
"""
Mission Data Models

Defines Pydantic models for mission-related data structures
used throughout the Backend API service.
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class MissionStatus(str, Enum):
    """Mission status enumeration."""
    CREATED = "created"
    PLANNING = "planning"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class MissionPriority(str, Enum):
    """Mission priority enumeration."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"

class MissionRequest(BaseModel):
    """Request model for creating a new mission."""
    user_input: str = Field(..., description="User's natural language input")
    title: Optional[str] = Field(None, description="Mission title")
    description: Optional[str] = Field(None, description="Mission description")
    priority: MissionPriority = Field(MissionPriority.NORMAL, description="Mission priority")
    
    @validator('user_input')
    def validate_user_input(cls, v):
        """Validate user input is not empty."""
        if not v or not v.strip():
            raise ValueError('User input cannot be empty')
        return v.strip()

class TaskData(BaseModel):
    """Task data structure."""
    task_id: str
    task_type: str
    status: str
    task_data: Dict[str, Any]
    dependencies: List[str] = []
    created_at: datetime
    updated_at: Optional[datetime] = None

class Mission(BaseModel):
    """Complete mission model."""
    mission_id: str
    status: MissionStatus
    title: str
    description: Optional[str] = None
    priority: MissionPriority
    user_input: str
    tasks: List[TaskData] = []
    created_at: datetime
    updated_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class MissionResponse(BaseModel):
    """Response model for mission operations."""
    mission_id: str
    status: MissionStatus
    message: str
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
```

#### **services/database.py - Database Management**
```python
"""
Database Management Service

Handles all database operations for the Backend API service,
including connection management, CRUD operations, and error handling.
"""

from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from pymongo.errors import ConnectionFailure, OperationFailure
from typing import Optional, Dict, Any, List
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Manages MongoDB Atlas database connections and operations."""
    
    def __init__(self, connection_string: str, database_name: str = "deeplica-dev"):
        """
        Initialize database manager.
        
        Args:
            connection_string: MongoDB Atlas connection string
            database_name: Name of the database to use
        """
        self.connection_string = connection_string
        self.database_name = database_name
        self.client: Optional[AsyncIOMotorClient] = None
        self.database: Optional[AsyncIOMotorDatabase] = None
    
    async def connect(self) -> bool:
        """
        Establish database connection.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            self.client = AsyncIOMotorClient(
                self.connection_string,
                maxPoolSize=10,
                minPoolSize=1,
                serverSelectionTimeoutMS=5000
            )
            
            # Test connection
            await self.client.admin.command('ping')
            
            self.database = self.client[self.database_name]
            
            # Create indexes
            await self._create_indexes()
            
            logger.info(f"Connected to database: {self.database_name}")
            return True
            
        except ConnectionFailure as e:
            logger.error(f"Database connection failed: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected database error: {e}")
            return False
    
    async def disconnect(self):
        """Close database connection."""
        if self.client:
            self.client.close()
            logger.info("Database connection closed")
    
    async def _create_indexes(self):
        """Create database indexes for optimal performance."""
        try:
            # Mission indexes
            await self.database.missions.create_index("mission_id", unique=True)
            await self.database.missions.create_index([("status", 1), ("created_at", -1)])
            await self.database.missions.create_index("priority")
            
            # Task indexes
            await self.database.tasks.create_index("task_id", unique=True)
            await self.database.tasks.create_index("mission_id")
            await self.database.tasks.create_index([("status", 1), ("created_at", -1)])
            
            logger.info("Database indexes created successfully")
            
        except Exception as e:
            logger.warning(f"Index creation failed: {e}")
    
    async def create_mission(self, mission_data: Dict[str, Any]) -> str:
        """
        Create a new mission in the database.
        
        Args:
            mission_data: Mission data dictionary
            
        Returns:
            Mission ID if successful
            
        Raises:
            OperationFailure: If database operation fails
        """
        try:
            mission_data["created_at"] = datetime.utcnow()
            result = await self.database.missions.insert_one(mission_data)
            
            logger.info(f"Mission created: {mission_data['mission_id']}")
            return mission_data["mission_id"]
            
        except OperationFailure as e:
            logger.error(f"Failed to create mission: {e}")
            raise
    
    async def get_mission(self, mission_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve a mission by ID.
        
        Args:
            mission_id: Mission identifier
            
        Returns:
            Mission data if found, None otherwise
        """
        try:
            mission = await self.database.missions.find_one(
                {"mission_id": mission_id},
                {"_id": 0}  # Exclude MongoDB ObjectId
            )
            return mission
            
        except Exception as e:
            logger.error(f"Failed to retrieve mission {mission_id}: {e}")
            return None
    
    async def update_mission(self, mission_id: str, update_data: Dict[str, Any]) -> bool:
        """
        Update a mission in the database.
        
        Args:
            mission_id: Mission identifier
            update_data: Data to update
            
        Returns:
            True if successful, False otherwise
        """
        try:
            update_data["updated_at"] = datetime.utcnow()
            
            result = await self.database.missions.update_one(
                {"mission_id": mission_id},
                {"$set": update_data}
            )
            
            success = result.modified_count > 0
            if success:
                logger.info(f"Mission updated: {mission_id}")
            else:
                logger.warning(f"No mission found to update: {mission_id}")
                
            return success
            
        except Exception as e:
            logger.error(f"Failed to update mission {mission_id}: {e}")
            return False
    
    async def list_missions(self, limit: int = 50, skip: int = 0) -> List[Dict[str, Any]]:
        """
        List missions with pagination.
        
        Args:
            limit: Maximum number of missions to return
            skip: Number of missions to skip
            
        Returns:
            List of mission data
        """
        try:
            cursor = self.database.missions.find(
                {},
                {"_id": 0}
            ).sort("created_at", -1).skip(skip).limit(limit)
            
            missions = await cursor.to_list(length=limit)
            return missions
            
        except Exception as e:
            logger.error(f"Failed to list missions: {e}")
            return []
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform database health check.
        
        Returns:
            Health status information
        """
        try:
            # Test basic connectivity
            await self.client.admin.command('ping')
            
            # Get database stats
            stats = await self.database.command("dbStats")
            
            return {
                "status": "healthy",
                "database": self.database_name,
                "collections": stats.get("collections", 0),
                "data_size": stats.get("dataSize", 0),
                "storage_size": stats.get("storageSize", 0)
            }
            
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e)
            }
```

---

## 📞 **Phone Service Implementation**

### **twilio_service.py - Twilio Integration**
```python
"""
Twilio Service Integration

Handles all Twilio-related operations including call initiation,
webhook processing, and conversation management.
"""

from twilio.rest import Client
from twilio.twiml import VoiceResponse, Gather
from typing import Dict, Any, Optional
import logging
import os
from datetime import datetime

logger = logging.getLogger(__name__)

class TwilioService:
    """Manages Twilio voice call operations."""
    
    def __init__(self):
        """Initialize Twilio service with credentials."""
        self.account_sid = os.getenv('TWILIO_ACCOUNT_SID')
        self.auth_token = os.getenv('TWILIO_AUTH_TOKEN')
        self.phone_number = os.getenv('TWILIO_PHONE_NUMBER')
        self.webhook_base_url = os.getenv('TWILIO_WEBHOOK_URL')
        
        if not all([self.account_sid, self.auth_token, self.phone_number]):
            raise ValueError("Missing required Twilio credentials")
        
        self.client = Client(self.account_sid, self.auth_token)
        self.active_calls: Dict[str, Any] = {}
    
    async def initiate_call(self, task_id: str, phone_number: str, 
                          initial_message: str) -> Dict[str, Any]:
        """
        Initiate an outbound phone call.
        
        Args:
            task_id: Unique task identifier
            phone_number: Target phone number
            initial_message: Initial message to speak
            
        Returns:
            Call initiation result
        """
        try:
            # Validate phone number format
            if not self._validate_phone_number(phone_number):
                raise ValueError(f"Invalid phone number format: {phone_number}")
            
            # Create call state
            call_state = {
                "task_id": task_id,
                "phone_number": phone_number,
                "initial_message": initial_message,
                "status": "initiating",
                "created_at": datetime.utcnow(),
                "conversation_log": []
            }
            
            self.active_calls[task_id] = call_state
            
            # Initiate Twilio call
            call = self.client.calls.create(
                to=phone_number,
                from_=self.phone_number,
                url=f"{self.webhook_base_url}/voice/{task_id}",
                method='POST',
                timeout=30,
                record=False  # Disable recording for privacy
            )
            
            call_state["call_sid"] = call.sid
            call_state["status"] = "calling"
            
            logger.info(f"Call initiated: {task_id} -> {phone_number}")
            
            return {
                "success": True,
                "task_id": task_id,
                "call_sid": call.sid,
                "status": "calling"
            }
            
        except Exception as e:
            logger.error(f"Failed to initiate call {task_id}: {e}")
            
            if task_id in self.active_calls:
                self.active_calls[task_id]["status"] = "failed"
                self.active_calls[task_id]["error"] = str(e)
            
            return {
                "success": False,
                "task_id": task_id,
                "error": str(e)
            }
    
    def generate_voice_response(self, task_id: str, message: str, 
                              gather_speech: bool = True) -> str:
        """
        Generate TwiML response for voice interaction.
        
        Args:
            task_id: Task identifier
            message: Message to speak
            gather_speech: Whether to gather speech input
            
        Returns:
            TwiML XML response
        """
        call_state = self.active_calls.get(task_id)
        if not call_state:
            logger.error(f"No call state found for task: {task_id}")
            return str(VoiceResponse())
        
        response = VoiceResponse()
        
        # Speak the message
        response.say(
            message,
            language='en-US',
            voice='alice'
        )
        
        # Log the AI message
        call_state["conversation_log"].append({
            "timestamp": datetime.utcnow(),
            "speaker": "AI",
            "message": message
        })
        
        if gather_speech:
            # Gather speech input
            gather = Gather(
                input='speech',
                action=f"{self.webhook_base_url}/process_speech/{task_id}",
                method='POST',
                speech_timeout='auto',
                language='en-US',
                timeout=30
            )
            response.append(gather)
            
            # Fallback for no speech
            response.redirect(f"{self.webhook_base_url}/no_speech/{task_id}")
        else:
            # End the call
            response.hangup()
        
        return str(response)
    
    def process_speech_input(self, task_id: str, speech_result: str) -> str:
        """
        Process speech input from user.
        
        Args:
            task_id: Task identifier
            speech_result: Transcribed speech
            
        Returns:
            TwiML response
        """
        call_state = self.active_calls.get(task_id)
        if not call_state:
            logger.error(f"No call state found for task: {task_id}")
            return str(VoiceResponse())
        
        # Log the human speech
        call_state["conversation_log"].append({
            "timestamp": datetime.utcnow(),
            "speaker": "HUMAN",
            "message": speech_result
        })
        
        logger.info(f"💬 HUMAN → DEEPLICA: {speech_result}")
        
        # Process the speech with AI (placeholder)
        ai_response = self._process_with_ai(speech_result, call_state)
        
        logger.info(f"💬 DEEPLICA → HUMAN: {ai_response}")
        
        # Generate response
        return self.generate_voice_response(task_id, ai_response, gather_speech=True)
    
    def _process_with_ai(self, user_input: str, call_state: Dict[str, Any]) -> str:
        """
        Process user input with AI and generate response.
        
        Args:
            user_input: User's speech input
            call_state: Current call state
            
        Returns:
            AI-generated response
        """
        # This would integrate with the dialogue service
        # For now, return a simple response
        
        if "goodbye" in user_input.lower() or "bye" in user_input.lower():
            call_state["status"] = "completed"
            return "Thank you for your time. Goodbye!"
        
        return f"I heard you say: {user_input}. Is there anything else I can help you with?"
    
    def _validate_phone_number(self, phone_number: str) -> bool:
        """
        Validate phone number format.
        
        Args:
            phone_number: Phone number to validate
            
        Returns:
            True if valid, False otherwise
        """
        # Basic validation - should start with + and contain only digits
        if not phone_number.startswith('+'):
            return False
        
        # Remove + and check if remaining characters are digits
        digits_only = phone_number[1:]
        if not digits_only.isdigit():
            return False
        
        # Check length (international format)
        if len(digits_only) < 10 or len(digits_only) > 15:
            return False
        
        return True
    
    def get_call_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Get current call status.
        
        Args:
            task_id: Task identifier
            
        Returns:
            Call status information
        """
        return self.active_calls.get(task_id)
    
    def end_call(self, task_id: str) -> bool:
        """
        End an active call.
        
        Args:
            task_id: Task identifier
            
        Returns:
            True if successful, False otherwise
        """
        try:
            call_state = self.active_calls.get(task_id)
            if not call_state:
                return False
            
            call_sid = call_state.get("call_sid")
            if call_sid:
                # Update call to completed status
                self.client.calls(call_sid).update(status='completed')
            
            call_state["status"] = "ended"
            call_state["ended_at"] = datetime.utcnow()
            
            logger.info(f"Call ended: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to end call {task_id}: {e}")
            return False
```

---

**This comprehensive code documentation provides detailed implementation examples for the core services. The documentation includes proper error handling, logging, type hints, and follows Python best practices.**
