#!/usr/bin/env python3
"""
🔧 Comprehensive Port Fix - ALL Hardcoded Ports
Scan and fix EVERY hardcoded port in ALL Python files.
NO exceptions - ALL ports must use port manager.
"""

import os
import re
import sys
from pathlib import Path
from typing import List, <PERSON><PERSON>

def find_all_python_files():
    """Find all Python files in the repository"""
    python_files = []
    for root, dirs, files in os.walk('.'):
        # Skip hidden directories and __pycache__
        dirs[:] = [d for d in dirs if not d.startswith('.') and d != '__pycache__']
        
        for file in files:
            if file.endswith('.py'):
                file_path = Path(root) / file
                python_files.append(file_path)
    
    return python_files

def fix_hardcoded_ports():
    """Fix all hardcoded ports in Python files"""
    
    print("🔧 COMPREHENSIVE PORT FIX - Scanning ALL Python files...")
    
    python_files = find_all_python_files()
    print(f"📁 Found {len(python_files)} Python files")
    
    # Comprehensive port replacements
    replacements = [
        # Direct port numbers
        (r'\b8888\b(?!["\'])', 'get_service_port("backend")'),
        (r'\b8001\b(?!["\'])', 'get_service_port("dispatcher")'),
        (r'\b8002\b(?!["\'])', 'get_service_port("dialogue")'),
        (r'\b8003\b(?!["\'])', 'get_service_port("planner")'),
        (r'\b8004\b(?!["\'])', 'get_service_port("phone")'),
        (r'\b8005\b(?!["\'])', 'get_service_port("watchdog")'),
        (r'\b8007\b(?!["\'])', 'get_service_port("web-chat")'),
        (r'\b8008\b(?!["\'])', 'get_service_port("cli")'),
        (r'\b8009\b(?!["\'])', 'get_service_port("twilio-echo-bot")'),
        (r'\b8010\b(?!["\'])', 'get_service_port("webhook-server")'),
        (r'\b4040\b(?!["\'])', 'get_service_port("ngrok-api")'),
        (r'\b8080\b(?!["\'])', 'get_service_port("ngrok-tunnel")'),
        
        # String port numbers
        (r'"8888"', 'str(get_service_port("backend"))'),
        (r'"8001"', 'str(get_service_port("dispatcher"))'),
        (r'"8002"', 'str(get_service_port("dialogue"))'),
        (r'"8003"', 'str(get_service_port("planner"))'),
        (r'"8004"', 'str(get_service_port("phone"))'),
        (r'"8005"', 'str(get_service_port("watchdog"))'),
        (r'"8007"', 'str(get_service_port("web-chat"))'),
        (r'"8008"', 'str(get_service_port("cli"))'),
        (r'"8009"', 'str(get_service_port("twilio-echo-bot"))'),
        (r'"8010"', 'str(get_service_port("webhook-server"))'),
        (r'"4040"', 'str(get_service_port("ngrok-api"))'),
        (r'"8080"', 'str(get_service_port("ngrok-tunnel"))'),
        
        # URL patterns
        (r'http://localhost:8888', 'f"http://localhost:{get_service_port(\'backend\')}"'),
        (r'http://localhost:8001', 'f"http://localhost:{get_service_port(\'dispatcher\')}"'),
        (r'http://localhost:8002', 'f"http://localhost:{get_service_port(\'dialogue\')}"'),
        (r'http://localhost:8003', 'f"http://localhost:{get_service_port(\'planner\')}"'),
        (r'http://localhost:8004', 'f"http://localhost:{get_service_port(\'phone\')}"'),
        (r'http://localhost:8005', 'f"http://localhost:{get_service_port(\'watchdog\')}"'),
        (r'http://localhost:8007', 'f"http://localhost:{get_service_port(\'web-chat\')}"'),
        (r'http://localhost:8008', 'f"http://localhost:{get_service_port(\'cli\')}"'),
        (r'http://localhost:8009', 'f"http://localhost:{get_service_port(\'twilio-echo-bot\')}"'),
        (r'http://localhost:8010', 'f"http://localhost:{get_service_port(\'webhook-server\')}"'),
        (r'http://localhost:4040', 'f"http://localhost:{get_service_port(\'ngrok-api\')}"'),
        (r'http://localhost:8080', 'f"http://localhost:{get_service_port(\'ngrok-tunnel\')}"'),
        
        # Localhost patterns
        (r'localhost:8888', 'f"localhost:{get_service_port(\'backend\')}"'),
        (r'localhost:8001', 'f"localhost:{get_service_port(\'dispatcher\')}"'),
        (r'localhost:8002', 'f"localhost:{get_service_port(\'dialogue\')}"'),
        (r'localhost:8003', 'f"localhost:{get_service_port(\'planner\')}"'),
        (r'localhost:8004', 'f"localhost:{get_service_port(\'phone\')}"'),
        (r'localhost:8005', 'f"localhost:{get_service_port(\'watchdog\')}"'),
        (r'localhost:8007', 'f"localhost:{get_service_port(\'web-chat\')}"'),
        (r'localhost:8008', 'f"localhost:{get_service_port(\'cli\')}"'),
        (r'localhost:8009', 'f"localhost:{get_service_port(\'twilio-echo-bot\')}"'),
        (r'localhost:8010', 'f"localhost:{get_service_port(\'webhook-server\')}"'),
        (r'localhost:4040', 'f"localhost:{get_service_port(\'ngrok-api\')}"'),
        (r'localhost:8080', 'f"localhost:{get_service_port(\'ngrok-tunnel\')}"'),
    ]
    
    files_modified = 0
    total_fixes = 0
    
    for file_path in python_files:
        # Skip our own scripts
        if 'fix_' in file_path.name or 'update_' in file_path.name or 'comprehensive_' in file_path.name:
            continue
            
        fixes = fix_file(file_path, replacements)
        if fixes > 0:
            files_modified += 1
            total_fixes += fixes
    
    print(f"\n📊 COMPREHENSIVE FIX RESULTS:")
    print(f"   📁 Files scanned: {len(python_files)}")
    print(f"   🔧 Files modified: {files_modified}")
    print(f"   ✅ Total fixes: {total_fixes}")
    
    return files_modified, total_fixes

def fix_file(file_path: Path, replacements: List[Tuple[str, str]]) -> int:
    """Fix hardcoded ports in a single file"""
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        original_content = content
        fixes_made = 0
        
        # Check if file needs port manager import
        needs_import = False
        for pattern, _ in replacements:
            if re.search(pattern, content):
                needs_import = True
                break
        
        # Add import if needed
        if needs_import and 'from shared.port_manager import get_service_port' not in content:
            lines = content.split('\n')
            
            # Find import section
            import_pos = 0
            for i, line in enumerate(lines):
                if line.startswith('import ') or line.startswith('from '):
                    import_pos = i + 1
                elif line.strip() == '' and import_pos > 0:
                    break
            
            lines.insert(import_pos, 'from shared.port_manager import get_service_port')
            content = '\n'.join(lines)
            fixes_made += 1
            print(f"✅ {file_path}: Added port manager import")
        
        # Apply replacements
        for pattern, replacement in replacements:
            if re.search(pattern, content):
                # Don't replace in comments
                lines = content.split('\n')
                new_lines = []
                
                for line in lines:
                    if line.strip().startswith('#'):
                        new_lines.append(line)
                        continue
                    
                    if re.search(pattern, line):
                        new_line = re.sub(pattern, replacement, line)
                        if new_line != line:
                            new_lines.append(new_line)
                            fixes_made += 1
                            print(f"✅ {file_path}: Fixed {pattern[:15]}...")
                        else:
                            new_lines.append(line)
                    else:
                        new_lines.append(line)
                
                content = '\n'.join(new_lines)
        
        # Write back if modified
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
        
        return fixes_made
    
    except Exception as e:
        print(f"⚠️ Error processing {file_path}: {e}")
        return 0

if __name__ == "__main__":
    print("🔧 Starting COMPREHENSIVE port fix for ALL Python files...")
    files_modified, total_fixes = fix_hardcoded_ports()
    print(f"\n🎉 COMPREHENSIVE PORT FIX COMPLETED!")
    print(f"✅ Modified {files_modified} files with {total_fixes} fixes")
    print("🔄 ALL hardcoded ports have been replaced with port manager calls")
