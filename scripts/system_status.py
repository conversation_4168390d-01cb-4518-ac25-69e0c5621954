#!/usr/bin/env python3
"""
DEEPLICA System Status and Health Check
Comprehensive monitoring of all services, ports, and external dependencies.
"""

import os
import sys
import time
import subprocess
import requests
import json
from pathlib import Path
from datetime import datetime
from shared.port_manager import get_service_port

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Service definitions
SERVICES = {
    "watchdog": {"port": get_service_port("watchdog"), "critical": True, "description": "System monitoring and auto-recovery"},
    "backend": {"port": 8000, "critical": True, "description": "Core API and database interface"},
    "dispatcher": {"port": get_service_port("dispatcher"), "critical": True, "description": "Mission orchestration service"},
    "web-chat": {"port": get_service_port("web-chat"), "critical": False, "description": "Web interface and admin panel"},
    "dialogue": {"port": get_service_port("dialogue"), "critical": False, "description": "AI dialogue agent"},
    "planner": {"port": get_service_port("planner"), "critical": False, "description": "Mission planning agent"},
    "phone": {"port": get_service_port("phone"), "critical": False, "description": "Phone call agent"},
    "cli": {"port": get_service_port("cli"), "critical": False, "description": "Command line interface"}
}

EXTERNAL_SERVICES = {
    "ngrok": {"port": get_service_port("ngrok-api"), "description": "Tunnel service for webhooks"},
    "mongodb": {"description": "Database service (cloud)"},
    "twilio": {"description": "Phone service API"},
    "gemini": {"description": "AI language model API"}
}

def check_port_status(port):
    """Check if a port is in use"""
    try:
        result = subprocess.run(
            ["lsof", "-ti", f":{port}"],
            capture_output=True,
            text=True
        )
        return result.returncode == 0  # Port is in use if lsof returns 0
    except Exception:
        return False

def check_service_health(port, endpoint="/health", timeout=3):
    """Check service health via HTTP"""
    try:
        url = f"http://localhost:{port}{endpoint}"
        response = requests.get(url, timeout=timeout)
        return response.status_code == 200, response.json() if response.content else {}
    except Exception as e:
        return False, {"error": str(e)}

def check_external_service_health():
    """Check external services health"""
    results = {}
    
    # Check ngrok
    try:
        response = requests.get("http://localhost:get_service_port("ngrok-api")/api/tunnels", timeout=3)
        if response.status_code == 200:
            data = response.json()
            tunnels = data.get("tunnels", [])
            https_tunnel = next((t for t in tunnels if t.get("proto") == "https"), None)
            results["ngrok"] = {
                "status": "healthy",
                "tunnels": len(tunnels),
                "https_url": https_tunnel.get("public_url") if https_tunnel else None
            }
        else:
            results["ngrok"] = {"status": "unhealthy", "error": f"HTTP {response.status_code}"}
    except Exception as e:
        results["ngrok"] = {"status": "unhealthy", "error": str(e)}
    
    # Check MongoDB via backend
    try:
        from shared.port_manager import get_service_port, get_service_host
            backend_url = f"http://{get_service_host('backend')}:{get_service_port('backend')}"
            response = requests.get(f"{backend_url}/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            db_status = health_data.get("database", {})
            results["mongodb"] = {
                "status": "healthy" if db_status.get("status") == "connected" else "unhealthy",
                "details": db_status
            }
        else:
            results["mongodb"] = {"status": "unknown", "error": "Backend not responding"}
    except Exception as e:
        results["mongodb"] = {"status": "unknown", "error": str(e)}
    
    # Check Twilio via phone agent
    try:
        response = requests.post("http://localhost:get_service_port("phone")/test_twilio", timeout=10)
        if response.status_code == 200:
            test_result = response.json()
            results["twilio"] = {
                "status": "healthy" if test_result.get("status") == "success" else "unhealthy",
                "details": test_result
            }
        else:
            results["twilio"] = {"status": "unknown", "error": "Phone agent not responding"}
    except Exception as e:
        results["twilio"] = {"status": "unknown", "error": str(e)}
    
    # Check Gemini via backend
    try:
        response = requests.post(f"{backend_url}/gemini/test", timeout=15)
        if response.status_code == 200:
            test_result = response.json()
            results["gemini"] = {
                "status": "healthy" if test_result.get("status") == "success" else "unhealthy",
                "details": test_result
            }
        else:
            results["gemini"] = {"status": "unknown", "error": "Backend not responding"}
    except Exception as e:
        results["gemini"] = {"status": "unknown", "error": str(e)}
    
    return results

def get_system_resources():
    """Get system resource usage"""
    try:
        import psutil
        
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return {
            "cpu_percent": cpu_percent,
            "memory_percent": memory.percent,
            "memory_available_gb": round(memory.available / (1024**3), 2),
            "disk_percent": disk.percent,
            "disk_free_gb": round(disk.free / (1024**3), 2)
        }
    except Exception as e:
        return {"error": str(e)}

def print_status_header():
    """Print status header"""
    print("=" * 80)
    print("🚀 DEEPLICA SYSTEM STATUS")
    print("=" * 80)
    print(f"📅 Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

def print_service_status():
    """Print status of all DEEPLICA services"""
    print("🔧 DEEPLICA SERVICES")
    print("-" * 40)
    
    all_critical_healthy = True
    total_services = len(SERVICES)
    healthy_services = 0
    
    for service_name, config in SERVICES.items():
        port = config["port"]
        critical = config["critical"]
        description = config["description"]
        
        # Check port status
        port_in_use = check_port_status(port)
        
        # Check service health
        if port_in_use:
            is_healthy, health_data = check_service_health(port)
            if is_healthy:
                status = "🟢 HEALTHY"
                healthy_services += 1
            else:
                status = "🔴 UNHEALTHY"
                if critical:
                    all_critical_healthy = False
        else:
            status = "⚪ OFFLINE"
            if critical:
                all_critical_healthy = False
        
        critical_marker = "🔥" if critical else "  "
        print(f"{critical_marker} {service_name:12} {port:5} {status:12} {description}")
    
    print()
    print(f"📊 Summary: {healthy_services}/{total_services} services healthy")
    if not all_critical_healthy:
        print("⚠️  CRITICAL SERVICES ARE DOWN!")
    print()

def print_external_status():
    """Print status of external services"""
    print("🌐 EXTERNAL SERVICES")
    print("-" * 40)
    
    external_results = check_external_service_health()
    
    for service_name, config in EXTERNAL_SERVICES.items():
        description = config["description"]
        result = external_results.get(service_name, {"status": "unknown"})
        
        status = result["status"]
        if status == "healthy":
            status_icon = "🟢 HEALTHY"
        elif status == "unhealthy":
            status_icon = "🔴 UNHEALTHY"
        else:
            status_icon = "⚪ UNKNOWN"
        
        print(f"   {service_name:12}      {status_icon:12} {description}")
        
        # Show additional details for some services
        if service_name == "ngrok" and "https_url" in result:
            if result["https_url"]:
                print(f"      └─ Tunnel: {result['https_url']}")
            else:
                print(f"      └─ No HTTPS tunnel found")
    
    print()

def print_system_resources():
    """Print system resource usage"""
    print("💻 SYSTEM RESOURCES")
    print("-" * 40)
    
    resources = get_system_resources()
    
    if "error" not in resources:
        cpu = resources["cpu_percent"]
        memory = resources["memory_percent"]
        disk = resources["disk_percent"]
        
        cpu_status = "🟢" if cpu < 70 else "🟡" if cpu < 90 else "🔴"
        memory_status = "🟢" if memory < 70 else "🟡" if memory < 90 else "🔴"
        disk_status = "🟢" if disk < 80 else "🟡" if disk < 95 else "🔴"
        
        print(f"   CPU Usage:    {cpu_status} {cpu:5.1f}%")
        print(f"   Memory Usage: {memory_status} {memory:5.1f}% ({resources['memory_available_gb']} GB free)")
        print(f"   Disk Usage:   {disk_status} {disk:5.1f}% ({resources['disk_free_gb']} GB free)")
    else:
        print(f"   ❌ Error getting system resources: {resources['error']}")
    
    print()

def print_recommendations():
    """Print system recommendations"""
    print("💡 RECOMMENDATIONS")
    print("-" * 40)
    
    # Check for common issues and provide recommendations
    recommendations = []
    
    # Check critical services
    critical_down = []
    for service_name, config in SERVICES.items():
        if config["critical"]:
            port = config["port"]
            if not check_port_status(port):
                critical_down.append(service_name)
    
    if critical_down:
        recommendations.append(f"🔥 Start critical services: {', '.join(critical_down)}")
        recommendations.append("   Run: python3 scripts/start_deeplica_fixed.py")
    
    # Check external services
    external_results = check_external_service_health()
    if external_results.get("ngrok", {}).get("status") != "healthy":
        recommendations.append("🌐 Start ngrok tunnel: ngrok http 8004")
    
    if external_results.get("mongodb", {}).get("status") != "healthy":
        recommendations.append("🍃 Check MongoDB connection in .env file")
    
    if external_results.get("twilio", {}).get("status") != "healthy":
        recommendations.append("📞 Check Twilio credentials in .env file")
    
    if not recommendations:
        recommendations.append("✅ System is running well!")
    
    for rec in recommendations:
        print(f"   {rec}")
    
    print()

def main():
    """Main status check function"""
    print_status_header()
    print_service_status()
    print_external_status()
    print_system_resources()
    print_recommendations()
    
    print("🔄 For continuous monitoring, run: watch -n 5 python3 scripts/system_status.py")
    print("🌐 Access admin interface: http://localhost:get_service_port("web-chat")/admin")

if __name__ == "__main__":
    main()
