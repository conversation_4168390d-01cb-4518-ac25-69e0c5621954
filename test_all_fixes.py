#!/usr/bin/env python3
"""
🧪 Test All Fixes
Comprehensive test to verify:
1. No syntax errors
2. No import errors  
3. Unified logging format compliance
4. Anti-spam logging working
5. Port caching working
6. Browser tab prevention working
"""

import ast
import glob
import os
import sys
import re
import time
from pathlib import Path
from datetime import datetime

def test_syntax_errors():
    """Test for syntax errors across all Python files"""
    print("🔍 Testing syntax errors...")
    
    python_files = glob.glob('**/*.py', recursive=True)
    python_files = [f for f in python_files if '__pycache__' not in f]
    
    errors = []
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            ast.parse(content)
        except SyntaxError as e:
            errors.append((file_path, e.lineno, e.msg))
        except Exception:
            pass
    
    if errors:
        print(f"❌ Found {len(errors)} syntax errors:")
        for file_path, line, msg in errors:
            print(f"  {file_path}:{line} - {msg}")
        return False
    else:
        print("✅ No syntax errors found!")
        return True

def test_logging_format():
    """Test that print statements follow unified logging format"""
    print("🔍 Testing logging format compliance...")
    
    # Pattern for unified format: YYYY-MM-DD HH:MM:SS - [LEVEL] - Svc: service, Mod: module, Cod: routine, msg: message
    unified_pattern = r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2} - \[(?:INFO|ERROR|WARNING|DEBUG|HEALTH)\] - Svc: .+?, Mod: .+?, Cod: .+?, msg: .+'
    
    # Files to check for logging format
    main_files = [
        'backend/app/main.py',
        'dispatcher/app/main.py', 
        'agents/phone/app/main.py',
        'agents/dialogue/app/main.py',
        'shared/port_manager.py'
    ]
    
    non_compliant = []
    
    for file_path in main_files:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Find print statements
            print_lines = re.findall(r'print\(f?"([^"]+)"', content)
            
            for line in print_lines:
                # Skip terminal control sequences
                if '\\033' in line or line.startswith('\033'):
                    continue
                    
                # Check if it matches unified format
                if not re.match(unified_pattern, line):
                    # Allow some exceptions for specific cases
                    if any(exception in line for exception in [
                        'Running in debugger',
                        'Phone Agent completed',
                        'Debugger interrupted'
                    ]):
                        continue
                    non_compliant.append((file_path, line))
    
    if non_compliant:
        print(f"❌ Found {len(non_compliant)} non-compliant logging statements:")
        for file_path, line in non_compliant[:5]:  # Show first 5
            print(f"  {file_path}: {line[:80]}...")
        return False
    else:
        print("✅ All logging statements follow unified format!")
        return True

def test_port_cache():
    """Test port caching functionality"""
    print("🔍 Testing port cache system...")
    
    try:
        from shared.port_cache import get_port_cache, get_cached_port
        
        # Test cache creation
        cache = get_port_cache("test-service")
        
        # Test port caching (should work even if port manager fails)
        try:
            port = cache.get_port("backend")
            print(f"  📊 Cached backend port: {port}")
        except Exception as e:
            print(f"  ⚠️ Port cache test failed: {e}")
            return False
        
        # Test cache stats
        stats = cache.get_cache_stats()
        print(f"  📈 Cache stats: {stats}")
        
        print("✅ Port cache system working!")
        return True
        
    except ImportError as e:
        print(f"❌ Port cache import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Port cache test failed: {e}")
        return False

def test_anti_spam_logging():
    """Test anti-spam logging functionality"""
    print("🔍 Testing anti-spam logging...")
    
    try:
        from shared.unified_logging import should_log_status_change
        
        # Test repetitive status suppression
        first_log = should_log_status_change("TEST-SERVICE", "health_check", "healthy")
        second_log = should_log_status_change("TEST-SERVICE", "health_check", "healthy")
        
        if first_log and not second_log:
            print("✅ Anti-spam logging working correctly!")
            return True
        else:
            print(f"❌ Anti-spam not working: first={first_log}, second={second_log}")
            return False
            
    except ImportError as e:
        print(f"❌ Anti-spam logging import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Anti-spam logging test failed: {e}")
        return False

def test_browser_tab_prevention():
    """Test browser tab prevention JavaScript"""
    print("🔍 Testing browser tab prevention...")
    
    # Check if JavaScript is present in templates
    template_files = ['web_chat/templates/login.html', 'web_chat/templates/chat_new.html']
    
    for template_file in template_files:
        if os.path.exists(template_file):
            with open(template_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'preventMultipleTabs' in content and 'deeplica_deepchat_main' in content:
                print(f"  ✅ Tab prevention found in {template_file}")
            else:
                print(f"  ❌ Tab prevention missing in {template_file}")
                return False
        else:
            print(f"  ⚠️ Template file not found: {template_file}")
    
    print("✅ Browser tab prevention implemented!")
    return True

def test_import_errors():
    """Test for import errors in main modules"""
    print("🔍 Testing import errors...")
    
    # Test key imports
    test_imports = [
        ('shared.port_cache', 'get_port_cache'),
        ('shared.unified_logging', 'get_backend_logger'),
        ('shared.port_manager', 'get_service_port'),
    ]
    
    for module_name, function_name in test_imports:
        try:
            module = __import__(module_name, fromlist=[function_name])
            getattr(module, function_name)
            print(f"  ✅ {module_name}.{function_name}")
        except ImportError as e:
            print(f"  ❌ Import error: {module_name}.{function_name} - {e}")
            return False
        except AttributeError as e:
            print(f"  ❌ Attribute error: {module_name}.{function_name} - {e}")
            return False
    
    print("✅ All imports working!")
    return True

def main():
    """Run all tests"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"{timestamp} - [INFO] - Svc: TEST-SUITE, Mod: test_all_fixes, Cod: main, msg: 🚀 Starting comprehensive test suite")
    print("=" * 80)
    
    tests = [
        ("Syntax Errors", test_syntax_errors),
        ("Import Errors", test_import_errors),
        ("Logging Format", test_logging_format),
        ("Port Cache", test_port_cache),
        ("Anti-Spam Logging", test_anti_spam_logging),
        ("Browser Tab Prevention", test_browser_tab_prevention),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 80)
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    if failed == 0:
        print(f"{timestamp} - [INFO] - Svc: TEST-SUITE, Mod: test_all_fixes, Cod: main, msg: 🎉 All {passed} tests passed!")
        print(f"{timestamp} - [INFO] - Svc: TEST-SUITE, Mod: test_all_fixes, Cod: main, msg: ✅ No syntax errors")
        print(f"{timestamp} - [INFO] - Svc: TEST-SUITE, Mod: test_all_fixes, Cod: main, msg: ✅ Unified logging format compliant")
        print(f"{timestamp} - [INFO] - Svc: TEST-SUITE, Mod: test_all_fixes, Cod: main, msg: ✅ Anti-spam logging working")
        print(f"{timestamp} - [INFO] - Svc: TEST-SUITE, Mod: test_all_fixes, Cod: main, msg: ✅ Port caching optimized")
        print(f"{timestamp} - [INFO] - Svc: TEST-SUITE, Mod: test_all_fixes, Cod: main, msg: ✅ Browser tab prevention implemented")
        return 0
    else:
        print(f"{timestamp} - [ERROR] - Svc: TEST-SUITE, Mod: test_all_fixes, Cod: main, msg: ❌ {failed} tests failed, {passed} passed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
