#!/bin/bash
# =============================================================================
# DEEPLICA SERVICE SCRIPT - ALL PORTS MANAGED DYNAMICALLY
# =============================================================================
# ALL ports are assigned by shared/port_manager.py - NO HARDCODED PORTS
# External services adapt to assigned ports through configuration
# =============================================================================

# 🚀 Deeplica Separate Terminal Launcher (macOS)
# This script launches each Deeplica microservice in its own separate Terminal.app window

set -e

# Get the project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
echo "🚀 Launching Deeplica microservices in separate terminals..."
echo "📁 Project root: $PROJECT_ROOT"

# Function to launch a service in a new terminal
launch_service() {
    local service_name="$1"
    local service_dir="$2"
    local service_command="$3"
    local env_vars="$4"
    
    echo "🚀 Launching $service_name..."
    
    # Create AppleScript to open new terminal window
    osascript <<EOF
tell application "Terminal"
    do script "echo '🚀 Starting $service_name' && cd '$PROJECT_ROOT/$service_dir' && export PYTHONPATH='$PROJECT_ROOT' && $env_vars $service_command"
    set custom title of front window to "$service_name"
end tell
EOF
    
    echo "✅ $service_name launched in separate terminal"
    sleep 2  # Small delay between launches
}

# Function to wait for backend to be ready
wait_for_backend() {
    echo "⏳ Waiting for Backend API to be ready..."
    for i in {1..60}; do
        if curl -s http://localhost:8000/api/v1/health > /dev/null 2>&1; then
            echo "✅ Backend API is ready!"
            return 0
        fi
        sleep 2
    done
    echo "❌ Backend API failed to start within 2 minutes"
    return 1
}

# Function to wait for all services
wait_for_all_services() {
    echo "⏳ Waiting for all services to be ready..."
    
    local services=(
        "Backend API:http://localhost:8000/api/v1/health"
        "Dispatcher:http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('dispatcher')")/health"
        "Dialogue Agent:http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('dialogue')")/health"
        "Planner Agent:http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('planner')")/health"
        "Phone Agent:http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('phone')")/health"
    )
    
    for service_info in "${services[@]}"; do
        local name="${service_info%:*}"
        local url="${service_info#*:}"
        
        for i in {1..30}; do
            if curl -s "$url" > /dev/null 2>&1; then
                echo "✅ $name is ready!"
                break
            fi
            sleep 2
        done
    done
    
    echo "✅ All services check completed!"
}

# Launch services in order

# 1. Backend API (starts first)
launch_service "🌐 DEEPLICA-BACKEND-API" "backend" "python3 -m app.main" "PORT=8000"

# Wait for backend to be ready
if ! wait_for_backend; then
    echo "❌ Cannot continue without backend. Exiting."
    exit 1
fi

# 2. Dispatcher Service
launch_service "🎯 DEEPLICA-DISPATCHER" "dispatcher" "python3 -m app.main" "PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('dispatcher')") WAIT_FOR_BACKEND=true"

# 3. Dialogue Agent
launch_service "💬 DEEPLICA-DIALOGUE-AGENT" "agents/dialogue" "python3 -m app.main" "PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('dialogue')") WAIT_FOR_BACKEND=true"

# 4. Planner Agent
launch_service "🧠 DEEPLICA-PLANNER-AGENT" "agents/planner" "python3 -m app.main" "PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('planner')") WAIT_FOR_BACKEND=true"

# 5. Phone Agent
launch_service "📞 DEEPLICA-PHONE-AGENT" "agents/phone" "python3 -m app.main" "PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('phone')") WAIT_FOR_BACKEND=true"

# Wait for all services to be ready
wait_for_all_services

# 6. CLI Terminal UI (starts last)
launch_service "🖥️ DEEPLICA-CLI-TERMINAL" "cli" "python3 terminal_ui.py" "WAIT_FOR_BACKEND=true"

echo ""
echo "🎉 All 6 services launched in separate Terminal windows!"
echo ""
echo "📋 Launched services:"
echo "  ✅ 🌐 DEEPLICA-BACKEND-API (Port 8000)"
echo "  ✅ 🎯 DEEPLICA-DISPATCHER (Port 8001)"
echo "  ✅ 💬 DEEPLICA-DIALOGUE-AGENT (Port 8002)"
echo "  ✅ 🧠 DEEPLICA-PLANNER-AGENT (Port 8003)"
echo "  ✅ 📞 DEEPLICA-PHONE-AGENT (Port 8004)"
echo "  ✅ 🖥️ DEEPLICA-CLI-TERMINAL"
echo ""
echo "🔍 To monitor services:"
echo "  • Check each Terminal window for service logs"
echo "  • Use 'ps aux | grep DEEPLICA' to see running processes"
echo "  • Each service has its own dedicated Terminal window"
echo ""
echo "🛑 To stop services:"
echo "  • Close individual Terminal windows, or"
echo "  • Run: pkill -f DEEPLICA"
echo ""
echo "✅ All services are now running independently!"
