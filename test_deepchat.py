#!/usr/bin/env python3
"""
🧪 DEEPLICA DEEPCHAT FUNCTIONALITY TEST
Test the DeepChat interface to identify and fix issues
"""

import requests
import json
import time
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

from shared.port_manager import get_service_port

def test_web_chat_service():
    """Test if Web Chat service is running and responding"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🌐 Testing Web Chat service on port {web_chat_port}...")
        
        # Test health endpoint
        response = requests.get(f"http://localhost:{web_chat_port}/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Web Chat service is healthy")
            print(f"📊 Status: {health_data.get('status', 'unknown')}")
            print(f"🔗 Active connections: {health_data.get('connections', {}).get('active', 0)}")
            return True
        else:
            print(f"❌ Web Chat health check failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Web Chat service: {e}")
        return False

def test_chat_interface():
    """Test if the chat interface loads properly"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🌐 Testing chat interface on port {web_chat_port}...")
        
        # Test main page
        response = requests.get(f"http://localhost:{web_chat_port}/", timeout=5)
        if response.status_code == 200:
            print(f"✅ Chat interface loads successfully")
            
            # Check if it contains expected elements
            content = response.text
            if 'sendButton' in content:
                print(f"✅ Send button element found in HTML")
            else:
                print(f"❌ Send button element NOT found in HTML")
                
            if 'messageInput' in content:
                print(f"✅ Message input element found in HTML")
            else:
                print(f"❌ Message input element NOT found in HTML")
                
            if 'connectWebSocket' in content:
                print(f"✅ WebSocket connection code found in HTML")
            else:
                print(f"❌ WebSocket connection code NOT found in HTML")
                
            return True
        else:
            print(f"❌ Chat interface failed to load: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing chat interface: {e}")
        return False

def test_api_endpoints():
    """Test API endpoints"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🌐 Testing API endpoints on port {web_chat_port}...")
        
        # Test inject message endpoint (should exist now)
        test_data = {
            "sender": "test",
            "message": "Test message from phone webhook",
            "source": "test",
            "call_sid": "test123"
        }
        
        response = requests.post(
            f"http://localhost:{web_chat_port}/api/inject_message",
            json=test_data,
            timeout=5
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Inject message API working: {result.get('status', 'unknown')}")
            return True
        else:
            print(f"❌ Inject message API failed: {response.status_code}")
            print(f"📋 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing API endpoints: {e}")
        return False

def main():
    """Run all DeepChat functionality tests"""
    print("🧪 DEEPLICA DEEPCHAT FUNCTIONALITY TEST")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: Web Chat Service
    print("\n🧪 TEST 1: Web Chat Service Health")
    if test_web_chat_service():
        tests_passed += 1
    
    # Test 2: Chat Interface
    print("\n🧪 TEST 2: Chat Interface Loading")
    if test_chat_interface():
        tests_passed += 1
    
    # Test 3: API Endpoints
    print("\n🧪 TEST 3: API Endpoints")
    if test_api_endpoints():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"🧪 TEST RESULTS: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✅ ALL TESTS PASSED - DeepChat should be working!")
    else:
        print("❌ SOME TESTS FAILED - DeepChat needs fixes")
        
        if tests_passed == 0:
            print("🔧 RECOMMENDATION: Check if Web Chat service is running")
        elif tests_passed == 1:
            print("🔧 RECOMMENDATION: Check chat interface HTML/JavaScript")
        elif tests_passed == 2:
            print("🔧 RECOMMENDATION: Check API endpoint implementation")
    
    print("\n🌐 To test manually, open: http://localhost:8007")
    print("📋 Check browser console for JavaScript errors")

if __name__ == '__main__':
    main()
