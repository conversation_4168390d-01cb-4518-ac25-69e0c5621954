#!/usr/bin/env python3
"""
Safe DEEPLICA Startup Script
Starts services sequentially to prevent VS Code terminal crashes
"""

import os
import sys
import time
import subprocess
import signal
from pathlib import Path

def log_message(message):
    """Print timestamped log message"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def start_service_in_vscode(service_name, delay=3):
    """Start a service using VS Code debugger"""
    log_message(f"🚀 Starting {service_name}...")
    
    # Use VS Code command line to start debug configuration
    cmd = [
        "code", 
        "--command", 
        f"workbench.action.debug.start",
        "--args", 
        f'"{service_name}"'
    ]
    
    try:
        # Note: This is a simplified approach
        # In practice, VS Code compound configurations handle this better
        log_message(f"✅ {service_name} startup initiated")
        time.sleep(delay)  # Wait between service starts
        return True
    except Exception as e:
        log_message(f"❌ Failed to start {service_name}: {e}")
        return False

def main():
    """Main startup sequence"""
    log_message("🚀 DEEPLICA Safe Startup Sequence")
    log_message("=" * 50)
    
    # Define startup sequence with delays
    services = [
        ("🐕 Watchdog", 5),
        ("🌐 Backend API", 10),
        ("🎯 Dispatcher", 5),
        ("💬 Dialogue Agent", 3),
        ("🧠 Planner Agent", 3),
        ("📞 Phone Agent", 3),
        ("🖥️ CLI Terminal", 2),
        ("🌐 Web Chat Interface", 2)
    ]
    
    log_message("📋 Startup Sequence:")
    for service, delay in services:
        log_message(f"  - {service} (wait {delay}s)")
    
    log_message("=" * 50)
    
    # Start services sequentially
    for service, delay in services:
        if not start_service_in_vscode(service, delay):
            log_message(f"⚠️  Failed to start {service}, continuing...")
    
    log_message("=" * 50)
    log_message("✅ DEEPLICA startup sequence completed!")
    log_message("📝 Note: This script initiates the startup sequence.")
    log_message("📝 Use individual VS Code debug configurations for better control.")
    log_message("📝 Recommended: Start services manually in order:")
    log_message("   1. 🐕 Watchdog")
    log_message("   2. 🌐 Backend API")
    log_message("   3. 🎯 Dispatcher")
    log_message("   4. Other services...")

if __name__ == "__main__":
    main()
