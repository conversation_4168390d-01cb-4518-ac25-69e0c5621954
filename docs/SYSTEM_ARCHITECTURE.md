# 🏗️ DEEPLICA V3 SYSTEM ARCHITECTURE

## **Microservices Architecture Overview**

Deeplica V3 follows a distributed microservices architecture with 7 core services, each responsible for specific functionality. The system is designed for high availability, scalability, and maintainability.

## 🔧 **Core Services**

### **1. Backend API (Port 8000)**
- **Purpose**: Central API gateway and database interface
- **Technology**: FastAPI, Python 3.11+
- **Responsibilities**:
  - Mission CRUD operations
  - Database connectivity (MongoDB Atlas)
  - Health monitoring endpoints
  - Authentication and authorization
- **Key Endpoints**:
  - `GET /health` - Service health check
  - `POST /api/v1/missions` - Create new mission
  - `GET /api/v1/missions/{id}` - Retrieve mission status
  - `PUT /api/v1/missions/{id}` - Update mission
  - `DELETE /api/v1/missions/{id}` - Delete mission

### **2. Dispatcher (Port 8001)**
- **Purpose**: Task orchestration and routing
- **Technology**: FastAPI, Python 3.11+
- **Responsibilities**:
  - Task distribution to appropriate agents
  - Inter-service communication coordination
  - Task status tracking and callbacks
  - Load balancing and queue management
- **Key Endpoints**:
  - `GET /health` - Service health check
  - `POST /task_callback` - Task completion callbacks
  - `POST /dispatch` - Task distribution
  - `GET /status` - System status overview

### **3. Dialogue Agent (Port 8002)**
- **Purpose**: User interaction and conversation management
- **Technology**: FastAPI, Python 3.11+, Gemini API
- **Responsibilities**:
  - Natural language processing
  - User conversation state management
  - Intent recognition and response generation
  - Multi-turn dialogue handling
- **Key Endpoints**:
  - `GET /health` - Service health check
  - `POST /chat` - Process user messages
  - `GET /conversation/{id}` - Retrieve conversation history
  - `POST /reset/{id}` - Reset conversation state

### **4. Planner Agent (Port 8003)**
- **Purpose**: Mission planning and task decomposition
- **Technology**: FastAPI, Python 3.11+, Gemini API
- **Responsibilities**:
  - Mission analysis and breakdown
  - Task dependency graph creation
  - Resource allocation planning
  - Execution strategy optimization
- **Key Endpoints**:
  - `GET /health` - Service health check
  - `POST /plan` - Create mission plan
  - `GET /plan/{id}` - Retrieve plan details
  - `POST /optimize` - Optimize execution strategy

### **5. Phone Agent (Port 8004)**
- **Purpose**: Voice communication via Twilio
- **Technology**: FastAPI, Python 3.11+, Twilio SDK, ngrok
- **Responsibilities**:
  - Outbound phone call execution
  - Voice conversation handling
  - Speech-to-text and text-to-speech
  - Call state management
- **Key Endpoints**:
  - `GET /health` - Service health check
  - `POST /execute` - Execute phone call task
  - `POST /voice/{task_id}` - Twilio webhook handler
  - `POST /process_speech/{task_id}` - Speech processing
  - `POST /reload_config` - Reload configuration
  - `POST /emergency_stop` - Stop all active calls

### **6. Watchdog (Port 8005)**
- **Purpose**: System monitoring and auto-recovery
- **Technology**: FastAPI, Python 3.11+
- **Responsibilities**:
  - Service health monitoring
  - Auto-recovery and restart
  - External service monitoring (Twilio, ngrok, MongoDB)
  - Performance metrics collection
- **Key Endpoints**:
  - `GET /health` - Service health check
  - `GET /system_status` - Complete system overview
  - `POST /recover/{service}` - Manual service recovery
  - `GET /metrics` - Performance metrics

### **7. CLI Terminal UI (No Port)**
- **Purpose**: Command-line interface for system interaction
- **Technology**: Python 3.11+, Rich library
- **Responsibilities**:
  - User command processing
  - System status display
  - Interactive mission management
  - Debug information presentation

## 🔄 **Service Communication Patterns**

### **Synchronous Communication**
- **HTTP/REST APIs**: Primary communication method
- **Health Checks**: Regular service availability verification
- **Direct Service Calls**: For real-time operations

### **Asynchronous Communication**
- **Task Callbacks**: Completion notifications via webhooks
- **Event Broadcasting**: Status updates across services
- **Queue-based Processing**: For non-blocking operations

## 🗄️ **Data Architecture**

### **Database: MongoDB Atlas**
- **Connection**: Private hosted MongoDB Atlas cluster
- **Access Pattern**: Only Backend API has direct database access
- **Collections**:
  - `missions`: Mission metadata and state
  - `tasks`: Individual work units with dependencies
  - `conversations`: Dialogue history and context
  - `system_logs`: Operational logs and metrics

### **Data Flow**
1. **Mission Creation**: User → CLI/API → Backend → Database
2. **Task Planning**: Backend → Planner → Task Graph
3. **Task Execution**: Dispatcher → Agents → Callbacks
4. **State Updates**: Agents → Dispatcher → Backend → Database

## 🛡️ **Security Architecture**

### **Network Security**
- **Local Development**: All services on localhost
- **Production**: VPC with private subnets
- **External Access**: ngrok tunnel for Twilio webhooks only

### **Authentication & Authorization**
- **API Keys**: Environment variable storage
- **Service-to-Service**: Internal network trust
- **External APIs**: Secure credential management

### **Data Protection**
- **Encryption**: TLS for all external communications
- **Secrets Management**: Environment variables and secure storage
- **Access Control**: Role-based service permissions

## 📊 **Scalability Design**

### **Horizontal Scaling**
- **Stateless Services**: All services designed for horizontal scaling
- **Load Balancing**: Ready for multiple instance deployment
- **Database Scaling**: MongoDB Atlas auto-scaling

### **Performance Optimization**
- **Async Processing**: Non-blocking I/O operations
- **Connection Pooling**: Efficient resource utilization
- **Caching**: Strategic caching for frequently accessed data

## 🔧 **Development Architecture**

### **Code Organization**
```
deeplica-v3/
├── agents/                 # Microservices
│   ├── backend/           # Backend API
│   ├── dispatcher/        # Task orchestration
│   ├── dialogue/          # User interaction
│   ├── planner/           # Mission planning
│   ├── phone/             # Voice communication
│   └── cli/               # Terminal interface
├── watchdog/              # System monitoring
├── orchestrator/          # Service startup
├── stop_deeplica/         # Safe shutdown
├── shared/                # Common libraries
├── docs/                  # Documentation
├── tests/                 # Test suites
└── .vscode/               # VS Code configuration
```

### **Shared Libraries**
- **Common Models**: Shared data structures
- **Utilities**: Reusable helper functions
- **Configuration**: Centralized settings management

## 🚀 **Deployment Architecture**

### **Development Environment**
- **VS Code Integration**: Launch configurations for all services
- **Local Services**: All services run on localhost
- **Hot Reload**: Automatic code reloading during development

### **Production Considerations**
- **Container Ready**: Docker containerization support
- **Cloud Native**: Kubernetes deployment ready
- **Monitoring**: Comprehensive observability stack

## 🔍 **Monitoring & Observability**

### **Health Monitoring**
- **Service Health**: Individual service status endpoints
- **System Health**: Aggregate system status
- **External Dependencies**: Twilio, ngrok, MongoDB monitoring

### **Logging**
- **Structured Logging**: JSON-formatted logs
- **Centralized Collection**: Log aggregation ready
- **Error Tracking**: Comprehensive error reporting

### **Metrics**
- **Performance Metrics**: Response times, throughput
- **Business Metrics**: Mission success rates, call completion
- **System Metrics**: Resource utilization, error rates

---

**Next**: See [API_REFERENCE.md](API_REFERENCE.md) for detailed API documentation
