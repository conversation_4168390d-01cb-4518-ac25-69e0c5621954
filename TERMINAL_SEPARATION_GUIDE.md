# 🖥️ DEEPLICA TERMINAL SEPARATION SYSTEM

## 📋 Overview

The DEEPLICA system implements proper terminal separation to ensure each service has its own dedicated console/terminal for better monitoring and debugging. This document explains how the terminal separation works and how to use it.

## 🎯 Key Requirements

1. **Each service must print to its own terminal** (except CLI messages)
2. **CLI service messages must go to Watchdog terminal** (as specified by user)
3. **Proper terminal naming** for easy identification
4. **VS Code integration** with named terminal tabs

## 🏗️ Architecture

### Terminal Management Components

1. **TerminalManager** (`shared/terminal_manager.py`)
   - Manages individual service terminals
   - Handles process naming and identification
   - Provides unified logging format
   - Tracks terminal statistics

2. **CLIWatchdogRedirector** (`shared/terminal_manager.py`)
   - Redirects CLI messages to Watchdog terminal
   - Buffers messages when Watchdog is unavailable
   - Ensures CLI terminal only shows startup messages

3. **VS Code Launch Configurations** (`.vscode/launch.json`)
   - Each service has `"console": "integratedTerminal"`
   - Services grouped with presentation order
   - Named terminal tabs for easy identification

## 🚀 How It Works

### 1. VS Code Terminal Separation

When using VS Code debug configurations, each service automatically gets its own terminal tab:

```json
{
    "name": "🐕 Watchdog Service",
    "console": "integratedTerminal",
    "presentation": {
        "group": "DEEPLICA-SERVICES",
        "order": 1
    }
}
```

### 2. Service Terminal Management

Each service initializes its terminal manager:

```python
from shared.terminal_manager import get_terminal_manager

# Initialize terminal for this service
terminal = get_terminal_manager("PHONE-AGENT")

# Log messages to this service's terminal
terminal.log_terminal_message("STARTUP", "🚀 Phone Agent starting...")
terminal.log_startup_complete(port=8004)
terminal.log_health_status("HEALTHY", {"connections": 5})
```

### 3. CLI Message Redirection

CLI service messages are automatically redirected to Watchdog terminal:

```python
from shared.terminal_manager import log_cli_to_watchdog

# This goes to Watchdog terminal, not CLI terminal
log_cli_to_watchdog("USER_INPUT", "User entered command: 'status'")
log_cli_to_watchdog("COMMAND_RESULT", "All services operational")
```

## 📊 Terminal Layout

When running the full DEEPLICA system, you'll see these terminal tabs in VS Code:

```
🐕 Watchdog Service          ← Watchdog + CLI messages
🌐 Backend API Service       ← Backend API messages only
🎯 Dispatcher Service        ← Dispatcher messages only
💬 Dialogue Agent Service    ← Dialogue Agent messages only
🧠 Planner Agent Service     ← Planner Agent messages only
📞 Phone Agent Service       ← Phone Agent messages only
🌐 Web Chat Service          ← Web Chat messages only
🖥️ CLI Terminal Service      ← CLI startup messages only
```

## 🔧 Usage Instructions

### Starting Services with Proper Terminal Separation

1. **Use VS Code Compound Launch** (Recommended):
   ```
   🚀 FULL DEEPLICA SYSTEM (Optimized Order)
   ```
   This starts all services in the correct order with separate terminals.

2. **Individual Service Launch**:
   Each service can be started individually and will get its own terminal.

3. **Manual Terminal Launch**:
   ```bash
   # Each command in a separate terminal
   python3 watchdog/main.py
   python3 -m backend.app.main
   python3 -m agents.phone.app.main
   # etc.
   ```

### Monitoring Service Output

- **Service-specific messages**: Check the service's own terminal tab
- **CLI user interactions**: Check the Watchdog terminal
- **Cross-service communication**: Check individual service terminals
- **System health**: Check Watchdog terminal for aggregated status

## 🛠️ Implementation Details

### Process Naming

Each service sets a distinctive process name for easy identification:

```python
# Automatic via TerminalManager
terminal = get_terminal_manager("PHONE-AGENT")
# Sets process name to: DEEPLICA-PHONE-AGENT
```

### Message Formatting

All terminal messages use unified format:

```
2025-07-11 23:04:11 - [INFO] - Svc: PHONE-AGENT, Mod: STARTUP, Cod: log_terminal_message, msg: 🚀 Starting service...
```

### CLI Message Routing

```python
# CLI service initialization
terminal = get_terminal_manager("CLI-TERMINAL")
terminal.log_terminal_message("STARTUP", "CLI starting...")  # Goes to CLI terminal

# User interactions
log_cli_to_watchdog("USER_INPUT", "User command")  # Goes to Watchdog terminal
```

## 🧪 Testing Terminal Separation

Run the terminal separation test:

```bash
python3 test_terminal_separation.py
```

This demonstrates:
- Multiple service terminals running simultaneously
- CLI message redirection to Watchdog
- Terminal statistics and monitoring

## 📋 Best Practices

### For Service Developers

1. **Always use TerminalManager** for service output:
   ```python
   terminal = get_terminal_manager("YOUR-SERVICE")
   terminal.log_terminal_message("CATEGORY", "Your message")
   ```

2. **Use appropriate log categories**:
   - `STARTUP`: Service initialization
   - `SYSTEM`: Configuration and setup
   - `HEALTH`: Status and monitoring
   - `ERROR`: Error conditions
   - `RECOVERY`: Recovery actions
   - `SHUTDOWN`: Service termination

3. **Log startup completion**:
   ```python
   terminal.log_startup_complete(
       port=8004,
       additional_info={"Version": "1.0.0", "Mode": "Production"}
   )
   ```

### For CLI Developers

1. **Startup messages**: Use CLI terminal
   ```python
   terminal.log_terminal_message("STARTUP", "CLI initializing...")
   ```

2. **User interactions**: Use Watchdog redirection
   ```python
   log_cli_to_watchdog("USER_INPUT", "User entered command")
   ```

## 🔍 Troubleshooting

### Common Issues

1. **Messages appearing in wrong terminal**:
   - Ensure using correct terminal manager
   - Check if service is using shared logging handlers

2. **CLI messages not reaching Watchdog**:
   - Verify Watchdog service is running
   - Check network connectivity to Watchdog port

3. **Terminal tabs not named correctly**:
   - Ensure using VS Code launch configurations
   - Check presentation group settings

### Debug Commands

```bash
# Check running DEEPLICA processes
python3 show_processes.py

# Test terminal separation
python3 test_terminal_separation.py

# Check port assignments
python3 -c "from shared.port_manager import get_all_port_assignments; print(get_all_port_assignments())"
```

## ✅ Verification Checklist

- [ ] Each service has its own VS Code terminal tab
- [ ] Terminal tabs are properly named with service icons
- [ ] CLI user interactions appear in Watchdog terminal
- [ ] Service startup messages appear in service terminals
- [ ] Process names are set correctly (DEEPLICA-SERVICE-NAME)
- [ ] Unified logging format is used consistently
- [ ] Terminal statistics are available and accurate

## 🎯 Summary

The DEEPLICA terminal separation system ensures:

1. **Clear separation**: Each service has its own terminal
2. **Proper routing**: CLI messages go to Watchdog as required
3. **Easy monitoring**: Named terminals for quick identification
4. **Unified format**: Consistent logging across all services
5. **VS Code integration**: Seamless development experience

This system provides excellent visibility into system operation while maintaining the required message routing for CLI interactions.
