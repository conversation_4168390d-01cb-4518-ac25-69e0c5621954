### **🧩 v0 Scope**

**Goal:** Terminal-based prototype that handles a single user message, decomposes it into a task graph via Planner Agent, and executes it using Dialogue Agent, with full logging.

---

### **✅ In Scope**

| Component | Purpose |
| ----- | ----- |
| **Terminal UI** | Chat-style CLI interaction |
| **Planner Agent** | Converts user input to task graph (LLM-powered) |
| **Dialogue Agent** | Executes tasks and responds to user |
| **Agent Dispatcher** | Orchestrates task execution |
| **MongoDB** | Stores mission JSON only |
| **Cloud Run** | Deploy backend (FastAPI) |
| **Logging** | Logs all user input, responses, task statuses |

---

### **🚫 Not Included (v0)**

| Item |
| ----- |
| **Frontend UI (Web/App)** |
| **Phone Agent** |
| **Email Agent** |
| **Voice Input/Output** |
| **User authentication or identification** |
| **User memory or long-term data** |
| **Prompt Engineering Agent** |
| **Guardian Agent** |
| **Parallel or conditional tasks** |

---

### **🔧 Tech Stack**

| Layer | Tech |
| ----- | ----- |
| Terminal | Python CLI |
| Backend | FastAPI (Python), deployed on GCP Cloud Run |
| Agents | Planner Agent, Dialogue Agent (Gemini API) |
| Storage | MongoDB Atlas (mission graph only) |
| Logging | GCP logs |

---

### **⚙️ Flow Example**

1. **User input** via terminal (e.g., `"Please book a table for 8 tonight."`)

2. **Planner Agent** (LLM) parses intent & generates JSON task graph

3. **Mission JSON** is saved to MongoDB

4. **Agent Dispatcher** runs through the graph, dispatching tasks

5. **Dialogue Agent** (LLM) handles task prompts and user responses

6. **Output** printed in terminal, all interactions logged

---

### **✅ Core Design Decisions**

* **Planner Agent Prompting:** Using Few-shot examples  
* **Mission object’s structure:** DAG, JSON-structured  
* **Dispatcher Execution Model:** Sequential (one task at a time)  
* **LLM Integration:** Abstraction layer over Gemini API (and others)  
* **Task Statuses:** `pending`, `in_progress`, `done`, `failed`  
* **MongoDB:** One `missions` collection only  
* **Logging:** Multi layers (from debug to error), using `loguru`  
* **Retry Policy:** Retry once on LLM failure  
* **LLM Validation:** Via `pydantic` models  
* **CLI chat:** Using `prompt_toolkit` or similar  
* **Dialogue Agent Prompting:** Chain-of-thought  
* **Session Management:** Loop per session, until keyboard interrupt  
* **Deployment:** GitHub Actions → GCP Cloud Run  
* **Error Handling:** Log and skip faulty tasks  
* **App State:** Fully stateless (no in-memory session tracking)  
* **Dependency Mgmt:** Per-module `requirements.txt`  
* **Codebase Modularity:** Fully modular with interfaces and clean structure

---

