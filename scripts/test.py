#!/usr/bin/env python3
"""
Deeplica v0 Test Script
Comprehensive testing of the system components.
"""

import asyncio
import sys
import os
import json
from typing import Dict, Any

# Add CLI directory to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'cli'))

try:
    from api_client import DeepplicaAPIClient
except ImportError:
    print("❌ Cannot import API client. Make sure CLI dependencies are installed.")
    sys.exit(1)


class DeepplicaTester:
    """Comprehensive test suite for Deeplica v0"""
    
    def __init__(self, api_url: str = None):
        if api_url is None:
            from shared.port_manager import get_service_port, get_service_host
            api_url = f"http://{get_service_host('backend')}:{get_service_port('backend')}"
        self.api_client = DeepplicaAPIClient(api_url)
        self.test_results = []
    
    def log_test(self, test_name: str, success: bool, message: str = ""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message
        })
    
    async def test_backend_health(self) -> bool:
        """Test backend health and connectivity"""
        try:
            health_data = await self.api_client.check_health()
            self.log_test("Backend Health", True, "Backend is healthy and responsive")
            return True
        except Exception as e:
            self.log_test("Backend Health", False, str(e))
            return False
    
    async def test_system_status(self) -> bool:
        """Test system status endpoint"""
        try:
            status = await self.api_client.get_system_status()
            
            # Check required fields
            required_fields = ["status", "services"]
            missing_fields = [field for field in required_fields if field not in status]
            
            if missing_fields:
                self.log_test("System Status", False, f"Missing fields: {missing_fields}")
                return False
            
            self.log_test("System Status", True, f"Status: {status['status']}")
            return True
            
        except Exception as e:
            self.log_test("System Status", False, str(e))
            return False
    
    async def test_mission_creation(self) -> str:
        """Test mission creation"""
        try:
            test_input = "Test mission: book a table for 4 people tonight"
            response = await self.api_client.create_mission(test_input)
            
            # Check response structure
            required_fields = ["mission_id", "status", "data"]
            missing_fields = [field for field in required_fields if field not in response]
            
            if missing_fields:
                self.log_test("Mission Creation", False, f"Missing fields: {missing_fields}")
                return None
            
            mission_id = response["mission_id"]
            self.log_test("Mission Creation", True, f"Created mission: {mission_id[:8]}...")
            return mission_id
            
        except Exception as e:
            self.log_test("Mission Creation", False, str(e))
            return None
    
    async def test_mission_status(self, mission_id: str) -> bool:
        """Test mission status retrieval"""
        try:
            response = await self.api_client.get_mission_status(mission_id)
            
            # Check response structure
            if "data" not in response:
                self.log_test("Mission Status", False, "Missing data field")
                return False
            
            status_data = response["data"]
            required_fields = ["status", "progress"]
            missing_fields = [field for field in required_fields if field not in status_data]
            
            if missing_fields:
                self.log_test("Mission Status", False, f"Missing fields: {missing_fields}")
                return False
            
            self.log_test("Mission Status", True, f"Status: {status_data['status']}")
            return True
            
        except Exception as e:
            self.log_test("Mission Status", False, str(e))
            return False
    
    async def test_mission_continuation(self, mission_id: str) -> bool:
        """Test mission continuation with user response"""
        try:
            test_response = "I prefer Italian cuisine, budget around $50 per person, 7 PM"
            response = await self.api_client.continue_mission(mission_id, test_response)
            
            # Check response structure
            if "data" not in response:
                self.log_test("Mission Continuation", False, "Missing data field")
                return False
            
            self.log_test("Mission Continuation", True, "Mission continued successfully")
            return True
            
        except Exception as e:
            self.log_test("Mission Continuation", False, str(e))
            return False
    
    async def test_mission_listing(self) -> bool:
        """Test mission listing"""
        try:
            missions = await self.api_client.list_missions(limit=5)
            
            if not isinstance(missions, list):
                self.log_test("Mission Listing", False, "Response is not a list")
                return False
            
            self.log_test("Mission Listing", True, f"Retrieved {len(missions)} missions")
            return True
            
        except Exception as e:
            self.log_test("Mission Listing", False, str(e))
            return False
    
    async def test_mission_cancellation(self, mission_id: str) -> bool:
        """Test mission cancellation"""
        try:
            response = await self.api_client.cancel_mission(mission_id)
            
            if response.get("status") != "cancelled":
                self.log_test("Mission Cancellation", False, "Mission not marked as cancelled")
                return False
            
            self.log_test("Mission Cancellation", True, "Mission cancelled successfully")
            return True
            
        except Exception as e:
            self.log_test("Mission Cancellation", False, str(e))
            return False
    
    async def test_error_handling(self) -> bool:
        """Test error handling with invalid requests"""
        try:
            # Test with non-existent mission ID
            try:
                await self.api_client.get_mission_status("non-existent-mission-id")
                self.log_test("Error Handling", False, "Should have failed with non-existent mission")
                return False
            except Exception:
                # This is expected
                pass
            
            # Test with empty mission input
            try:
                await self.api_client.create_mission("")
                self.log_test("Error Handling", False, "Should have failed with empty input")
                return False
            except Exception:
                # This is expected
                pass
            
            self.log_test("Error Handling", True, "Error handling works correctly")
            return True
            
        except Exception as e:
            self.log_test("Error Handling", False, str(e))
            return False
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all tests and return results"""
        print("🧪 Starting Deeplica v0 Test Suite")
        print("=" * 50)
        
        # Test 1: Backend Health
        if not await self.test_backend_health():
            print("\n❌ Backend health check failed. Cannot continue with other tests.")
            return self.get_test_summary()
        
        # Test 2: System Status
        await self.test_system_status()
        
        # Test 3: Mission Creation
        mission_id = await self.test_mission_creation()
        
        if mission_id:
            # Test 4: Mission Status
            await self.test_mission_status(mission_id)
            
            # Test 5: Mission Continuation
            await self.test_mission_continuation(mission_id)
            
            # Test 6: Mission Listing
            await self.test_mission_listing()
            
            # Test 7: Mission Cancellation
            await self.test_mission_cancellation(mission_id)
        
        # Test 8: Error Handling
        await self.test_error_handling()
        
        return self.get_test_summary()
    
    def get_test_summary(self) -> Dict[str, Any]:
        """Get test summary"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        return {
            "total_tests": total_tests,
            "passed": passed_tests,
            "failed": failed_tests,
            "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            "results": self.test_results
        }
    
    def print_summary(self, summary: Dict[str, Any]):
        """Print test summary"""
        print("\n" + "=" * 50)
        print("🧪 Test Summary")
        print("=" * 50)
        print(f"Total Tests: {summary['total_tests']}")
        print(f"Passed: {summary['passed']}")
        print(f"Failed: {summary['failed']}")
        print(f"Success Rate: {summary['success_rate']:.1f}%")
        
        if summary['failed'] > 0:
            print("\n❌ Failed Tests:")
            for result in summary['results']:
                if not result['success']:
                    print(f"  • {result['test']}: {result['message']}")
        
        if summary['success_rate'] == 100:
            print("\n🎉 All tests passed! Deeplica v0 is working correctly.")
        elif summary['success_rate'] >= 80:
            print("\n✅ Most tests passed. System is mostly functional.")
        else:
            print("\n⚠️ Many tests failed. Please check the system configuration.")


async def main():
    """Main test runner"""
    # Get API URL from command line or use default
    if len(sys.argv) > 1:
        api_url = sys.argv[1]
    else:
        from shared.port_manager import get_service_port, get_service_host
        api_url = f"http://{get_service_host('backend')}:{get_service_port('backend')}"
    
    print(f"🔗 Testing Deeplica v0 at {api_url}")
    print()
    
    # Create tester and run tests
    tester = DeepplicaTester(api_url)
    summary = await tester.run_all_tests()
    
    # Print summary
    tester.print_summary(summary)
    
    # Exit with appropriate code
    sys.exit(0 if summary['success_rate'] == 100 else 1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test runner failed: {e}")
        sys.exit(1)
