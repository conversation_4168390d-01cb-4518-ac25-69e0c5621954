"""
Mission model for the orchestration system.
Represents a complete user request broken down into a task graph.
"""

from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from bson import ObjectId

from .constants import MissionStatus, MissionPriority


class Mission(BaseModel):
    """
    Complete mission representing a user request.
    
    A mission contains the original user request, the generated task graph,
    and all execution state and results.
    """
    
    # Core identification
    mission_id: str = Field(..., description="Unique identifier for the mission")
    
    # User request
    user_input: str = Field(..., description="Original user request")
    user_id: Optional[str] = Field(None, description="User identifier (v0: hardcoded users)")
    
    # Mission metadata
    title: str = Field(..., description="Generated title for the mission")
    description: str = Field(..., description="Detailed description of the mission")
    priority: MissionPriority = Field(default=MissionPriority.NORMAL)
    
    # Mission execution
    status: MissionStatus = Field(default=MissionStatus.CREATED)
    
    # Results and context
    final_result: Optional[Dict[str, Any]] = Field(None, description="Final mission result")
    context: Dict[str, Any] = Field(
        default_factory=dict,
        description="Mission-wide context and parameters"
    )
    
    # Execution tracking
    current_task_id: Optional[str] = Field(None, description="Currently executing task")
    completed_tasks: List[str] = Field(
        default_factory=list,
        description="List of completed task IDs"
    )
    failed_tasks: List[str] = Field(
        default_factory=list,
        description="List of failed task IDs"
    )
    
    # Timing
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    started_at: Optional[datetime] = Field(None)
    completed_at: Optional[datetime] = Field(None)
    
    # Error handling
    error_message: Optional[str] = Field(None)
    retry_count: int = Field(default=0)
    max_retries: int = Field(default=3)
    
    # Logging and audit
    execution_log: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="Detailed execution log"
    )
    
    class Config:
        """Pydantic configuration"""
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            ObjectId: str
        }
    
    def add_log_entry(self, event_type: str, message: str, details: Optional[Dict] = None) -> None:
        """Add an entry to the execution log"""
        log_entry = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "event_type": event_type,
            "message": message,
            "details": details or {}
        }
        self.execution_log.append(log_entry)
    
    def mark_started(self) -> None:
        """Mark mission as started"""
        self.status = MissionStatus.IN_PROGRESS
        self.started_at = datetime.now(timezone.utc)
        self.add_log_entry("status_change", "Mission execution started")

    def mark_completed(self, final_result: Dict[str, Any]) -> None:
        """Mark mission as completed"""
        self.status = MissionStatus.COMPLETED
        self.completed_at = datetime.now(timezone.utc)
        self.final_result = final_result
        self.add_log_entry("status_change", "Mission completed successfully", final_result)

    def mark_failed(self, error_message: str) -> None:
        """Mark mission as failed"""
        self.status = MissionStatus.FAILED
        self.completed_at = datetime.now(timezone.utc)
        self.error_message = error_message
        self.add_log_entry("status_change", f"Mission failed: {error_message}")

    def mark_cancelled(self) -> None:
        """Mark mission as cancelled"""
        self.status = MissionStatus.CANCELLED
        self.completed_at = datetime.now(timezone.utc)
        self.add_log_entry("status_change", "Mission cancelled by user")

    def update_task_completion(self, task_id: str, success: bool) -> None:
        """Update mission state when a task completes"""
        if success:
            if task_id not in self.completed_tasks:
                self.completed_tasks.append(task_id)
            if task_id in self.failed_tasks:
                self.failed_tasks.remove(task_id)
            self.add_log_entry("task_completed", f"Task {task_id} completed successfully")
        else:
            if task_id not in self.failed_tasks:
                self.failed_tasks.append(task_id)
            if task_id in self.completed_tasks:
                self.completed_tasks.remove(task_id)
            self.add_log_entry("task_failed", f"Task {task_id} failed")

        # Note: Mission completion check will be handled by the database service
        # which has access to all tasks for this mission
    
    def _get_execution_time_seconds(self) -> Optional[float]:
        """Calculate total execution time in seconds"""
        if not self.started_at:
            return None
        
        end_time = self.completed_at or datetime.now(timezone.utc)
        return (end_time - self.started_at).total_seconds()
