#!/usr/bin/env python3
"""
🔌 Update Service Ports Script
Comprehensive script to replace ALL hardcoded ports in service main files with port manager calls.
This ensures all services use the centralized port management system.
"""

import os
import re
import sys
from pathlib import Path
from typing import Dict, List, <PERSON><PERSON>

def update_service_main_files():
    """Update all service main.py files to use port manager"""
    
    print("🔧 Updating service main files to use centralized port manager...")
    
    # Service files to update
    service_files = [
        "backend/app/main.py",
        "dispatcher/app/main.py", 
        "agents/dialogue/app/main.py",
        "agents/planner/app/main.py",
        "agents/phone/app/main.py",
        "watchdog/main.py",
        "web_chat/main.py",
        "cli/main.py",
        "orchestrator/main.py"
    ]
    
    # Port replacement patterns
    port_replacements = [
        # Backend API (constant port 8888)
        (r'http://localhost:8888/', 'http://localhost:8888/'),  # Keep constant
        (r'"http://localhost:8888"', '"http://localhost:8888"'),  # Keep constant
        (r'localhost:8888', 'localhost:8888'),  # Keep constant
        
        # Dispatcher (8001)
        (r'http://localhost:8001/', f'http://localhost:{{get_service_port("dispatcher")}}/'),
        (r'localhost:8001', f'localhost:{{get_service_port("dispatcher")}}'),
        (r':8001/', f':{{get_service_port("dispatcher")}}/'),
        
        # Dialogue Agent (8002)
        (r'http://localhost:8002/', f'http://localhost:{{get_service_port("dialogue")}}/'),
        (r'localhost:8002', f'localhost:{{get_service_port("dialogue")}}'),
        (r':8002/', f':{{get_service_port("dialogue")}}/'),
        
        # Planner Agent (8003)
        (r'http://localhost:8003/', f'http://localhost:{{get_service_port("planner")}}/'),
        (r'localhost:8003', f'localhost:{{get_service_port("planner")}}'),
        (r':8003/', f':{{get_service_port("planner")}}/'),
        
        # Phone Agent (8004)
        (r'http://localhost:8004/', f'http://localhost:{{get_service_port("phone")}}/'),
        (r'localhost:8004', f'localhost:{{get_service_port("phone")}}'),
        (r':8004/', f':{{get_service_port("phone")}}/'),
        (r'ngrok http 8004', f'ngrok http {{get_service_port("phone")}}'),
        
        # Watchdog (8005)
        (r'http://localhost:8005/', f'http://localhost:{{get_service_port("watchdog")}}/'),
        (r'localhost:8005', f'localhost:{{get_service_port("watchdog")}}'),
        (r':8005/', f':{{get_service_port("watchdog")}}/'),
        
        # Web Chat (8007)
        (r'http://localhost:8007/', f'http://localhost:{{get_service_port("web-chat")}}/'),
        (r'localhost:8007', f'localhost:{{get_service_port("web-chat")}}'),
        (r':8007/', f':{{get_service_port("web-chat")}}/'),
        
        # CLI Terminal (8008)
        (r'http://localhost:8008/', f'http://localhost:{{get_service_port("cli")}}/'),
        (r'localhost:8008', f'localhost:{{get_service_port("cli")}}'),
        (r':8008/', f':{{get_service_port("cli")}}/'),
        
        # Twilio Echo Bot (8009) - CONSTANT
        (r'http://localhost:8009/', 'http://localhost:8009/'),  # Keep constant
        (r'localhost:8009', 'localhost:8009'),  # Keep constant
        
        # Webhook Server (8010) - CONSTANT
        (r'http://localhost:8010/', 'http://localhost:8010/'),  # Keep constant
        (r'localhost:8010', 'localhost:8010'),  # Keep constant
        
        # Ngrok API (4040) - CONSTANT
        (r'http://localhost:4040/', 'http://localhost:4040/'),  # Keep constant
        (r'localhost:4040', 'localhost:4040'),  # Keep constant
        
        # Array patterns for agent ports
        (r'agent_ports = \[8002, 8003, 8004\]', 
         'agent_ports = [get_service_port("dialogue"), get_service_port("planner"), get_service_port("phone")]'),
        (r'\[8002, 8003, 8004\]', 
         '[get_service_port("dialogue"), get_service_port("planner"), get_service_port("phone")]'),
    ]
    
    # Special patterns for hardcoded port assignments in fallback configurations
    fallback_patterns = [
        # Watchdog fallback service definitions
        (r'"port": 8001,', '"port": get_service_port("dispatcher"),'),
        (r'"port": 8002,', '"port": get_service_port("dialogue"),'),
        (r'"port": 8003,', '"port": get_service_port("planner"),'),
        (r'"port": 8004,', '"port": get_service_port("phone"),'),
        (r'"port": 8005,', '"port": get_service_port("watchdog"),'),
        (r'"port": 8007,', '"port": get_service_port("web-chat"),'),
        (r'"port": 8008,', '"port": get_service_port("cli"),'),
        
        # Health URL patterns
        (r'"health_url": "http://localhost:8001/health"', 
         '"health_url": f"http://localhost:{get_service_port(\'dispatcher\')}/health"'),
        (r'"health_url": "http://localhost:8002/health"', 
         '"health_url": f"http://localhost:{get_service_port(\'dialogue\')}/health"'),
        (r'"health_url": "http://localhost:8003/health"', 
         '"health_url": f"http://localhost:{get_service_port(\'planner\')}/health"'),
        (r'"health_url": "http://localhost:8004/health"', 
         '"health_url": f"http://localhost:{get_service_port(\'phone\')}/health"'),
        (r'"health_url": "http://localhost:8007/health"', 
         '"health_url": f"http://localhost:{get_service_port(\'web-chat\')}/health"'),
        (r'"health_url": "http://localhost:8008/health"', 
         '"health_url": f"http://localhost:{get_service_port(\'cli\')}/health"'),
    ]
    
    # Process each service file
    for file_path in service_files:
        full_path = Path(file_path)
        if not full_path.exists():
            print(f"⚠️ File not found: {file_path}")
            continue
            
        try:
            with open(full_path, 'r') as f:
                content = f.read()
            
            original_content = content
            modified = False
            
            # Check if port manager is already imported
            if 'from shared.port_manager import' not in content:
                # Add import if not present
                import_lines = []
                lines = content.split('\n')
                
                # Find the best place to add the import
                for i, line in enumerate(lines):
                    if line.startswith('from shared.') and 'port_manager' not in line:
                        # Add after existing shared imports
                        if i + 1 < len(lines) and not lines[i + 1].startswith('from shared.'):
                            import_lines.append(i + 1)
                            break
                    elif line.startswith('import') and 'shared' not in line:
                        # Add before other imports
                        import_lines.append(i)
                        break
                
                if import_lines:
                    insert_pos = import_lines[0]
                    lines.insert(insert_pos, 'from shared.port_manager import get_service_port, get_service_host')
                    content = '\n'.join(lines)
                    modified = True
                    print(f"✅ {file_path}: Added port manager import")
            
            # Apply port replacements
            for pattern, replacement in port_replacements:
                if re.search(pattern, content):
                    content = re.sub(pattern, replacement, content)
                    modified = True
                    print(f"✅ {file_path}: Replaced pattern {pattern}")
            
            # Apply fallback patterns
            for pattern, replacement in fallback_patterns:
                if re.search(pattern, content):
                    content = re.sub(pattern, replacement, content)
                    modified = True
                    print(f"✅ {file_path}: Updated fallback pattern {pattern}")
            
            # Write back if modified
            if modified:
                with open(full_path, 'w') as f:
                    f.write(content)
                print(f"✅ {file_path}: Updated successfully")
            else:
                print(f"ℹ️ {file_path}: No changes needed")
                
        except Exception as e:
            print(f"❌ Error processing {file_path}: {e}")

if __name__ == "__main__":
    print("🔌 Starting service port updates...")
    update_service_main_files()
    print("✅ Service port updates completed!")
