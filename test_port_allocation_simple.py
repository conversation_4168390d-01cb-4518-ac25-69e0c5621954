#!/usr/bin/env python3
"""
🔌 Simple Port Allocation Test
Test the DHCP-like port allocation without triggering background processes
"""

import sys
import os

# Add shared directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'shared'))

# Import the port manager class directly
from port_manager import DeepLicaPortManager

def test_port_allocation():
    """Test the DHCP-like port allocation system"""
    print("🔌 Testing DHCP-like Port Allocation")
    print("=" * 50)
    
    # Create a fresh port manager instance
    pm = DeepLicaPortManager()
    
    print("\n🔍 Testing SERVICE_ALIASES...")
    print(f"phone -> {pm.SERVICE_ALIASES.get('phone', 'NOT FOUND')}")
    print(f"planner -> {pm.SERVICE_ALIASES.get('planner', 'NOT FOUND')}")
    
    print("\n🔍 Testing RESERVED_PORTS...")
    print(f"PHONE-AGENT -> {pm.RESERVED_PORTS.get('PHONE-AGENT', 'NOT FOUND')}")
    print(f"PLANNER-AGENT -> {pm.RESERVED_PORTS.get('PLANNER-AGENT', 'NOT FOUND')}")
    
    print("\n🔌 Testing port allocation...")
    
    # Test phone agent allocation
    print("\n📞 Allocating port for 'phone'...")
    phone_port = pm.allocate_port('phone')
    print(f"✅ Phone Agent allocated port: {phone_port}")
    
    # Test planner agent allocation
    print("\n🧠 Allocating port for 'planner'...")
    planner_port = pm.allocate_port('planner')
    print(f"✅ Planner Agent allocated port: {planner_port}")
    
    # Test conflict detection
    print("\n🚨 Checking for conflicts...")
    if phone_port == planner_port:
        print("❌ CONFLICT DETECTED: Same port allocated to different services!")
    else:
        print("✅ No conflicts: Different ports allocated")
    
    # Show lease information
    print("\n📋 Port lease information:")
    phone_lease = pm.get_port_lease_info('phone')
    planner_lease = pm.get_port_lease_info('planner')
    print(f"  phone: {phone_lease}")
    print(f"  planner: {planner_lease}")
    
    # Test expected ports
    print("\n🎯 Expected vs Actual:")
    print(f"  Phone Agent - Expected: 8004, Actual: {phone_port}")
    print(f"  Planner Agent - Expected: 8003, Actual: {planner_port}")
    
    if phone_port == 8004 and planner_port == 8003:
        print("✅ SUCCESS: All services got their reserved ports!")
    else:
        print("❌ ISSUE: Services did not get their reserved ports")
    
    print("\n🎉 Port allocation test completed!")

if __name__ == "__main__":
    test_port_allocation()
