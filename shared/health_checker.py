"""
Shared health checker utility for service dependencies
"""

import asyncio
import httpx
import time
from loguru import logger
from typing import Optional


class HealthChecker:
    """Utility to check service health and wait for dependencies"""
    
    def __init__(self, service_name: str = "Unknown"):
        self.service_name = service_name
        
    async def wait_for_service(
        self, 
        url: str, 
        service_name: str = "Backend API",
        max_attempts: int = 60,
        retry_interval: float = 2.0,
        timeout: float = 5.0
    ) -> bool:
        """
        Wait for a service to become healthy
        
        Args:
            url: Health check URL
            service_name: Name of the service for logging
            max_attempts: Maximum number of attempts
            retry_interval: Seconds between attempts
            timeout: Timeout for each request
            
        Returns:
            True if service becomes healthy, False if timeout
        """
        logger.info(f"🔍 {self.service_name} waiting for {service_name} at {url}")
        
        for attempt in range(1, max_attempts + 1):
            try:
                async with httpx.AsyncClient(timeout=timeout) as client:
                    response = await client.get(url)
                    
                    if response.status_code == 200:
                        logger.info(f"✅ {service_name} is healthy! ({attempt}/{max_attempts})")
                        return True
                    else:
                        logger.warning(f"⚠️ {service_name} returned status {response.status_code}")
                        
            except httpx.TimeoutException:
                logger.debug(f"⏱️ Timeout waiting for {service_name} (attempt {attempt}/{max_attempts})")
            except httpx.ConnectError:
                logger.debug(f"🔌 Cannot connect to {service_name} (attempt {attempt}/{max_attempts})")
            except Exception as e:
                logger.debug(f"❌ Error checking {service_name}: {e} (attempt {attempt}/{max_attempts})")
            
            if attempt < max_attempts:
                logger.debug(f"⏳ Retrying in {retry_interval}s...")
                await asyncio.sleep(retry_interval)
        
        logger.error(f"❌ {service_name} failed to become healthy after {max_attempts} attempts")
        return False
    
    def wait_for_service_sync(
        self, 
        url: str, 
        service_name: str = "Backend API",
        max_attempts: int = 60,
        retry_interval: float = 2.0,
        timeout: float = 5.0
    ) -> bool:
        """Synchronous version of wait_for_service"""
        return asyncio.run(self.wait_for_service(
            url, service_name, max_attempts, retry_interval, timeout
        ))
    
    async def check_backend_ready(self, backend_url: str = None) -> bool:
        """Check if the backend API is ready"""
        if backend_url is None:
            from shared.port_manager import get_service_port, get_service_host
            backend_url = f"http://{get_service_host('backend')}:{get_service_port('backend')}"
        return await self.wait_for_service(
            f"{backend_url}/health",
            "Backend API",
            max_attempts=30,  # 1 minute with 2s intervals
            retry_interval=2.0
        )
    
    def check_backend_ready_sync(self, backend_url: str = None) -> bool:
        """Synchronous version of check_backend_ready"""
        return asyncio.run(self.check_backend_ready(backend_url))


# Convenience function for quick health checks
async def wait_for_backend(service_name: str = "Service", backend_url: str = None) -> bool:
    """Quick function to wait for backend to be ready"""
    checker = HealthChecker(service_name)
    return await checker.check_backend_ready(backend_url)


def wait_for_backend_sync(service_name: str = "Service", backend_url: str = None) -> bool:
    """Synchronous version of wait_for_backend"""
    checker = HealthChecker(service_name)
    return checker.check_backend_ready_sync(backend_url)
