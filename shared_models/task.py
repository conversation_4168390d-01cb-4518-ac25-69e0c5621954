"""
Task model for the orchestration system.
Represents individual units of work within missions.
"""

from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from bson import ObjectId

from .constants import TaskStatus, TaskType


class Task(BaseModel):
    """
    Individual task within a mission's task graph.

    Each task represents a single unit of work that can be executed
    by an agent within the mission orchestration system.
    """

    # Core identification
    task_id: str = Field(..., description="Unique identifier for the task")
    mission_id: str = Field(..., description="ID of the mission this task belongs to")
    task_type: TaskType = Field(..., description="Type of task to be executed")
    
    # Task definition
    description: str = Field(..., description="Human-readable description of the task")
    prompt: str = Field(..., description="Detailed prompt for the executing agent")
    
    # Task relationships
    parent_tasks: List[str] = Field(
        default_factory=list,
        description="List of task IDs that must complete before this task can start"
    )
    child_tasks: List[str] = Field(
        default_factory=list,
        description="List of task IDs that will be triggered after this task completes"
    )
    
    # Execution state
    status: TaskStatus = Field(default=TaskStatus.PENDING, description="Current task status")
    assigned_agent: Optional[str] = Field(None, description="Agent responsible for executing this task")
    
    # Results and context
    result: Optional[Dict[str, Any]] = Field(None, description="Task execution result")
    context: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional context and parameters for task execution"
    )
    error_message: Optional[str] = Field(None, description="Error message if task failed")

    # Parameter resolution
    has_unresolved_placeholders: bool = Field(
        default=False,
        description="Whether task contains unresolved parameter placeholders"
    )
    
    # Timing
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    started_at: Optional[datetime] = Field(None, description="When task execution began")
    completed_at: Optional[datetime] = Field(None, description="When task execution finished")
    
    # Retry mechanism
    retry_count: int = Field(default=0, description="Number of retry attempts")
    max_retries: int = Field(default=3, description="Maximum number of retry attempts")
    
    class Config:
        """Pydantic configuration"""
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            ObjectId: str
        }
    
    def can_execute(self, completed_tasks: List[str]) -> bool:
        """
        Check if this task can be executed based on its dependencies.
        
        Args:
            completed_tasks: List of task IDs that have been completed
            
        Returns:
            True if all parent tasks are completed, False otherwise
        """
        if not self.parent_tasks:
            return True
        
        return all(parent_id in completed_tasks for parent_id in self.parent_tasks)
    
    def mark_started(self) -> None:
        """Mark task as started"""
        self.status = TaskStatus.IN_PROGRESS
        self.started_at = datetime.now(timezone.utc)

    def mark_completed(self, result: Dict[str, Any]) -> None:
        """Mark task as completed with result"""
        self.status = TaskStatus.DONE
        self.completed_at = datetime.now(timezone.utc)
        self.result = result

    def mark_failed(self, error_message: str) -> None:
        """Mark task as failed with error"""
        self.status = TaskStatus.FAILED
        self.error_message = error_message
        self.completed_at = datetime.now(timezone.utc)

    def should_retry(self) -> bool:
        """Check if task should be retried based on retry count"""
        return self.retry_count < self.max_retries

    def increment_retry(self) -> None:
        """Increment retry count"""
        self.retry_count += 1

    def get_execution_time_seconds(self) -> Optional[float]:
        """Calculate task execution time in seconds"""
        if not self.started_at:
            return None
        
        end_time = self.completed_at or datetime.now(timezone.utc)
        return (end_time - self.started_at).total_seconds()
