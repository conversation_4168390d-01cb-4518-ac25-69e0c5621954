"""
🔐 STRICT SINGLE-TAB SESSION ENFORCEMENT - Backend Security Module

This module implements server-side tab session binding and validation
to prevent multi-tab session abuse with cryptographic security.

Security Features:
- Tab ID to session token binding
- Redis-based secure session storage
- Comprehensive request validation
- Automatic session invalidation
- Security audit logging
- Rate limiting and CSRF protection

<AUTHOR> Security Team
@version 1.0.0
@date 2025-07-11
"""

import uuid
import time
import json
import hashlib
import secrets
import logging
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from functools import wraps

import redis
from flask import Flask, request, session, jsonify, abort
from werkzeug.security import check_password_hash, generate_password_hash

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - [%(levelname)s] - %(name)s - %(message)s'
)
logger = logging.getLogger('StrictTabSessionManager')

@dataclass
class TabSession:
    """Represents a tab-bound session with security metadata"""
    tab_id: str
    session_token: str
    user_id: str
    username: str
    created_at: datetime
    last_activity: datetime
    ip_address: str
    user_agent: str
    csrf_token: str
    validation_count: int = 0
    security_violations: int = 0

class StrictTabSessionManager:
    """
    🔐 Strict Single-Tab Session Enforcement Manager
    
    Implements cryptographically secure tab-bound sessions with
    comprehensive security validation and audit logging.
    """
    
    def __init__(self, redis_client=None, session_timeout=1800):
        """
        Initialize the strict tab session manager
        
        Args:
            redis_client: Redis client instance (optional, uses in-memory if None)
            session_timeout: Session timeout in seconds (default: 30 minutes)
        """
        self.redis_client = redis_client
        self.session_timeout = session_timeout
        self.in_memory_store = {} if not redis_client else None
        
        # Security configuration
        self.config = {
            'max_validation_failures': 3,
            'rate_limit_window': 300,  # 5 minutes
            'max_requests_per_window': 100,
            'csrf_token_length': 32,
            'session_token_length': 64,
            'audit_log_retention': 86400 * 30  # 30 days
        }
        
        # Rate limiting storage
        self.rate_limit_store = {}
        
        logger.info("🔐 Strict Tab Session Manager initialized")

    def _get_storage_key(self, key_type: str, identifier: str) -> str:
        """Generate storage key with namespace"""
        return f"strict_tab_session:{key_type}:{identifier}"

    def _store_data(self, key: str, data: Dict[str, Any], ttl: int = None) -> None:
        """Store data in Redis or in-memory"""
        if self.redis_client:
            serialized = json.dumps(data, default=str)
            if ttl:
                self.redis_client.setex(key, ttl, serialized)
            else:
                self.redis_client.set(key, serialized)
        else:
            self.in_memory_store[key] = {
                'data': data,
                'expires_at': time.time() + (ttl or self.session_timeout)
            }

    def _get_data(self, key: str) -> Optional[Dict[str, Any]]:
        """Retrieve data from Redis or in-memory"""
        if self.redis_client:
            data = self.redis_client.get(key)
            return json.loads(data) if data else None
        else:
            entry = self.in_memory_store.get(key)
            if entry and entry['expires_at'] > time.time():
                return entry['data']
            elif entry:
                del self.in_memory_store[key]
            return None

    def _delete_data(self, key: str) -> None:
        """Delete data from Redis or in-memory"""
        if self.redis_client:
            self.redis_client.delete(key)
        else:
            self.in_memory_store.pop(key, None)

    def generate_secure_token(self, length: int = 64) -> str:
        """Generate cryptographically secure token"""
        return secrets.token_urlsafe(length)

    def generate_csrf_token(self) -> str:
        """Generate CSRF token"""
        return secrets.token_urlsafe(self.config['csrf_token_length'])

    def validate_tab_id(self, tab_id: str) -> bool:
        """Validate Tab ID format (UUIDv4)"""
        if not tab_id or not isinstance(tab_id, str):
            return False
        
        try:
            # Parse as UUID to validate format
            uuid_obj = uuid.UUID(tab_id, version=4)
            return str(uuid_obj) == tab_id.lower()
        except (ValueError, AttributeError):
            return False

    def create_session(self, tab_id: str, user_id: str, username: str, 
                      ip_address: str, user_agent: str) -> Tuple[str, str]:
        """
        Create a new tab-bound session
        
        Returns:
            Tuple of (session_token, csrf_token)
        """
        if not self.validate_tab_id(tab_id):
            raise ValueError("Invalid Tab ID format")

        # Check for existing sessions for this user
        existing_sessions = self._get_user_sessions(user_id)
        if existing_sessions:
            logger.warning(f"🚨 SECURITY VIOLATION: User {username} attempted multi-tab session")
            self._audit_log('multi_tab_attempt', {
                'user_id': user_id,
                'username': username,
                'tab_id': tab_id,
                'ip_address': ip_address,
                'existing_sessions': len(existing_sessions)
            })
            raise SecurityError("Multi-tab sessions not allowed")

        # Generate secure tokens
        session_token = self.generate_secure_token(self.config['session_token_length'])
        csrf_token = self.generate_csrf_token()
        
        # Create tab session
        now = datetime.now()
        tab_session = TabSession(
            tab_id=tab_id,
            session_token=session_token,
            user_id=user_id,
            username=username,
            created_at=now,
            last_activity=now,
            ip_address=ip_address,
            user_agent=user_agent,
            csrf_token=csrf_token
        )
        
        # Store session mappings
        session_key = self._get_storage_key('session', session_token)
        tab_key = self._get_storage_key('tab', tab_id)
        user_key = self._get_storage_key('user', user_id)
        
        session_data = asdict(tab_session)
        
        self._store_data(session_key, session_data, self.session_timeout)
        self._store_data(tab_key, {'session_token': session_token}, self.session_timeout)
        self._store_data(user_key, {'session_token': session_token, 'tab_id': tab_id}, self.session_timeout)
        
        # Audit log
        self._audit_log('session_created', {
            'user_id': user_id,
            'username': username,
            'tab_id': tab_id[:8] + '...',
            'session_token': session_token[:8] + '...',
            'ip_address': ip_address
        })
        
        logger.info(f"✅ Session created for user {username} with tab {tab_id[:8]}...")
        return session_token, csrf_token

    def validate_session(self, tab_id: str, session_token: str, 
                        ip_address: str, user_agent: str) -> Tuple[bool, Optional[str]]:
        """
        Validate tab-bound session
        
        Returns:
            Tuple of (is_valid, reason_if_invalid)
        """
        try:
            # Validate Tab ID format
            if not self.validate_tab_id(tab_id):
                return False, "Invalid Tab ID format"

            # Get session data
            session_key = self._get_storage_key('session', session_token)
            session_data = self._get_data(session_key)
            
            if not session_data:
                return False, "Session not found"

            # Validate tab binding
            if session_data['tab_id'] != tab_id:
                self._audit_log('tab_mismatch', {
                    'expected_tab_id': session_data['tab_id'][:8] + '...',
                    'received_tab_id': tab_id[:8] + '...',
                    'session_token': session_token[:8] + '...',
                    'ip_address': ip_address
                })
                
                # Invalidate session due to security violation
                self.invalidate_session(session_token, "Tab ID mismatch")
                return False, "Tab ID mismatch - session invalidated"

            # Validate IP address (optional strict mode)
            if session_data.get('ip_address') != ip_address:
                logger.warning(f"⚠️ IP address changed for session {session_token[:8]}...")

            # Update last activity
            session_data['last_activity'] = datetime.now().isoformat()
            session_data['validation_count'] = session_data.get('validation_count', 0) + 1
            
            self._store_data(session_key, session_data, self.session_timeout)
            
            return True, None
            
        except Exception as e:
            logger.error(f"❌ Session validation error: {e}")
            return False, f"Validation error: {str(e)}"

    def invalidate_session(self, session_token: str, reason: str = "Manual invalidation") -> bool:
        """Invalidate a session and clean up all related data"""
        try:
            session_key = self._get_storage_key('session', session_token)
            session_data = self._get_data(session_key)
            
            if not session_data:
                return False

            tab_id = session_data['tab_id']
            user_id = session_data['user_id']
            
            # Clean up all related keys
            tab_key = self._get_storage_key('tab', tab_id)
            user_key = self._get_storage_key('user', user_id)
            
            self._delete_data(session_key)
            self._delete_data(tab_key)
            self._delete_data(user_key)
            
            # Audit log
            self._audit_log('session_invalidated', {
                'user_id': user_id,
                'username': session_data.get('username'),
                'tab_id': tab_id[:8] + '...',
                'session_token': session_token[:8] + '...',
                'reason': reason
            })
            
            logger.info(f"🔒 Session invalidated: {reason}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Session invalidation error: {e}")
            return False

    def _get_user_sessions(self, user_id: str) -> list:
        """Get all active sessions for a user"""
        user_key = self._get_storage_key('user', user_id)
        user_data = self._get_data(user_key)
        return [user_data] if user_data else []

    def check_rate_limit(self, identifier: str, max_requests: int = None, 
                        window: int = None) -> Tuple[bool, int]:
        """
        Check rate limiting for an identifier
        
        Returns:
            Tuple of (is_allowed, remaining_requests)
        """
        max_requests = max_requests or self.config['max_requests_per_window']
        window = window or self.config['rate_limit_window']
        
        now = time.time()
        window_start = now - window
        
        # Clean old entries
        if identifier in self.rate_limit_store:
            self.rate_limit_store[identifier] = [
                timestamp for timestamp in self.rate_limit_store[identifier]
                if timestamp > window_start
            ]
        else:
            self.rate_limit_store[identifier] = []
        
        current_requests = len(self.rate_limit_store[identifier])
        
        if current_requests >= max_requests:
            return False, 0
        
        # Add current request
        self.rate_limit_store[identifier].append(now)
        return True, max_requests - current_requests - 1

    def _audit_log(self, event_type: str, data: Dict[str, Any]) -> None:
        """Log security events for audit purposes"""
        audit_entry = {
            'timestamp': datetime.now().isoformat(),
            'event_type': event_type,
            'data': data
        }
        
        # Store in Redis or log to file
        audit_key = f"audit_log:{int(time.time())}:{secrets.token_hex(8)}"
        
        if self.redis_client:
            self._store_data(audit_key, audit_entry, self.config['audit_log_retention'])
        
        # Also log to standard logger
        logger.info(f"🔍 AUDIT: {event_type} - {json.dumps(data)}")

    def get_session_info(self, session_token: str) -> Optional[Dict[str, Any]]:
        """Get session information for admin purposes"""
        session_key = self._get_storage_key('session', session_token)
        session_data = self._get_data(session_key)
        
        if session_data:
            # Remove sensitive data
            safe_data = session_data.copy()
            safe_data['session_token'] = session_token[:8] + '...'
            safe_data['tab_id'] = session_data['tab_id'][:8] + '...'
            return safe_data
        
        return None

class SecurityError(Exception):
    """Custom exception for security violations"""
    pass

# Flask integration decorators
def require_valid_tab_session(f):
    """Decorator to require valid tab session for Flask routes"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        tab_id = request.headers.get('X-Tab-ID')
        session_token = request.headers.get('X-Session-Token')
        
        if not tab_id or not session_token:
            abort(401, description="Missing tab ID or session token")
        
        # Get session manager from Flask app
        session_manager = getattr(request, 'session_manager', None)
        if not session_manager:
            abort(500, description="Session manager not configured")
        
        # Validate session
        is_valid, reason = session_manager.validate_session(
            tab_id, session_token, 
            request.remote_addr, 
            request.headers.get('User-Agent', '')
        )
        
        if not is_valid:
            abort(401, description=f"Session validation failed: {reason}")
        
        return f(*args, **kwargs)
    
    return decorated_function

def rate_limit(max_requests: int = 100, window: int = 300):
    """Decorator for rate limiting Flask routes"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            identifier = request.remote_addr
            session_manager = getattr(request, 'session_manager', None)
            
            if session_manager:
                allowed, remaining = session_manager.check_rate_limit(
                    identifier, max_requests, window
                )
                
                if not allowed:
                    abort(429, description="Rate limit exceeded")
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator
