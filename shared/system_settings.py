#!/usr/bin/env python3
"""
🔧 System Settings Database Management
Handles storage and retrieval of system configuration in MongoDB
"""

import os
import sys
from typing import Dict, Any, Optional
from datetime import datetime
from shared.port_manager import get_service_port

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.unified_logging import get_backend_logger

# Initialize logger
logger = get_backend_logger()

class SystemSettingsManager:
    """Manages system settings in MongoDB"""
    
    def __init__(self):
        self.collection_name = "System_Settings"
        self.settings_doc_id = "deeplica_system_config"
        
        # Default settings
        self.default_settings = {
            "_id": self.settings_doc_id,
            "host_config": {
                "default_host": "0.0.0.0",
                "localhost": "127.0.0.1",
                "external_host": "0.0.0.0"
            },
            "port_config": {
                # Core Services
                "backend": get_service_port("backend"),
                "dispatcher": get_service_port("dispatcher"),
                "dialogue": get_service_port("dialogue"),
                "planner": get_service_port("planner"),
                "phone": get_service_port("phone"),

                # System Services
                "watchdog": get_service_port("watchdog"),
                # REMOVED: stop-deeplica service no longer exists

                # Web Services
                "web-chat": get_service_port("web-chat"),
                "cli": get_service_port("cli"),

                # External Services
                "twilio-echo-bot": get_service_port("twilio-echo-bot"),
                "webhook": get_service_port("webhook-server"),

                # Development & Utility Services
                "ngrok-api": get_service_port("ngrok-api"),
                "ngrok-tunnel": get_service_port("ngrok-tunnel"),
                # Development & Utility Services (fallback ports for services not in port manager)
                "test-server": 8011,  # TODO: Add to port manager if needed
                "dev-server": 8012,   # TODO: Add to port manager if needed
                "proxy-server": 8013  # TODO: Add to port manager if needed
            },
            "external_services": {
                "mongodb": {
                    "name": "MongoDB Atlas",
                    "connection_string": "mongodb+srv://deeplica-db:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0",
                    "database_name": "deeplica-dev",
                    "enabled": True,
                    "description": "Primary database for missions, tasks, and system data"
                },
                "gemini": {
                    "name": "Google Gemini API",
                    "api_key": os.getenv("GEMINI_API_KEY", ""),
                    "model": "gemini-1.5-flash",
                    "enabled": True,
                    "description": "LLM service for AI conversations and planning"
                },
                "twilio": {
                    "name": "Twilio Phone Service",
                    "account_sid": os.getenv("TWILIO_ACCOUNT_SID", ""),
                    "auth_token": os.getenv("TWILIO_AUTH_TOKEN", ""),
                    "phone_number": os.getenv("TWILIO_PHONE_NUMBER", ""),
                    "webhook_url": os.getenv("TWILIO_WEBHOOK_URL", ""),
                    "enabled": True,
                    "description": "Phone call service for voice interactions"
                },
                "ngrok": {
                    "name": "ngrok Tunnel Service",
                    "auth_token": os.getenv("NGROK_API_KEY", ""),
                    "tunnel_port": get_service_port("ngrok-tunnel"),
                    "api_port": get_service_port("ngrok-api"),
                    "enabled": True,
                    "description": "Secure tunneling for webhook endpoints"
                }
            },
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
    
    def get_database_client(self):
        """Get MongoDB client - RESILIENT to port manager issues"""
        try:
            # Import here to avoid circular imports
            from shared.port_manager import get_localhost, get_service_port

            # Use backend API to access database
            backend_url = f"http://{get_localhost()}:{get_service_port('backend')}"
            return backend_url
        except Exception as e:
            # Don't log as error - this is expected during startup
            logger.debug(f"Cannot get backend URL (likely during startup): {e}", module="SYSTEM-SETTINGS", routine="get_database_client")
            return None
    
    async def load_settings(self) -> Dict[str, Any]:
        """Load system settings from database - RESILIENT to backend unavailability"""
        try:
            backend_url = self.get_database_client()
            if not backend_url:
                logger.debug("Backend not available, using default settings", module="SYSTEM-SETTINGS", routine="load_settings")
                return self.default_settings

            import httpx
            # Use short timeout to avoid hanging during startup
            async with httpx.AsyncClient(timeout=3.0) as client:
                response = await client.get(f"{backend_url}/api/system-settings")
                if response.status_code == 200:
                    settings = response.json()
                    logger.info("System settings loaded from database", module="SYSTEM-SETTINGS", routine="load_settings")
                    return settings
                elif response.status_code == 404:
                    # Settings don't exist, create them (but don't fail if we can't)
                    logger.debug("Settings not found in database, using defaults", module="SYSTEM-SETTINGS", routine="load_settings")
                    return self.default_settings
                else:
                    logger.debug(f"Backend returned {response.status_code}, using default settings", module="SYSTEM-SETTINGS", routine="load_settings")
                    return self.default_settings
        except Exception as e:
            # Don't log as error during startup - backend might not be ready yet
            logger.debug(f"Cannot load settings from backend (likely not ready yet): {e}", module="SYSTEM-SETTINGS", routine="load_settings")
            return self.default_settings
    
    async def save_settings(self, settings: Dict[str, Any]) -> bool:
        """Save system settings to database"""
        try:
            backend_url = self.get_database_client()
            if not backend_url:
                logger.error("Backend not available, cannot save settings", module="SYSTEM-SETTINGS", routine="save_settings")
                return False
            
            # Update timestamp (convert to ISO string for JSON serialization)
            settings["updated_at"] = datetime.utcnow().isoformat()
            
            import httpx
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{backend_url}/api/system-settings",
                    json=settings,
                    timeout=10.0
                )
                if response.status_code in [200, 201]:
                    logger.info("System settings saved to database", module="SYSTEM-SETTINGS", routine="save_settings")
                    return True
                else:
                    logger.error(f"Failed to save settings: {response.status_code}", module="SYSTEM-SETTINGS", routine="save_settings")
                    return False
        except Exception as e:
            logger.error(f"Error saving settings: {e}", module="SYSTEM-SETTINGS", routine="save_settings")
            return False
    
    async def update_host_settings(self, host_config: Dict[str, str]) -> bool:
        """Update host configuration"""
        try:
            settings = await self.load_settings()
            settings["host_config"].update(host_config)
            return await self.save_settings(settings)
        except Exception as e:
            logger.error(f"Error updating host settings: {e}", module="SYSTEM-SETTINGS", routine="update_host_settings")
            return False
    
    async def update_port_settings(self, port_config: Dict[str, int]) -> bool:
        """Update port configuration"""
        try:
            settings = await self.load_settings()
            settings["port_config"].update(port_config)
            return await self.save_settings(settings)
        except Exception as e:
            logger.error(f"Error updating port settings: {e}", module="SYSTEM-SETTINGS", routine="update_port_settings")
            return False
    
    async def get_host_config(self) -> Dict[str, str]:
        """Get current host configuration"""
        try:
            settings = await self.load_settings()
            return settings.get("host_config", self.default_settings["host_config"])
        except Exception as e:
            logger.error(f"Error getting host config: {e}", module="SYSTEM-SETTINGS", routine="get_host_config")
            return self.default_settings["host_config"]
    
    async def get_port_config(self) -> Dict[str, int]:
        """Get current port configuration"""
        try:
            settings = await self.load_settings()
            return settings.get("port_config", self.default_settings["port_config"])
        except Exception as e:
            logger.error(f"Error getting port config: {e}", module="SYSTEM-SETTINGS", routine="get_port_config")
            return self.default_settings["port_config"]
    
    def get_port_config_sync(self) -> Dict[str, int]:
        """Get current port configuration synchronously (for startup)"""
        try:
            # Try to read from local backup file first (available before database)
            from pathlib import Path
            import json

            backup_file = Path("shared/deeplica_port_settings.json")
            if backup_file.exists():
                try:
                    with open(backup_file, 'r') as f:
                        data = json.load(f)
                        port_config = data.get('port_config', {})
                        if port_config:
                            # Ensure Backend API is always 8888
                            port = get_service_port("backend")
                            logger.info("Port config loaded from backup file", module="SYSTEM-SETTINGS", routine="get_port_config_sync")
                            return port_config
                except Exception as e:
                    logger.warning(f"Error reading backup file: {e}", module="SYSTEM-SETTINGS", routine="get_port_config_sync")

            # Fallback to defaults
            port_config = self.default_settings["port_config"].copy()

            # Check for environment overrides (backend port is managed by port manager)
            for service, default_port in port_config.items():
                if service == 'backend':
                    continue  # Backend port is managed by port manager

                env_var = f"{service.upper().replace('-', '_')}_PORT"
                if env_var in os.environ:
                    try:
                        port_config[service] = int(os.environ[env_var])
                    except ValueError:
                        pass

            # Ensure Backend API uses port manager value
            port_config['backend'] = get_service_port("backend")

            return port_config
        except Exception as e:
            logger.error(f"Error getting port config sync: {e}", module="SYSTEM-SETTINGS", routine="get_port_config_sync")
            return self.default_settings["port_config"]

    def get_host_config_sync(self) -> Dict[str, str]:
        """Get current host configuration synchronously"""
        try:
            # Try to read from local backup file first
            from pathlib import Path
            import json

            backup_file = Path("shared/deeplica_host_settings.json")
            if backup_file.exists():
                try:
                    with open(backup_file, 'r') as f:
                        data = json.load(f)
                        host_config = data.get('host_config', {})
                        if host_config:
                            logger.info("Host config loaded from backup file", module="SYSTEM-SETTINGS", routine="get_host_config_sync")
                            return host_config
                except Exception as e:
                    logger.warning(f"Error reading host backup file: {e}", module="SYSTEM-SETTINGS", routine="get_host_config_sync")

            # Fallback to defaults
            host_config = self.default_settings["host_config"].copy()

            # Check for environment overrides
            if "DEFAULT_HOST" in os.environ:
                host_config["default_host"] = os.environ["DEFAULT_HOST"]
            if "EXTERNAL_HOST" in os.environ:
                host_config["external_host"] = os.environ["EXTERNAL_HOST"]

            return host_config
        except Exception as e:
            logger.error(f"Error getting host config sync: {e}", module="SYSTEM-SETTINGS", routine="get_host_config_sync")
            return self.default_settings["host_config"]

    async def reset_to_factory_defaults(self) -> bool:
        """Reset all settings to factory defaults"""
        try:
            logger.info("Resetting system settings to factory defaults", module="SYSTEM-SETTINGS", routine="reset_to_factory_defaults")
            return await self.save_settings(self.default_settings)
        except Exception as e:
            logger.error(f"Error resetting to factory defaults: {e}", module="SYSTEM-SETTINGS", routine="reset_to_factory_defaults")
            return False

    async def get_external_services_config(self) -> Dict[str, Any]:
        """Get external services configuration"""
        try:
            settings = await self.get_settings()
            return settings.get("external_services", self.default_settings["external_services"])
        except Exception as e:
            logger.error(f"Error getting external services config: {e}", module="SYSTEM-SETTINGS", routine="get_external_services_config")
            return self.default_settings["external_services"]

    async def update_external_services_config(self, external_services: Dict[str, Any]) -> bool:
        """Update external services configuration"""
        try:
            settings = await self.get_settings()
            settings["external_services"] = external_services
            settings["updated_at"] = datetime.utcnow().isoformat()

            success = await self.save_settings(settings)
            if success:
                logger.info("External services configuration updated", module="SYSTEM-SETTINGS", routine="update_external_services_config")
            return success
        except Exception as e:
            logger.error(f"Error updating external services config: {e}", module="SYSTEM-SETTINGS", routine="update_external_services_config")
            return False

    def get_external_services_config_sync(self) -> Dict[str, Any]:
        """Get external services configuration synchronously (for startup)"""
        try:
            # Try to read from local backup file first
            from pathlib import Path
            import json

            backup_file = Path("shared/deeplica_external_services.json")
            if backup_file.exists():
                try:
                    with open(backup_file, 'r') as f:
                        data = json.load(f)
                        external_services = data.get('external_services', {})
                        if external_services:
                            logger.info("External services config loaded from backup file", module="SYSTEM-SETTINGS", routine="get_external_services_config_sync")
                            return external_services
                except Exception as e:
                    logger.warning(f"Error reading external services backup file: {e}", module="SYSTEM-SETTINGS", routine="get_external_services_config_sync")

            # Fallback to defaults
            return self.default_settings["external_services"]
        except Exception as e:
            logger.error(f"Error getting external services config sync: {e}", module="SYSTEM-SETTINGS", routine="get_external_services_config_sync")
            return self.default_settings["external_services"]


# Global instance
system_settings = SystemSettingsManager()


# Convenience functions
async def get_system_host_config() -> Dict[str, str]:
    """Get system host configuration"""
    return await system_settings.get_host_config()


async def get_system_port_config() -> Dict[str, int]:
    """Get system port configuration"""
    return await system_settings.get_port_config()


def get_system_port_config_sync() -> Dict[str, int]:
    """Get system port configuration synchronously"""
    return system_settings.get_port_config_sync()


def get_service_host(service_name: str = None) -> str:
    """Get the host for a specific service or default host"""
    try:
        # Get host config synchronously
        host_config = system_settings.get_host_config_sync()

        # Return appropriate host based on service
        if service_name and service_name.lower() in ['web', 'web-chat', 'webchat']:
            return host_config.get("external_host", "0.0.0.0")
        else:
            return host_config.get("default_host", "0.0.0.0")
    except Exception as e:
        # Fallback to safe default
        return "0.0.0.0"


def get_service_host_sync() -> str:
    """Get default service host synchronously"""
    return get_service_host()


async def update_system_host_config(host_config: Dict[str, str]) -> bool:
    """Update system host configuration"""
    return await system_settings.update_host_settings(host_config)


async def update_system_port_config(port_config: Dict[str, int]) -> bool:
    """Update system port configuration"""
    return await system_settings.update_port_settings(port_config)


async def reset_system_to_factory_defaults() -> bool:
    """Reset system settings to factory defaults"""
    return await system_settings.reset_to_factory_defaults()


async def get_external_services_config() -> Dict[str, Any]:
    """Get external services configuration"""
    return await system_settings.get_external_services_config()


async def update_external_services_config(external_services: Dict[str, Any]) -> bool:
    """Update external services configuration"""
    return await system_settings.update_external_services_config(external_services)


def get_external_services_config_sync() -> Dict[str, Any]:
    """Get external services configuration synchronously"""
    return system_settings.get_external_services_config_sync()
