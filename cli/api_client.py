"""
API Client for Deeplica v0 Backend
Handles HTTP communication with the FastAPI backend.
"""

from typing import Dict, Any, List, Optional
import json
# Loguru logger removed to keep CLI terminal completely clean

try:
    import httpx
    HTTPX_AVAILABLE = True
except ImportError:
    HTTPX_AVAILABLE = False
    print(f"[CLI-TERMINAL:main] SYSTEM | ❌ httpx not available. Please install: pip install httpx")


class MissionNotFoundError(Exception):
    """Raised when a mission is not found (404 error)"""
    pass


class DeepplicaAPIClient:
    """
    Async HTTP client for communicating with the Deeplica v0 backend.
    
    Provides methods for all API endpoints with proper error handling
    and response formatting.
    """
    
    def __init__(self, base_url: str = None):
        if base_url is None:
            from shared.port_manager import get_service_port, get_service_host
            base_url = f"http://{get_service_host('backend')}:{get_service_port('backend')}"
        self.base_url = base_url.rstrip('/')
        self.api_base = f"{self.base_url}/api/v1"
        self.timeout = 30.0  # 30 second timeout
        
        if not HTTPX_AVAILABLE:
            raise ImportError("httpx is required. Install with: pip install httpx")
    
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Make HTTP request to the API with bulletproof error handling"""
        url = f"{self.api_base}{endpoint}"
        # Debug logging removed to keep CLI terminal clean

        try:
            # Use bulletproof timeout and connection settings
            timeout = httpx.Timeout(self.timeout, connect=5.0, read=10.0, write=10.0, pool=5.0)
            limits = httpx.Limits(max_connections=1, max_keepalive_connections=0)

            async with httpx.AsyncClient(timeout=timeout, limits=limits) as client:
                try:
                    if method.upper() == "GET":
                        response = await client.get(url, params=params)
                    elif method.upper() == "POST":
                        response = await client.post(url, json=data, params=params)
                    elif method.upper() == "DELETE":
                        response = await client.delete(url, params=params)
                    else:
                        raise ValueError(f"Unsupported HTTP method: {method}")

                    # Check for HTTP errors
                    response.raise_for_status()

                    # Parse JSON response with error handling
                    try:
                        result = response.json()
                        return result
                    except (ValueError, TypeError, json.JSONDecodeError) as e:
                        raise Exception(f"Invalid response format from server: {e}")

                except httpx.ConnectError as e:
                    raise Exception(f"Cannot connect to backend (port not open): {e}")
                except httpx.TimeoutException as e:
                    raise Exception(f"Request timed out (backend slow/unavailable): {e}")
                except httpx.ReadTimeout as e:
                    raise Exception(f"Read timeout (backend processing): {e}")
                except httpx.WriteTimeout as e:
                    raise Exception(f"Write timeout: {e}")
                except httpx.PoolTimeout as e:
                    raise Exception(f"Connection pool timeout: {e}")
                except httpx.HTTPStatusError as e:
                    if e.response.status_code == 404:
                        raise MissionNotFoundError("Mission not found (404)")
                    elif e.response.status_code == 500:
                        try:
                            error_detail = e.response.json().get("detail", "Internal server error")
                        except (ValueError, TypeError, json.JSONDecodeError):
                            error_detail = "Internal server error"
                        raise Exception(f"Server error: {error_detail}")
                    elif e.response.status_code == 503:
                        raise Exception("Backend service unavailable (still starting up)")
                    else:
                        try:
                            error_text = e.response.text
                        except Exception:
                            error_text = "Unknown error"
                        raise Exception(f"HTTP {e.response.status_code}: {error_text}")
                except httpx.RequestError as e:
                    raise Exception(f"Request error: {e}")
                except OSError as e:
                    raise Exception(f"Network/socket error: {e}")
                except Exception as e:
                    raise Exception(f"Unexpected HTTP error: {e}")

        except ImportError as e:
            raise Exception(f"HTTP client not available: {e}")
        except Exception as e:
            # Re-raise if it's already our custom exception
            if str(e).startswith(("Cannot connect", "Request timed out", "HTTP", "Server error", "Network")):
                raise
            else:
                raise Exception(f"Unexpected error making request: {e}")
    
    async def check_health(self) -> Dict[str, Any]:
        """Check if the backend is healthy and accessible with bulletproof error handling"""
        try:
            # First try the /ready endpoint to ensure backend is fully initialized
            try:
                ready_response = await self._make_request("GET", "/ready")
                if isinstance(ready_response, dict) and ready_response.get("status") == "ready":
                    # Backend is fully ready
                    return {"status": "ready", "ready": ready_response}
                else:
                    raise Exception("Backend not fully ready yet")
            except Exception as ready_error:
                # If /ready fails, try basic health check
                try:
                    health_response = await self._make_request("GET", "/health")
                    if isinstance(health_response, dict):
                        return {"status": "healthy", "health": health_response}
                    else:
                        raise Exception("Invalid health response format")
                except Exception as health_error:
                    # Try root health endpoint as fallback
                    try:
                        timeout = httpx.Timeout(self.timeout, connect=5.0, read=10.0, write=10.0, pool=5.0)
                        limits = httpx.Limits(max_connections=1, max_keepalive_connections=0)

                        async with httpx.AsyncClient(timeout=timeout, limits=limits) as client:
                            response = await client.get(f"{self.base_url}/health")
                            response.raise_for_status()

                            try:
                                health_data = response.json()
                                if isinstance(health_data, dict):
                                    return {"status": "healthy", "health": health_data}
                                else:
                                    raise Exception("Invalid health response format")
                            except (ValueError, TypeError, json.JSONDecodeError) as json_error:
                                raise Exception(f"Invalid JSON in health response: {json_error}")

                    except httpx.ConnectError as e:
                        raise Exception(f"Cannot connect to backend (port not open): {e}")
                    except httpx.TimeoutException as e:
                        raise Exception(f"Health check timed out: {e}")
                    except httpx.HTTPStatusError as e:
                        if e.response.status_code == 503:
                            raise Exception("Backend service unavailable (still starting up)")
                        else:
                            raise Exception(f"Health check HTTP error {e.response.status_code}")
                    except Exception as e:
                        raise Exception(f"Health check failed: {e}")

        except Exception as e:
            # Re-raise with context if it's already our custom exception
            if any(keyword in str(e) for keyword in ["Cannot connect", "timed out", "HTTP error", "unavailable", "not ready"]):
                raise
            else:
                raise Exception(f"Backend health check failed: {e}")
    
    async def create_mission(self, user_input: str, user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Create a new mission from user input.

        Args:
            user_input: The user's request
            user_id: Optional user identifier

        Returns:
            Mission creation response
        """
        request_data = {
            "user_input": user_input,
            "user_id": user_id
        }

        result = await self._make_request("POST", "/missions", data=request_data)
        return result
    
    async def continue_mission(
        self,
        mission_id: str,
        user_response: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Continue mission execution with optional user response.
        
        Args:
            mission_id: ID of the mission to continue
            user_response: Optional user response
            
        Returns:
            Mission continuation response
        """
        request_data = {
            "user_response": user_response
        }

        return await self._make_request(
            "POST",
            f"/missions/{mission_id}/continue",
            data=request_data
        )
    
    async def get_mission_status(self, mission_id: str) -> Dict[str, Any]:
        """
        Get current status of a mission.
        
        Args:
            mission_id: ID of the mission
            
        Returns:
            Mission status response
        """
        return await self._make_request("GET", f"/missions/{mission_id}")
    
    async def list_missions(
        self,
        status: Optional[str] = None,
        user_id: Optional[str] = None,
        limit: int = 20,
        skip: int = 0
    ) -> List[Dict[str, Any]]:
        """
        List missions with optional filtering.
        
        Args:
            status: Filter by mission status
            user_id: Filter by user ID
            limit: Maximum number of missions to return
            skip: Number of missions to skip
            
        Returns:
            List of mission summaries
        """
        params = {
            "limit": limit,
            "skip": skip
        }
        
        if status:
            params["status"] = status
        if user_id:
            params["user_id"] = user_id
        
        return await self._make_request("GET", "/missions", params=params)
    
    async def cancel_mission(self, mission_id: str) -> Dict[str, Any]:
        """
        Cancel an active mission.
        
        Args:
            mission_id: ID of the mission to cancel
            
        Returns:
            Cancellation response
        """
        return await self._make_request("DELETE", f"/missions/{mission_id}")
    
    async def get_system_status(self) -> Dict[str, Any]:
        """
        Get system status and health information.
        
        Returns:
            System status response
        """
        return await self._make_request("GET", "/system/status")
    
    async def get_system_metrics(self) -> Dict[str, Any]:
        """
        Get system metrics and usage statistics.
        
        Returns:
            System metrics response
        """
        return await self._make_request("GET", "/system/metrics")


# Utility functions for CLI usage





