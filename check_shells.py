#!/usr/bin/env python3
"""
Check available shells and their paths on macOS
"""

import os
import subprocess
import sys

def check_shell_exists(shell_path):
    """Check if a shell exists and is executable"""
    try:
        return os.path.exists(shell_path) and os.access(shell_path, os.X_OK)
    except:
        return False

def get_shell_version(shell_path):
    """Get shell version"""
    try:
        result = subprocess.run([shell_path, '--version'], 
                              capture_output=True, text=True, timeout=5)
        return result.stdout.strip().split('\n')[0] if result.returncode == 0 else "Unknown"
    except:
        return "Error getting version"

def main():
    print("🔍 Checking Shell Configuration for VS Code")
    print("=" * 50)
    
    # Common shell locations on macOS
    shells_to_check = [
        "/bin/bash",
        "/bin/zsh", 
        "/bin/sh",
        "/usr/bin/bash",
        "/usr/bin/zsh",
        "/usr/local/bin/bash",
        "/usr/local/bin/zsh",
        "/opt/homebrew/bin/bash",
        "/opt/homebrew/bin/zsh"
    ]
    
    print("📋 Available Shells:")
    working_shells = []
    
    for shell in shells_to_check:
        if check_shell_exists(shell):
            version = get_shell_version(shell)
            print(f"✅ {shell} - {version}")
            working_shells.append(shell)
        else:
            print(f"❌ {shell} - Not found or not executable")
    
    print("\n" + "=" * 50)
    print("🎯 Recommended VS Code Terminal Settings:")
    
    if working_shells:
        primary_shell = working_shells[0]
        print(f"""
{{
    "terminal.integrated.defaultProfile.osx": "primary",
    "terminal.integrated.profiles.osx": {{
        "primary": {{
            "path": "{primary_shell}"
        }}
    }}
}}""")
    else:
        print("❌ No working shells found! This is unusual for macOS.")
    
    print("\n" + "=" * 50)
    print("🔧 Current Environment:")
    print(f"Python executable: {sys.executable}")
    print(f"Current shell (SHELL): {os.environ.get('SHELL', 'Not set')}")
    print(f"PATH: {os.environ.get('PATH', 'Not set')}")

if __name__ == "__main__":
    main()
