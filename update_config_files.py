#!/usr/bin/env python3
"""
🔧 Update Configuration Files Script
Updates all .env and configuration files to reference the centralized port manager.
This ensures all configuration files are consistent with the port management system.
"""

import os
import re
from pathlib import Path
from typing import Dict, List

def update_env_files():
    """Update all .env files to use port manager references"""
    
    print("🔧 Updating configuration files to reference centralized port manager...")
    
    # Configuration files to update
    config_files = [
        "DEMO_MODE.env",
        "PRODUCTION_MODE.env", 
        ".env.bak"
    ]
    
    # Comments to add explaining port management
    port_manager_comment = """
# ============================================================================
# PORT CONFIGURATION - CENTRALLY MANAGED
# ============================================================================
# IMPORTANT: All ports are managed by shared/port_manager.py
# 
# CONSTANT PORTS (never change):
#   - Backend API: 8888 (required for external integrations)
#   - Twilio Echo Bot: 8009 (webhook compatibility)
#   - Webhook Server: 8010 (external webhook compatibility)
#   - Ngrok API: 4040 (standard ngrok API port)
#   - Ngrok Tunnel: 8080 (default tunnel port)
#
# CONFIGURABLE PORTS (can be changed via admin interface):
#   - All other service ports are dynamically assigned
#
# To change configurable ports:
#   1. Use the admin interface in the web chat
#   2. Or modify shared/deeplica_port_settings.json
#   3. Restart all services for changes to take effect
# ============================================================================
"""
    
    # URL patterns that need updating
    url_patterns = [
        # Service URLs - use port manager references in comments
        (r'DISPATCHER_URL=http://localhost:8001', 
         'DISPATCHER_URL=http://localhost:8001  # Port managed by port_manager.py'),
        (r'DIALOGUE_AGENT_URL=http://localhost:8002', 
         'DIALOGUE_AGENT_URL=http://localhost:8002  # Port managed by port_manager.py'),
        (r'PLANNER_AGENT_URL=http://localhost:8003', 
         'PLANNER_AGENT_URL=http://localhost:8003  # Port managed by port_manager.py'),
        (r'PHONE_AGENT_URL=http://localhost:8004', 
         'PHONE_AGENT_URL=http://localhost:8004  # Port managed by port_manager.py'),
        (r'BACKEND_URL=http://localhost:8888', 
         'BACKEND_URL=http://localhost:8888  # CONSTANT PORT - never changes'),
        
        # Individual port assignments - add comments
        (r'BACKEND_API_PORT=8888$', 
         'BACKEND_API_PORT=8888  # CONSTANT - Backend API must always be on 8888'),
        (r'DISPATCHER_PORT=8001$', 
         'DISPATCHER_PORT=8001  # Managed by port_manager.py - configurable'),
        (r'DIALOGUE_AGENT_PORT=8002$', 
         'DIALOGUE_AGENT_PORT=8002  # Managed by port_manager.py - configurable'),
        (r'PLANNER_AGENT_PORT=8003$', 
         'PLANNER_AGENT_PORT=8003  # Managed by port_manager.py - configurable'),
        (r'PHONE_AGENT_PORT=8004$', 
         'PHONE_AGENT_PORT=8004  # Managed by port_manager.py - configurable'),
        (r'WATCHDOG_PORT=8005$', 
         'WATCHDOG_PORT=8005  # Managed by port_manager.py - configurable'),
        (r'WEB_CHAT_PORT=8007$', 
         'WEB_CHAT_PORT=8007  # Managed by port_manager.py - configurable'),
        (r'CLI_TERMINAL_PORT=8008$', 
         'CLI_TERMINAL_PORT=8008  # Managed by port_manager.py - configurable'),
        (r'TWILIO_ECHO_BOT_PORT=8009$', 
         'TWILIO_ECHO_BOT_PORT=8009  # CONSTANT - Twilio webhook compatibility'),
        (r'WEBHOOK_SERVER_PORT=8010$', 
         'WEBHOOK_SERVER_PORT=8010  # CONSTANT - External webhook compatibility'),
        
        # External service ports
        (r'NGROK_API_PORT=4040$', 
         'NGROK_API_PORT=4040  # CONSTANT - Standard ngrok API port'),
        (r'NGROK_TUNNEL_PORT=8080$', 
         'NGROK_TUNNEL_PORT=8080  # CONSTANT - Default tunnel port'),
        
        # Development ports
        (r'TEST_SERVER_PORT=8011$', 
         'TEST_SERVER_PORT=8011  # Managed by port_manager.py - configurable'),
        (r'DEV_SERVER_PORT=8012$', 
         'DEV_SERVER_PORT=8012  # Managed by port_manager.py - configurable'),
        (r'PROXY_SERVER_PORT=8013$', 
         'PROXY_SERVER_PORT=8013  # Managed by port_manager.py - configurable'),
    ]
    
    # Process each configuration file
    for file_path in config_files:
        full_path = Path(file_path)
        if not full_path.exists():
            print(f"⚠️ File not found: {file_path}")
            continue
            
        try:
            with open(full_path, 'r') as f:
                content = f.read()
            
            original_content = content
            modified = False
            
            # Add port manager comment if not present
            if "PORT CONFIGURATION - CENTRALLY MANAGED" not in content:
                # Find the best place to insert the comment
                lines = content.split('\n')
                insert_pos = 0
                
                # Look for existing port configuration section
                for i, line in enumerate(lines):
                    if 'Service Port Configuration' in line or 'PORT' in line.upper():
                        insert_pos = i
                        break
                    elif 'BACKEND_API_PORT' in line or 'DISPATCHER_PORT' in line:
                        insert_pos = i
                        break
                
                if insert_pos > 0:
                    lines.insert(insert_pos, port_manager_comment.strip())
                    content = '\n'.join(lines)
                    modified = True
                    print(f"✅ {file_path}: Added port manager documentation")
            
            # Apply URL pattern updates
            for pattern, replacement in url_patterns:
                if re.search(pattern, content):
                    content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
                    modified = True
                    print(f"✅ {file_path}: Updated pattern {pattern}")
            
            # Write back if modified
            if modified:
                with open(full_path, 'w') as f:
                    f.write(content)
                print(f"✅ {file_path}: Updated successfully")
            else:
                print(f"ℹ️ {file_path}: No changes needed")
                
        except Exception as e:
            print(f"❌ Error processing {file_path}: {e}")

def create_port_reference_guide():
    """Create a reference guide for port management"""
    
    guide_content = """# DEEPLICA PORT MANAGEMENT REFERENCE

## Overview
All ports in the DEEPLICA system are managed centrally through `shared/port_manager.py`.

## Port Categories

### CONSTANT PORTS (Never Change)
These ports are fixed for external service compatibility:
- **Backend API**: 8888 (required for external integrations)
- **Twilio Echo Bot**: 8009 (Twilio webhook compatibility)
- **Webhook Server**: 8010 (external webhook compatibility)
- **Ngrok API**: 4040 (standard ngrok API port)
- **Ngrok Tunnel**: 8080 (default tunnel port)

### CONFIGURABLE PORTS (Can Be Changed)
These ports can be modified via admin interface:
- **Dispatcher**: 8001 (default)
- **Dialogue Agent**: 8002 (default)
- **Planner Agent**: 8003 (default)
- **Phone Agent**: 8004 (default)
- **Watchdog**: 8005 (default)
- **Web Chat**: 8007 (default)
- **CLI Terminal**: 8008 (default)
- **Test Server**: 8011 (default)
- **Dev Server**: 8012 (default)
- **Proxy Server**: 8013 (default)

## How to Change Ports

### Method 1: Admin Interface
1. Open the web chat interface
2. Navigate to the admin panel
3. Go to "Service Manager" section
4. Edit the port assignments
5. Restart all services

### Method 2: Direct Configuration
1. Edit `shared/deeplica_port_settings.json`
2. Update the port assignments
3. Restart all services

### Method 3: Environment Variables
Set environment variables to override defaults:
- `DISPATCHER_PORT=8001`
- `DIALOGUE_AGENT_PORT=8002`
- etc.

## Important Notes
- Backend API port (8888) cannot be changed
- External service ports (Twilio, Ngrok) should not be changed
- Always restart all services after changing ports
- Port conflicts are automatically detected and resolved
"""
    
    try:
        with open("PORT_MANAGEMENT_GUIDE.md", 'w') as f:
            f.write(guide_content)
        print("✅ Created PORT_MANAGEMENT_GUIDE.md")
    except Exception as e:
        print(f"❌ Error creating port guide: {e}")

if __name__ == "__main__":
    print("🔧 Starting configuration file updates...")
    update_env_files()
    create_port_reference_guide()
    print("✅ Configuration file updates completed!")
