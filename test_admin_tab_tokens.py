#!/usr/bin/env python3
"""
🔧 ADMIN TAB-SPECIFIC TOKEN SYSTEM TEST

Tests the admin page tab-specific token system that:
1. Uses the same tab token system as chat
2. Handles navigation from chat to admin
3. Prevents URL copying between browser tabs
4. Works with the admin icon in chat
"""

import requests
import json
import time
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

from shared.port_manager import get_service_port

def test_admin_login_flow():
    """Test the complete login to admin flow"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔧 Testing admin login flow on port {web_chat_port}...")
        
        # Create session
        session = requests.Session()
        
        # Step 1: Login
        login_data = {'username': 'admin', 'password': 'admin123'}
        
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
        
        print(f"✅ Login successful")
        
        # Step 2: Access admin (should redirect to setup)
        admin_response = session.get(
            f"http://localhost:{web_chat_port}/admin",
            allow_redirects=True,
            timeout=10
        )
        
        if admin_response.status_code != 200:
            print(f"❌ Admin access failed: {admin_response.status_code}")
            return False
        
        # Check if we got setup mode
        final_url = admin_response.url
        if 'setup=1' in final_url:
            print(f"✅ Admin setup mode detected in URL: {final_url}")
        else:
            print(f"⚠️ No admin setup mode in URL: {final_url}")
        
        # Check if page contains admin setup functionality
        content = admin_response.text
        if 'setupAdminTabToken' in content or 'admin_tab_' in content:
            print(f"✅ Admin setup functionality found in page")
        else:
            print(f"❌ Admin setup functionality not found")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing admin login flow: {e}")
        return False

def test_admin_tab_token_api():
    """Test tab token API for admin pages"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔐 Testing admin tab token API on port {web_chat_port}...")
        
        # Login first
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login failed for admin API test")
            return False
        
        # Test admin tab token generation API
        admin_tab_id = f"admin_tab_{int(time.time())}_test"
        
        api_response = session.post(
            f"http://localhost:{web_chat_port}/api/generate-tab-token",
            json={"tab_id": admin_tab_id},
            timeout=10
        )
        
        print(f"📋 Admin API Response Status: {api_response.status_code}")
        
        if api_response.status_code == 200:
            result = api_response.json()
            
            if result.get('success'):
                print(f"✅ Admin tab token API working")
                print(f"🔗 Tab ID: {result['tab_id']}")
                print(f"🔗 Token: {result['tab_token'][:20]}...")
                print(f"🔗 Page Type: {result.get('page_type', 'unknown')}")
                
                # Check if it detects admin context
                if 'admin' in result.get('page_type', '') or 'admin' in result.get('secure_url', ''):
                    print(f"✅ Admin context detected correctly")
                else:
                    print(f"⚠️ Admin context not detected")
                
                return True
            else:
                print(f"❌ Admin API returned success=false: {result}")
                return False
        else:
            print(f"❌ Admin API failed with status {api_response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing admin tab token API: {e}")
        return False

def test_chat_to_admin_navigation():
    """Test navigation from chat to admin using the admin icon"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔄 Testing chat to admin navigation on port {web_chat_port}...")
        
        # Login and get to chat first
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login failed for navigation test")
            return False
        
        # Access chat first
        chat_response = session.get(
            f"http://localhost:{web_chat_port}/chat",
            allow_redirects=True,
            timeout=10
        )
        
        if chat_response.status_code != 200:
            print(f"❌ Chat access failed")
            return False
        
        # Check if chat page has admin navigation
        chat_content = chat_response.text
        if 'navigateToAdmin' in chat_content and '⚙️' in chat_content:
            print(f"✅ Chat page has admin navigation functionality")
        else:
            print(f"❌ Chat page missing admin navigation")
            return False
        
        # Extract tokens from chat URL for admin navigation
        chat_url = chat_response.url
        if 'access_token=' in chat_url and 'page_token=' in chat_url:
            print(f"✅ Chat has security tokens for admin navigation")
        else:
            print(f"❌ Chat missing security tokens")
            return False
        
        # Now try to access admin (simulating the navigation)
        admin_response = session.get(
            f"http://localhost:{web_chat_port}/admin",
            allow_redirects=True,
            timeout=10
        )
        
        if admin_response.status_code == 200:
            print(f"✅ Admin navigation successful")
            return True
        else:
            print(f"❌ Admin navigation failed: {admin_response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing chat to admin navigation: {e}")
        return False

def test_admin_page_content():
    """Test that admin page has proper tab verification"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔍 Testing admin page content on port {web_chat_port}...")
        
        # Login first
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login failed for admin content test")
            return False
        
        # Access admin page
        admin_response = session.get(
            f"http://localhost:{web_chat_port}/admin",
            allow_redirects=True,
            timeout=10
        )
        
        if admin_response.status_code != 200:
            print(f"❌ Admin page access failed")
            return False
        
        content = admin_response.text
        
        # Check for key admin security functions
        admin_security_functions = [
            'verifyTabToken',
            'setupAdminTabToken',
            'generateTabId',
            'TAB_TOKEN_DATA',
            'admin_tab_'
        ]
        
        found_functions = []
        for func in admin_security_functions:
            if func in content:
                found_functions.append(func)
        
        if len(found_functions) >= 4:
            print(f"✅ Admin security functions found: {found_functions}")
            return True
        else:
            print(f"❌ Missing admin security functions. Found: {found_functions}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing admin page content: {e}")
        return False

def test_admin_url_copying_prevention():
    """Test that admin URLs can't be copied between tabs"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🚫 Testing admin URL copying prevention on port {web_chat_port}...")
        
        # This test simulates what happens when someone copies an admin URL
        # to a different browser session
        
        # Session 1: Get admin URL
        session1 = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        
        login_response1 = session1.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=10
        )
        
        if login_response1.status_code != 200:
            print(f"❌ Session 1 login failed")
            return False
        
        admin_response1 = session1.get(
            f"http://localhost:{web_chat_port}/admin",
            allow_redirects=True,
            timeout=10
        )
        
        if admin_response1.status_code != 200:
            print(f"❌ Session 1 admin access failed")
            return False
        
        admin_url1 = admin_response1.url
        print(f"✅ Session 1 got admin URL")
        
        # Session 2: Try to use Session 1's admin URL
        session2 = requests.Session()
        
        # Try to access the admin URL from session 1 with session 2
        admin_response2 = session2.get(admin_url1, allow_redirects=True, timeout=10)
        
        # Should be redirected to login or show error
        if admin_response2.status_code == 200:
            if '/login' in admin_response2.url or 'login' in admin_response2.text.lower():
                print(f"✅ Admin URL copying correctly blocked (redirected to login)")
                return True
            else:
                print(f"❌ Admin URL copying not blocked properly")
                return False
        else:
            print(f"✅ Admin URL copying blocked (status: {admin_response2.status_code})")
            return True
        
    except Exception as e:
        print(f"❌ Error testing admin URL copying prevention: {e}")
        return False

def main():
    """Run all admin tab-specific token tests"""
    print("🔧 ADMIN TAB-SPECIFIC TOKEN SYSTEM TEST")
    print("=" * 60)
    print("Testing admin features:")
    print("- Admin login and setup flow")
    print("- Admin tab token API")
    print("- Chat to admin navigation")
    print("- Admin page security content")
    print("- Admin URL copying prevention")
    print()
    
    tests_passed = 0
    total_tests = 5
    
    # Test 1: Admin login flow
    print("🧪 TEST 1: Admin Login Flow")
    if test_admin_login_flow():
        tests_passed += 1
    
    # Test 2: Admin tab token API
    print("\n🧪 TEST 2: Admin Tab Token API")
    if test_admin_tab_token_api():
        tests_passed += 1
    
    # Test 3: Chat to admin navigation
    print("\n🧪 TEST 3: Chat to Admin Navigation")
    if test_chat_to_admin_navigation():
        tests_passed += 1
    
    # Test 4: Admin page content
    print("\n🧪 TEST 4: Admin Page Content")
    if test_admin_page_content():
        tests_passed += 1
    
    # Test 5: Admin URL copying prevention
    print("\n🧪 TEST 5: Admin URL Copying Prevention")
    if test_admin_url_copying_prevention():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 60)
    print(f"🔧 TEST RESULTS: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✅ ALL TESTS PASSED - Admin tab-specific system working!")
        print("🎉 Admin features verified:")
        print("   - Tab-specific admin authentication")
        print("   - Chat to admin navigation")
        print("   - Admin URL copying prevention")
        print("   - Proper security implementation")
    else:
        print("❌ SOME TESTS FAILED - Admin system needs fixes")
        
        if tests_passed >= 4:
            print("🔧 RECOMMENDATION: Minor admin issues remain")
        elif tests_passed >= 3:
            print("🔧 RECOMMENDATION: Most admin features work")
        else:
            print("🔧 RECOMMENDATION: Major admin fixes needed")
    
    print(f"\n🌐 Manual test instructions:")
    print(f"1. Open http://localhost:8007 in browser")
    print(f"2. Login with: admin / admin123")
    print(f"3. Should redirect to chat with setup")
    print(f"4. Click the ⚙️ admin icon")
    print(f"5. Should navigate to admin with tab token")
    print(f"6. Copy admin URL and paste in new tab")
    print(f"7. New tab should reject admin URL")

if __name__ == '__main__':
    main()
