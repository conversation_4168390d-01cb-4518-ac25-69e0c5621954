"""
Dispatcher-specific models for mission orchestration.
These models are used for dispatcher operations and callbacks.
"""

from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
from enum import Enum

from .constants import TaskStatus


class TaskCompletionStatus(str, Enum):
    """Status values for task completion callbacks from agents"""
    COMPLETED = "completed"
    FAILED = "failed"
    REQUIRES_USER_INPUT = "requires_user_input"


class TaskCompletionCallback(BaseModel):
    """Callback from agent when task is completed"""
    task_id: str = Field(..., description="Task identifier")
    mission_id: str = Field(..., description="Mission identifier")
    status: TaskCompletionStatus = Field(..., description="Completion status")
    result: Dict[str, Any] = Field(default_factory=dict, description="Task result")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    suggested_tasks: Optional[List] = Field(None, description="Tasks suggested by agent")


class MissionExecutionResult(BaseModel):
    """Result of mission execution step"""
    mission_id: str
    status: str
    executed_tasks: List[str] = Field(default_factory=list)
    responses: List[Dict[str, Any]] = Field(default_factory=list)
    progress: Dict[str, Any] = Field(default_factory=dict)
    is_complete: bool = False
    requires_user_input: bool = False
    waiting_message: Optional[str] = None


class AgentInfo(BaseModel):
    """Information about an agent service"""
    agent_type: str
    base_url: str
    health_endpoint: str = "/health"
    execute_endpoint: str = "/execute"
