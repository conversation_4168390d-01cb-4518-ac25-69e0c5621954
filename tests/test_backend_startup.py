#!/usr/bin/env python3
"""
Test script to verify the backend startup improvements
"""

import asyncio
import httpx
import time
import subprocess
import os
import signal
from shared.port_manager import get_service_port

async def test_backend_readiness():
    """Test the backend readiness endpoint timing"""
    # [BACKEND-API:test_backend_readiness] TEST | 🔍 Testing Backend API readiness timing...
    
    # Start backend in background
    # [BACKEND-API:test_backend_readiness] STARTUP | 🚀 Starting backend API...
    backend_process = subprocess.Popen(
        ["python3", "-m", "app.main"],
        cwd="/Users/<USER>/Documents/prototype/backend",
        env={**os.environ, "PORT": "{get_service_port('backend')}"},
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
    )
    
    try:
        # Wait a moment for startup
        await asyncio.sleep(3)
        
        start_time = time.time()
        ready_time = None
        
        # Test readiness endpoint
        for i in range(30):  # Test for up to 60 seconds
            try:
                async with httpx.AsyncClient() as client:
                    response = await client.get(f"http://localhost:{get_service_port('backend')}/ready", timeout=5.0)
                    elapsed = time.time() - start_time
                    
                    if response.status_code == 200:
                        ready_time = elapsed
                        # [BACKEND-API:test_backend_readiness] SUCCESS | ✅ Backend ready after {elapsed:.1f} seconds
                        break
                    else:
                        # [BACKEND-API:test_backend_readiness] SUCCESS | ⏳ Backend not ready yet ({elapsed:.1f}: {response.status_code}")
                        pass

            except Exception as e:
                elapsed = time.time() - start_time
                # [BACKEND-API:test_backend_readiness] SUCCESS | ⏳ Backend not ready yet ({elapsed:.1f}: {e}")
            
            await asyncio.sleep(2)
        
        if ready_time:
            # [BACKEND-API:test_backend_readiness] SUCCESS | 🎉 Backend became ready in {ready_time:.1f} seconds
            
            # Test system status
            try:
                async with httpx.AsyncClient() as client:
                    response = await client.get("http://localhost:{get_service_port("backend")}/system/status", timeout=10.0)
                    if response.status_code == 200:
                        data = response.json()
                        # [BACKEND-API:test_backend_readiness] SYSTEM | 📊 System status: {data.get('status', 'unknown}")
                        # [BACKEND-API:test_backend_readiness] SYSTEM | 📋 Services: {len(data.get('services', {)}")
                    else:
                        # [BACKEND-API:test_backend_readiness] ERROR | ❌ System status failed: {response.status_code}
                        pass
            except Exception as e:
                # [BACKEND-API:test_backend_readiness] ERROR | ❌ System status error: {e}
                pass
        else:
            # [BACKEND-API:test_backend_readiness] ERROR | ❌ Backend failed to become ready within timeout
            
            pass
    finally:
        # Clean up
        # [BACKEND-API:test_backend_readiness] CLEANUP | 🧹 Cleaning up backend process...
        try:
            backend_process.terminate()
            backend_process.wait(timeout=10)
        except:
            backend_process.kill()

async def main():
    # [BACKEND-API:main] STARTUP | 🔍 Testing Backend API Startup Improvements
    # [BACKEND-API:main] SYSTEM | =" * 5
    
    await test_backend_readiness()

if __name__ == "__main__":
    asyncio.run(main())
