#!/usr/bin/env python3
"""
🔧 DEEPLICA DEPENDENCY FIXER
Analyzes and fixes service dependency issues
"""

import os
import sys
import asyncio
import time
from pathlib import Path
from typing import Dict, List, Set, Optional
from dataclasses import dataclass

# Add project root to path
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

from shared.port_manager import get_service_port
from shared.unified_logging import get_logger

logger = get_logger("DEPENDENCY-FIXER")

@dataclass
class ServiceConfig:
    """Service configuration"""
    name: str
    dependencies: List[str]
    wait_for_backend: bool
    critical: bool
    startup_order: int
    health_endpoint: Optional[str] = None
    port: Optional[int] = None

class DependencyAnalyzer:
    """Analyze and fix DEEPLICA service dependencies"""
    
    def __init__(self):
        self.services = {
            "WATCHDOG": ServiceConfig(
                name="WATCHDOG",
                dependencies=[],  # No dependencies - registry service
                wait_for_backend=False,
                critical=True,
                startup_order=1,
                health_endpoint="/health",
                port=get_service_port("watchdog")
            ),
            "BACKEND-API": ServiceConfig(
                name="BACKEND-API", 
                dependencies=["WATCHDOG"],  # Should register with Watchdog
                wait_for_backend=False,  # IS the backend
                critical=True,
                startup_order=2,
                health_endpoint="/health",
                port=get_service_port("backend")
            ),
            "DISPATCHER": ServiceConfig(
                name="DISPATCHER",
                dependencies=["BACKEND-API"],
                wait_for_backend=True,
                critical=True,
                startup_order=3,
                health_endpoint="/health",
                port=get_service_port("dispatcher")
            ),
            "DIALOGUE-AGENT": ServiceConfig(
                name="DIALOGUE-AGENT",
                dependencies=["BACKEND-API"],
                wait_for_backend=True,
                critical=False,
                startup_order=4,
                health_endpoint="/health",
                port=get_service_port("dialogue")
            ),
            "PLANNER-AGENT": ServiceConfig(
                name="PLANNER-AGENT",
                dependencies=["BACKEND-API"],
                wait_for_backend=True,
                critical=False,
                startup_order=5,
                health_endpoint="/health",
                port=get_service_port("planner")
            ),
            "PHONE-AGENT": ServiceConfig(
                name="PHONE-AGENT",
                dependencies=["BACKEND-API"],
                wait_for_backend=True,
                critical=False,
                startup_order=6,
                health_endpoint="/health",
                port=get_service_port("phone")
            ),
            "CLI-TERMINAL": ServiceConfig(
                name="CLI-TERMINAL",
                dependencies=["BACKEND-API"],
                wait_for_backend=False,  # Client interface
                critical=False,
                startup_order=7,
                health_endpoint=None,
                port=None
            ),
            "WEB-CHAT": ServiceConfig(
                name="WEB-CHAT",
                dependencies=["BACKEND-API"],
                wait_for_backend=False,  # Independent web service
                critical=False,
                startup_order=8,
                health_endpoint="/health",
                port=get_service_port("webchat")
            )
        }
    
    def detect_circular_dependencies(self) -> List[List[str]]:
        """Detect circular dependencies using DFS"""
        def dfs(service: str, path: List[str], visited: Set[str]) -> List[List[str]]:
            if service in path:
                # Found cycle
                cycle_start = path.index(service)
                return [path[cycle_start:] + [service]]
            
            if service in visited:
                return []
            
            visited.add(service)
            cycles = []
            
            for dep in self.services.get(service, ServiceConfig("", [], False, False, 0)).dependencies:
                cycles.extend(dfs(dep, path + [service], visited.copy()))
            
            return cycles
        
        all_cycles = []
        for service in self.services:
            cycles = dfs(service, [], set())
            for cycle in cycles:
                if cycle not in all_cycles:
                    all_cycles.append(cycle)
        
        return all_cycles
    
    def get_startup_order(self) -> List[str]:
        """Get correct startup order based on dependencies"""
        # Topological sort
        in_degree = {service: 0 for service in self.services}
        
        # Calculate in-degrees
        for service, config in self.services.items():
            for dep in config.dependencies:
                if dep in in_degree:
                    in_degree[service] += 1
        
        # Queue for services with no dependencies
        queue = [service for service, degree in in_degree.items() if degree == 0]
        result = []
        
        while queue:
            # Sort by startup_order for deterministic results
            queue.sort(key=lambda s: self.services[s].startup_order)
            service = queue.pop(0)
            result.append(service)
            
            # Reduce in-degree for dependent services
            for other_service, config in self.services.items():
                if service in config.dependencies:
                    in_degree[other_service] -= 1
                    if in_degree[other_service] == 0:
                        queue.append(other_service)
        
        return result
    
    def analyze_current_issues(self) -> Dict[str, List[str]]:
        """Analyze current dependency configuration issues"""
        issues = {}
        
        # Check for circular dependencies
        cycles = self.detect_circular_dependencies()
        if cycles:
            issues["circular_dependencies"] = [f"{' → '.join(cycle)}" for cycle in cycles]
        
        # Check for missing dependencies
        missing_deps = []
        for service, config in self.services.items():
            for dep in config.dependencies:
                if dep not in self.services:
                    missing_deps.append(f"{service} depends on non-existent {dep}")
        
        if missing_deps:
            issues["missing_dependencies"] = missing_deps
        
        # Check for conflicting configurations
        conflicts = []
        
        # Backend API should not wait for itself
        if self.services["BACKEND-API"].wait_for_backend:
            conflicts.append("BACKEND-API is configured to wait for itself")
        
        # Services that depend on Backend should wait for it
        for service, config in self.services.items():
            if "BACKEND-API" in config.dependencies and not config.wait_for_backend and service != "WATCHDOG":
                conflicts.append(f"{service} depends on BACKEND-API but wait_for_backend=False")
        
        if conflicts:
            issues["configuration_conflicts"] = conflicts
        
        return issues
    
    def generate_fixes(self) -> Dict[str, str]:
        """Generate fixes for identified issues"""
        fixes = {}
        issues = self.analyze_current_issues()
        
        if "circular_dependencies" in issues:
            fixes["circular_dependencies"] = """
🔄 CIRCULAR DEPENDENCY FIXES:
1. Remove circular dependencies by restructuring service relationships
2. Consider using event-driven communication instead of direct dependencies
3. Implement service discovery pattern for loose coupling
"""
        
        if "configuration_conflicts" in issues:
            fixes["configuration_conflicts"] = """
⚙️ CONFIGURATION FIXES:
1. Set WAIT_FOR_BACKEND=true for all services that depend on BACKEND-API
2. Set WAIT_FOR_BACKEND=false only for BACKEND-API itself and independent services
3. Update launch.json environment variables accordingly
"""
        
        # Generate optimal startup sequence
        startup_order = self.get_startup_order()
        fixes["optimal_startup_order"] = f"""
🚀 OPTIMAL STARTUP SEQUENCE:
{chr(10).join(f"{i+1}. {service}" for i, service in enumerate(startup_order))}

RATIONALE:
- WATCHDOG first (registry service, no dependencies)
- BACKEND-API second (core service that others depend on)
- All other services after BACKEND-API is ready
"""
        
        return fixes
    
    def generate_report(self) -> str:
        """Generate comprehensive dependency analysis report"""
        report = []
        report.append("🔧 DEEPLICA DEPENDENCY ANALYSIS REPORT")
        report.append("=" * 80)
        
        # Current configuration
        report.append("\n📋 CURRENT SERVICE CONFIGURATION")
        report.append("-" * 40)
        for service, config in sorted(self.services.items(), key=lambda x: x[1].startup_order):
            report.append(f"{config.startup_order}. {service}")
            report.append(f"   Dependencies: {', '.join(config.dependencies) if config.dependencies else 'None'}")
            report.append(f"   Wait for Backend: {config.wait_for_backend}")
            report.append(f"   Critical: {config.critical}")
            report.append(f"   Port: {config.port}")
        
        # Issues analysis
        issues = self.analyze_current_issues()
        if issues:
            report.append("\n❌ IDENTIFIED ISSUES")
            report.append("-" * 40)
            for issue_type, issue_list in issues.items():
                report.append(f"\n{issue_type.upper().replace('_', ' ')}:")
                for issue in issue_list:
                    report.append(f"   • {issue}")
        else:
            report.append("\n✅ NO DEPENDENCY ISSUES DETECTED")
        
        # Fixes
        fixes = self.generate_fixes()
        if fixes:
            report.append("\n🔧 RECOMMENDED FIXES")
            report.append("-" * 40)
            for fix_type, fix_description in fixes.items():
                report.append(f"\n{fix_description}")
        
        return "\n".join(report)

def main():
    """Main function"""
    print("🔧 DEEPLICA DEPENDENCY ANALYZER")
    print("=" * 50)
    
    analyzer = DependencyAnalyzer()
    report = analyzer.generate_report()
    
    print(report)
    
    # Save report
    from datetime import datetime
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"dependency_analysis_{timestamp}.txt"
    
    with open(report_file, 'w') as f:
        f.write(report)
    
    print(f"\n📄 Analysis saved to: {report_file}")

if __name__ == "__main__":
    main()
