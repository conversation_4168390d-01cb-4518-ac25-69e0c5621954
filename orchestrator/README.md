# 🎬 START DEEPLICA - Microservice Orchestrator

The **START DEEPLICA** orchestrator is a comprehensive microservice management system that ensures all Deeplica services start in the correct order and remain operational.

## 🎯 Features

- **🔄 Dependency-Aware Startup**: Starts services in the correct order based on dependencies
- **🛡️ Backend Readiness Validation**: Ensures Backend API is fully operational before starting dependent services
- **🔍 Health Monitoring**: Continuously monitors all services and reports status
- **🛑 Graceful Shutdown**: Properly shuts down all services in reverse dependency order
- **📊 Real-time Status**: Logs service status every 30 seconds
- **💪 Bulletproof Resilience**: Never crashes, handles all error conditions gracefully

## 🚀 Quick Start

### Using VS Code Launch Configuration (Recommended)

1. Open VS Code in the project root
2. Go to **Run and Debug** (Ctrl+Shift+D)
3. Select **"🎬 START DEEPLICA (Recommended)"** from the dropdown
4. Click the **Play** button or press **F5**

### Command Line

```bash
cd /path/to/deeplica
python3 orchestrator/main.py
```

## 📋 Service Startup Order

The orchestrator starts services in this dependency order:

1. **🌐 Backend API** (Port 8000) - Must be fully operational first
2. **🖥️ CLI Terminal UI** - User interface
3. **💬 Dialogue Agent** (Port 8002) - Conversation handling
4. **🎯 Dispatcher Service** (Port 8001) - Mission orchestration
5. **📞 Phone Agent** (Port 8004) - Phone call handling
6. **🧠 Planner Agent** (Port 8003) - Mission planning

## 🔍 Health Checks

Each service undergoes comprehensive health validation:

- **Backend API**: Uses `/ready` endpoint to ensure database, LLM, and all core services are operational
- **Other Services**: Uses `/health` endpoints to verify service readiness
- **CLI Terminal**: Process-based monitoring (no HTTP endpoint)

## 📊 Monitoring

The orchestrator provides real-time monitoring:

- **Service Status**: 🟢 Running, 🟡 Starting, 🔴 Failed, ⚫ Stopped, ⚪ Not Started
- **Automatic Recovery**: Detects failed services and reports issues
- **Graceful Shutdown**: Ctrl+C triggers orderly shutdown of all services

## 🛑 Shutdown

To stop all services:

1. Press **Ctrl+C** in the orchestrator terminal
2. The orchestrator will gracefully shut down all services in reverse order
3. Each service gets 10 seconds for graceful shutdown before force termination

## 📝 Logs

Orchestrator logs are saved to:
- `orchestrator/logs/orchestrator.log` (rotated daily, 30-day retention)

## 🔧 Configuration

Services are configured in the `DeepplicaOrchestrator._configure_services()` method:

- **Dependencies**: Which services must start before others
- **Ports**: Network ports for each service
- **Health Checks**: URLs for service health validation
- **Timeouts**: Maximum time to wait for service startup

## 🎉 Benefits

### For Development
- **One-Click Startup**: Start entire system with single command
- **Proper Dependencies**: Never worry about startup order again
- **Real-time Monitoring**: See exactly which services are running
- **Clean Shutdown**: Stop everything cleanly with Ctrl+C

### For Production
- **Bulletproof Reliability**: Handles all edge cases and failures
- **Comprehensive Logging**: Full audit trail of service lifecycle
- **Health Monitoring**: Continuous validation of service health
- **Graceful Recovery**: Proper handling of service failures

## 🚨 Troubleshooting

### Service Won't Start
- Check the orchestrator logs for detailed error messages
- Verify all dependencies (MongoDB, environment variables) are available
- Ensure no port conflicts exist

### Backend Not Ready
- The orchestrator waits up to 2 minutes for backend readiness
- Check MongoDB connection and LLM service configuration
- Verify environment variables are properly set

### Service Health Check Fails
- Each service gets 60 seconds for health check to pass
- Check individual service logs for specific errors
- Verify service dependencies are met

## 🎯 Next Steps

The orchestrator is now your primary way to start the Deeplica system:

1. **Always use the orchestrator** instead of starting services manually
2. **Monitor the status logs** to ensure all services are healthy
3. **Use Ctrl+C for shutdown** to ensure clean termination
4. **Check logs** if any service fails to start

**🎉 Enjoy your bulletproof Deeplica system startup!**
