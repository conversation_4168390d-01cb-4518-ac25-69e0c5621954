#!/usr/bin/env python3
"""
🔧 Fix Ngrok SSL Certificate Issues
Fix SSL certificate verification issues with ngrok installation.
"""

import os
import sys
import subprocess
import ssl
import urllib.request
from pathlib import Path

def fix_ssl_certificates():
    """Fix SSL certificate issues for ngrok"""
    print("🔧 Fixing SSL certificate issues for ngrok...")
    
    try:
        # Method 1: Try to install certificates using macOS command
        if sys.platform == 'darwin':  # macOS
            print("🍎 Detected macOS - attempting certificate fix...")
            
            # Try to update certificates
            try:
                result = subprocess.run([
                    '/Applications/Python 3.13/Install Certificates.command'
                ], capture_output=True, text=True, timeout=30)
                
                if result.returncode == 0:
                    print("✅ SSL certificates updated successfully")
                    return True
                else:
                    print("⚠️ Certificate update command failed, trying alternative...")
            except Exception as e:
                print(f"⚠️ Certificate update failed: {e}")
        
        # Method 2: Try pip install with trusted hosts
        print("🔧 Attempting to reinstall ngrok with trusted hosts...")
        try:
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', '--upgrade', 
                '--trusted-host', 'pypi.org',
                '--trusted-host', 'pypi.python.org', 
                '--trusted-host', 'files.pythonhosted.org',
                'pyngrok'
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("✅ Pyngrok reinstalled successfully")
                return True
            else:
                print(f"⚠️ Pyngrok reinstall failed: {result.stderr}")
        except Exception as e:
            print(f"⚠️ Pyngrok reinstall error: {e}")
        
        # Method 3: Use system ngrok instead of pyngrok
        print("🔧 Checking for system ngrok installation...")
        try:
            result = subprocess.run(['which', 'ngrok'], capture_output=True, text=True)
            if result.returncode == 0:
                ngrok_path = result.stdout.strip()
                print(f"✅ Found system ngrok at: {ngrok_path}")
                
                # Test ngrok version
                version_result = subprocess.run(['ngrok', 'version'], capture_output=True, text=True)
                if version_result.returncode == 0:
                    print(f"✅ System ngrok working: {version_result.stdout.strip()}")
                    return True
            else:
                print("⚠️ System ngrok not found")
        except Exception as e:
            print(f"⚠️ System ngrok check failed: {e}")
        
        return False
        
    except Exception as e:
        print(f"❌ SSL certificate fix failed: {e}")
        return False

def install_ngrok_manually():
    """Install ngrok manually if automatic installation fails"""
    print("🔧 Manual ngrok installation guide...")
    
    print("\n💡 MANUAL NGROK INSTALLATION:")
    print("1. Download ngrok from: https://ngrok.com/download")
    print("2. Extract the ngrok binary")
    print("3. Move it to /usr/local/bin/ or add to PATH")
    print("4. Run: ngrok config add-authtoken YOUR_TOKEN")
    print("5. Test with: ngrok version")
    
    print("\n🍎 For macOS with Homebrew:")
    print("   brew install ngrok/ngrok/ngrok")
    print("   ngrok config add-authtoken *************************************************")
    
    print("\n🐧 For Linux:")
    print("   curl -s https://ngrok-agent.s3.amazonaws.com/ngrok.asc | sudo tee /etc/apt/trusted.gpg.d/ngrok.asc >/dev/null")
    print("   echo 'deb https://ngrok-agent.s3.amazonaws.com buster main' | sudo tee /etc/apt/sources.list.d/ngrok.list")
    print("   sudo apt update && sudo apt install ngrok")

def test_ngrok_installation():
    """Test if ngrok is working correctly"""
    print("🧪 Testing ngrok installation...")
    
    try:
        # Test ngrok version
        result = subprocess.run(['ngrok', 'version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ Ngrok version: {result.stdout.strip()}")
            
            # Test ngrok config
            config_result = subprocess.run(['ngrok', 'config', 'check'], capture_output=True, text=True, timeout=10)
            if config_result.returncode == 0:
                print("✅ Ngrok configuration is valid")
                return True
            else:
                print("⚠️ Ngrok configuration needs setup")
                print("💡 Run: ngrok config add-authtoken YOUR_TOKEN")
                return False
        else:
            print("❌ Ngrok not working properly")
            return False
    except FileNotFoundError:
        print("❌ Ngrok not found in PATH")
        return False
    except Exception as e:
        print(f"❌ Ngrok test failed: {e}")
        return False

def create_ngrok_wrapper():
    """Create a wrapper script for ngrok that handles SSL issues"""
    print("🔧 Creating ngrok wrapper script...")
    
    wrapper_script = '''#!/bin/bash
# Ngrok wrapper script to handle SSL issues
export PYTHONHTTPSVERIFY=0
export SSL_VERIFY=false

# Use system ngrok if available, otherwise use pyngrok
if command -v ngrok >/dev/null 2>&1; then
    exec ngrok "$@"
else
    # Try pyngrok with SSL verification disabled
    python3 -c "
import ssl
ssl._create_default_https_context = ssl._create_unverified_context
import pyngrok.ngrok as ngrok
import sys
ngrok.main()
" "$@"
fi
'''
    
    try:
        wrapper_path = Path("ngrok_wrapper.sh")
        with open(wrapper_path, 'w') as f:
            f.write(wrapper_script)
        
        # Make executable
        os.chmod(wrapper_path, 0o755)
        print(f"✅ Created ngrok wrapper: {wrapper_path}")
        return True
    except Exception as e:
        print(f"❌ Failed to create wrapper: {e}")
        return False

def main():
    """Main function to fix ngrok SSL issues"""
    print("🔧 NGROK SSL CERTIFICATE FIX")
    print("=" * 40)
    
    # Step 1: Try to fix SSL certificates
    ssl_fixed = fix_ssl_certificates()
    
    # Step 2: Test ngrok installation
    ngrok_working = test_ngrok_installation()
    
    if ngrok_working:
        print("\n✅ NGROK IS WORKING CORRECTLY!")
        print("🎉 SSL certificate issues have been resolved")
        return True
    
    # Step 3: Create wrapper script as fallback
    wrapper_created = create_ngrok_wrapper()
    
    # Step 4: Provide manual installation guide
    install_ngrok_manually()
    
    print("\n📋 SUMMARY:")
    if ssl_fixed:
        print("✅ SSL certificates fixed")
    else:
        print("⚠️ SSL certificate fix incomplete")
    
    if ngrok_working:
        print("✅ Ngrok is working")
    else:
        print("❌ Ngrok needs manual setup")
    
    if wrapper_created:
        print("✅ Wrapper script created")
    
    print("\n💡 NEXT STEPS:")
    print("1. Try running: ngrok version")
    print("2. If that fails, use: ./ngrok_wrapper.sh version")
    print("3. Configure auth token: ngrok config add-authtoken YOUR_TOKEN")
    print("4. Test with DEEPLICA: python3 start_ngrok.py")
    
    return ngrok_working

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
