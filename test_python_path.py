#!/usr/bin/env python3
"""
Test script to verify Python path and environment setup
"""

import sys
import os

print("🐍 Python Path Test")
print("=" * 50)
print(f"Python executable: {sys.executable}")
print(f"Python version: {sys.version}")
print(f"Current working directory: {os.getcwd()}")
print(f"PYTHONPATH: {os.environ.get('PYTHONPATH', 'Not set')}")
print("=" * 50)
print("✅ Python environment is working correctly!")
