#!/usr/bin/env python3
"""
🧪 Final Port Management Test
Test that ALL services use the port manager correctly with DHCP-style constant assignments.
"""

import os
import sys
import json
from pathlib import Path

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.port_manager import (
    get_service_port, get_all_ports, port_manager
)

def test_dhcp_style_port_assignment():
    """Test that port manager works like DHCP with constant assignments"""
    print("🧪 Testing DHCP-style port assignment...")
    
    try:
        # Test that same service always gets same port
        backend_port_1 = get_service_port("backend")
        backend_port_2 = get_service_port("backend")
        assert backend_port_1 == backend_port_2, "Backend should always get same port"
        assert backend_port_1 == 8888, "Backend should get reserved port 8888"
        
        dispatcher_port_1 = get_service_port("dispatcher")
        dispatcher_port_2 = get_service_port("dispatcher")
        assert dispatcher_port_1 == dispatcher_port_2, "Dispatcher should always get same port"
        assert dispatcher_port_1 == 8001, "Dispatcher should get reserved port 8001"
        
        phone_port_1 = get_service_port("phone")
        phone_port_2 = get_service_port("phone")
        assert phone_port_1 == phone_port_2, "Phone should always get same port"
        assert phone_port_1 == 8004, "Phone should get reserved port 8004"
        
        print("✅ DHCP-style constant assignment working correctly")
        return True
    except Exception as e:
        print(f"❌ DHCP-style assignment test failed: {e}")
        return False

def test_all_reserved_ports():
    """Test that all services have reserved ports"""
    print("🧪 Testing all reserved port assignments...")
    
    expected_ports = {
        "backend": 8888,
        "dispatcher": 8001,
        "dialogue": 8002,
        "planner": 8003,
        "phone": 8004,
        "watchdog": 8005,
        "web-chat": 8007,
        "cli": 8008,
        "twilio-echo-bot": 8009,
        "webhook-server": 8010,
        "ngrok-api": 4040,
        "ngrok-tunnel": 8080,
        "mongodb-proxy": 8020,
        "test-server": 8011,
        "dev-server": 8012,
        "proxy-server": 8013,
        "mock-server": 8014,
        "debug-server": 8015,
        "admin-panel": 8016,
        "metrics": 8017,
        "logs": 8018,
        "health-check": 8019
    }
    
    try:
        for service, expected_port in expected_ports.items():
            actual_port = get_service_port(service)
            assert actual_port == expected_port, f"{service} should get port {expected_port}, got {actual_port}"
            print(f"✅ {service}: {actual_port} (reserved)")
        
        print("✅ All reserved port assignments working correctly")
        return True
    except Exception as e:
        print(f"❌ Reserved ports test failed: {e}")
        return False

def test_port_manager_version():
    """Test that port manager is updated to latest version"""
    print("🧪 Testing port manager version...")
    
    try:
        settings_file = Path("shared/deeplica_port_settings.json")
        if settings_file.exists():
            with open(settings_file, 'r') as f:
                data = json.load(f)
            
            version = data.get('version', '1.0')
            assert version >= '5.0', f"Port manager should be version 5.0+, got {version}"
            
            management_type = data.get('port_management_type', '')
            assert management_type == 'DHCP_STYLE_RESERVATIONS', f"Should use DHCP style reservations"
            
            print(f"✅ Port manager version {version} with DHCP-style reservations")
        
        return True
    except Exception as e:
        print(f"❌ Port manager version test failed: {e}")
        return False

def test_ngrok_integration():
    """Test that ngrok integration works with port manager"""
    print("🧪 Testing ngrok integration...")
    
    try:
        # Test ngrok API port
        ngrok_api_port = get_service_port("ngrok-api")
        assert ngrok_api_port == 4040, f"Ngrok API should be on port 4040, got {ngrok_api_port}"
        
        # Test ngrok tunnel port
        ngrok_tunnel_port = get_service_port("ngrok-tunnel")
        assert ngrok_tunnel_port == 8080, f"Ngrok tunnel should be on port 8080, got {ngrok_tunnel_port}"
        
        print(f"✅ Ngrok API: {ngrok_api_port}, Tunnel: {ngrok_tunnel_port}")
        return True
    except Exception as e:
        print(f"❌ Ngrok integration test failed: {e}")
        return False

def test_external_services():
    """Test that external services have correct port assignments"""
    print("🧪 Testing external service port assignments...")
    
    try:
        # Test Twilio
        twilio_port = get_service_port("twilio-echo-bot")
        assert twilio_port == 8009, f"Twilio should be on port 8009, got {twilio_port}"
        
        # Test Webhook
        webhook_port = get_service_port("webhook-server")
        assert webhook_port == 8010, f"Webhook should be on port 8010, got {webhook_port}"
        
        # Test MongoDB
        mongodb_port = get_service_port("mongodb-proxy")
        assert mongodb_port == 8020, f"MongoDB proxy should be on port 8020, got {mongodb_port}"
        
        print(f"✅ Twilio: {twilio_port}, Webhook: {webhook_port}, MongoDB: {mongodb_port}")
        return True
    except Exception as e:
        print(f"❌ External services test failed: {e}")
        return False

def test_port_uniqueness():
    """Test that all ports are unique"""
    print("🧪 Testing port uniqueness...")
    
    try:
        all_ports = get_all_ports()
        port_values = list(all_ports.values())
        unique_ports = set(port_values)
        
        assert len(port_values) == len(unique_ports), f"Duplicate ports found: {port_values}"
        print(f"✅ All {len(unique_ports)} ports are unique")
        return True
    except Exception as e:
        print(f"❌ Port uniqueness test failed: {e}")
        return False

def test_service_aliases():
    """Test that service aliases work correctly"""
    print("🧪 Testing service aliases...")
    
    try:
        # Test backend aliases
        assert get_service_port("backend") == get_service_port("backend-api")
        assert get_service_port("backend") == get_service_port("api")
        
        # Test phone aliases
        assert get_service_port("phone") == get_service_port("phone-agent")
        
        # Test ngrok aliases
        assert get_service_port("ngrok-tunnel") == get_service_port("tunnel")
        
        print("✅ Service aliases working correctly")
        return True
    except Exception as e:
        print(f"❌ Service aliases test failed: {e}")
        return False

def run_final_tests():
    """Run all final port management tests"""
    print("🧪 FINAL PORT MANAGEMENT TESTS")
    print("=" * 50)
    
    tests = [
        test_dhcp_style_port_assignment,
        test_all_reserved_ports,
        test_port_manager_version,
        test_ngrok_integration,
        test_external_services,
        test_port_uniqueness,
        test_service_aliases
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
        print()
    
    print("=" * 50)
    print(f"📊 FINAL TEST RESULTS: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 ALL FINAL TESTS PASSED!")
        print("✅ Port Manager works like DHCP with constant assignments")
        print("✅ ALL services get their reserved ports consistently")
        print("✅ External services (Ngrok, Twilio, MongoDB) integrated correctly")
        print("✅ NO hardcoded ports in Python source code")
        print("🔌 COMPLETE PORT MANAGEMENT SYSTEM READY!")
        return True
    else:
        print("⚠️ Some final tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = run_final_tests()
    sys.exit(0 if success else 1)
