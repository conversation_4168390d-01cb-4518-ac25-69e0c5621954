#!/usr/bin/env python3
"""
🎤 DEEPLICA PHONE WEBHOOK SERVER
Handles Twilio voice calls with conversation logging and admin monitoring
"""

import os
import sys
import json
import datetime
from pathlib import Path
from flask import Flask, request, Response, render_template_string
from twilio.twiml.voice_response import VoiceResponse

# Add project root to path
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

app = Flask(__name__)

# Global conversation log
conversation_log = []
current_call = None

def log_conversation(event_type, content, call_sid=None, from_number=None):
    """Log conversation events"""
    global conversation_log, current_call

    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    log_entry = {
        'timestamp': timestamp,
        'event_type': event_type,
        'content': content,
        'call_sid': call_sid or (current_call['call_sid'] if current_call else 'Unknown'),
        'from_number': from_number or (current_call['from_number'] if current_call else 'Unknown')
    }

    conversation_log.append(log_entry)

    # Print to console for real-time monitoring
    if event_type == 'SYSTEM_SAY':
        print(f"🎤 DEEPLICA SAYS: {content}")
    elif event_type == 'USER_SPEECH':
        print(f"👤 USER SAYS: {content}")
    elif event_type == 'CALL_START':
        print(f"📞 CALL STARTED: {content}")
    elif event_type == 'CALL_END':
        print(f"📞 CALL ENDED: {content}")
    else:
        print(f"📋 {event_type}: {content}")

@app.route('/webhook/voice', methods=['POST'])
def handle_voice_call():
    """Handle incoming voice call from Twilio"""
    global current_call

    try:
        # Get call information
        from_number = request.form.get('From', 'Unknown')
        to_number = request.form.get('To', 'Unknown')
        call_sid = request.form.get('CallSid', 'Unknown')
        call_status = request.form.get('CallStatus', 'Unknown')

        # Initialize current call
        current_call = {
            'call_sid': call_sid,
            'from_number': from_number,
            'to_number': to_number,
            'start_time': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        # Log call start
        log_conversation('CALL_START', f"Call from {from_number} to {to_number} (Status: {call_status})", call_sid, from_number)

        # Create TwiML response
        response = VoiceResponse()

        # Welcome message with 3-second wait
        welcome_message = (
            "Hello Eran! This is DEEPLICA calling for a test. "
            "Please wait 3 seconds, then say your name Eran 5 times clearly. "
            "Starting in 3... 2... 1..."
        )

        log_conversation('SYSTEM_SAY', welcome_message, call_sid, from_number)
        response.say(welcome_message, voice='alice', language='en-US')

        # 3-second pause
        log_conversation('SYSTEM_ACTION', "Pausing for 3 seconds", call_sid, from_number)
        response.pause(length=3)

        # Instructions for saying Eran 5 times
        instruction_message = "Now please say Eran 5 times. Begin now."
        log_conversation('SYSTEM_SAY', instruction_message, call_sid, from_number)
        response.say(instruction_message, voice='alice', language='en-US')

        # Record user speech
        log_conversation('SYSTEM_ACTION', "Recording user speech for 15 seconds", call_sid, from_number)
        response.record(
            action='/webhook/recording',
            method='POST',
            max_length=15,
            play_beep=False,
            transcribe=True,
            transcribe_callback='/webhook/transcription'
        )

        # Thank you message
        thank_you_message = "Thank you Eran! DEEPLICA test call completed successfully. Goodbye!"
        log_conversation('SYSTEM_SAY', thank_you_message, call_sid, from_number)
        response.say(thank_you_message, voice='alice', language='en-US')

        print(f"✅ TwiML response generated for call {call_sid}")

        return Response(str(response), mimetype='text/xml')

    except Exception as e:
        print(f"❌ Error handling voice call: {e}")
        log_conversation('ERROR', f"Error handling voice call: {e}", call_sid, from_number)

        # Fallback response - no error message to user
        response = VoiceResponse()
        fallback_message = "Hello Eran! This is DEEPLICA. Please wait 3 seconds then say Eran 5 times. Thank you!"
        log_conversation('SYSTEM_SAY', f"FALLBACK: {fallback_message}", call_sid, from_number)
        response.say(fallback_message, voice='alice', language='en-US')
        response.pause(length=18)  # 3 + 15 seconds
        response.say("Goodbye!", voice='alice', language='en-US')

        return Response(str(response), mimetype='text/xml')

@app.route('/webhook/recording', methods=['POST'])
def handle_recording():
    """Handle recording completion"""
    global current_call

    try:
        recording_url = request.form.get('RecordingUrl', 'No URL')
        recording_duration = request.form.get('RecordingDuration', '0')
        call_sid = request.form.get('CallSid', 'Unknown')

        log_conversation('USER_RECORDING', f"Recording completed: {recording_duration}s - URL: {recording_url}", call_sid)

        # Continue with call flow
        response = VoiceResponse()
        return Response(str(response), mimetype='text/xml')

    except Exception as e:
        print(f"❌ Error handling recording: {e}")
        log_conversation('ERROR', f"Error handling recording: {e}")
        return Response('', mimetype='text/xml')

@app.route('/webhook/transcription', methods=['POST'])
def handle_transcription():
    """Handle speech transcription"""
    global current_call

    try:
        transcription_text = request.form.get('TranscriptionText', 'No transcription')
        transcription_status = request.form.get('TranscriptionStatus', 'Unknown')
        call_sid = request.form.get('CallSid', 'Unknown')

        if transcription_status == 'completed' and transcription_text:
            log_conversation('USER_SPEECH', transcription_text, call_sid)

            # Count how many times "Eran" was said
            eran_count = transcription_text.lower().count('eran')
            log_conversation('ANALYSIS', f"User said 'Eran' {eran_count} times", call_sid)
        else:
            log_conversation('USER_SPEECH', f"Transcription failed: {transcription_status}", call_sid)

        return Response('OK', mimetype='text/plain')

    except Exception as e:
        print(f"❌ Error handling transcription: {e}")
        log_conversation('ERROR', f"Error handling transcription: {e}")
        return Response('Error', mimetype='text/plain')

@app.route('/conversation', methods=['GET'])
def view_conversation():
    """View conversation log in web interface"""
    global conversation_log, current_call

    html_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>🎤 DEEPLICA Phone Conversation Log</title>
        <meta http-equiv="refresh" content="5">
        <style>
            body {
                font-family: 'Courier New', monospace;
                background: #0a0a0a;
                color: #00ff00;
                margin: 20px;
                line-height: 1.6;
            }
            .header {
                background: #1a1a1a;
                padding: 20px;
                border-radius: 10px;
                margin-bottom: 20px;
                border: 2px solid #00ff00;
            }
            .call-info {
                background: #2a2a2a;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 20px;
                border-left: 4px solid #00d4ff;
            }
            .log-entry {
                background: #1a1a1a;
                padding: 10px;
                margin: 5px 0;
                border-radius: 5px;
                border-left: 4px solid #333;
            }
            .system-say { border-left-color: #00ff00; }
            .user-speech { border-left-color: #ff6b35; background: #2a1a1a; }
            .call-start { border-left-color: #00d4ff; }
            .call-end { border-left-color: #ff4757; }
            .error { border-left-color: #ff4757; background: #3a1a1a; }
            .analysis { border-left-color: #ffa502; background: #2a2a1a; }
            .timestamp { color: #888; font-size: 0.9em; }
            .event-type { color: #00d4ff; font-weight: bold; }
            .content { color: #fff; margin-top: 5px; }
            .stats {
                display: flex;
                gap: 20px;
                margin-bottom: 20px;
            }
            .stat-card {
                background: #2a2a2a;
                padding: 15px;
                border-radius: 8px;
                text-align: center;
                border: 1px solid #444;
            }
            .stat-number { font-size: 2em; color: #00ff00; font-weight: bold; }
            .stat-label { color: #888; margin-top: 5px; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🎤 DEEPLICA Phone Conversation Monitor</h1>
            <p>Real-time conversation logging and analysis</p>
        </div>

        {% if current_call %}
        <div class="call-info">
            <h3>📞 Current Call Information</h3>
            <p><strong>Call SID:</strong> {{ current_call.call_sid }}</p>
            <p><strong>From:</strong> {{ current_call.from_number }}</p>
            <p><strong>To:</strong> {{ current_call.to_number }}</p>
            <p><strong>Start Time:</strong> {{ current_call.start_time }}</p>
        </div>
        {% endif %}

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{{ conversation_log|length }}</div>
                <div class="stat-label">Total Events</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ user_speech_count }}</div>
                <div class="stat-label">User Responses</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ system_messages_count }}</div>
                <div class="stat-label">System Messages</div>
            </div>
        </div>

        <h3>📋 Conversation Log</h3>
        {% for entry in conversation_log %}
        <div class="log-entry {{ entry.event_type.lower().replace('_', '-') }}">
            <div class="timestamp">{{ entry.timestamp }}</div>
            <div class="event-type">{{ entry.event_type }}</div>
            <div class="content">{{ entry.content }}</div>
        </div>
        {% endfor %}

        {% if not conversation_log %}
        <div class="log-entry">
            <div class="content">No conversation data yet. Waiting for phone calls...</div>
        </div>
        {% endif %}
    </body>
    </html>
    """

    # Calculate stats
    user_speech_count = len([e for e in conversation_log if e['event_type'] == 'USER_SPEECH'])
    system_messages_count = len([e for e in conversation_log if e['event_type'] == 'SYSTEM_SAY'])

    return render_template_string(
        html_template,
        conversation_log=conversation_log,
        current_call=current_call,
        user_speech_count=user_speech_count,
        system_messages_count=system_messages_count
    )

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "phone-webhook", "conversations": len(conversation_log)}, 200

@app.route('/', methods=['GET'])
def root():
    """Root endpoint - redirect to conversation view"""
    return view_conversation()

@app.route('/api/conversation', methods=['GET'])
def api_conversation():
    """API endpoint for conversation data"""
    global conversation_log, current_call

    return {
        "status": "success",
        "current_call": current_call,
        "conversation_log": conversation_log,
        "stats": {
            "total_events": len(conversation_log),
            "user_speech_count": len([e for e in conversation_log if e['event_type'] == 'USER_SPEECH']),
            "system_messages_count": len([e for e in conversation_log if e['event_type'] == 'SYSTEM_SAY']),
            "call_count": len(set([e['call_sid'] for e in conversation_log if e['call_sid'] != 'Unknown']))
        }
    }

@app.route('/api/clear', methods=['POST'])
def api_clear_log():
    """Clear conversation log"""
    global conversation_log, current_call

    conversation_log = []
    current_call = None

    return {"status": "success", "message": "Conversation log cleared"}

if __name__ == '__main__':
    print("🎤 DEEPLICA PHONE WEBHOOK SERVER")
    print("=" * 50)
    print("🚀 Starting enhanced webhook server on port 8004...")
    print("📞 Ready to handle Twilio voice calls with conversation logging")
    print("🔗 Webhook URL: /webhook/voice")
    print("🌐 Web Interface: http://localhost:8004/conversation")
    print("📊 API Endpoint: http://localhost:8004/api/conversation")
    print("🧹 Clear Log: POST http://localhost:8004/api/clear")
    print()
    print("🎯 TEST SCENARIO:")
    print("   1. Call will greet Eran")
    print("   2. Wait 3 seconds")
    print("   3. Ask to say 'Eran' 5 times")
    print("   4. Record and transcribe response")
    print("   5. Log everything to conversation monitor")
    print()

    app.run(
        host='0.0.0.0',
        port=8004,
        debug=False,
        threaded=True
    )
