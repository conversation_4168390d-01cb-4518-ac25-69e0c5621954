#!/usr/bin/env python3
"""
🧪 Port Manager Integration Tests
Comprehensive tests to verify all services use the port manager correctly.
"""

import os
import sys
import json
from pathlib import Path
from shared.port_manager import get_service_port

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.port_manager import (
    get_service_port, get_all_ports, is_constant_port, 
    get_constant_services, get_configurable_services,
    port_manager
)

def test_port_manager_basic_functionality():
    """Test basic port manager functionality"""
    print("🧪 Testing basic port manager functionality...")
    
    # Test getting service ports
    try:
        backend_port = get_service_port("backend")
        assert backend_port == get_service_port("backend"), f"Backend port should be {get_service_port('backend')}, got {backend_port}"
        print(f"✅ Backend port correct ({get_service_port('backend')})")

        dispatcher_port = get_service_port("dispatcher")
        assert dispatcher_port == get_service_port("dispatcher"), f"Dispatcher port should be {get_service_port('dispatcher')}, got {dispatcher_port}"
        print(f"✅ Dispatcher port correct ({get_service_port('dispatcher')})")

        phone_port = get_service_port("phone")
        assert phone_port == get_service_port("phone"), f"Phone port should be {get_service_port('phone')}, got {phone_port}"
        print(f"✅ Phone port correct ({get_service_port('phone')})")
        
        # Test constant ports
        assert is_constant_port("backend"), "Backend should be constant port"
        assert is_constant_port("twilio-echo-bot"), "Twilio should be constant port"
        assert not is_constant_port("dispatcher"), "Dispatcher should not be constant port"
        print("✅ Constant port detection working")
        
        return True
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False

def test_port_settings_file():
    """Test that port settings file exists and is valid"""
    print("🧪 Testing port settings file...")
    
    try:
        settings_file = Path("shared/deeplica_port_settings.json")
        assert settings_file.exists(), "Port settings file should exist"
        print("✅ Port settings file exists")
        
        with open(settings_file, 'r') as f:
            data = json.load(f)
        
        assert 'port_config' in data, "Port config should be in settings file"
        assert 'version' in data, "Version should be in settings file"
        
        port_config = data['port_config']
        assert 'backend' in port_config, "Backend should be in port config"
        assert port_config['backend'] == get_service_port("backend"), f"Backend port mismatch"
        
        print("✅ Port settings file is valid")
        return True
    except Exception as e:
        print(f"❌ Port settings file test failed: {e}")
        return False

def test_no_duplicate_settings_files():
    """Test that there are no duplicate port settings files"""
    print("🧪 Testing for duplicate port settings files...")
    
    try:
        # Search for all deeplica_port_settings.json files
        duplicate_files = []
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file == 'deeplica_port_settings.json':
                    file_path = os.path.join(root, file)
                    if file_path != './shared/deeplica_port_settings.json':
                        duplicate_files.append(file_path)
        
        assert len(duplicate_files) == 0, f"Found duplicate port settings files: {duplicate_files}"
        print("✅ No duplicate port settings files found")
        return True
    except Exception as e:
        print(f"❌ Duplicate files test failed: {e}")
        return False

def test_service_imports():
    """Test that services can import port manager correctly"""
    print("🧪 Testing service imports...")
    
    service_files = [
        "backend/app/main.py",
        "dispatcher/app/main.py",
        "agents/dialogue/app/main.py",
        "agents/planner/app/main.py",
        "agents/phone/app/main.py",
        "watchdog/main.py",
        "web_chat/main.py"
    ]
    
    try:
        for service_file in service_files:
            if Path(service_file).exists():
                with open(service_file, 'r') as f:
                    content = f.read()
                
                # Check if port manager is imported
                has_import = (
                    'from shared.port_manager import' in content or
                    'import shared.port_manager' in content
                )
                
                if has_import:
                    print(f"✅ {service_file}: Has port manager import")
                else:
                    print(f"⚠️ {service_file}: No port manager import found")
        
        return True
    except Exception as e:
        print(f"❌ Service imports test failed: {e}")
        return False

def test_port_ranges():
    """Test that all ports are in expected ranges"""
    print("🧪 Testing port ranges...")
    
    try:
        all_ports = get_all_ports()
        
        for service, port in all_ports.items():
            # Check port is in valid range
            assert 1024 <= port <= 65535, f"Port {port} for {service} is out of valid range"
            
            # Check DEEPLICA ports are in expected range
            if service not in ['NGROK-API']:  # Ngrok API uses dynamic port
                assert 8000 <= port <= 8999, f"DEEPLICA port {port} for {service} should be in 8000-8999 range"
        
        print("✅ All ports are in valid ranges")
        return True
    except Exception as e:
        print(f"❌ Port ranges test failed: {e}")
        return False

def test_port_uniqueness():
    """Test that all ports are unique"""
    print("🧪 Testing port uniqueness...")
    
    try:
        all_ports = get_all_ports()
        port_values = list(all_ports.values())
        unique_ports = set(port_values)
        
        assert len(port_values) == len(unique_ports), f"Duplicate ports found: {port_values}"
        print("✅ All ports are unique")
        return True
    except Exception as e:
        print(f"❌ Port uniqueness test failed: {e}")
        return False

def test_constant_vs_configurable():
    """Test constant vs configurable port classification"""
    print("🧪 Testing constant vs configurable ports...")
    
    try:
        constant_services = get_constant_services()
        configurable_services = get_configurable_services()
        
        # Backend should be constant
        assert 'BACKEND-API' in constant_services, "Backend should be constant"
        assert constant_services['BACKEND-API'] == get_service_port("backend"), "Backend constant port mismatch"
        
        # Dispatcher should be configurable
        assert 'DISPATCHER' in configurable_services, "Dispatcher should be configurable"
        
        # No overlap between constant and configurable
        constant_names = set(constant_services.keys())
        configurable_names = set(configurable_services.keys())
        overlap = constant_names.intersection(configurable_names)
        assert len(overlap) == 0, f"Overlap between constant and configurable: {overlap}"
        
        print("✅ Constant vs configurable classification correct")
        return True
    except Exception as e:
        print(f"❌ Constant vs configurable test failed: {e}")
        return False

def run_all_tests():
    """Run all port manager integration tests"""
    print("🧪 DEEPLICA PORT MANAGER INTEGRATION TESTS")
    print("=" * 60)
    
    tests = [
        test_port_manager_basic_functionality,
        test_port_settings_file,
        test_no_duplicate_settings_files,
        test_service_imports,
        test_port_ranges,
        test_port_uniqueness,
        test_constant_vs_configurable
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
        print()
    
    print("=" * 60)
    print(f"📊 TEST RESULTS: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 ALL TESTS PASSED! Port manager integration is working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
