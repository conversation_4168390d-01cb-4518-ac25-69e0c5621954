#!/usr/bin/env python3
"""
Test imports for startup orchestrator
"""

print("🔍 Testing startup orchestrator imports...")

try:
    print("1. Testing basic imports...")
    import asyncio
    import httpx
    import time
    import sys
    import subprocess
    import os
    import webbrowser
    from datetime import datetime
    print("✅ Basic imports successful")
    
    print("2. Testing port manager import...")
    from shared.port_manager import get_service_port, cleanup_all_ports, ensure_service_port_free
    print("✅ Port manager import successful")
    
    print("3. Testing port manager functions...")
    backend_port = get_service_port("BACKEND-API")
    print(f"✅ Backend port: {backend_port}")
    
    watchdog_port = get_service_port("WATCHDOG")
    print(f"✅ Watchdog port: {watchdog_port}")
    
    web_chat_port = get_service_port("WEB-CHAT")
    print(f"✅ Web chat port: {web_chat_port}")
    
    print("4. Testing orchestrator class creation...")
    from startup_orchestrator import DeepplicaStartupOrchestrator
    orchestrator = DeepplicaStartupOrchestrator()
    print("✅ Orchestrator created successfully")
    
    print("🎉 All imports and basic functionality working!")
    
except Exception as e:
    print(f"❌ Import test failed: {e}")
    import traceback
    traceback.print_exc()
