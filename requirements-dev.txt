# Development Requirements
# Additional tools for development, testing, and debugging

# Include base requirements
-r requirements.txt

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================

# Code Quality
pre-commit==3.6.0
bandit==1.7.5  # Security linting

# Documentation
mkdocs==1.5.3
mkdocs-material==9.4.8

# Jupyter for exploration
jupyter==1.0.0
ipython==8.17.2

# Database tools
mongomock==4.1.2  # Mock MongoDB for testing

# API documentation - FastAPI provides built-in docs at /docs and /redoc
# No additional packages needed!

# =============================================================================
# TESTING ENHANCEMENTS
# =============================================================================

# Coverage
pytest-cov==4.1.0
coverage==7.3.2

# Performance testing
pytest-benchmark==4.0.0

# Async testing helpers
pytest-asyncio==0.21.1
aioresponses==0.7.4  # Mock HTTP responses

# =============================================================================
# DEBUGGING & PROFILING
# =============================================================================

# Memory profiling
memory-profiler==0.61.0

# Performance profiling
py-spy==0.3.14

# Request debugging
requests-mock==1.11.0

# =============================================================================
# DOCKER DEVELOPMENT
# =============================================================================

# Docker compose for local development
# (Note: docker-compose is typically installed separately)

# =============================================================================
# IDE SUPPORT
# =============================================================================

# Language server
python-lsp-server==1.9.0

# Type checking
mypy==1.7.1
types-requests==2.31.0.10
