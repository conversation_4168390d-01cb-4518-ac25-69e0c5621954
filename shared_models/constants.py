"""
Shared constants for the Deeplica v0 system.
Contains enum values for mission and task states and types.
"""

from enum import Enum


class MissionStatus(str, Enum):
    """Mission execution status - shared across all services"""
    CREATED = "created"
    PLANNING = "planning"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskStatus(str, Enum):
    """Task execution status - shared across all services"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    DONE = "done"
    FAILED = "failed"


class MissionPriority(str, Enum):
    """Mission priority levels - shared across all services"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class TaskType(str, Enum):
    """Types of tasks that can be executed - shared across all services"""
    PLANNING = "planning"
    DIALOGUE = "dialogue"
    INFORMATION_GATHERING = "information_gathering"
    DECISION_MAKING = "decision_making"
    EXTERNAL_INTERACTION = "external_interaction"
    EXECUTION = "execution"
    PHONE_CALL = "phone_call"
