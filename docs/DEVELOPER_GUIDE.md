# 👨‍💻 DEEPLICA V3 DEVELOPER GUIDE

## **Complete Development Environment Setup & Best Practices**

---

## 🛠️ **Development Environment Setup**

### **Prerequisites**
- **Python 3.11+**: Required for all services
- **VS Code**: Recommended IDE with integrated debugging
- **MongoDB Atlas**: Database service (or local MongoDB)
- **Twilio Account**: For phone call functionality
- **ngrok**: For webhook tunneling

### **Initial Setup**
```bash
# Clone repository
git clone <repository-url>
cd deeplica-v3

# Create virtual environment
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Setup environment variables
cp .env.example .env
# Edit .env with your configuration
```

### **VS Code Configuration**
The project includes comprehensive VS Code configuration:
- **Launch configurations**: All services with debugging
- **Tasks**: Build and test automation
- **Extensions**: Recommended extensions for Python development
- **Settings**: Optimized for the project structure

---

## 🏗️ **Project Structure**

```
deeplica-v3/
├── agents/                     # Microservices
│   ├── backend/               # Backend API service
│   │   ├── app/
│   │   │   ├── main.py       # FastAPI application
│   │   │   ├── models/       # Data models
│   │   │   ├── routes/       # API endpoints
│   │   │   └── services/     # Business logic
│   │   └── requirements.txt
│   ├── dispatcher/            # Task orchestration
│   ├── dialogue/              # User interaction
│   ├── planner/               # Mission planning
│   ├── phone/                 # Voice communication
│   └── cli/                   # Terminal interface
├── watchdog/                  # System monitoring
├── orchestrator/              # Service startup
├── stop_deeplica/             # Safe shutdown
├── shared/                    # Common libraries
│   ├── models/               # Shared data models
│   ├── utils/                # Utility functions
│   └── config/               # Configuration management
├── docs/                      # Documentation
├── tests/                     # Test suites
├── scripts/                   # Utility scripts
├── .vscode/                   # VS Code configuration
├── requirements.txt           # Main dependencies
└── .env                       # Environment variables
```

---

## 🔧 **Development Workflow**

### **Starting Development**
1. **Start all services**: Use VS Code "🚀 START DEEPLICA"
2. **Verify health**: Check all service health endpoints
3. **Run tests**: Execute test suite to ensure everything works
4. **Start coding**: Make changes with hot reload enabled

### **Code Development Process**
1. **Create feature branch**: `git checkout -b feature/new-feature`
2. **Write tests first**: Test-driven development approach
3. **Implement feature**: Write clean, documented code
4. **Run tests**: Ensure all tests pass
5. **Test integration**: Verify with other services
6. **Create pull request**: Code review process

### **Testing Strategy**
```bash
# Run all tests
python -m pytest tests/

# Run specific service tests
python -m pytest tests/test_backend.py

# Run integration tests
python -m pytest tests/integration/

# Run phone system tests
python3 phone_test_system.py

# Performance tests
python -m pytest tests/performance/
```

---

## 📝 **Coding Standards**

### **Python Code Style**
- **PEP 8**: Follow Python style guidelines
- **Type hints**: Use type annotations for all functions
- **Docstrings**: Document all classes and functions
- **Error handling**: Comprehensive exception handling

### **Example Code Structure**
```python
from typing import Optional, Dict, Any
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel

class TaskRequest(BaseModel):
    """Request model for task creation."""
    task_type: str
    task_data: Dict[str, Any]
    priority: Optional[str] = "normal"

async def create_task(request: TaskRequest) -> Dict[str, Any]:
    """
    Create a new task for execution.
    
    Args:
        request: Task creation request
        
    Returns:
        Task creation response with task ID
        
    Raises:
        HTTPException: If task creation fails
    """
    try:
        # Implementation here
        return {"task_id": "uuid", "status": "created"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

### **API Design Principles**
- **RESTful endpoints**: Follow REST conventions
- **Consistent responses**: Standardized response format
- **Error handling**: Proper HTTP status codes
- **Documentation**: OpenAPI/Swagger documentation

---

## 🧪 **Testing Framework**

### **Test Structure**
```
tests/
├── unit/                      # Unit tests
│   ├── test_backend.py
│   ├── test_dialogue.py
│   └── test_phone.py
├── integration/               # Integration tests
│   ├── test_mission_flow.py
│   └── test_phone_calls.py
├── performance/               # Performance tests
│   └── test_load.py
└── fixtures/                  # Test data
    └── sample_data.py
```

### **Writing Tests**
```python
import pytest
from fastapi.testclient import TestClient
from agents.backend.app.main import app

client = TestClient(app)

def test_health_endpoint():
    """Test service health endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"

@pytest.mark.asyncio
async def test_mission_creation():
    """Test mission creation flow."""
    mission_data = {
        "user_input": "Test mission",
        "title": "Test",
        "priority": "normal"
    }
    response = client.post("/api/v1/missions", json=mission_data)
    assert response.status_code == 201
    assert "mission_id" in response.json()
```

---

## 🔍 **Debugging**

### **VS Code Debugging**
- **Breakpoints**: Set breakpoints in any service
- **Variable inspection**: Examine variables at runtime
- **Call stack**: Navigate through function calls
- **Debug console**: Execute code during debugging

### **Logging Best Practices**
```python
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='[%(name)s:%(funcName)s] %(levelname)s | %(message)s'
)
logger = logging.getLogger(__name__)

# Use structured logging
logger.info("Task created", extra={
    "task_id": task_id,
    "task_type": task_type,
    "user_id": user_id
})
```

### **Debug Tools**
```bash
# Service health check
curl http://localhost:8000/health

# Database connection test
python3 -c "from agents.backend.app.database import test_connection; test_connection()"

# Phone system test
python3 phone_test_system.py

# Watchdog system status
curl http://localhost:8005/system_status
```

---

## 🔄 **Service Development**

### **Adding New Service**
1. **Create service directory**: `agents/new_service/`
2. **Implement FastAPI app**: Basic service structure
3. **Add health endpoint**: Required for monitoring
4. **Update orchestrator**: Include in startup sequence
5. **Add VS Code config**: Launch configuration
6. **Write tests**: Unit and integration tests

### **Service Template**
```python
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import os

app = FastAPI(title="New Service", version="1.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/health")
async def health_check():
    """Service health check endpoint."""
    return {
        "status": "healthy",
        "service": "new-service",
        "version": "1.0.0"
    }

if __name__ == "__main__":
    port = int(os.getenv("PORT", 8006))
    uvicorn.run(app, host="0.0.0.0", port=port)
```

---

## 📦 **Dependency Management**

### **Requirements Structure**
```
requirements.txt              # Main dependencies
agents/backend/requirements.txt    # Backend-specific
agents/phone/requirements.txt      # Phone-specific
requirements-dev.txt          # Development dependencies
requirements-test.txt         # Testing dependencies
```

### **Adding Dependencies**
```bash
# Add to appropriate requirements file
echo "new-package==1.0.0" >> requirements.txt

# Install in development environment
pip install -r requirements.txt

# Update all services
./scripts/update_dependencies.sh
```

---

## 🚀 **Performance Optimization**

### **Code Optimization**
- **Async/await**: Use async programming for I/O operations
- **Connection pooling**: Reuse database and HTTP connections
- **Caching**: Cache frequently accessed data
- **Lazy loading**: Load data only when needed

### **Database Optimization**
```python
# Use connection pooling
from motor.motor_asyncio import AsyncIOMotorClient

client = AsyncIOMotorClient(
    connection_string,
    maxPoolSize=10,
    minPoolSize=1
)

# Use indexes for queries
await collection.create_index("mission_id")
await collection.create_index([("status", 1), ("created_at", -1)])
```

### **API Optimization**
```python
# Use response models for serialization
from pydantic import BaseModel

class MissionResponse(BaseModel):
    mission_id: str
    status: str
    created_at: datetime

@app.get("/missions/{mission_id}", response_model=MissionResponse)
async def get_mission(mission_id: str):
    # Implementation
    pass
```

---

## 🔐 **Security Best Practices**

### **Environment Variables**
- **Never commit secrets**: Use .env files (gitignored)
- **Validate inputs**: Sanitize all user inputs
- **Use HTTPS**: Secure all external communications
- **Rate limiting**: Implement API rate limiting

### **Code Security**
```python
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer

security = HTTPBearer()

async def verify_token(token: str = Depends(security)):
    """Verify API token."""
    if not validate_token(token.credentials):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )
    return token
```

---

## 📚 **Documentation**

### **Code Documentation**
- **Docstrings**: Document all functions and classes
- **Type hints**: Use type annotations
- **Comments**: Explain complex logic
- **README files**: Service-specific documentation

### **API Documentation**
FastAPI automatically generates OpenAPI documentation:
- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`
- **OpenAPI JSON**: `http://localhost:8000/openapi.json`

---

**Next**: See [VOICE_INTEGRATION.md](VOICE_INTEGRATION.md) for voice interface development
