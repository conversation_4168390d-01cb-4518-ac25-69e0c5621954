#!/usr/bin/env python3
"""
Test script to validate Twilio Echo-Bot setup
"""

import os
import sys

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("📁 Loaded environment variables from .env file")
except ImportError:
    print("⚠️ python-dotenv not installed, .env file not loaded automatically")
    print("💡 Install with: pip install python-dotenv")
    print("💡 Or manually run: source .env")
except Exception as e:
    print(f"⚠️ Could not load .env file: {e}")

print()

def test_imports():
    """Test if all required packages can be imported."""
    print("🧪 Testing imports...")
    
    try:
        import twilio
        print(f"✅ Twilio SDK: {twilio.__version__}")
    except ImportError as e:
        print(f"❌ Failed to import Twilio SDK: {e}")
        return False
    
    try:
        import flask
        print(f"✅ Flask: {flask.__version__}")
    except ImportError as e:
        print(f"❌ Failed to import Flask: {e}")
        return False
    
    try:
        from pyngrok import ngrok
        print("✅ Ngrok: Available")
    except ImportError as e:
        print(f"❌ Failed to import pyngrok: {e}")
        return False
    
    return True

def test_environment():
    """Test if environment variables are set."""
    print("\n🔧 Testing environment variables...")
    
    required_vars = [
        'TWILIO_ACCOUNT_SID',
        'TWILIO_AUTH_TOKEN', 
        'TWILIO_PHONE_NUMBER'
    ]
    
    missing_vars = []
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            # Mask sensitive values
            if 'TOKEN' in var or 'SID' in var:
                masked = value[:8] + '*' * (len(value) - 8) if len(value) > 8 else '*' * len(value)
                print(f"✅ {var}: {masked}")
            else:
                print(f"✅ {var}: {value}")
        else:
            print(f"❌ {var}: Not set")
            missing_vars.append(var)
    
    optional_vars = ['NGROK_AUTHTOKEN']
    for var in optional_vars:
        value = os.getenv(var)
        if value:
            masked = value[:8] + '*' * (len(value) - 8) if len(value) > 8 else '*' * len(value)
            print(f"ℹ️ {var}: {masked} (optional)")
        else:
            print(f"ℹ️ {var}: Not set (optional)")
    
    return len(missing_vars) == 0, missing_vars

def test_twilio_connection():
    """Test Twilio API connection."""
    print("\n📞 Testing Twilio connection...")
    
    account_sid = os.getenv('TWILIO_ACCOUNT_SID')
    auth_token = os.getenv('TWILIO_AUTH_TOKEN')
    
    if not account_sid or not auth_token:
        print("❌ Cannot test Twilio connection: Missing credentials")
        return False
    
    try:
        from twilio.rest import Client
        client = Client(account_sid, auth_token)
        
        # Test connection by fetching account info
        account = client.api.accounts(account_sid).fetch()
        print(f"✅ Connected to Twilio account: {account.friendly_name}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to connect to Twilio: {e}")
        return False

def main():
    """Run all tests."""
    print("🤖 Twilio Echo-Bot Setup Test\n")
    
    # Test imports
    imports_ok = test_imports()
    
    # Test environment
    env_ok, missing_vars = test_environment()
    
    # Test Twilio connection (only if env vars are set)
    twilio_ok = test_twilio_connection() if env_ok else False
    
    # Summary
    print("\n📊 Test Summary:")
    print(f"   Imports: {'✅ PASS' if imports_ok else '❌ FAIL'}")
    print(f"   Environment: {'✅ PASS' if env_ok else '❌ FAIL'}")
    print(f"   Twilio Connection: {'✅ PASS' if twilio_ok else '❌ FAIL'}")
    
    if imports_ok and env_ok and twilio_ok:
        print("\n🎉 All tests passed! You're ready to run the echo bot.")
        print("\nUsage: python echo_bot.py +**********")
        return 0
    else:
        print("\n❌ Some tests failed. Please check the issues above.")
        
        if not imports_ok:
            print("\n💡 To fix import issues:")
            print("   pip install -r requirements.txt")
        
        if not env_ok:
            print(f"\n💡 To fix environment issues:")
            print("   Set the following environment variables:")
            for var in missing_vars:
                print(f"   export {var}=your_value_here")
            print("   Or edit the .env file and run: source .env")
        
        return 1

if __name__ == '__main__':
    sys.exit(main())
