#!/usr/bin/env python3
"""
🔧 ADMIN NAVIGATION TEST

Quick test to verify admin navigation is working properly.
"""

import requests
import time
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

from shared.port_manager import get_service_port

def test_admin_navigation_simple():
    """Simple test of admin navigation"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔧 Testing admin navigation on port {web_chat_port}...")
        
        # Create session
        session = requests.Session()
        
        # Login
        login_data = {'username': 'admin', 'password': 'admin123'}
        
        print("📝 Logging in...")
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
        
        print(f"✅ Login successful")
        print(f"🔗 Login final URL: {login_response.url}")
        
        # Try to access chat first
        print("💬 Accessing chat...")
        chat_response = session.get(
            f"http://localhost:{web_chat_port}/chat",
            allow_redirects=True,
            timeout=10
        )
        
        if chat_response.status_code != 200:
            print(f"❌ Chat access failed: {chat_response.status_code}")
            return False
        
        print(f"✅ Chat access successful")
        print(f"🔗 Chat URL: {chat_response.url}")
        
        # Check if chat has admin navigation
        chat_content = chat_response.text
        if 'navigateToAdmin' in chat_content:
            print(f"✅ Chat has admin navigation function")
        else:
            print(f"❌ Chat missing admin navigation function")
            return False
        
        if '⚙️' in chat_content:
            print(f"✅ Chat has admin icon")
        else:
            print(f"❌ Chat missing admin icon")
            return False
        
        # Now try to access admin directly
        print("🔧 Accessing admin...")
        admin_response = session.get(
            f"http://localhost:{web_chat_port}/admin",
            allow_redirects=True,
            timeout=10
        )
        
        if admin_response.status_code != 200:
            print(f"❌ Admin access failed: {admin_response.status_code}")
            print(f"📋 Response text: {admin_response.text[:200]}...")
            return False
        
        print(f"✅ Admin access successful")
        print(f"🔗 Admin URL: {admin_response.url}")
        
        # Check if admin page has proper content
        admin_content = admin_response.text
        if 'Admin Panel' in admin_content or 'DEEPLICA Admin' in admin_content:
            print(f"✅ Admin page loaded correctly")
        else:
            print(f"❌ Admin page content issue")
            return False
        
        # Check if admin has tab verification
        if 'verifyTabToken' in admin_content:
            print(f"✅ Admin has tab verification")
        else:
            print(f"❌ Admin missing tab verification")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing admin navigation: {e}")
        return False

def test_admin_token_generation():
    """Test admin token generation"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔐 Testing admin token generation on port {web_chat_port}...")
        
        # Login first
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login failed for token test")
            return False
        
        # Test admin token generation
        admin_tab_id = f"admin_test_{int(time.time())}"
        
        api_response = session.post(
            f"http://localhost:{web_chat_port}/api/generate-tab-token",
            json={"tab_id": admin_tab_id},
            timeout=10
        )
        
        if api_response.status_code != 200:
            print(f"❌ Admin token API failed: {api_response.status_code}")
            print(f"📋 Response: {api_response.text}")
            return False
        
        result = api_response.json()
        
        if result.get('success'):
            print(f"✅ Admin token generated successfully")
            print(f"🔗 Token: {result['tab_token'][:20]}...")
            print(f"🔗 URL: {result['secure_url']}")
            return True
        else:
            print(f"❌ Admin token generation failed: {result}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing admin token generation: {e}")
        return False

def main():
    """Run admin navigation tests"""
    print("🔧 ADMIN NAVIGATION TEST")
    print("=" * 50)
    print("Testing admin functionality:")
    print("- Admin navigation from chat")
    print("- Admin page access")
    print("- Admin token generation")
    print()
    
    tests_passed = 0
    total_tests = 2
    
    # Test 1: Admin navigation
    print("🧪 TEST 1: Admin Navigation")
    if test_admin_navigation_simple():
        tests_passed += 1
    
    # Test 2: Admin token generation
    print("\n🧪 TEST 2: Admin Token Generation")
    if test_admin_token_generation():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"🔧 TEST RESULTS: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✅ ALL TESTS PASSED - Admin navigation working!")
        print("🎉 Admin features verified:")
        print("   - Chat to admin navigation")
        print("   - Admin page access")
        print("   - Admin token generation")
        print("   - Tab verification system")
    else:
        print("❌ SOME TESTS FAILED - Admin navigation needs fixes")
        
        if tests_passed >= 1:
            print("🔧 RECOMMENDATION: Some admin features work")
        else:
            print("🔧 RECOMMENDATION: Major admin fixes needed")
    
    print(f"\n🌐 Manual test:")
    print(f"1. Open http://localhost:8007")
    print(f"2. Login with: admin / admin123")
    print(f"3. Click the ⚙️ admin icon in chat")
    print(f"4. Should navigate to admin panel")

if __name__ == '__main__':
    main()
