#!/usr/bin/env python3
"""
🔍 DEEPLICA PROCESS MONITOR
Comprehensive process investigation and monitoring tool
"""

import os
import sys
import psutil
import json
import time
import requests
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

# Add project root to path
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

from shared.port_manager import get_service_port, get_localhost
from shared.unified_logging import get_logger

logger = get_logger("PROCESS-MONITOR")

@dataclass
class ProcessInfo:
    """Process information structure"""
    pid: int
    name: str
    service_name: str
    port: Optional[int]
    status: str
    cpu_percent: float
    memory_mb: float
    cmdline: List[str]
    create_time: datetime
    connections: List[Dict]
    health_status: str = "unknown"
    dependencies: List[str] = None

class DeepplicaProcessMonitor:
    """Comprehensive DEEPLICA process monitoring"""
    
    def __init__(self):
        self.services = {
            "WATCHDOG": {
                "identifiers": ["DEEPLICA-WATCHDOG", "watchdog/main.py", "watchdog.main"],
                "port": get_service_port("watchdog"),
                "health_endpoint": "/health",
                "dependencies": []
            },
            "BACKEND-API": {
                "identifiers": ["DEEPLICA-BACKEND-API", "backend.app.main", "backend/app/main.py"],
                "port": get_service_port("backend"),
                "health_endpoint": "/health",
                "dependencies": ["WATCHDOG"]
            },
            "DISPATCHER": {
                "identifiers": ["DEEPLICA-DISPATCHER", "dispatcher.app.main", "dispatcher/app/main.py"],
                "port": get_service_port("dispatcher"),
                "health_endpoint": "/health",
                "dependencies": ["BACKEND-API"]
            },
            "DIALOGUE-AGENT": {
                "identifiers": ["DEEPLICA-DIALOGUE-AGENT", "agents.dialogue.app.main", "agents/dialogue/app/main.py"],
                "port": get_service_port("dialogue"),
                "health_endpoint": "/health",
                "dependencies": ["BACKEND-API"]
            },
            "PLANNER-AGENT": {
                "identifiers": ["DEEPLICA-PLANNER-AGENT", "agents.planner.app.main", "agents/planner/app/main.py"],
                "port": get_service_port("planner"),
                "health_endpoint": "/health",
                "dependencies": ["BACKEND-API"]
            },
            "PHONE-AGENT": {
                "identifiers": ["DEEPLICA-PHONE-AGENT", "agents.phone.app.main", "agents/phone/app/main.py"],
                "port": get_service_port("phone"),
                "health_endpoint": "/health",
                "dependencies": ["BACKEND-API"]
            },
            "CLI-TERMINAL": {
                "identifiers": ["DEEPLICA-CLI-TERMINAL", "cli/main.py", "cli.main"],
                "port": None,
                "health_endpoint": None,
                "dependencies": ["BACKEND-API"]
            },
            "WEB-CHAT": {
                "identifiers": ["DEEPLICA-WEB-CHAT", "web_chat/main.py", "web_chat.main"],
                "port": get_service_port("webchat"),
                "health_endpoint": "/health",
                "dependencies": ["BACKEND-API"]
            }
        }
        
    def find_deeplica_processes(self) -> Dict[str, List[ProcessInfo]]:
        """Find all DEEPLICA processes"""
        processes = {}
        
        for service_name, config in self.services.items():
            processes[service_name] = []
            
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'status', 'cpu_percent', 'memory_info', 'create_time']):
                try:
                    cmdline_str = ' '.join(proc.info['cmdline'] or [])

                    # Check if this process matches any service identifier
                    for identifier in config['identifiers']:
                        if identifier in cmdline_str:
                            # Get network connections separately
                            connections = []
                            try:
                                process_obj = psutil.Process(proc.info['pid'])
                                for conn in process_obj.connections():
                                    if conn.status == 'LISTEN':
                                        connections.append({
                                            'port': conn.laddr.port,
                                            'status': conn.status
                                        })
                            except:
                                pass
                            
                            process_info = ProcessInfo(
                                pid=proc.info['pid'],
                                name=proc.info['name'],
                                service_name=service_name,
                                port=config['port'],
                                status=proc.info['status'],
                                cpu_percent=proc.info['cpu_percent'] or 0.0,
                                memory_mb=proc.info['memory_info'].rss / 1024 / 1024 if proc.info['memory_info'] else 0.0,
                                cmdline=proc.info['cmdline'] or [],
                                create_time=datetime.fromtimestamp(proc.info['create_time']),
                                connections=connections,
                                dependencies=config['dependencies']
                            )
                            
                            # Check health status
                            process_info.health_status = self.check_service_health(service_name, config)
                            
                            processes[service_name].append(process_info)
                            break
                            
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
                    
        return processes
    
    def check_service_health(self, service_name: str, config: Dict) -> str:
        """Check service health via HTTP endpoint"""
        if not config.get('port') or not config.get('health_endpoint'):
            return "no_health_check"
            
        try:
            url = f"http://{get_localhost()}:{config['port']}{config['health_endpoint']}"
            response = requests.get(url, timeout=2)
            if response.status_code == 200:
                return "healthy"
            else:
                return f"unhealthy_http_{response.status_code}"
        except requests.exceptions.ConnectionError:
            return "connection_refused"
        except requests.exceptions.Timeout:
            return "timeout"
        except Exception as e:
            return f"error_{type(e).__name__}"
    
    def analyze_dependencies(self, processes: Dict[str, List[ProcessInfo]]) -> Dict[str, str]:
        """Analyze dependency satisfaction"""
        dependency_status = {}
        
        for service_name, service_processes in processes.items():
            if not service_processes:
                dependency_status[service_name] = "not_running"
                continue
                
            # Check if dependencies are satisfied
            config = self.services[service_name]
            missing_deps = []
            
            for dep in config['dependencies']:
                if not processes.get(dep) or not any(p.health_status == "healthy" for p in processes[dep]):
                    missing_deps.append(dep)
            
            if missing_deps:
                dependency_status[service_name] = f"missing_deps: {', '.join(missing_deps)}"
            else:
                dependency_status[service_name] = "dependencies_satisfied"
                
        return dependency_status
    
    def detect_circular_dependencies(self) -> List[List[str]]:
        """Detect circular dependencies"""
        def has_path(start, end, visited=None):
            if visited is None:
                visited = set()
            if start == end:
                return True
            if start in visited:
                return False
            visited.add(start)
            
            for dep in self.services.get(start, {}).get('dependencies', []):
                if has_path(dep, end, visited.copy()):
                    return True
            return False
        
        cycles = []
        for service in self.services:
            for dep in self.services[service].get('dependencies', []):
                if has_path(dep, service):
                    cycle = [service, dep]
                    if cycle not in cycles and cycle[::-1] not in cycles:
                        cycles.append(cycle)
        
        return cycles
    
    def generate_report(self) -> str:
        """Generate comprehensive process report"""
        report = []
        report.append("🔍 DEEPLICA PROCESS MONITOR REPORT")
        report.append("=" * 80)
        report.append(f"📅 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Find all processes
        processes = self.find_deeplica_processes()
        
        # Service overview
        report.append("📊 SERVICE OVERVIEW")
        report.append("-" * 40)
        total_processes = sum(len(procs) for procs in processes.values())
        running_services = sum(1 for procs in processes.values() if procs)
        
        report.append(f"Total DEEPLICA processes: {total_processes}")
        report.append(f"Services running: {running_services}/{len(self.services)}")
        report.append("")
        
        # Dependency analysis
        dependency_status = self.analyze_dependencies(processes)
        cycles = self.detect_circular_dependencies()
        
        if cycles:
            report.append("🔄 CIRCULAR DEPENDENCIES DETECTED!")
            for cycle in cycles:
                report.append(f"   ⚠️  {' → '.join(cycle + [cycle[0]])}")
            report.append("")
        
        # Detailed process information
        report.append("🔍 DETAILED PROCESS INFORMATION")
        report.append("-" * 40)
        
        for service_name, service_processes in processes.items():
            config = self.services[service_name]
            report.append(f"\n🔧 {service_name}")
            report.append(f"   Expected port: {config['port']}")
            report.append(f"   Dependencies: {', '.join(config['dependencies']) if config['dependencies'] else 'None'}")
            report.append(f"   Dependency status: {dependency_status.get(service_name, 'unknown')}")
            
            if not service_processes:
                report.append("   ❌ NOT RUNNING")
            else:
                for i, proc in enumerate(service_processes, 1):
                    report.append(f"   Instance #{i}:")
                    report.append(f"      PID: {proc.pid}")
                    report.append(f"      Status: {proc.status}")
                    report.append(f"      Health: {proc.health_status}")
                    report.append(f"      CPU: {proc.cpu_percent:.1f}%")
                    report.append(f"      Memory: {proc.memory_mb:.1f} MB")
                    report.append(f"      Started: {proc.create_time.strftime('%H:%M:%S')}")
                    if proc.connections:
                        ports = [str(conn['port']) for conn in proc.connections]
                        report.append(f"      Listening ports: {', '.join(ports)}")
                    else:
                        report.append(f"      Listening ports: None")
        
        return "\n".join(report)

def main():
    """Main function"""
    print("🔍 DEEPLICA PROCESS MONITOR")
    print("=" * 50)
    
    monitor = DeepplicaProcessMonitor()
    report = monitor.generate_report()
    
    print(report)
    
    # Save report to file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"process_report_{timestamp}.txt"
    
    with open(report_file, 'w') as f:
        f.write(report)
    
    print(f"\n📄 Report saved to: {report_file}")

if __name__ == "__main__":
    main()
