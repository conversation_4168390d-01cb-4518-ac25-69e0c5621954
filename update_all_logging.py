#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to systematically update all logging calls across DEEPLICA services
to use the unified logging format: time - [LEVEL] - Svc: [service], Mod: [module], Cod: [routine], msg: [message]
"""

import os
import re
import sys
from pathlib import Path


def update_logging_calls(file_path: str, service_name: str):
    """Update logging calls in a specific file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Pattern to match old logging calls like logger.info("[SERVICE] routine() - message")
        # or logger.info(f"[SERVICE:routine] CATEGORY | message")
        patterns = [
            # Pattern 1: [SERVICE] routine() - message
            (r'logger\.(info|debug|warning|error|critical)\(f?"?\[([^\]]+)\]\s*([^-\n]+?)\s*-\s*([^"]+)"?\)', 
             r'logger.\1("\4", module="\2", routine="\3")'),
            
            # Pattern 2: [SERVICE:routine] CATEGORY | message  
            (r'logger\.(info|debug|warning|error|critical)\(f?"?\[([^:]+):([^\]]+)\]\s*[A-Z]+\s*\|\s*([^"]+)"?\)',
             r'logger.\1("\4", module="\2", routine="\3")'),
            
            # Pattern 3: Simple logger calls without brackets
            (r'logger\.(info|debug|warning|error|critical)\(f?"([^"]+)"\)',
             r'logger.\1("\2", module="main", routine="unknown")'),
        ]
        
        for pattern, replacement in patterns:
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        # Clean up any remaining bracket patterns
        content = re.sub(r'\[([^\]]+)\]\s*([^-\n]+?)\s*-\s*', '', content)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Updated logging in {file_path}")
            return True
        else:
            print(f"⏭️ No changes needed in {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ Error updating {file_path}: {e}")
        return False


def update_service_logging(service_dir: str, service_name: str):
    """Update all Python files in a service directory"""
    service_path = Path(service_dir)
    if not service_path.exists():
        print(f"⚠️ Service directory not found: {service_dir}")
        return
    
    print(f"\n🔧 Updating {service_name} logging...")
    
    # Find all Python files
    python_files = list(service_path.rglob("*.py"))
    
    updated_count = 0
    for py_file in python_files:
        if update_logging_calls(str(py_file), service_name):
            updated_count += 1
    
    print(f"📊 Updated {updated_count} files in {service_name}")


def main():
    """Main function to update all services"""
    print("🚀 Starting unified logging format update across all DEEPLICA services")
    print("=" * 80)
    
    # Define services and their directories
    services = [
        ("backend", "BACKEND-API"),
        ("dispatcher", "DISPATCHER"), 
        ("agents/planner", "PLANNER-AGENT"),
        ("agents/phone", "PHONE-AGENT"),
        ("agents/dialogue", "DIALOGUE-AGENT"),
        ("watchdog", "WATCHDOG"),
        ("web_chat", "WEB-CHAT"),
        ("cli", "CLI-TERMINAL"),
    ]
    
    total_updated = 0
    
    for service_dir, service_name in services:
        try:
            update_service_logging(service_dir, service_name)
            total_updated += 1
        except Exception as e:
            print(f"❌ Failed to update {service_name}: {e}")
    
    print("\n" + "=" * 80)
    print(f"🎉 Logging update complete! Updated {total_updated} services")
    print("📋 All services now use unified format:")
    print("   time (yyyy-mm-dd HH:mm:ss) - [LEVEL] - Svc: [service], Mod: [module], Cod: [routine], msg: [message]")
    print("\n✅ CLI Terminal UI already redirects system messages to watchdog console")
    print("✅ All other services print directly to their own debug consoles")


if __name__ == "__main__":
    main()
