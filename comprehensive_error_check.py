#!/usr/bin/env python3
"""
Comprehensive error checker for the entire Deeplica codebase.
This script checks for syntax errors, import errors, and other issues.
"""

import os
import sys
import ast
import glob
import importlib.util
from typing import List, <PERSON><PERSON>, Dict

def check_syntax_errors(file_path: str) -> <PERSON><PERSON>[bool, str]:
    """Check for syntax errors"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        ast.parse(content)
        return True, ""
    except SyntaxError as e:
        return False, f"Syntax error at line {e.lineno}: {e.msg}"
    except Exception as e:
        return False, f"Error reading file: {e}"

def check_import_errors(file_path: str) -> <PERSON><PERSON>[bool, List[str]]:
    """Check for import errors by attempting to load the module"""
    try:
        # Add the project root to Python path
        project_root = os.path.dirname(os.path.abspath(__file__))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        
        # Try to load the module
        spec = importlib.util.spec_from_file_location("test_module", file_path)
        if spec and spec.loader:
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            return True, []
        else:
            return False, ["Could not create module spec"]
    except ImportError as e:
        return False, [f"Import error: {e}"]
    except ModuleNotFoundError as e:
        return False, [f"Module not found: {e}"]
    except Exception as e:
        return False, [f"Other error: {type(e).__name__}: {e}"]

def main():
    """Main function to check all errors"""
    print("🔍 [ERROR-CHECK:main] SYSTEM | Starting comprehensive error check...")
    print("=" * 80)
    
    # Find all Python files
    python_files = []
    for pattern in ["**/*.py"]:
        python_files.extend(glob.glob(pattern, recursive=True))
    
    # Filter out __pycache__ and .pyc files, and test files that might have intentional errors
    python_files = [f for f in python_files if '__pycache__' not in f and f.endswith('.py')]
    
    print(f"📁 [ERROR-CHECK:main] SYSTEM | Found {len(python_files)} Python files to check")
    print()
    
    syntax_errors = 0
    import_errors = 0
    total_files = len(python_files)
    
    # Check syntax errors
    print("🔍 [ERROR-CHECK:main] SYSTEM | Checking syntax errors...")
    for file_path in sorted(python_files):
        is_valid, error_msg = check_syntax_errors(file_path)
        if not is_valid:
            print(f"❌ [ERROR-CHECK:main] SYNTAX | {file_path}: {error_msg}")
            syntax_errors += 1
    
    if syntax_errors == 0:
        print("✅ [ERROR-CHECK:main] SUCCESS | No syntax errors found!")
    
    print()
    print("🔍 [ERROR-CHECK:main] SYSTEM | Checking import errors...")
    
    # Check import errors for main modules only (to avoid test files)
    main_modules = [
        "backend/app/main.py",
        "dispatcher/app/main.py", 
        "agents/dialogue/app/main.py",
        "agents/phone/app/main.py",
        "agents/planner/app/main.py",
        "cli/main.py",
        "watchdog/main.py",
        "orchestrator/main.py",
        "stop_deeplica/main.py"
    ]
    
    for file_path in main_modules:
        if os.path.exists(file_path):
            is_valid, errors = check_import_errors(file_path)
            if not is_valid:
                print(f"❌ [ERROR-CHECK:main] IMPORT | {file_path}:")
                for error in errors:
                    print(f"   {error}")
                import_errors += 1
            else:
                print(f"✅ [ERROR-CHECK:main] SUCCESS | {file_path}")
    
    print()
    print("=" * 80)
    print(f"📊 [ERROR-CHECK:main] SYSTEM | Comprehensive error check complete!")
    print(f"📊 [ERROR-CHECK:main] SYSTEM | Files checked: {total_files}")
    print(f"📊 [ERROR-CHECK:main] SYSTEM | Syntax errors: {syntax_errors}")
    print(f"📊 [ERROR-CHECK:main] SYSTEM | Import errors: {import_errors}")
    
    if syntax_errors == 0 and import_errors == 0:
        print("🎉 [ERROR-CHECK:main] SUCCESS | ALL CHECKS PASSED! No errors found!")
    else:
        print("⚠️ [ERROR-CHECK:main] WARNING | Some errors found - see details above")

if __name__ == "__main__":
    main()
