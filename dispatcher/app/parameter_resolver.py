"""
Parameter Resolution Service - Resolves task parameter placeholders.

This service handles the resolution of placeholders in task parameters
that reference results from previously completed tasks.

Placeholder format: {{task:full_task_id:path.to.value}}
Example: {{task:2c2cbbb6-7b56-44f8-9637-b08d700be1d2_call_eran:result.extracted_answer}}

The full_task_id includes the mission ID prefix (e.g., mission_id_task_name).
"""

import re
import json
from typing import Dict, Any, List, Optional, Union
from shared.unified_logging import get_dispatcher_logger

# Initialize unified logger
logger = get_dispatcher_logger()


class ParameterResolver:
    """Service for resolving task parameter placeholders"""

    # Regex patterns for different placeholder types
    TASK_PLACEHOLDER_PATTERN = r'\{\{task:([^:]+):([^}]+)\}\}'
    USER_PLACEHOLDER_PATTERN = r'\{\{user:([^}]+)\}\}'
    MISSION_PLACEHOLDER_PATTERN = r'\{\{mission:([^}]+)\}\}'

    # Special placeholder values
    USER_WILL_PROVIDE = "user_will_provide"

    def __init__(self, db_service):
        """Initialize with database service for task lookup"""
        self.db_service = db_service
    
    async def resolve_task_parameters(
        self,
        task_data: Dict[str, Any],
        mission_id: str,
        mission_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Resolve all placeholders in task data.

        Args:
            task_data: Task data that may contain placeholders
            mission_id: Mission ID for task lookup context
            mission_context: Mission context for resolving mission placeholders

        Returns:
            Task data with resolved placeholders

        Raises:
            ValueError: If placeholder references invalid task or path
        """
        logger.info("🔍 Resolving parameters for mission: {mission_id}", module="DISPATCHER", routine="resolve_task_parameters")

        # Deep copy to avoid modifying original
        resolved_data = json.loads(json.dumps(task_data))

        # Handle special "user_will_provide" values first
        resolved_data = await self._resolve_user_will_provide(resolved_data, mission_id, mission_context)

        # Find all remaining placeholders in the data
        placeholders = self._find_all_placeholders(resolved_data)

        if not placeholders:
            logger.debug("No placeholders found in task data", module="DISPATCHER", routine="resolve_task_parameters")
            return resolved_data

        logger.info("Found {len(placeholders)} placeholders to resolve", module="DISPATCHER", routine="resolve_task_parameters")

        # Resolve each placeholder
        for placeholder_info in placeholders:
            await self._resolve_single_placeholder(
                resolved_data,
                placeholder_info,
                mission_id,
                mission_context
            )

        logger.info("✅ All placeholders resolved successfully", module="DISPATCHER", routine="resolve_task_parameters")
        return resolved_data

    async def _resolve_user_will_provide(
        self,
        data: Dict[str, Any],
        mission_id: str,
        mission_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Resolve 'user_will_provide' placeholders by looking for values in mission context
        or from dialogue tasks that collected user input.
        """

        # Get mission context if not provided
        if not mission_context:
            mission = await self.db_service.get_mission(mission_id)
            mission_context = mission.context if mission else {}

        # Check for phone number specifically
        if data.get("phone_number") == self.USER_WILL_PROVIDE:
            logger.info("🔍 Resolving 'user_will_provide' phone number", module="DISPATCHER", routine="_resolve_user_will_provide")

            # Try to find phone number from mission context
            # Handle both dict and MissionContext object
            if hasattr(mission_context, 'get'):
                # It's a dictionary
                phone_number = mission_context.get("phone_number")
            elif hasattr(mission_context, 'phone_number'):
                # It's a MissionContext object
                phone_number = getattr(mission_context, 'phone_number', None)
            else:
                # Fallback - try to convert to dict if it's a Pydantic model
                try:
                    if hasattr(mission_context, 'model_dump'):
                        context_dict = mission_context.model_dump()
                        phone_number = context_dict.get("phone_number")
                    else:
                        phone_number = None
                except Exception:
                    phone_number = None

            # If not in mission context, try to find from dialogue tasks
            if not phone_number:
                phone_number = await self._extract_phone_from_dialogue_tasks(mission_id)

            # If still not found, try to extract from user input
            if not phone_number:
                phone_number = await self._extract_phone_from_user_input(mission_id)

            if phone_number:
                # Validate the phone number before using it
                if self._validate_phone_number(phone_number):
                    logger.info("✅ Resolved and validated phone number: {phone_number}", module="DISPATCHER", routine="_resolve_user_will_provide")
                    data["phone_number"] = phone_number
                else:
                    logger.error("❌ Invalid phone number format: {phone_number}", module="DISPATCHER", routine="_resolve_user_will_provide")
                    logger.error("❌ Phone number validation failed for mission: {mission_id}", module="DISPATCHER", routine="_resolve_user_will_provide")
                    # Set a clear error value that the phone agent can detect
                    data["phone_number"] = f"INVALID_PHONE_NUMBER:{phone_number}"
            else:
                logger.error("❌ Could not resolve phone number for mission: {mission_id}", module="DISPATCHER", routine="_resolve_user_will_provide")
                # Set a clear error value that the phone agent can detect
                data["phone_number"] = "PHONE_NUMBER_NOT_FOUND"

        return data

    async def _extract_phone_from_dialogue_tasks(self, mission_id: str) -> Optional[str]:
        """Extract phone number from completed dialogue tasks"""
        try:
            # Get all completed dialogue tasks for this mission
            tasks = await self.db_service.get_tasks_by_mission(mission_id)

            for task in tasks:
                if (task.task_type == "dialogue" and
                    task.status == "done" and
                    task.result):

                    # Look for phone number in task result
                    result = task.result

                    # Check various possible fields
                    phone_fields = ["phone_number", "phone", "number", "contact_number"]
                    for field in phone_fields:
                        if field in result and result[field]:
                            return result[field]

                    # Check if phone number is mentioned in the response text
                    response_text = result.get("message", "")
                    phone_number = self._extract_phone_from_text(response_text)
                    if phone_number:
                        return phone_number

            return None
        except Exception as e:
            logger.error("❌ Error extracting phone from dialogue tasks: {e}", module="DISPATCHER", routine="_extract_phone_from_dialogue_tasks")
            return None

    async def _extract_phone_from_user_input(self, mission_id: str) -> Optional[str]:
        """Extract phone number from original user input"""
        try:
            mission = await self.db_service.get_mission(mission_id)
            if not mission:
                return None

            user_input = mission.user_input
            return self._extract_phone_from_text(user_input)
        except Exception as e:
            logger.error("❌ Error extracting phone from user input: {e}", module="DISPATCHER", routine="_extract_phone_from_user_input")
            return None

    def _extract_phone_from_text(self, text: str) -> Optional[str]:
        """Extract phone number from text using regex patterns"""
        import re

        # Common phone number patterns
        patterns = [
            r'\+\d{1,4}[-.\s]?\d{1,4}[-.\s]?\d{1,9}',  # International format
            r'\+\d{10,15}',  # Simple international format
            r'\d{3}[-.\s]?\d{3}[-.\s]?\d{4}',  # US format
            r'\(\d{3}\)[-.\s]?\d{3}[-.\s]?\d{4}',  # US format with parentheses
        ]

        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                # Clean up the phone number
                phone = match.group(0)
                # Remove common separators but keep the + for international numbers
                phone = re.sub(r'[-.\s()]', '', phone)
                return phone

        return None

    def _validate_phone_number(self, phone_number: str) -> bool:
        """Validate phone number format"""
        import re

        if not phone_number:
            return False

        # Remove all non-digit characters except +
        cleaned = re.sub(r'[^\d+]', '', phone_number)

        # Must start with + for international format
        if not cleaned.startswith('+'):
            return False

        # Must have between 10-15 digits after the +
        digits_only = cleaned[1:]  # Remove the +
        if not digits_only.isdigit():
            return False

        if len(digits_only) < 10 or len(digits_only) > 15:
            return False

        return True

    def _find_all_placeholders(self, data: Any, path: str = "") -> List[Dict[str, Any]]:
        """
        Recursively find all types of placeholders in nested data structure.

        Returns list of placeholder info dictionaries with:
        - placeholder: The full placeholder string
        - placeholder_type: Type of placeholder (task, user, mission)
        - task_id/field: Referenced task ID or field name
        - result_path: Path to value in task result (for task placeholders)
        - data_path: Path to placeholder in task data
        """
        placeholders = []

        if isinstance(data, dict):
            for key, value in data.items():
                current_path = f"{path}.{key}" if path else key
                placeholders.extend(self._find_all_placeholders(value, current_path))

        elif isinstance(data, list):
            for i, item in enumerate(data):
                current_path = f"{path}[{i}]"
                placeholders.extend(self._find_all_placeholders(item, current_path))

        elif isinstance(data, str):
            # Check for task placeholders: {{task:task_id:result.path}}
            task_matches = re.findall(self.TASK_PLACEHOLDER_PATTERN, data)
            for task_id, result_path in task_matches:
                placeholder = f"{{{{task:{task_id}:{result_path}}}}}"
                placeholders.append({
                    "placeholder": placeholder,
                    "placeholder_type": "task",
                    "task_id": task_id,
                    "result_path": result_path,
                    "data_path": path,
                    "full_string": data
                })

            # Check for user placeholders: {{user:field}}
            user_matches = re.findall(self.USER_PLACEHOLDER_PATTERN, data)
            for field in user_matches:
                placeholder = f"{{{{user:{field}}}}}"
                placeholders.append({
                    "placeholder": placeholder,
                    "placeholder_type": "user",
                    "field": field,
                    "data_path": path,
                    "full_string": data
                })

            # Check for mission placeholders: {{mission:field}}
            mission_matches = re.findall(self.MISSION_PLACEHOLDER_PATTERN, data)
            for field in mission_matches:
                placeholder = f"{{{{mission:{field}}}}}"
                placeholders.append({
                    "placeholder": placeholder,
                    "placeholder_type": "mission",
                    "field": field,
                    "data_path": path,
                    "full_string": data
                })

        return placeholders
    
    async def _resolve_single_placeholder(
        self,
        data: Dict[str, Any],
        placeholder_info: Dict[str, Any],
        mission_id: str,
        mission_context: Optional[Dict[str, Any]] = None
    ) -> None:
        """Resolve a single placeholder and update the data structure"""

        placeholder_type = placeholder_info["placeholder_type"]
        placeholder = placeholder_info["placeholder"]
        data_path = placeholder_info["data_path"]
        full_string = placeholder_info["full_string"]

        logger.debug("Resolving {placeholder_type} placeholder: {placeholder}", module="DISPATCHER", routine="_resolve_single_placeholder")

        resolved_value = None

        if placeholder_type == "task":
            resolved_value = await self._resolve_task_placeholder(placeholder_info, mission_id)
        elif placeholder_type == "user":
            resolved_value = await self._resolve_user_placeholder(placeholder_info, mission_id)
        elif placeholder_type == "mission":
            resolved_value = await self._resolve_mission_placeholder(placeholder_info, mission_id, mission_context)
        else:
            raise ValueError(f"Unknown placeholder type: {placeholder_type}")

        if resolved_value is not None:
            # Update the data structure
            self._update_data_at_path(data, data_path, placeholder, resolved_value, full_string)
            logger.debug("> {resolved_value}", module="DISPATCHER:_resolve_single_placeholder", routine="SYSTEM | ✅ Resolved {placeholder}")
        else:
            logger.warning("⚠️ Could not resolve placeholder: {placeholder}", module="DISPATCHER", routine="_resolve_single_placeholder")

    async def _resolve_task_placeholder(self, placeholder_info: Dict[str, Any]) -> Any:
        """Resolve a task placeholder: {{task:task_id:result.path}}"""

        task_id = placeholder_info["task_id"]
        result_path = placeholder_info["result_path"]

        # Get the referenced task
        task = await self.db_service.get_task(task_id)
        if not task:
            raise ValueError(f"Referenced task not found: {task_id}")

        # Check if task belongs to same mission
        if task.mission_id != mission_id:
            raise ValueError(f"Task {task_id} belongs to different mission")

        # Check if task is completed
        if not task.result:
            raise ValueError(f"Task {task_id} has no result (not completed)")

        # Extract value from task result using path
        try:
            return self._extract_value_by_path(task.result, result_path)
        except Exception as e:
            raise ValueError(f"Failed to extract value from {task_id}:{result_path}: {e}")

    async def _resolve_user_placeholder(self, placeholder_info: Dict[str, Any]) -> Optional[str]:
        """Resolve a user placeholder: {{user:field}}"""

        field = placeholder_info["field"]

        if field == "phone_number":
            # Try to extract phone number from dialogue tasks or user input
            phone_number = await self._extract_phone_from_dialogue_tasks(mission_id)
            if not phone_number:
                phone_number = await self._extract_phone_from_user_input(mission_id)
            return phone_number

        # Add more user fields as needed
        logger.warning("⚠️ Unknown user field: {field}", module="DISPATCHER", routine="_resolve_user_placeholder")
        return None

    async def _resolve_mission_placeholder(
        self,
        placeholder_info: Dict[str, Any],
        mission_id: str,
        mission_context: Optional[Dict[str, Any]] = None
    ) -> Optional[Any]:
        """Resolve a mission placeholder: {{mission:field}}"""

        field = placeholder_info["field"]

        # Get mission context if not provided
        if not mission_context:
            mission = await self.db_service.get_mission(mission_id)
            mission_context = mission.context if mission else {}

        # Convert MissionContext object to dict if needed
        if hasattr(mission_context, 'model_dump'):
            try:
                mission_context = mission_context.model_dump()
            except Exception:
                pass

        # Extract value from mission context using path
        try:
            return self._extract_value_by_path(mission_context, field)
        except Exception as e:
            logger.warning("⚠️ Could not extract mission field {field}: {e}", module="DISPATCHER", routine="_resolve_mission_placeholder")
            return None
    
    def _extract_value_by_path(self, data: Dict[str, Any], path: str) -> Any:
        """
        Extract value from nested dictionary using dot notation path.
        
        Example: "result.extracted_answer" -> data["result"]["extracted_answer"]
        """
        current = data
        parts = path.split('.')
        
        for part in parts:
            if isinstance(current, dict) and part in current:
                current = current[part]
            else:
                raise KeyError(f"Path '{path}' not found in data")
        
        return current
    
    def _update_data_at_path(
        self, 
        data: Dict[str, Any], 
        path: str, 
        placeholder: str, 
        resolved_value: Any,
        full_string: str
    ) -> None:
        """Update data structure at specified path, replacing placeholder"""
        
        if not path:
            # Root level replacement (shouldn't happen in practice)
            return
        
        # Navigate to parent of target location
        parts = path.split('.')
        current = data
        
        for part in parts[:-1]:
            # Handle array indices
            if '[' in part and ']' in part:
                key, index_str = part.split('[')
                index = int(index_str.rstrip(']'))
                current = current[key][index]
            else:
                current = current[part]
        
        # Update the final value
        final_key = parts[-1]
        
        # Handle array indices in final key
        if '[' in final_key and ']' in final_key:
            key, index_str = final_key.split('[')
            index = int(index_str.rstrip(']'))
            target_array = current[key]
            
            # Replace placeholder in string or entire value
            if isinstance(target_array[index], str):
                target_array[index] = target_array[index].replace(placeholder, str(resolved_value))
            else:
                target_array[index] = resolved_value
        else:
            # Replace placeholder in string or entire value
            if isinstance(current[final_key], str):
                current[final_key] = current[final_key].replace(placeholder, str(resolved_value))
            else:
                current[final_key] = resolved_value
    
    def has_placeholders(self, data: Any) -> bool:
        """Check if data contains any placeholders or special values"""
        # Check for placeholder patterns
        if len(self._find_all_placeholders(data)) > 0:
            return True

        # Check for special "user_will_provide" values
        if isinstance(data, dict):
            for value in data.values():
                if value == self.USER_WILL_PROVIDE:
                    return True
                if isinstance(value, (dict, list)):
                    if self.has_placeholders(value):
                        return True
        elif isinstance(data, list):
            for item in data:
                if item == self.USER_WILL_PROVIDE:
                    return True
                if isinstance(item, (dict, list)):
                    if self.has_placeholders(item):
                        return True
        elif isinstance(data, str):
            if data == self.USER_WILL_PROVIDE:
                return True

        return False
    
    async def validate_placeholders(
        self,
        data: Dict[str, Any],
        mission_id: str,
        available_task_ids: List[str]
    ) -> List[str]:
        """
        Validate that all placeholders reference valid, available tasks.
        
        Returns list of validation errors (empty if all valid).
        """
        errors = []
        placeholders = self._find_placeholders(data)
        
        for placeholder_info in placeholders:
            task_id = placeholder_info["task_id"]
            
            if task_id not in available_task_ids:
                errors.append(f"Placeholder references unavailable task: {task_id}")
        
        return errors
