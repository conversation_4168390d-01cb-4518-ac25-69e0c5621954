#!/usr/bin/env python3
"""
Fix syntax errors in f-strings caused by the automated logging fix script.
This script will find and fix malformed f-strings with unmatched parentheses and quotes.
"""

import os
import re
import glob
import ast

def fix_fstring_syntax(content: str) -> str:
    """Fix common f-string syntax errors"""
    lines = content.split('\n')
    fixed_lines = []
    
    for line in lines:
        # Fix unmatched parentheses in f-strings
        if 'logger.' in line and 'f"' in line:
            # Pattern: {variable")} -> {variable})
            line = re.sub(r'\{([^}]+)"}\)', r'{\1})', line)
            
            # Pattern: {variable")" -> {variable})"
            line = re.sub(r'\{([^}]+)"\)"', r'{\1})"', line)
            
            # Pattern: attempt {attempt")" -> attempt {attempt})"
            line = re.sub(r'\{([^}]+)"\)', r'{\1})', line)
            
            # Pattern: {len(task")} -> {len(tasks)}
            line = re.sub(r'\{len\(([^}]+)"\)', r'{len(\1s)}', line)
            
            # Pattern: {len(mission")} -> {len(missions)}
            line = re.sub(r'\{len\(mission"\)', r'{len(missions)}', line)
            
            # Pattern: {len(executable_task")} -> {len(executable_tasks)}
            line = re.sub(r'\{len\(executable_task"\)', r'{len(executable_tasks)}', line)
            
            # Pattern: elapse")" -> elapsed)"
            line = re.sub(r'elapse"\)', r'elapsed)', line)
            
        fixed_lines.append(line)
    
    return '\n'.join(fixed_lines)

def validate_python_syntax(file_path: str) -> bool:
    """Check if a Python file has valid syntax"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        ast.parse(content)
        return True
    except SyntaxError as e:
        print(f"❌ [FIX-SYNTAX:validate_python_syntax] ERROR | Syntax error in {file_path}: {e}")
        return False
    except Exception as e:
        print(f"❌ [FIX-SYNTAX:validate_python_syntax] ERROR | Error reading {file_path}: {e}")
        return False

def fix_file_syntax(file_path: str) -> bool:
    """Fix syntax errors in a single file"""
    try:
        # First check if file has syntax errors
        if validate_python_syntax(file_path):
            print(f"✅ [FIX-SYNTAX:fix_file_syntax] SUCCESS | No syntax errors in: {file_path}")
            return False
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Apply fixes
        fixed_content = fix_fstring_syntax(content)
        
        # Write back if changed
        if fixed_content != content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(fixed_content)
            
            # Validate the fix
            if validate_python_syntax(file_path):
                print(f"✅ [FIX-SYNTAX:fix_file_syntax] SUCCESS | Fixed syntax errors in: {file_path}")
                return True
            else:
                print(f"❌ [FIX-SYNTAX:fix_file_syntax] ERROR | Fix failed for: {file_path}")
                return False
        else:
            print(f"⏭️ [FIX-SYNTAX:fix_file_syntax] SYSTEM | No changes needed: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ [FIX-SYNTAX:fix_file_syntax] ERROR | Error processing {file_path}: {type(e).__name__}: {e}")
        return False

def main():
    """Main function to fix syntax errors in all Python files"""
    print("🔧 [FIX-SYNTAX:main] SYSTEM | Starting syntax error fix...")
    print("=" * 80)
    
    # Find all Python files in the project
    python_files = []
    
    # Add specific directories
    directories = [
        "backend/**/*.py",
        "dispatcher/**/*.py", 
        "agents/**/*.py",
        "cli/**/*.py",
        "watchdog/**/*.py",
        "orchestrator/**/*.py",
        "stop_deeplica/**/*.py",
        "*.py"
    ]
    
    for pattern in directories:
        python_files.extend(glob.glob(pattern, recursive=True))
    
    # Remove duplicates and filter out __pycache__ and .pyc files
    python_files = list(set([f for f in python_files if '__pycache__' not in f and f.endswith('.py')]))
    
    print(f"📁 [FIX-SYNTAX:main] SYSTEM | Found {len(python_files)} Python files to check")
    
    fixed_count = 0
    error_count = 0
    total_count = len(python_files)
    
    for file_path in python_files:
        try:
            if fix_file_syntax(file_path):
                fixed_count += 1
        except Exception as e:
            error_count += 1
            print(f"❌ [FIX-SYNTAX:main] ERROR | Failed to process {file_path}: {e}")
    
    print("=" * 80)
    print(f"🎉 [FIX-SYNTAX:main] SUCCESS | Syntax error fix complete!")
    print(f"📊 [FIX-SYNTAX:main] SYSTEM | Files processed: {total_count}")
    print(f"📊 [FIX-SYNTAX:main] SYSTEM | Files fixed: {fixed_count}")
    print(f"📊 [FIX-SYNTAX:main] SYSTEM | Files with errors: {error_count}")
    print(f"📊 [FIX-SYNTAX:main] SYSTEM | Files unchanged: {total_count - fixed_count - error_count}")

if __name__ == "__main__":
    main()
