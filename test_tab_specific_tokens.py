#!/usr/bin/env python3
"""
🔒 TAB-SPECIFIC TOKEN SYSTEM TEST

Tests the new tab-specific token system that:
1. Generates unique tab IDs in JavaScript
2. Includes tab IDs in token hash generation
3. Prevents URL copying between browser tabs
4. Verifies tab IDs on both server and client side
"""

import requests
import json
import time
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

from shared.port_manager import get_service_port

def test_tab_setup_page():
    """Test that the tab setup page loads and works"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔒 Testing tab setup page on port {web_chat_port}...")
        
        # Login first
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
        
        # Access chat without tab token (should get setup page)
        chat_response = session.get(
            f"http://localhost:{web_chat_port}/chat",
            allow_redirects=True,
            timeout=10
        )
        
        if chat_response.status_code != 200:
            print(f"❌ Chat access failed: {chat_response.status_code}")
            return False
        
        # Check if we got the tab setup page
        content = chat_response.text
        if 'chat_tab_setup.html' in content or 'Secure Tab Setup' in content:
            print(f"✅ Tab setup page loaded correctly")
        elif 'generateTabId' in content or 'tab_id' in content:
            print(f"✅ Tab setup functionality found in page")
        else:
            print(f"❌ Tab setup page not found")
            print(f"📋 Page content preview: {content[:200]}...")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing tab setup page: {e}")
        return False

def test_tab_token_generation_api():
    """Test the tab token generation API endpoint"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔐 Testing tab token generation API on port {web_chat_port}...")
        
        # Login first
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login failed for API test")
            return False
        
        # Test tab token generation API
        test_tab_id = f"test_tab_{int(time.time())}_12345"
        
        api_response = session.post(
            f"http://localhost:{web_chat_port}/api/generate-tab-token",
            json={"tab_id": test_tab_id},
            timeout=10
        )
        
        if api_response.status_code != 200:
            print(f"❌ Tab token API failed: {api_response.status_code}")
            print(f"📋 Response: {api_response.text}")
            return False
        
        result = api_response.json()
        
        if not result.get('success'):
            print(f"❌ Tab token generation failed: {result}")
            return False
        
        # Verify response structure
        required_fields = ['tab_token', 'secure_url', 'tab_id', 'browser_fingerprint']
        for field in required_fields:
            if field not in result:
                print(f"❌ Missing field in response: {field}")
                return False
        
        # Verify tab ID matches
        if result['tab_id'] != test_tab_id:
            print(f"❌ Tab ID mismatch: expected {test_tab_id}, got {result['tab_id']}")
            return False
        
        print(f"✅ Tab token generated successfully")
        print(f"🔗 Tab ID: {result['tab_id']}")
        print(f"🔗 Token: {result['tab_token'][:20]}...")
        print(f"🔗 URL: {result['secure_url'][:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing tab token API: {e}")
        return False

def test_tab_token_uniqueness():
    """Test that different tab IDs generate different tokens"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔄 Testing tab token uniqueness on port {web_chat_port}...")
        
        # Login first
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login failed for uniqueness test")
            return False
        
        # Generate tokens for different tab IDs
        tokens = []
        tab_ids = [
            f"tab_1_{int(time.time())}_aaaa",
            f"tab_2_{int(time.time())}_bbbb",
            f"tab_3_{int(time.time())}_cccc"
        ]
        
        for tab_id in tab_ids:
            api_response = session.post(
                f"http://localhost:{web_chat_port}/api/generate-tab-token",
                json={"tab_id": tab_id},
                timeout=10
            )
            
            if api_response.status_code != 200:
                print(f"❌ Failed to generate token for {tab_id}")
                return False
            
            result = api_response.json()
            if result.get('success'):
                tokens.append(result['tab_token'])
                print(f"✅ Generated token for {tab_id}: {result['tab_token'][:20]}...")
        
        # Verify all tokens are unique
        if len(set(tokens)) == len(tokens):
            print(f"✅ All {len(tokens)} tokens are unique")
            return True
        else:
            print(f"❌ Duplicate tokens found: {len(set(tokens))} unique out of {len(tokens)}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing token uniqueness: {e}")
        return False

def test_invalid_tab_id_handling():
    """Test handling of invalid or missing tab IDs"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🚫 Testing invalid tab ID handling on port {web_chat_port}...")
        
        # Login first
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login failed for invalid tab ID test")
            return False
        
        # Test missing tab_id
        api_response = session.post(
            f"http://localhost:{web_chat_port}/api/generate-tab-token",
            json={},
            timeout=10
        )
        
        if api_response.status_code == 400:
            print(f"✅ Missing tab_id correctly rejected")
        else:
            print(f"❌ Missing tab_id should return 400, got {api_response.status_code}")
            return False
        
        # Test empty tab_id
        api_response = session.post(
            f"http://localhost:{web_chat_port}/api/generate-tab-token",
            json={"tab_id": ""},
            timeout=10
        )
        
        if api_response.status_code == 400:
            print(f"✅ Empty tab_id correctly rejected")
        else:
            print(f"❌ Empty tab_id should return 400, got {api_response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing invalid tab ID handling: {e}")
        return False

def test_token_format_validation():
    """Test that generated tokens have the correct format"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔍 Testing token format validation on port {web_chat_port}...")
        
        # Login first
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login failed for format validation test")
            return False
        
        # Generate a token
        test_tab_id = f"format_test_{int(time.time())}_xyz"
        
        api_response = session.post(
            f"http://localhost:{web_chat_port}/api/generate-tab-token",
            json={"tab_id": test_tab_id},
            timeout=10
        )
        
        if api_response.status_code != 200:
            print(f"❌ Token generation failed for format test")
            return False
        
        result = api_response.json()
        token = result.get('tab_token')
        
        if not token:
            print(f"❌ No token in response")
            return False
        
        # Validate token format
        try:
            import base64
            decoded = base64.urlsafe_b64decode(token.encode()).decode()
            parts = decoded.split(':')
            
            if len(parts) != 5:
                print(f"❌ Token should have 5 parts, got {len(parts)}")
                return False
            
            user_id, fingerprint, tab_id, timestamp, signature = parts
            
            # Verify tab ID is included
            if tab_id != test_tab_id:
                print(f"❌ Tab ID mismatch in token: expected {test_tab_id}, got {tab_id}")
                return False
            
            # Verify timestamp is reasonable
            token_time = float(timestamp)
            current_time = time.time()
            if abs(current_time - token_time) > 60:  # Within 1 minute
                print(f"❌ Token timestamp seems wrong: {token_time} vs {current_time}")
                return False
            
            # Verify signature exists and is hex
            if len(signature) != 64:  # SHA256 hex is 64 chars
                print(f"❌ Signature should be 64 chars, got {len(signature)}")
                return False
            
            print(f"✅ Token format is correct")
            print(f"🔍 User ID: {user_id}")
            print(f"🔍 Fingerprint: {fingerprint}")
            print(f"🔍 Tab ID: {tab_id}")
            print(f"🔍 Timestamp: {timestamp}")
            print(f"🔍 Signature: {signature[:16]}...")
            
            return True
            
        except Exception as e:
            print(f"❌ Token format validation error: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing token format: {e}")
        return False

def main():
    """Run all tab-specific token tests"""
    print("🔒 TAB-SPECIFIC TOKEN SYSTEM TEST")
    print("=" * 60)
    print("Testing new features:")
    print("- JavaScript-generated unique tab IDs")
    print("- Tab ID inclusion in token hash")
    print("- Prevention of URL copying between tabs")
    print("- Server-side tab ID verification")
    print()
    
    tests_passed = 0
    total_tests = 5
    
    # Test 1: Tab setup page
    print("🧪 TEST 1: Tab Setup Page")
    if test_tab_setup_page():
        tests_passed += 1
    
    # Test 2: Tab token generation API
    print("\n🧪 TEST 2: Tab Token Generation API")
    if test_tab_token_generation_api():
        tests_passed += 1
    
    # Test 3: Token uniqueness
    print("\n🧪 TEST 3: Tab Token Uniqueness")
    if test_tab_token_uniqueness():
        tests_passed += 1
    
    # Test 4: Invalid tab ID handling
    print("\n🧪 TEST 4: Invalid Tab ID Handling")
    if test_invalid_tab_id_handling():
        tests_passed += 1
    
    # Test 5: Token format validation
    print("\n🧪 TEST 5: Token Format Validation")
    if test_token_format_validation():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 60)
    print(f"🔒 TEST RESULTS: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✅ ALL TESTS PASSED - Tab-specific token system is working!")
        print("🎉 Security features:")
        print("   - Unique tab IDs generated in JavaScript")
        print("   - Tab IDs included in token hash")
        print("   - URLs cannot be copied between browser tabs")
        print("   - Server-side tab ID verification")
        print("   - Client-side tab ID validation")
    else:
        print("❌ SOME TESTS FAILED - Tab-specific system needs fixes")
        
        if tests_passed >= 4:
            print("🔧 RECOMMENDATION: Minor issues remain")
        elif tests_passed >= 3:
            print("🔧 RECOMMENDATION: Most features work, some edge cases")
        else:
            print("🔧 RECOMMENDATION: Major implementation issues")
    
    print(f"\n🌐 Manual test instructions:")
    print(f"1. Open http://localhost:8007 in browser")
    print(f"2. Login with: admin / admin123")
    print(f"3. Notice the tab setup process")
    print(f"4. Copy the final chat URL")
    print(f"5. Open new tab and paste URL")
    print(f"6. New tab should reject the URL and show error")

if __name__ == '__main__':
    main()
