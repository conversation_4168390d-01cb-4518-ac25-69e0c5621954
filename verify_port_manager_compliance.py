#!/usr/bin/env python3
"""
🔍 Verify Port Manager Compliance
Checks that all hardcoded ports have been replaced with Port Manager calls
"""

import os
import re
import sys
from pathlib import Path

def scan_for_hardcoded_ports():
    """Scan all Python files for hardcoded ports"""
    print("🔍 Scanning for hardcoded ports...")
    print("=" * 60)
    
    # Directories to scan
    scan_dirs = [
        'agents', 'backend', 'dispatcher', 'web_chat', 'cli', 
        'watchdog', 'shared', 'tests', 'scripts'
    ]
    
    # Port patterns to look for (excluding .env and database URLs)
    port_patterns = [
        r'localhost:80[0-9][0-9]',  # localhost:8000-8099
        r'127\.0\.0\.1:80[0-9][0-9]',  # 127.0.0.1:8000-8099
        r'0\.0\.0\.0:80[0-9][0-9]',  # 0.0.0.0:8000-8099
        r':[80][0-9][0-9][0-9]',  # :8000-8999
        r'port.*=.*80[0-9][0-9]',  # port = 8000
        r'PORT.*=.*80[0-9][0-9]',  # PORT = 8000
    ]
    
    # Files to exclude from scanning
    exclude_files = {
        '.env', 'DEMO_MODE.env', 'PRODUCTION_MODE.env',
        'verify_port_manager_compliance.py',
        'port_manager.py',  # Port manager itself can have constants
        'deeplica_port_settings.json'
    }
    
    violations = []
    
    for scan_dir in scan_dirs:
        if not os.path.exists(scan_dir):
            continue
            
        for root, dirs, files in os.walk(scan_dir):
            for file in files:
                if not file.endswith('.py'):
                    continue
                    
                if file in exclude_files:
                    continue
                    
                file_path = os.path.join(root, file)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    for line_num, line in enumerate(content.split('\n'), 1):
                        for pattern in port_patterns:
                            if re.search(pattern, line, re.IGNORECASE):
                                # Skip comments and certain exceptions
                                if (line.strip().startswith('#') or 
                                    'mongodb' in line.lower() or
                                    'get_service_port' in line or
                                    'FACTORY_DEFAULTS' in line or
                                    'RESERVED_PORTS' in line):
                                    continue
                                    
                                violations.append({
                                    'file': file_path,
                                    'line': line_num,
                                    'content': line.strip(),
                                    'pattern': pattern
                                })
                                
                except Exception as e:
                    print(f"⚠️ Error reading {file_path}: {e}")
    
    return violations

def check_html_templates():
    """Check HTML templates for hardcoded ports"""
    print("\n🌐 Checking HTML templates...")
    
    html_violations = []
    html_dir = 'web_chat/templates'
    
    if os.path.exists(html_dir):
        for file in os.listdir(html_dir):
            if not file.endswith('.html'):
                continue
                
            file_path = os.path.join(html_dir, file)
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # Look for hardcoded ports in HTML/JavaScript
                port_pattern = r'80[0-9][0-9]'
                for line_num, line in enumerate(content.split('\n'), 1):
                    if re.search(port_pattern, line):
                        # Skip FACTORY_DEFAULTS and comments
                        if ('FACTORY_DEFAULTS' in line or 
                            line.strip().startswith('//') or
                            line.strip().startswith('<!--')):
                            continue
                            
                        html_violations.append({
                            'file': file_path,
                            'line': line_num,
                            'content': line.strip()[:100] + '...' if len(line.strip()) > 100 else line.strip()
                        })
                        
            except Exception as e:
                print(f"⚠️ Error reading {file_path}: {e}")
    
    return html_violations

def main():
    """Main verification function"""
    print("🔍 DEEPLICA Port Manager Compliance Check")
    print("=" * 60)
    
    # Check Python files
    violations = scan_for_hardcoded_ports()
    
    # Check HTML templates
    html_violations = check_html_templates()
    
    # Report results
    print("\n📊 RESULTS:")
    print("=" * 60)
    
    if violations:
        print(f"❌ Found {len(violations)} hardcoded port violations in Python files:")
        for v in violations:
            print(f"  📁 {v['file']}:{v['line']}")
            print(f"     {v['content']}")
            print()
    else:
        print("✅ No hardcoded ports found in Python files!")
    
    if html_violations:
        print(f"⚠️ Found {len(html_violations)} potential port references in HTML files:")
        for v in html_violations:
            print(f"  📁 {v['file']}:{v['line']}")
            print(f"     {v['content']}")
            print()
    else:
        print("✅ No problematic ports found in HTML files!")
    
    # Summary
    total_violations = len(violations)
    
    print("=" * 60)
    if total_violations == 0:
        print("🎉 ALL HARDCODED PORTS HAVE BEEN REPLACED WITH PORT MANAGER!")
        print("✅ DEEPLICA is now fully compliant with Port Manager architecture")
        return True
    else:
        print(f"❌ {total_violations} violations found - please fix before proceeding")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
