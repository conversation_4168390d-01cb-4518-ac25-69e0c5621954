#!/bin/bash
# =============================================================================
# DEEPLICA SERVICE SCRIPT - ALL PORTS MANAGED DYNAMICALLY
# =============================================================================
# ALL ports are assigned by shared/port_manager.py - NO HARDCODED PORTS
# External services adapt to assigned ports through configuration
# =============================================================================
# =============================================================================
# DEEPLICA SERVICE STARTUP SCRIPT
# =============================================================================
# NOTE: All ports are managed by shared/port_manager.py
# - Backend API: 8888 (CONSTANT - never changes)
# - Other services: configurable via port manager
# =============================================================================

# Deeplica v0 - Orchestrated System Startup
# This script starts only the backend API, which will automatically orchestrate all other services

echo "🚀 Starting Deeplica v0 with Service Orchestration"
echo "=================================================="
echo ""
echo "The Backend API will automatically:"
echo "  ✅ Connect to MongoDB Atlas"
echo "  ✅ Start ngrok tunnel for webhooks"
echo "  ✅ Start all microservices (Dispatcher, Dialogue, Planner, Phone)"
echo "  ✅ Verify all services are healthy"
echo "  ✅ Monitor and restart services if they crash"
echo ""
echo "🔍 You can monitor progress at:"
echo "  - Health: http://localhost:{get_service_port("backend")}/health  # Backend API - CONSTANT port"
echo "  - System Status: http://localhost:{get_service_port("backend")}/system/status"
echo ""

# Kill any existing processes
echo "🧹 Cleaning up any existing processes..."
pkill -f "python3.*app.main" 2>/dev/null || true
pkill -f "ngrok" 2>/dev/null || true

# Kill processes on specific ports
for port in 8888 8001 8002 8003 8004; do
    lsof -ti:$port | xargs kill -9 2>/dev/null || true
done

sleep 2

# Start the backend API (which will orchestrate everything else)
echo "🚀 Starting Backend API with Service Orchestration..."
cd backend
PORT = get_service_port("backend") python3 -m app.main

echo ""
echo "🛑 System shutdown complete"
