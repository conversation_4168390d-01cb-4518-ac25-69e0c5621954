# 🤖 DEEPLICA V3 - AI Mission Orchestration System

[![Python 3.11+](https://img.shields.io/badge/python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com/)
[![MongoDB](https://img.shields.io/badge/MongoDB-Atlas-green.svg)](https://www.mongodb.com/atlas)
[![Twilio](https://img.shields.io/badge/Twilio-Voice-red.svg)](https://www.twilio.com/)

**Enterprise-grade AI mission orchestration system with autonomous task execution, phone call integration, and self-healing architecture.**

## Architecture

- **Terminal CLI**: Chat interface for user interaction
- **Planner Agent**: Converts user requests into task graphs
- **Dialogue Agent**: Executes tasks and communicates with user
- **Agent Dispatcher**: Orchestrates task execution
- **MongoDB**: Stores mission JSON structures
- **FastAPI Backend**: API server deployed on GCP Cloud Run

## Quick Start

### 🚀 Automated Setup (Recommended)

```bash
# Run the setup script
./scripts/start.sh
```

The script will guide you through:
1. **Local Development** (Requires external MongoDB)
2. **Backend Only**
3. **CLI Only**

### 📋 Manual Setup

#### Prerequisites

1. Python 3.9+
2. MongoDB Atlas connection string
3. Google Gemini API key
4. GCP credentials (for deployment)

#### Backend Setup
```bash
cd backend
pip install -r requirements.txt
cp .env.example .env
# Edit .env with your credentials
python -m app.main
```

#### CLI Setup
```bash
cd cli
pip install -r requirements.txt
python terminal_ui.py
```



### 🔧 Environment Variables

Create `backend/.env`

## Project Structure

```
prototype/
├── backend/           # FastAPI backend
│   ├── app/
│   │   ├── agents/    # Planner, Dialogue, Dispatcher
│   │   ├── models/    # Mission and Task models
│   │   ├── services/  # LLM and Database services
│   │   └── api/       # API routes
│   └── requirements.txt
├── cli/               # Terminal interface
│   ├── terminal_ui.py
│   └── requirements.txt
├── scripts/           # Setup and utility scripts
└── deploy/            # GCP deployment configuration
```

## Usage Example

```
$ python cli/terminal_ui.py

🤖 Deeplica v0 - AI Mission Assistant
Type 'exit' to quit, 'help' for commands

You: Please book a table for 8 people tonight at a nice restaurant

🧠 Planning your mission...
📋 Mission created with 3 tasks:
  1. Gather restaurant preferences
  2. Search for available restaurants
  3. Make reservation

💬 I'll help you book a table for 8 people tonight. Let me start by asking a few questions:
- What type of cuisine do you prefer?
- What's your budget range?
- What time would you like to dine?
```

## 🧪 Testing

### Run Tests
```bash
# Test the complete system
python scripts/test.py

# Test specific API endpoint
python cli/api_client.py http://localhost:8888  # Backend API port
```

### Manual Testing
```bash
# Start the CLI
cd cli && python terminal_ui.py

# Example interactions:
You: Book a table for 8 people tonight
You: Help me negotiate my cable bill
You: Plan a weekend trip to San Francisco
```

## 🚀 Deployment

### Local Development
```bash
# Local setup
./scripts/start.sh
```

### GCP Cloud Run
```bash
# Set environment variables
export MONGODB_CONNECTION_STRING="your_connection_string"
export GEMINI_API_KEY="your_api_key"

# Deploy to GCP
./deploy/deploy.sh
```

### Manual GCP Deployment
```bash
# Build and push
gcloud builds submit --config=deploy/cloudbuild.yaml
```

## 🔧 Development

### Architecture Components
- **Planner Agent**: Converts user requests to task graphs using LLM
- **Dialogue Agent**: Executes tasks and handles user communication
- **Dispatcher**: Orchestrates task execution and manages state
- **Database Service**: MongoDB operations with async support
- **LLM Service**: Abstraction over Gemini/OpenAI with fallback
- **Terminal UI**: Chat-style CLI with prompt_toolkit

### Key Features
- **Logging**: Comprehensive logging with loguru (debug to error levels)
- **Error Handling**: Retry mechanisms for LLM failures with exponential backoff
- **Validation**: Pydantic models for all data structures
- **Async**: Full async/await support throughout the stack
- **Modular**: Clean separation of concerns with dependency injection
- **Scalable**: Stateless design ready for horizontal scaling

### Code Quality
- **Type Hints**: Full type annotations throughout
- **Documentation**: Comprehensive docstrings and comments
- **Testing**: Automated test suite with health checks
- **Linting**: Black, isort, flake8 configuration ready
- **Monitoring**: Health checks and metrics endpoints
