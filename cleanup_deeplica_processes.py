#!/usr/bin/env python3
"""
🛡️ DEEPLICA PROCESS CLEANUP SCRIPT
Safe cleanup of all DEEPLICA processes without affecting VS Code or other system processes.

This script:
- Finds all DEEPLICA service processes
- Protects VS Code, system processes, and the cleanup script itself
- Provides detailed logging of what will be killed
- Asks for confirmation before killing anything
- Uses graceful termination (SIGTERM) before force killing (SIGKILL)
"""

import os
import sys
import time
import signal
from typing import List, Dict, Any
from datetime import datetime


def log_message(level: str, message: str):
    """Log a message with timestamp and level."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"{timestamp} - [{level}] - CLEANUP: {message}")


def find_deeplica_processes() -> List[Dict[str, Any]]:
    """Find all DEEPLICA processes safely."""
    try:
        import psutil
    except ImportError:
        log_message("ERROR", "psutil not available. Install with: pip install psutil")
        return []
    
    deeplica_processes = []
    current_pid = os.getpid()
    
    # DEEPLICA service identifiers
    deeplica_identifiers = [
        "DEEPLICA-WATCHDOG",
        "DEEPLICA-BACKEND-API", 
        "DEEPLICA-DISPATCHER",
        "DEEPLICA-PLANNER-AGENT",
        "DEEPLICA-DIALOGUE-AGENT",
        "DEEPLICA-PHONE-AGENT",
        "DEEPLICA-CLI-TERMINAL",
        "DEEPLICA-WEB-CHAT",
        "backend.app.main",
        "dispatcher.app.main",
        "agents.planner.app.main",
        "agents.dialogue.app.main", 
        "agents.phone.app.main",
        "watchdog/main.py",
        "cli/main.py",
        "web_chat/main.py"
    ]
    
    log_message("INFO", "🔍 Scanning for DEEPLICA processes...")
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
        try:
            proc_pid = proc.info['pid']
            proc_name = proc.info['name'] or 'Unknown'
            cmdline = proc.info['cmdline'] or []
            cmdline_str = ' '.join(cmdline)
            create_time = proc.info['create_time']
            
            # Skip ourselves
            if proc_pid == current_pid:
                continue
                
            # Skip if this is the cleanup script
            if 'cleanup_deeplica_processes.py' in cmdline_str:
                continue
                
            # Skip VS Code processes
            if any(vscode_term in cmdline_str.lower() for vscode_term in [
                'visual studio code', 'vscode', 'code.exe', 'code helper',
                'electron', 'debugpy/launcher'
            ]):
                continue
                
            # Skip system processes
            if any(sys_term in cmdline_str.lower() for sys_term in [
                '/system/', '/usr/bin/', '/bin/', 'kernel', 'launchd'
            ]):
                continue
            
            # Check if this is a DEEPLICA process
            is_deeplica = any(identifier in cmdline_str for identifier in deeplica_identifiers)
            
            if is_deeplica:
                deeplica_processes.append({
                    'pid': proc_pid,
                    'name': proc_name,
                    'cmdline': cmdline_str,
                    'create_time': create_time,
                    'process': proc
                })
                
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue
    
    return deeplica_processes


def display_processes(processes: List[Dict[str, Any]]):
    """Display found processes in a nice format."""
    if not processes:
        log_message("INFO", "✅ No DEEPLICA processes found")
        return
        
    log_message("INFO", f"📊 Found {len(processes)} DEEPLICA process(es):")
    print("\n" + "="*80)
    print("DEEPLICA PROCESSES TO BE TERMINATED:")
    print("="*80)
    
    for i, proc_info in enumerate(processes, 1):
        pid = proc_info['pid']
        name = proc_info['name']
        cmdline = proc_info['cmdline']
        create_time = proc_info['create_time']
        
        # Format creation time
        create_dt = datetime.fromtimestamp(create_time)
        age = datetime.now() - create_dt
        
        print(f"\n{i}. PID: {pid}")
        print(f"   Name: {name}")
        print(f"   Age: {age}")
        print(f"   Command: {cmdline[:100]}{'...' if len(cmdline) > 100 else ''}")
    
    print("\n" + "="*80)


def kill_processes(processes: List[Dict[str, Any]], force: bool = False):
    """Kill the processes gracefully, then forcefully if needed."""
    if not processes:
        return
        
    log_message("INFO", f"🔥 Starting termination of {len(processes)} DEEPLICA processes...")
    
    # Phase 1: Graceful termination (SIGTERM)
    log_message("INFO", "📤 Phase 1: Sending SIGTERM (graceful termination)...")
    terminated_pids = []
    
    for proc_info in processes:
        pid = proc_info['pid']
        proc = proc_info['process']
        
        try:
            if proc.is_running():
                log_message("INFO", f"📤 Sending SIGTERM to PID {pid}")
                proc.terminate()
                terminated_pids.append(pid)
            else:
                log_message("INFO", f"✅ PID {pid} already terminated")
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            log_message("WARNING", f"⚠️ Could not terminate PID {pid}: {e}")
    
    # Wait for graceful termination
    if terminated_pids:
        log_message("INFO", "⏳ Waiting 5 seconds for graceful termination...")
        time.sleep(5)
    
    # Phase 2: Check what's still running and force kill if needed
    still_running = []
    for proc_info in processes:
        pid = proc_info['pid']
        proc = proc_info['process']
        
        try:
            if proc.is_running():
                still_running.append(proc_info)
                log_message("WARNING", f"⚠️ PID {pid} still running after SIGTERM")
            else:
                log_message("INFO", f"✅ PID {pid} terminated gracefully")
        except psutil.NoSuchProcess:
            log_message("INFO", f"✅ PID {pid} terminated gracefully")
    
    # Phase 3: Force kill remaining processes (SIGKILL)
    if still_running:
        if force:
            log_message("WARNING", f"🔥 Phase 2: Force killing {len(still_running)} remaining processes...")
            for proc_info in still_running:
                pid = proc_info['pid']
                proc = proc_info['process']
                
                try:
                    if proc.is_running():
                        log_message("WARNING", f"🔥 Force killing PID {pid}")
                        proc.kill()
                    time.sleep(0.5)
                except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                    log_message("WARNING", f"⚠️ Could not force kill PID {pid}: {e}")
        else:
            log_message("WARNING", f"⚠️ {len(still_running)} processes still running. Use --force to kill them.")
    
    log_message("INFO", "🎉 Process cleanup completed!")


def main():
    """Main cleanup function."""
    print("\n" + "🛡️" * 40)
    print("🛡️ DEEPLICA PROCESS CLEANUP SCRIPT")
    print("🛡️" * 40)
    
    # Check for force flag
    force = '--force' in sys.argv or '-f' in sys.argv
    auto_yes = '--yes' in sys.argv or '-y' in sys.argv
    
    if '--help' in sys.argv or '-h' in sys.argv:
        print("""
Usage: python3 cleanup_deeplica_processes.py [options]

Options:
  -h, --help    Show this help message
  -f, --force   Force kill processes that don't terminate gracefully
  -y, --yes     Auto-confirm without asking
  
Examples:
  python3 cleanup_deeplica_processes.py          # Interactive mode
  python3 cleanup_deeplica_processes.py -y       # Auto-confirm
  python3 cleanup_deeplica_processes.py -f -y    # Force kill + auto-confirm
        """)
        return
    
    # Find DEEPLICA processes
    processes = find_deeplica_processes()
    
    # Display what we found
    display_processes(processes)
    
    if not processes:
        log_message("INFO", "🎉 No DEEPLICA processes to clean up!")
        return
    
    # Ask for confirmation unless auto-yes
    if not auto_yes:
        print(f"\n⚠️  WARNING: This will terminate {len(processes)} DEEPLICA process(es)")
        if force:
            print("🔥 FORCE MODE: Processes will be force-killed if they don't terminate gracefully")
        
        response = input("\nDo you want to proceed? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            log_message("INFO", "❌ Cleanup cancelled by user")
            return
    
    # Kill the processes
    kill_processes(processes, force=force)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        log_message("INFO", "❌ Cleanup interrupted by user")
        sys.exit(1)
    except Exception as e:
        log_message("ERROR", f"💥 Cleanup failed: {e}")
        sys.exit(1)
