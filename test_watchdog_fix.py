#!/usr/bin/env python3
"""
🔧 WATCHDOG AUTO-FIX TEST SCRIPT

This script tests the watchdog auto-fix functionality for the phone agent
<PERSON><PERSON>lio service issues by making API calls to trigger the fix.

Usage:
    python test_watchdog_fix.py

<AUTHOR> Security Team
@version 1.0.0
@date 2025-07-11
"""

import requests
import json
import time
from datetime import datetime

def test_watchdog_fix():
    """Test the watchdog auto-fix functionality"""
    
    print("🔧 WATCHDOG AUTO-FIX TEST SCRIPT")
    print("=" * 50)
    print(f"📅 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Phone agent URL (adjust if different)
    base_url = "http://localhost:8002"  # Default phone agent port
    
    try:
        # Test 1: Check current health status
        print("🔍 Step 1: Checking current system health...")
        health_response = requests.get(f"{base_url}/system_health", timeout=10)
        
        if health_response.status_code == 200:
            health_data = health_response.json()
            print(f"✅ Health check response: {json.dumps(health_data, indent=2)}")
            
            if not health_data.get('system_healthy', False):
                print("⚠️ System is currently unhealthy - good for testing watchdog!")
            else:
                print("✅ System is currently healthy")
        else:
            print(f"❌ Health check failed: {health_response.status_code}")
            return
        
        print()
        
        # Test 2: Trigger manual watchdog fix
        print("🔧 Step 2: Triggering manual watchdog auto-fix...")
        fix_response = requests.post(f"{base_url}/watchdog_fix", timeout=30)
        
        if fix_response.status_code == 200:
            fix_data = fix_response.json()
            print(f"✅ Watchdog fix response: {json.dumps(fix_data, indent=2)}")
            
            if fix_data.get('status') == 'success':
                print("🎉 Watchdog auto-fix completed successfully!")
            elif fix_data.get('status') == 'warning':
                print("⚠️ Watchdog auto-fix completed but issues remain")
            else:
                print("❌ Watchdog auto-fix failed")
        else:
            print(f"❌ Watchdog fix failed: {fix_response.status_code}")
            try:
                error_data = fix_response.json()
                print(f"Error details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Error text: {fix_response.text}")
            return
        
        print()
        
        # Test 3: Check health status after fix
        print("🔍 Step 3: Checking system health after watchdog fix...")
        time.sleep(2)  # Give the system a moment to stabilize
        
        health_response_after = requests.get(f"{base_url}/system_health", timeout=10)
        
        if health_response_after.status_code == 200:
            health_data_after = health_response_after.json()
            print(f"✅ Health check after fix: {json.dumps(health_data_after, indent=2)}")
            
            # Compare before and after
            health_before = health_data.get('system_healthy', False)
            health_after = health_data_after.get('system_healthy', False)
            
            if health_before != health_after:
                if health_after:
                    print("🎉 SUCCESS: System health improved after watchdog fix!")
                else:
                    print("⚠️ System health did not improve after watchdog fix")
            else:
                if health_after:
                    print("✅ System remained healthy")
                else:
                    print("❌ System remained unhealthy after fix attempt")
        else:
            print(f"❌ Health check after fix failed: {health_response_after.status_code}")
        
        print()
        
        # Test 4: Check Twilio service health specifically
        print("📞 Step 4: Checking Twilio service health...")
        try:
            health_response = requests.get(f"{base_url}/health", timeout=10)
            if health_response.status_code == 200:
                health_data = health_response.json()
                twilio_status = health_data.get('twilio', {})
                print(f"📞 Twilio status: {json.dumps(twilio_status, indent=2)}")
            else:
                print(f"❌ Could not get detailed health status: {health_response.status_code}")
        except Exception as e:
            print(f"❌ Error checking Twilio health: {e}")
        
        print()
        print("🎯 WATCHDOG AUTO-FIX TEST COMPLETED")
        print("=" * 50)
        
    except requests.exceptions.ConnectionError:
        print("❌ ERROR: Could not connect to phone agent")
        print("   Make sure the phone agent is running on the expected port")
        print(f"   Expected URL: {base_url}")
        
    except requests.exceptions.Timeout:
        print("❌ ERROR: Request timed out")
        print("   The phone agent may be unresponsive")
        
    except Exception as e:
        print(f"❌ ERROR: Unexpected error occurred: {e}")

def test_multiple_fixes():
    """Test multiple consecutive watchdog fixes"""
    print("\n🔄 TESTING MULTIPLE CONSECUTIVE FIXES")
    print("-" * 40)
    
    base_url = "http://localhost:8002"
    
    for i in range(3):
        print(f"\n🔧 Fix attempt {i+1}/3...")
        try:
            response = requests.post(f"{base_url}/watchdog_fix", timeout=15)
            if response.status_code == 200:
                data = response.json()
                print(f"   Result: {data.get('status', 'unknown')}")
                print(f"   Message: {data.get('message', 'no message')}")
            else:
                print(f"   Failed: {response.status_code}")
        except Exception as e:
            print(f"   Error: {e}")
        
        time.sleep(1)  # Brief pause between attempts

if __name__ == "__main__":
    # Run the main test
    test_watchdog_fix()
    
    # Ask if user wants to test multiple fixes
    try:
        response = input("\n🔄 Would you like to test multiple consecutive fixes? (y/n): ")
        if response.lower().startswith('y'):
            test_multiple_fixes()
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
    except:
        pass  # Skip if input not available
    
    print("\n✅ All tests completed!")
