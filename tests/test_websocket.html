<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test</title>
</head>
<body>
    <h1>WebSocket Connection Test</h1>
    <div id="status">Connecting...</div>
    <div id="messages"></div>
    <input type="text" id="messageInput" placeholder="Type a message...">
    <button onclick="sendMessage()">Send</button>

    <script>
        const sessionId = 'a41f4c0e-33f3-4143-a643-99192be475eb';
        const wsUrl = `ws://${window.location.host}/ws?session_id=${sessionId}`;  // Use current host
        
        console.log('Connecting to:', wsUrl);
        
        const ws = new WebSocket(wsUrl);
        const status = document.getElementById('status');
        const messages = document.getElementById('messages');
        
        ws.onopen = function() {
            console.log('WebSocket connected');
            status.textContent = 'Connected';
            status.style.color = 'green';
        };
        
        ws.onmessage = function(event) {
            console.log('Received:', event.data);
            const div = document.createElement('div');
            div.textContent = 'Received: ' + event.data;
            messages.appendChild(div);
        };
        
        ws.onclose = function(event) {
            console.log('WebSocket closed:', event.code, event.reason);
            status.textContent = `Disconnected (${event.code}: ${event.reason})`;
            status.style.color = 'red';
        };
        
        ws.onerror = function(error) {
            console.error('WebSocket error:', error);
            status.textContent = 'Error';
            status.style.color = 'red';
        };
        
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            if (message && ws.readyState === WebSocket.OPEN) {
                const data = {
                    type: 'message',
                    content: message
                };
                ws.send(JSON.stringify(data));
                input.value = '';
                
                const div = document.createElement('div');
                div.textContent = 'Sent: ' + message;
                div.style.color = 'blue';
                messages.appendChild(div);
            }
        }
        
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>
