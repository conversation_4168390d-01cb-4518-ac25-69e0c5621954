#!/usr/bin/env python3
"""
Test script to verify anti-spam logging functionality
Demonstrates that repetitive status messages are suppressed while status changes are logged
"""

import sys
import os
import time

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.unified_logging import (
    get_dispatcher_logger, should_log_status_change, _global_status_tracker
)


def test_repetitive_logging_suppression():
    """Test that repetitive status messages are suppressed"""
    print("🧪 Testing Anti-Spam Logging Functionality")
    print("=" * 60)
    
    logger = get_dispatcher_logger()
    
    print("\n1️⃣ Testing repetitive 'no tasks' status (should only log first time):")
    print("-" * 50)
    
    # Simulate repetitive "no tasks" polling - should only log once
    for i in range(5):
        status = "no_tasks"
        if should_log_status_change("DISPATCHER", "task_polling", status):
            logger.info(f"📭 No pending tasks found (poll {i+1})", module="main", routine="task_polling_loop")
            print(f"  ✅ Logged: No tasks message #{i+1}")
        else:
            print(f"  🔇 Suppressed: No tasks message #{i+1} (repetitive)")
    
    print("\n2️⃣ Testing status change from 'no tasks' to 'found tasks' (should log):")
    print("-" * 50)
    
    # Now simulate finding tasks - should log because status changed
    status = "found_3_tasks"
    if should_log_status_change("DISPATCHER", "task_polling", status, force_log=True):
        logger.info("📋 Found 3 pending tasks", module="main", routine="task_polling_loop")
        print("  ✅ Logged: Found tasks (status changed)")
    
    print("\n3️⃣ Testing repetitive 'found tasks' status (should suppress):")
    print("-" * 50)
    
    # Simulate repetitive "found tasks" - should suppress
    for i in range(3):
        status = "found_3_tasks"
        if should_log_status_change("DISPATCHER", "task_polling", status):
            logger.info(f"📋 Found 3 pending tasks (poll {i+1})", module="main", routine="task_polling_loop")
            print(f"  ✅ Logged: Found tasks message #{i+1}")
        else:
            print(f"  🔇 Suppressed: Found tasks message #{i+1} (repetitive)")
    
    print("\n4️⃣ Testing status change back to 'no tasks' (should log):")
    print("-" * 50)
    
    # Change back to no tasks - should log because status changed
    status = "no_tasks"
    if should_log_status_change("DISPATCHER", "task_polling", status):
        logger.info("📭 No pending tasks found", module="main", routine="task_polling_loop")
        print("  ✅ Logged: No tasks (status changed back)")
    
    print("\n5️⃣ Testing different service/operation (should log first time):")
    print("-" * 50)
    
    # Test different service - should log because it's a different key
    status = "healthy"
    if should_log_status_change("BACKEND-API", "health_check", status):
        logger.info("✅ Backend API is healthy", module="main", routine="health_check")
        print("  ✅ Logged: Backend health (different service)")
    
    # Repeat same status - should suppress
    if should_log_status_change("BACKEND-API", "health_check", status):
        logger.info("✅ Backend API is healthy", module="main", routine="health_check")
        print("  ✅ Logged: Backend health (repeated)")
    else:
        print("  🔇 Suppressed: Backend health (repetitive)")
    
    print("\n6️⃣ Testing force_log parameter (should always log):")
    print("-" * 50)
    
    # Test force logging - should always log regardless of repetition
    for i in range(3):
        status = "healthy"
        if should_log_status_change("BACKEND-API", "health_check", status, force_log=True):
            logger.info(f"✅ Backend API is healthy (forced #{i+1})", module="main", routine="health_check")
            print(f"  ✅ Logged: Backend health (forced #{i+1})")


def test_backend_wait_logging():
    """Test backend waiting status logging"""
    print("\n7️⃣ Testing backend wait attempt logging (should suppress repetitive):")
    print("-" * 50)
    
    logger = get_dispatcher_logger()
    
    # Simulate multiple wait attempts - should only log when attempt group changes
    for attempt in [15, 30, 45, 60, 75]:  # Every 15 attempts
        status = f"waiting_attempt_{attempt // 15}"
        if should_log_status_change("DISPATCHER", "backend_wait", status):
            logger.info(f"⏳ Still waiting for Backend API... (attempt {attempt})", module="main", routine="wait_for_backend_and_initialize")
            print(f"  ✅ Logged: Wait attempt {attempt} (new group)")
        else:
            print(f"  🔇 Suppressed: Wait attempt {attempt} (same group)")


def main():
    """Main test function"""
    print("🚀 DEEPLICA Anti-Spam Logging Test")
    print("Testing smart logging that suppresses repetitive messages")
    print("=" * 80)
    
    # Clear any existing status cache for clean test
    _global_status_tracker.status_cache.clear()
    
    test_repetitive_logging_suppression()
    test_backend_wait_logging()
    
    print("\n" + "=" * 80)
    print("🎉 Anti-Spam Logging Test Complete!")
    print("\n📊 Summary:")
    print("✅ Repetitive status messages are properly suppressed")
    print("✅ Status changes are logged correctly")
    print("✅ Different services/operations are tracked separately")
    print("✅ Force logging works when needed")
    print("✅ Backend wait attempts are grouped intelligently")
    
    print(f"\n🗂️ Current status cache: {len(_global_status_tracker.status_cache)} entries")
    for key, status in _global_status_tracker.status_cache.items():
        print(f"   {key}: {status}")
    
    print("\n🔧 Benefits:")
    print("• Eliminates log spam from repetitive health checks")
    print("• Reduces noise in debug consoles")
    print("• Only shows meaningful status changes")
    print("• Maintains important error and change logging")
    print("• Improves debugging experience")


if __name__ == "__main__":
    main()
