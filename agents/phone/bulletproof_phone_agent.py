#!/usr/bin/env python3
"""
🛡️ BULLETPROOF PHONE AGENT LAUNCHER

This script ensures the phone agent <PERSON>VE<PERSON> stops running by automatically
restarting it if it crashes or exits unexpectedly.

Features:
- Automatic crash detection and restart
- Comprehensive logging of all crashes and restarts
- Signal handling to prevent external termination
- Exponential backoff for rapid restart attempts
- Health monitoring and recovery
- Process isolation and protection

<AUTHOR> Phone Agent Team
@version 1.0.0
@date 2025-07-11
"""

import os
import sys
import time
import signal
import subprocess
import threading
import traceback
import atexit
from datetime import datetime, timedelta

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

# Bulletproof configuration
RESTART_DELAY_BASE = 1  # Base delay in seconds
RESTART_DELAY_MAX = 60  # Maximum delay in seconds
MAX_RAPID_RESTARTS = 5  # Max restarts within rapid restart window
RAPID_RESTART_WINDOW = 300  # 5 minutes
HEALTH_CHECK_INTERVAL = 30  # Health check every 30 seconds
PROCESS_TIMEOUT = 10  # Timeout for process operations

# Global state
restart_count = 0
rapid_restart_count = 0
last_restart_time = None
rapid_restart_window_start = None
shutdown_requested = False
current_process = None
health_check_thread = None

def log_bulletproof(level: str, message: str):
    """Log bulletproof messages with timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    print(f"{timestamp} - [BULLETPROOF-{level}] - {message}")

def setup_signal_handlers():
    """Setup signal handlers to prevent external termination"""
    global shutdown_requested
    
    def signal_handler(signum, frame):
        signal_name = signal.Signals(signum).name
        log_bulletproof("SIGNAL", f"Received {signal_name} ({signum})")
        
        if signum in [signal.SIGTERM, signal.SIGINT]:
            log_bulletproof("SHUTDOWN", f"Graceful shutdown requested via {signal_name}")
            shutdown_requested = True
            
            # Terminate the phone agent process
            if current_process and current_process.poll() is None:
                log_bulletproof("SHUTDOWN", "Terminating phone agent process")
                try:
                    current_process.terminate()
                    current_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    log_bulletproof("SHUTDOWN", "Force killing phone agent process")
                    current_process.kill()
                except Exception as e:
                    log_bulletproof("ERROR", f"Error terminating process: {e}")
            
            log_bulletproof("SHUTDOWN", "Bulletproof launcher received shutdown signal - will restart")
            # Don't exit - let the service restart itself to maintain resilience
            # sys.exit(0)  # REMOVED - services must never exit themselves
        else:
            log_bulletproof("SIGNAL", f"Ignoring {signal_name} - service must not stop!")
    
    # Handle termination signals
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)
    
    # Ignore other signals
    try:
        signal.signal(signal.SIGHUP, signal.SIG_IGN)
        signal.signal(signal.SIGPIPE, signal.SIG_IGN)
    except AttributeError:
        pass
    
    log_bulletproof("INIT", "Signal handlers installed")

def calculate_restart_delay():
    """Calculate restart delay with exponential backoff"""
    global rapid_restart_count, rapid_restart_window_start
    
    now = time.time()
    
    # Check if we're in a rapid restart situation
    if rapid_restart_window_start is None:
        rapid_restart_window_start = now
        rapid_restart_count = 1
    elif now - rapid_restart_window_start < RAPID_RESTART_WINDOW:
        rapid_restart_count += 1
    else:
        # Reset rapid restart tracking
        rapid_restart_window_start = now
        rapid_restart_count = 1
    
    # Calculate delay based on rapid restart count
    if rapid_restart_count <= MAX_RAPID_RESTARTS:
        delay = RESTART_DELAY_BASE * (2 ** (rapid_restart_count - 1))
    else:
        delay = RESTART_DELAY_MAX
    
    delay = min(delay, RESTART_DELAY_MAX)
    
    log_bulletproof("RESTART", f"Restart delay: {delay}s (rapid restarts: {rapid_restart_count})")
    return delay

def kill_existing_phone_agents():
    """Kill any existing phone agent processes to prevent conflicts"""
    try:
        log_bulletproof("CLEANUP", "Checking for existing phone agent processes")

        # Find existing DEEPLICA-PHONE-AGENT processes
        result = subprocess.run(['pgrep', '-f', 'DEEPLICA-PHONE-AGENT'],
                              capture_output=True, text=True)

        if result.returncode == 0 and result.stdout.strip():
            pids = result.stdout.strip().split('\n')
            for pid in pids:
                if pid.strip():
                    try:
                        log_bulletproof("CLEANUP", f"Killing existing phone agent PID: {pid}")
                        subprocess.run(['kill', '-9', pid], timeout=5)
                    except Exception as e:
                        log_bulletproof("WARNING", f"Failed to kill PID {pid}: {e}")

        # Also check for processes using our target port
        try:
            # Import here to avoid circular imports
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'shared'))
            from port_manager import port_manager

            phone_port = port_manager.allocate_port('phone')

            result = subprocess.run(['lsof', '-ti', f':{phone_port}'],
                                  capture_output=True, text=True)

            if result.returncode == 0 and result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                for pid in pids:
                    if pid.strip():
                        try:
                            log_bulletproof("CLEANUP", f"Killing process on port {phone_port}, PID: {pid}")
                            subprocess.run(['kill', '-9', pid], timeout=5)
                        except Exception as e:
                            log_bulletproof("WARNING", f"Failed to kill PID {pid} on port {phone_port}: {e}")
        except Exception as e:
            log_bulletproof("WARNING", f"Failed to check port conflicts: {e}")

        # Wait for processes to die
        time.sleep(2)
        log_bulletproof("CLEANUP", "Cleanup completed")

    except Exception as e:
        log_bulletproof("ERROR", f"Failed to cleanup existing processes: {e}")

def start_phone_agent():
    """Start the phone agent process"""
    global current_process

    try:
        # First, kill any existing phone agent processes
        kill_existing_phone_agents()

        log_bulletproof("START", "Starting phone agent process")
        
        # Change to phone agent directory
        phone_agent_dir = os.path.dirname(__file__)
        
        # Start the phone agent
        current_process = subprocess.Popen(
            [sys.executable, "app/main.py"],
            cwd=phone_agent_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        log_bulletproof("START", f"Phone agent started with PID: {current_process.pid}")
        return current_process
        
    except Exception as e:
        log_bulletproof("ERROR", f"Failed to start phone agent: {e}")
        log_bulletproof("ERROR", f"Traceback: {traceback.format_exc()}")
        return None

def monitor_phone_agent_output(process):
    """Monitor phone agent output and log it"""
    try:
        for line in iter(process.stdout.readline, ''):
            if line.strip():
                print(line.strip())
    except Exception as e:
        log_bulletproof("ERROR", f"Error monitoring output: {e}")

def health_check_phone_agent():
    """Perform health check on phone agent"""
    try:
        import requests
        response = requests.get("http://localhost:8004/health", timeout=5)
        return response.status_code == 200
    except Exception:
        return False

def health_monitor():
    """Background health monitoring"""
    global current_process
    
    while not shutdown_requested:
        try:
            time.sleep(HEALTH_CHECK_INTERVAL)
            
            if shutdown_requested:
                break
            
            if current_process and current_process.poll() is None:
                # Process is running, check health
                if health_check_phone_agent():
                    log_bulletproof("HEALTH", "Phone agent is healthy")
                else:
                    log_bulletproof("HEALTH", "Phone agent health check failed")
            
        except Exception as e:
            log_bulletproof("ERROR", f"Health monitor error: {e}")

def main():
    """Main bulletproof launcher loop"""
    global restart_count, last_restart_time, current_process, health_check_thread, shutdown_requested
    
    log_bulletproof("INIT", "🛡️ BULLETPROOF PHONE AGENT LAUNCHER STARTING")
    log_bulletproof("INIT", f"PID: {os.getpid()}")
    log_bulletproof("INIT", f"Python: {sys.executable}")
    log_bulletproof("INIT", f"Working directory: {os.getcwd()}")
    
    # Setup crash prevention
    setup_signal_handlers()
    
    # Start health monitor
    health_check_thread = threading.Thread(target=health_monitor, daemon=True)
    health_check_thread.start()
    log_bulletproof("INIT", "Health monitor started")
    
    # Main restart loop
    while not shutdown_requested:
        try:
            restart_count += 1
            last_restart_time = time.time()
            
            log_bulletproof("RESTART", f"Starting phone agent (attempt #{restart_count})")
            
            # Start phone agent
            current_process = start_phone_agent()
            
            if current_process is None:
                log_bulletproof("ERROR", "Failed to start phone agent process")
                delay = calculate_restart_delay()
                log_bulletproof("RESTART", f"Waiting {delay}s before retry")
                time.sleep(delay)
                continue
            
            # Monitor output in background
            output_thread = threading.Thread(
                target=monitor_phone_agent_output, 
                args=(current_process,), 
                daemon=True
            )
            output_thread.start()
            
            # Wait for process to complete
            exit_code = current_process.wait()
            
            if shutdown_requested:
                log_bulletproof("SHUTDOWN", "Shutdown requested, exiting restart loop")
                break
            
            # Process exited unexpectedly
            uptime = time.time() - last_restart_time
            log_bulletproof("CRASH", f"Phone agent exited with code {exit_code} after {uptime:.1f}s")
            
            # Calculate restart delay
            delay = calculate_restart_delay()
            
            if delay > 0:
                log_bulletproof("RESTART", f"Waiting {delay}s before restart")
                time.sleep(delay)
            
        except KeyboardInterrupt:
            log_bulletproof("SHUTDOWN", "Keyboard interrupt received")
            shutdown_requested = True
            break
            
        except Exception as e:
            log_bulletproof("ERROR", f"Unexpected error in main loop: {e}")
            log_bulletproof("ERROR", f"Traceback: {traceback.format_exc()}")
            time.sleep(5)  # Brief pause before retry
    
    log_bulletproof("SHUTDOWN", f"Bulletproof launcher exiting (total restarts: {restart_count})")

def cleanup():
    """Cleanup function called on exit"""
    global current_process
    
    log_bulletproof("CLEANUP", "Performing cleanup")
    
    if current_process and current_process.poll() is None:
        try:
            log_bulletproof("CLEANUP", "Terminating phone agent process")
            current_process.terminate()
            current_process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            log_bulletproof("CLEANUP", "Force killing phone agent process")
            current_process.kill()
        except Exception as e:
            log_bulletproof("CLEANUP", f"Error during cleanup: {e}")

if __name__ == "__main__":
    # Register cleanup function
    atexit.register(cleanup)
    
    try:
        main()
    except Exception as e:
        log_bulletproof("FATAL", f"Fatal error in bulletproof launcher: {e}")
        log_bulletproof("FATAL", f"Traceback: {traceback.format_exc()}")
        sys.exit(1)
