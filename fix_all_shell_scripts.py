#!/usr/bin/env python3
"""
🔧 Fix ALL Shell Scripts for Dynamic Port Management
Update ALL shell scripts to use port manager for EVERY port reference.
NO MORE HARDCODED PORTS - ALL are managed dynamically.
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Dict, <PERSON><PERSON>

def fix_all_shell_scripts():
    """Fix all shell scripts to use port manager for ALL ports"""
    
    print("🔧 Fixing ALL shell scripts to use dynamic port management...")
    
    # Shell script replacements - ALL ports must be dynamic
    shell_replacements = [
        # Backend API (8888)
        (r'PORT=8888', 'PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'backend\')")'),
        (r'http://localhost:8888', 'http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'backend\')")'),
        (r'localhost:8888', 'localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'backend\')")'),
        
        # Dispatcher (8001)
        (r'PORT=8001', 'PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'dispatcher\')")'),
        (r'http://localhost:8001', 'http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'dispatcher\')")'),
        (r'localhost:8001', 'localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'dispatcher\')")'),
        
        # Dialogue Agent (8002)
        (r'PORT=8002', 'PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'dialogue\')")'),
        (r'http://localhost:8002', 'http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'dialogue\')")'),
        (r'localhost:8002', 'localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'dialogue\')")'),
        
        # Planner Agent (8003)
        (r'PORT=8003', 'PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'planner\')")'),
        (r'http://localhost:8003', 'http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'planner\')")'),
        (r'localhost:8003', 'localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'planner\')")'),
        
        # Phone Agent (8004)
        (r'PORT=8004', 'PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'phone\')")'),
        (r'http://localhost:8004', 'http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'phone\')")'),
        (r'localhost:8004', 'localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'phone\')")'),
        
        # Watchdog (8005)
        (r'PORT=8005', 'PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'watchdog\')")'),
        (r'http://localhost:8005', 'http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'watchdog\')")'),
        (r'localhost:8005', 'localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'watchdog\')")'),
        
        # Web Chat (8007)
        (r'PORT=8007', 'PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'web-chat\')")'),
        (r'http://localhost:8007', 'http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'web-chat\')")'),
        (r'localhost:8007', 'localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'web-chat\')")'),
        
        # CLI Terminal (8008)
        (r'PORT=8008', 'PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'cli\')")'),
        (r'http://localhost:8008', 'http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'cli\')")'),
        (r'localhost:8008', 'localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'cli\')")'),
        
        # Twilio Echo Bot (8009)
        (r'PORT=8009', 'PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'twilio-echo-bot\')")'),
        (r'http://localhost:8009', 'http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'twilio-echo-bot\')")'),
        (r'localhost:8009', 'localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'twilio-echo-bot\')")'),
        
        # Webhook Server (8010)
        (r'PORT=8010', 'PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'webhook-server\')")'),
        (r'http://localhost:8010', 'http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'webhook-server\')")'),
        (r'localhost:8010', 'localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'webhook-server\')")'),
        
        # Ngrok API (4040)
        (r'PORT=4040', 'PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'ngrok-api\')")'),
        (r'http://localhost:4040', 'http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'ngrok-api\')")'),
        (r'localhost:4040', 'localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'ngrok-api\')")'),
        
        # Ngrok Tunnel (8080)
        (r'PORT=8080', 'PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'ngrok-tunnel\')")'),
        (r'http://localhost:8080', 'http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'ngrok-tunnel\')")'),
        (r'localhost:8080', 'localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'ngrok-tunnel\')")'),
        
        # Update comments about port management
        (r'# Port managed by port_manager\.py', '# Port dynamically assigned by port_manager.py'),
        (r'# CONSTANT.*port', '# Port dynamically assigned by port_manager.py'),
        (r'# Port.*CONSTANT', '# Port dynamically assigned by port_manager.py'),
    ]
    
    # Find all shell scripts
    shell_scripts = []
    for file_path in Path('.').glob('*.sh'):
        shell_scripts.append(file_path)
    
    # Also check scripts directory
    scripts_dir = Path('scripts')
    if scripts_dir.exists():
        for file_path in scripts_dir.glob('*.sh'):
            shell_scripts.append(file_path)
    
    files_modified = 0
    
    for script_path in shell_scripts:
        if fix_shell_script_ports(script_path, shell_replacements):
            files_modified += 1
    
    print(f"✅ Fixed shell script ports in {files_modified} files")
    return files_modified

def fix_shell_script_ports(script_path: Path, replacements: List[Tuple[str, str]]) -> bool:
    """Fix port references in a single shell script"""
    
    try:
        with open(script_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        original_content = content
        modified = False
        
        # Add header comment about dynamic port management if not present
        if 'ALL PORTS MANAGED DYNAMICALLY' not in content and script_path.suffix == '.sh':
            header_comment = '''#!/bin/bash
# =============================================================================
# DEEPLICA SERVICE SCRIPT - ALL PORTS MANAGED DYNAMICALLY
# =============================================================================
# ALL ports are assigned by shared/port_manager.py - NO HARDCODED PORTS
# External services adapt to assigned ports through configuration
# =============================================================================

'''
            if content.startswith('#!/bin/bash'):
                # Replace the shebang line with our header
                lines = content.split('\n')
                lines[0] = header_comment.strip()
                content = '\n'.join(lines)
                modified = True
                print(f"✅ {script_path}: Added dynamic port management header")
        
        # Apply shell script replacements
        for pattern, replacement in replacements:
            if re.search(pattern, content):
                content = re.sub(pattern, replacement, content)
                modified = True
                print(f"✅ {script_path}: Fixed pattern {pattern[:30]}...")
        
        # Write back if modified
        if modified:
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
    
    except Exception as e:
        print(f"⚠️ Error processing {script_path}: {e}")
    
    return False

def create_port_helper_functions():
    """Create helper functions for shell scripts"""
    
    helper_script = '''#!/bin/bash
# =============================================================================
# DEEPLICA PORT HELPER FUNCTIONS
# =============================================================================
# Helper functions for shell scripts to get dynamic ports from port manager
# =============================================================================

# Get service port from port manager
get_service_port() {
    local service_name="$1"
    python3 -c "from shared.port_manager import get_service_port; print(get_service_port('$service_name'))"
}

# Get backend port
get_backend_port() {
    get_service_port "backend"
}

# Get dispatcher port
get_dispatcher_port() {
    get_service_port "dispatcher"
}

# Get dialogue agent port
get_dialogue_port() {
    get_service_port "dialogue"
}

# Get planner agent port
get_planner_port() {
    get_service_port "planner"
}

# Get phone agent port
get_phone_port() {
    get_service_port "phone"
}

# Get watchdog port
get_watchdog_port() {
    get_service_port "watchdog"
}

# Get web chat port
get_webchat_port() {
    get_service_port "web-chat"
}

# Get ngrok tunnel port
get_ngrok_port() {
    get_service_port "ngrok-tunnel"
}

# Get webhook server port
get_webhook_port() {
    get_service_port "webhook-server"
}

# Get twilio echo bot port
get_twilio_port() {
    get_service_port "twilio-echo-bot"
}
'''
    
    try:
        with open("port_helpers.sh", 'w') as f:
            f.write(helper_script)
        
        # Make it executable
        os.chmod("port_helpers.sh", 0o755)
        print("✅ Created port_helpers.sh with dynamic port functions")
    except Exception as e:
        print(f"❌ Error creating port helpers: {e}")

if __name__ == "__main__":
    print("🔧 Starting comprehensive shell script port fixes...")
    files_modified = fix_all_shell_scripts()
    create_port_helper_functions()
    print(f"✅ Shell script port fixes completed! Modified {files_modified} files")
    print("🔄 ALL shell scripts now use dynamic port management")
