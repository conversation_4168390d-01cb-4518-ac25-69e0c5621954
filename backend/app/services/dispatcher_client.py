"""
Dispatcher Client - Communicates with the Di<PERSON>atcher microservice.
Replaces direct EventsBus usage in the API layer.
"""

import os
import httpx
from typing import Dict, Any, Optional
from shared.unified_logging import get_backend_logger
from shared.port_manager import get_service_port

# Initialize unified logger
logger = get_backend_logger()


class DispatcherClient:
    """
    Client for communicating with the Dispatcher microservice.
    Handles mission execution requests and status queries.
    """
    
    def __init__(self):
        self.dispatcher_url = self._get_dispatcher_url()
        self.http_client = httpx.AsyncClient(timeout=30.0)
        logger.info(f"🔗 Dispatcher client initialized: {self.dispatcher_url}", module="BACKEND-API", routine="__init__")
    
    def _get_dispatcher_url(self) -> str:
        return os.getenv("DISPATCHER_URL", f"http://localhost:{get_service_port('dispatcher')}")
    
    async def start_mission_execution(self, mission_id: str) -> Dict[str, Any]:
        """Start mission execution via dispatcher"""
        logger.info(f"🚀 Starting mission execution: {mission_id}", module="BACKEND-API", routine="start_mission_execution")

        try:
            response = await self.http_client.post(
                f"{self.dispatcher_url}/missions/{mission_id}/start",
                timeout=30.0
            )
            response.raise_for_status()

            result = response.json()
            logger.info(f"✅ Mission execution started: {mission_id}", module="BACKEND-API", routine="start_mission_execution")
            return result
            
        except httpx.HTTPError as e:
            logger.error("❌ HTTP error starting mission: {e}", module="BACKEND-API", routine="start_mission_execution")
            raise
        except Exception as e:
            logger.error("❌ Unexpected error starting mission: {e}", module="BACKEND-API", routine="start_mission_execution")
            raise
    
    async def continue_mission_execution(
        self,
        mission_id: str,
        user_response: Optional[str] = None
    ) -> Dict[str, Any]:
        """Continue mission execution with optional user response"""
        logger.info(f"▶️ Continuing mission execution: {mission_id}", module="BACKEND-API", routine="continue_mission_execution")

        try:
            # Prepare request data
            params = {}
            if user_response:
                params["user_response"] = user_response

            response = await self.http_client.post(
                f"{self.dispatcher_url}/missions/{mission_id}/continue",
                params=params,
                timeout=30.0
            )
            response.raise_for_status()

            result = response.json()
            logger.info(f"✅ Mission execution continued: {mission_id}", module="BACKEND-API", routine="continue_mission_execution")
            return result
            
        except httpx.HTTPError as e:
            logger.error("❌ HTTP error continuing mission: {e}", module="BACKEND-API", routine="continue_mission_execution")
            raise
        except Exception as e:
            logger.error("❌ Unexpected error continuing mission: {e}", module="BACKEND-API", routine="continue_mission_execution")
            raise
    
    async def get_mission_status(self, mission_id: str) -> Optional[Dict[str, Any]]:
        """Get current mission status from dispatcher"""
        logger.info("📊 Getting mission status: {mission_id}", module="BACKEND-API", routine="get_mission_status")
        
        try:
            response = await self.http_client.get(
                f"{self.dispatcher_url}/missions/{mission_id}/status",
                timeout=10.0
            )
            
            if response.status_code == 404:
                return None
            
            response.raise_for_status()
            result = response.json()
            
            logger.debug("📊 Mission status retrieved: {mission_id}", module="BACKEND-API", routine="get_mission_status")
            return result
            
        except httpx.HTTPError as e:
            if e.response.status_code == 404:
                return None
            logger.error("❌ HTTP error getting mission status: {e}", module="BACKEND-API", routine="get_mission_status")
            raise
        except Exception as e:
            logger.error("❌ Unexpected error getting mission status: {e}", module="BACKEND-API", routine="get_mission_status")
            raise
    
    async def cancel_mission(self, mission_id: str) -> bool:
        """Cancel a mission via dispatcher"""
        logger.info("🛑 Cancelling mission: {mission_id}", module="BACKEND-API", routine="cancel_mission")

        try:
            response = await self.http_client.delete(
                f"{self.dispatcher_url}/missions/{mission_id}",
                timeout=10.0
            )

            if response.status_code == 404:
                logger.warning("🔍 Mission not found for cancellation: {mission_id}", module="BACKEND-API", routine="cancel_mission")
                return False

            response.raise_for_status()
            result = response.json()

            logger.info("✅ Mission cancelled successfully: {mission_id}", module="BACKEND-API", routine="cancel_mission")
            return result.get("status") == "cancelled"

        except httpx.HTTPError as e:
            if e.response.status_code == 404:
                logger.warning("🔍 Mission not found for cancellation: {mission_id}", module="BACKEND-API", routine="cancel_mission")
                return False
            logger.error("❌ HTTP error cancelling mission: {e}", module="BACKEND-API", routine="cancel_mission")
            return False
        except Exception as e:
            logger.error("❌ Failed to cancel mission: {e}", module="BACKEND-API", routine="cancel_mission")
            return False
    

    
    async def close(self):
        """Close the HTTP client"""
        await self.http_client.aclose()

    async def __aenter__(self):
        """Async context manager entry"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
        return False
