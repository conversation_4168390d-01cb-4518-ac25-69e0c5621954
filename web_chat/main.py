#!/usr/bin/env python3
"""
🌐 Deeplica Web Chat Server
ChatGPT-like web interface for communicating with Deeplica
"""

import os
import sys
import json
import uuid
import asyncio
import logging
import webbrowser
import threading
import time
import hashlib
import hmac
import secrets
import base64
from contextlib import asynccontextmanager
from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Dict, List, Optional
from pathlib import Path

import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Request, Form
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, RedirectResponse, FileResponse, Response, JSONResponse
from fastapi.exception_handlers import http_exception_handler
from starlette.exceptions import HTTPException as StarletteHTTPException
from security_manager import security_manager
import httpx
from dotenv import load_dotenv

# Add project root to path for imports
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.unified_logging import get_webchat_logger
from shared.port_manager import get_service_port

PROJECT_ROOT = Path(__file__).parent.parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

# Load environment variables from .env file
load_dotenv(PROJECT_ROOT / ".env")

from shared.port_manager import ensure_service_port_free, get_service_port, get_service_host, get_localhost, get_service_url
from shared.port_cache import get_port_cache
from shared.resilience_utils import bulletproof_retry, wait_for_service, bulletproof_service_wrapper

# Initialize unified logger
logger = get_webchat_logger()

# Initialize port cache for efficient port management
port_cache = get_port_cache("web-chat")

# Setup logging - suppress routine INFO messages
logging.basicConfig(level=logging.WARNING)
# Note: Keep using unified logger from line 38

# Suppress uvicorn access logs for health checks and routine requests
uvicorn_logger = logging.getLogger("uvicorn.access")
uvicorn_logger.setLevel(logging.WARNING)

# Custom filter to suppress health check logs
class HealthCheckFilter(logging.Filter):
    def filter(self, record):
        # Suppress health check requests and other routine requests
        if hasattr(record, 'getMessage'):
            message = record.getMessage()
            if any(pattern in message for pattern in [
                'GET /health',
                'GET /favicon.ico',
                'WebSocket /ws',
                '200 OK',
                '404 Not Found'
            ]):
                return False
        return True

# Apply filter to uvicorn access logger
uvicorn_logger.addFilter(HealthCheckFilter())

# Enhanced Watchdog integration
class WatchdogClient:
    def __init__(self):
        self.watchdog_url = f"http://{get_localhost()}:{get_service_port('watchdog')}"
        self.service_name = "WEB-CHAT"
        self.last_health_check = datetime.now()

    async def send_log(self, message: str, level: str = "INFO", module: str = "main"):
        """Send log message to watchdog"""
        try:
            async with httpx.AsyncClient() as client:
                await client.post(
                    f"{self.watchdog_url}/log",
                    json={
                        "service": self.service_name,
                        "module": module,
                        "level": level,
                        "message": message,
                        "timestamp": datetime.now().isoformat()
                    },
                    timeout=2.0
                )
        except Exception:
            # Silently fail if watchdog is not available
            pass

    async def send_health_status(self, status: str, details: dict = None):
        """Send health status to watchdog"""
        try:
            async with httpx.AsyncClient() as client:
                await client.post(
                    f"{self.watchdog_url}/health_status",
                    json={
                        "service": self.service_name,
                        "status": status,
                        "details": details or {},
                        "timestamp": datetime.now().isoformat()
                    },
                    timeout=2.0
                )
                self.last_health_check = datetime.now()
        except Exception:
            pass

    async def register_service(self):
        """Register service with watchdog"""
        try:
            async with httpx.AsyncClient() as client:
                await client.post(
                    f"{self.watchdog_url}/register",
                    json={
                        "service": self.service_name,
                        "port": get_service_port("web-chat"),
                        "health_endpoint": "/health",
                        "description": "Web Chat Interface for Deeplica",
                        "timestamp": datetime.now().isoformat()
                    },
                    timeout=2.0
                )
        except Exception:
            pass

watchdog_client = WatchdogClient()

# Backward compatibility
async def send_to_watchdog(message: str, level: str = "INFO"):
    """Send important messages to watchdog (backward compatibility)"""
    await watchdog_client.send_log(message, level)

# Browser auto-launch functionality
class BrowserLauncher:
    def __init__(self):
        self.web_chat_url = f"http://{get_localhost()}:{get_service_port('web-chat')}"
        self.backend_url = f"http://{get_localhost()}:{get_service_port('backend')}"
        self.cli_url = f"http://{get_localhost()}:{get_service_port('cli')}"
        self.max_wait_time = 60  # Maximum wait time in seconds
        self.check_interval = 2  # Check every 2 seconds

    async def check_service_health(self, url: str, service_name: str) -> bool:
        """Check if a service is healthy and ready"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{url}/health", timeout=5.0)
                if response.status_code == 200:
                    print(f"🌐 [WEB-CHAT] ✅ {service_name} is ready")
                    return True
                else:
                    print(f"🌐 [WEB-CHAT] ⚠️ {service_name} returned status {response.status_code}")
                    return False
        except Exception as e:
            print(f"🌐 [WEB-CHAT] ⏳ Waiting for {service_name}... ({str(e)[:50]})")
            return False

    async def wait_for_deeplica_ready(self) -> bool:
        """Wait for Deeplica backend services to be ready"""
        print(f"🌐 [WEB-CHAT] 🔍 Checking if Deeplica is ready for user communication...")

        start_time = time.time()

        while (time.time() - start_time) < self.max_wait_time:
            try:
                # Check CLI Terminal (optional - nice to have but not required)
                cli_ready = await self.check_service_health(self.cli_url, "CLI Terminal")
                if cli_ready:
                    print(f"🌐 [WEB-CHAT] ✅ CLI Terminal is ready")

                # Check Backend API (required for core functionality)
                backend_ready = await self.check_service_health(self.backend_url, "Backend API")
                if backend_ready:
                    print(f"🌐 [WEB-CHAT] ✅ Backend API is ready")

                # Backend API is sufficient for browser launch
                if backend_ready:
                    print(f"🌐 [WEB-CHAT] ✅ Deeplica is ready for user communication!")
                    return True

                # Wait before next check
                await asyncio.sleep(self.check_interval)

            except Exception as e:
                print(f"🌐 [WEB-CHAT] ⚠️ Error checking Deeplica readiness: {e}")
                await asyncio.sleep(self.check_interval)

        print(f"🌐 [WEB-CHAT] ⏰ Timeout waiting for Deeplica to be ready")
        return False

    def open_browser_to_login(self):
        """Open browser to the web chat login page"""
        try:
            # Add unique identifier to help prevent multiple tabs
            login_url = f"{self.web_chat_url}/login?deeplica_session=main"
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"{timestamp} - [INFO] - Svc: WEB-CHAT, Mod: BrowserLauncher, Cod: open_browser_to_login, msg: 🚀 Opening browser to: {login_url}")

            # Try to open browser with new=2 to reuse existing window if possible
            success = webbrowser.open(login_url, new=2)

            if success:
                print(f"{timestamp} - [INFO] - Svc: WEB-CHAT, Mod: BrowserLauncher, Cod: open_browser_to_login, msg: ✅ Browser opened successfully!")
                print(f"{timestamp} - [INFO] - Svc: WEB-CHAT, Mod: BrowserLauncher, Cod: open_browser_to_login, msg: 👤 Demo credentials: admin/admin123 or demo/demo123")
                print(f"{timestamp} - [INFO] - Svc: WEB-CHAT, Mod: BrowserLauncher, Cod: open_browser_to_login, msg: 💬 Ready for user communication!")
            else:
                print(f"{timestamp} - [WARNING] - Svc: WEB-CHAT, Mod: BrowserLauncher, Cod: open_browser_to_login, msg: ⚠️ Could not open browser automatically")
                print(f"{timestamp} - [INFO] - Svc: WEB-CHAT, Mod: BrowserLauncher, Cod: open_browser_to_login, msg: 🔗 Please manually open: {login_url}")

        except Exception as e:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"{timestamp} - [ERROR] - Svc: WEB-CHAT, Mod: BrowserLauncher, Cod: open_browser_to_login, msg: ❌ Error opening browser: {e}")
            print(f"{timestamp} - [INFO] - Svc: WEB-CHAT, Mod: BrowserLauncher, Cod: open_browser_to_login, msg: 🔗 Please manually open: {self.web_chat_url}/login")

    async def launch_browser_when_ready(self):
        """Wait for Deeplica to be ready, then launch browser"""
        print(f"🌐 launch initiated...")

        # Wait for Deeplica to be ready
        deeplica_ready = await self.wait_for_deeplica_ready()

        if deeplica_ready:
            # Small delay to ensure web chat is fully ready
            await asyncio.sleep(2)

            # Open browser in a separate thread to avoid blocking
            browser_thread = threading.Thread(
                target=self.open_browser_to_login,
                daemon=True
            )
            browser_thread.start()

            await watchdog_client.send_log("Browser auto-launched for user communication", "INFO", "browser_launcher")
        else:
            print(f"🌐 [WEB-CHAT] ⚠️ Deeplica not fully ready, but launching browser anyway...")
            print(f"🌐 [WEB-CHAT] 💡 Web Chat is running and ready for use!")

            # Launch browser anyway after a short delay
            await asyncio.sleep(3)
            browser_thread = threading.Thread(
                target=self.open_browser_to_login,
                daemon=True
            )
            browser_thread.start()

            await watchdog_client.send_log("Browser auto-launched (fallback mode)", "INFO", "browser_launcher")

browser_launcher = BrowserLauncher()

# CLI Terminal integration
async def send_to_cli_terminal(user: str, message: str, sender: str = "user"):
    """Send chat messages to CLI terminal in chat format"""
    try:
        async with httpx.AsyncClient() as client:
            await client.post(
                f"http://{get_localhost()}:{get_service_port('cli')}/chat",
                json={
                    "message": message,
                    "user": user,
                    "sender": sender,
                    "timestamp": datetime.now().isoformat()
                },
                timeout=1.0
            )
    except Exception:
        # Silently fail if CLI terminal is not available
        pass

# Add current directory to path for local imports
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# Import user service - ROUTES THROUGH BACKEND API ONLY
from user_service_api import UserServiceAPI
user_service = UserServiceAPI()
from shared_models.user import UserRole, UserPermission, UserCreate, UserUpdate

# Health monitoring task
async def health_monitoring_task():
    """Periodic health monitoring and reporting to watchdog"""
    while True:
        try:
            # Collect health metrics
            connection_stats = manager.get_connection_stats()
            health_details = {
                "active_connections": connection_stats["total_connections"],
                "active_users": connection_stats["unique_users"],
                "average_session_duration": connection_stats["average_session_duration"],
                "total_messages": connection_stats["total_messages"],
                "uptime_seconds": (datetime.now() - app.state.start_time).total_seconds() if hasattr(app.state, 'start_time') else 0,
                "memory_usage": "N/A",  # Could add psutil for memory monitoring
                "last_activity": app.state.last_activity.isoformat() if hasattr(app.state, 'last_activity') else None,
                "deeplica_client_failures": deeplica_client.circuit_breaker_failures,
                "circuit_breaker_open": deeplica_client.is_circuit_breaker_open()
            }

            # Determine health status
            status = "healthy"
            if len(manager.active_connections) == 0:
                status = "idle"
            elif len(manager.active_connections) > 100:  # Arbitrary threshold
                status = "busy"

            # Send to watchdog
            await watchdog_client.send_health_status(status, health_details)

        except Exception as e:
            await watchdog_client.send_log(f"Health monitoring error: {e}", "ERROR", "health_monitor")

        # Wait 30 seconds before next check
        await asyncio.sleep(30)

# FastAPI app with lifespan management
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    app.state.start_time = datetime.now()
    app.state.last_activity = datetime.now()

    # Initialize user service with resilience
    try:
        await user_service.connect()
        await watchdog_client.send_log("User service connected successfully", "INFO", "startup")
    except Exception as e:
        logger.warning(f"User service connection failed during startup: {e} - will retry in background", module="main", routine="lifespan")

    # Register with watchdog with resilience
    try:
        await watchdog_client.register_service()
        await watchdog_client.send_log("Web Chat service starting up", "INFO", "startup")
    except Exception as e:
        logger.warning(f"Watchdog registration failed during startup: {e} - will retry in background", module="main", routine="lifespan")

    # Start health monitoring task
    health_task = asyncio.create_task(health_monitoring_task())
    app.state.health_task = health_task

    # Start browser auto-launch task
    browser_task = asyncio.create_task(browser_launcher.launch_browser_when_ready())
    app.state.browser_task = browser_task

    yield

    # Shutdown
    await watchdog_client.send_log("Web Chat service shutting down", "INFO", "shutdown")
    if hasattr(app.state, 'health_task'):
        app.state.health_task.cancel()
    if hasattr(app.state, 'browser_task'):
        app.state.browser_task.cancel()

    # Close HTTP clients
    await deeplica_client.close()
    await watchdog_client.close() if hasattr(watchdog_client, 'close') else None

app = FastAPI(
    title="Deeplica Web Chat",
    version="1.0.0",
    description="Web-based chat interface for Deeplica",
    lifespan=lifespan
)

# Custom exception handler for authentication redirects
@app.exception_handler(HTTPException)
async def custom_http_exception_handler(request: Request, exc: HTTPException):
    """Handle authentication exceptions with proper redirects"""
    if exc.detail == "redirect_unauthorized":
        # Redirect to login instead of unauthorized for better UX
        return RedirectResponse(url="/login?error=session_expired", status_code=307)

    # For other HTTP exceptions, use default handler
    return await http_exception_handler(request, exc)

# 404 handler - redirect all invalid URLs to login page
@app.exception_handler(StarletteHTTPException)
async def custom_404_handler(request: Request, exc: StarletteHTTPException):
    """Handle 404 errors by redirecting to login page"""
    if exc.status_code == 404:
        # Log the invalid URL attempt for security monitoring
        asyncio.create_task(send_to_watchdog(f"Invalid URL access attempt: {request.url.path}"))
        return RedirectResponse(url="/login?error=invalid_url", status_code=307)

    # For other Starlette HTTP exceptions, use default handler
    return await http_exception_handler(request, exc)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add no-cache middleware
@app.middleware("http")
async def add_no_cache_headers(request: Request, call_next):
    response = await call_next(request)

    # Add no-cache headers to all HTML responses
    if request.url.path.endswith(('.html', '/')) or 'text/html' in response.headers.get('content-type', ''):
        response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate, max-age=0"
        response.headers["Pragma"] = "no-cache"
        response.headers["Expires"] = "0"
        response.headers["Last-Modified"] = datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')
        response.headers["ETag"] = f'"{secrets.token_hex(8)}"'

    return response

# Static files and templates
static_dir = Path(__file__).parent / "static"
media_pool_dir = Path(__file__).parent / "media_pool"
templates_dir = Path(__file__).parent / "templates"

static_dir.mkdir(exist_ok=True)
media_pool_dir.mkdir(exist_ok=True)
templates_dir.mkdir(exist_ok=True)

app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
app.mount("/media", StaticFiles(directory=str(media_pool_dir)), name="media")
templates = Jinja2Templates(directory=str(templates_dir))
# Disable template caching for development
templates.env.cache = {}

# Session storage (simple in-memory for demo)
user_sessions = {}

# Active WebSocket connections
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_sessions: Dict[str, str] = {}  # user_id -> username
        self.user_rate_limits: Dict[str, List[float]] = {}  # user_id -> list of message timestamps
        self.rate_limit_window = 60  # seconds
        self.rate_limit_max_messages = 30  # max messages per window
        self.connection_metadata: Dict[str, Dict] = {}  # user_id -> metadata

    async def connect(self, websocket: WebSocket, user_id: str, username: str):
        connection_id = str(uuid.uuid4())
        self.active_connections[connection_id] = websocket
        self.user_sessions[connection_id] = username
        self.connection_metadata[connection_id] = {
            "connected_at": datetime.now(),
            "last_activity": datetime.now(),
            "message_count": 0
        }

        # Initialize rate limiting for user
        if username not in self.user_rate_limits:
            self.user_rate_limits[username] = []

        logger.info(f"User {username} connected with ID {connection_id}", module="main", routine="unknown")
        return connection_id

    async def disconnect(self, connection_id: str):
        if connection_id in self.active_connections:
            username = self.user_sessions.get(connection_id, "unknown")
            del self.active_connections[connection_id]
            if connection_id in self.user_sessions:
                del self.user_sessions[connection_id]
            if connection_id in self.connection_metadata:
                del self.connection_metadata[connection_id]
            logger.info(f"User {username} disconnected", module="main", routine="unknown")

    def check_rate_limit(self, username: str) -> bool:
        """Check if user is within rate limits"""
        now = datetime.now()
        user_messages = self.user_rate_limits.get(username, [])

        # Remove old messages outside the window
        cutoff_time = now - timedelta(seconds=self.rate_limit_window)
        user_messages = [msg_time for msg_time in user_messages if msg_time > cutoff_time]
        self.user_rate_limits[username] = user_messages

        # Check if under limit
        return len(user_messages) < self.rate_limit_max_messages

    def record_message(self, username: str, connection_id: str):
        """Record a message for rate limiting and activity tracking"""
        if username not in self.user_rate_limits:
            self.user_rate_limits[username] = []
        self.user_rate_limits[username].append(datetime.now())

        # Update connection metadata
        if connection_id in self.connection_metadata:
            self.connection_metadata[connection_id]["last_activity"] = datetime.now()
            self.connection_metadata[connection_id]["message_count"] += 1

    async def send_personal_message(self, message: str, connection_id: str):
        """Send message to specific connection with error handling"""
        if connection_id in self.active_connections:
            websocket = self.active_connections[connection_id]
            try:
                await websocket.send_text(message)
                return True
            except Exception as e:
                # Connection is broken, clean it up
                await self.disconnect(connection_id)
                await watchdog_client.send_log(f"Failed to send message to {connection_id}: {e}", "WARNING", "connection_manager")
                return False
        return False

    async def broadcast_to_user(self, message: str, username: str):
        """Send message to all connections for a specific user"""
        sent_count = 0
        connections_to_remove = []

        for connection_id, user in self.user_sessions.items():
            if user == username:
                success = await self.send_personal_message(message, connection_id)
                if success:
                    sent_count += 1
                else:
                    connections_to_remove.append(connection_id)

        # Clean up failed connections
        for connection_id in connections_to_remove:
            await self.disconnect(connection_id)

        return sent_count

    async def broadcast(self, message: str):
        """Broadcast message to all active connections"""
        sent_count = 0
        connections_to_remove = []

        for connection_id in list(self.active_connections.keys()):
            success = await self.send_personal_message(message, connection_id)
            if success:
                sent_count += 1
            else:
                connections_to_remove.append(connection_id)

        # Clean up failed connections
        for connection_id in connections_to_remove:
            await self.disconnect(connection_id)

        return sent_count

    def get_connection_stats(self) -> Dict:
        """Get connection statistics for monitoring"""
        now = datetime.now()
        active_users = set(self.user_sessions.values())

        # Calculate average session duration
        total_duration = 0
        for metadata in self.connection_metadata.values():
            duration = (now - metadata["connected_at"]).total_seconds()
            total_duration += duration

        avg_duration = total_duration / len(self.connection_metadata) if self.connection_metadata else 0

        return {
            "total_connections": len(self.active_connections),
            "unique_users": len(active_users),
            "average_session_duration": avg_duration,
            "total_messages": sum(meta["message_count"] for meta in self.connection_metadata.values())
        }

manager = ConnectionManager()

# Authentication functions
async def authenticate_user(username: str, password: str):
    """Authenticate user with case-insensitive username"""
    return await user_service.authenticate_user(username, password)

async def create_session(user) -> str:
    """Create session for authenticated user"""
    return await user_service.create_session(user)

def get_backend_api_url():
    """Get Backend API URL - NO DIRECT DATABASE ACCESS ALLOWED"""
    try:
        from shared.api_manager import get_service_url
        backend_url = get_service_url("backend")
        if backend_url:
            return backend_url

        # Fallback to environment variable or default
        backend_port = os.getenv("BACKEND_PORT", "8001")
        return f"http://127.0.0.1:{backend_port}"
    except Exception as e:
        logger.error(f"❌ Failed to get backend API URL: {str(e)}")
        return None

async def get_user_from_session(session_id: str):
    """Get user from session ID - BULLETPROOF version that never crashes"""
    try:
        logger.debug(f"🔍 Getting user from session: {session_id[:8]}...")

        if not session_id:
            logger.debug("❌ Empty session ID provided")
            return None

        if user_service is None:
            logger.error("❌ User service not available")
            return None

        try:
            user = await user_service.get_user_from_session(session_id)
            if user:
                logger.debug(f"✅ User found for session: {user.username}")
            else:
                logger.debug("❌ No user found for session")
            return user
        except Exception as validation_error:
            logger.error(f"❌ Session validation error: {str(validation_error)}")
            return None

    except Exception as e:
        logger.error(f"❌ Unexpected error in get_user_from_session: {str(e)}")
        return None

async def require_authentication(request: Request):
    """Require authentication - BULLETPROOF version that never crashes"""
    try:
        logger.debug("🔍 Validating authentication")

        # Get session ID with error handling
        try:
            session_id = request.cookies.get("session_id")
            if not session_id:
                logger.debug("❌ No session ID found in cookies")
                raise HTTPException(status_code=401, detail="redirect_unauthorized")
        except Exception as cookie_error:
            logger.error(f"❌ Error reading session cookie: {str(cookie_error)}")
            raise HTTPException(status_code=401, detail="redirect_unauthorized")

        # Get user from session with bulletproof error handling
        try:
            user = await get_user_from_session(session_id)
            if not user:
                logger.debug("❌ Invalid session ID")
                raise HTTPException(status_code=401, detail="redirect_unauthorized")

            logger.debug(f"✅ User authenticated: {user.username}")
            return user

        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        except Exception as session_error:
            logger.error(f"❌ Session validation error: {str(session_error)}")
            # Don't crash - return 401 instead
            raise HTTPException(status_code=401, detail="redirect_unauthorized")

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"❌ Unexpected error in require_authentication: {str(e)}")
        # Don't crash - return 401 instead
        raise HTTPException(status_code=401, detail="redirect_unauthorized")

async def require_admin(request: Request):
    """Require admin authentication"""
    user = await require_authentication(request)
    if user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="redirect_unauthorized")
    return user

# Browser session management removed for simplicity

# Remaining browser session functions removed for simplicity

# ========================================
# TAB-SPECIFIC URL TOKEN SYSTEM
# ========================================

# Tab token and browser fingerprint functionality removed for simplicity

# Routes
@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """Root route - redirect to login page"""
    return RedirectResponse(url="/login")

@app.get("/unauthorized", response_class=HTMLResponse)
async def unauthorized_page(request: Request):
    """Unauthorized access page - styled redirect to login"""
    return templates.TemplateResponse("unauthorized.html", {"request": request})

@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    """Login page"""
    return templates.TemplateResponse("login.html", {"request": request})

# Tab token generation endpoint removed for simplicity

@app.post("/login")
async def login(request: Request):
    """Handle login with case-insensitive username"""
    form = await request.form()
    username = form.get("username")
    password = form.get("password")

    user = await authenticate_user(username, password)
    if not user:
        return templates.TemplateResponse("login.html", {
            "request": request,
            "error": "Invalid username or password"
        })

    session_id = await create_session(user)
    print(f"🔐 LOGIN: Created session {session_id} for user {user.username}")

    # Send to watchdog instead of console
    asyncio.create_task(send_to_watchdog(f"User {user.username} logged in, session: {session_id}"))

    response = RedirectResponse(url="/chat", status_code=303)
    response.set_cookie(
        key="session_id",
        value=session_id,
        httponly=False,  # Allow JavaScript access
        secure=False,    # Allow HTTP (not just HTTPS)
        samesite="lax"   # Allow cross-site requests
    )
    print(f"🔐 LOGIN: Set cookie session_id={session_id} for user {user.username}")
    return response

@app.get("/chat", response_class=HTMLResponse)
async def chat_page(request: Request):
    """DeepChat main interface - requires authentication"""
    try:
        user = await require_authentication(request)

        # Use the existing session_id from the authentication cookie
        session_id = request.cookies.get("session_id")
        print(f"🔐 CHAT: User {user.username} accessing chat with session: {session_id}")

        if not session_id:
            # Fallback: create new session if somehow missing
            session_id = await create_session(user)
            print(f"🔐 CHAT: Created fallback session {session_id} for user {user.username}")

        # Log access
        asyncio.create_task(send_to_watchdog(f"🔐 DeepChat access: {user.username} (Session: {session_id})"))

        # JavaScript will calculate session_id from cookies
        response = templates.TemplateResponse("simple_chat.html", {
            "request": request,
            "user": user
        })

        # Add cache-busting headers
        response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate, max-age=0"
        response.headers["Pragma"] = "no-cache"
        response.headers["Expires"] = "0"
        return response

    except HTTPException as e:
        if e.detail == "redirect_unauthorized":
            return RedirectResponse(url="/unauthorized", status_code=307)
        else:
            return RedirectResponse(url="/login", status_code=307)

# Admin access route for chat users
@app.get("/api/check-admin-access")
async def check_admin_access(request: Request):
    """Check if current user should have admin access"""
    try:
        # For now, allow admin access based on simple criteria
        # In a real system, this would check the database
        user_agent = request.headers.get("user-agent", "")
        client_ip = request.client.host if request.client else "unknown"

        # Simple admin detection - can be enhanced later
        is_admin = (
            client_ip in ["127.0.0.1", "localhost"] or  # Local access
            "admin" in user_agent.lower()  # Admin user agent
        )

        return {
            "is_admin": is_admin,
            "role": "admin" if is_admin else "guest",
            "message": "Admin access granted" if is_admin else "Guest access"
        }
    except Exception as e:
        logger.error(f"Error checking admin access: {e}", module="WEB-CHAT", routine="check_admin_access")
        return {"is_admin": False, "role": "guest", "message": "Error checking access"}

# Enhanced chat route with admin detection
@app.get("/chat-admin", response_class=HTMLResponse)
async def chat_admin_page(request: Request):
    """DeepChat with automatic admin detection"""
    # Check admin access
    admin_check = await check_admin_access(request)
    is_admin = admin_check.get("is_admin", False)

    user = {
        "username": "Admin" if is_admin else "Guest",
        "user_id": ("admin_" if is_admin else "guest_") + str(int(time.time())),
        "full_name": "Administrator" if is_admin else "Guest User",
        "role": {"value": "admin" if is_admin else "guest"}
    }

    session_id = "session_" + str(int(time.time())) + "_" + str(uuid.uuid4())[:8]

    # Log access
    asyncio.create_task(send_to_watchdog(f"🔐 DeepChat access: {user.username} ({'Admin' if is_admin else 'Guest'}) (Session: {session_id})"))

    # JavaScript will calculate session_id from cookies
    response = templates.TemplateResponse("simple_chat.html", {
        "request": request,
        "user": user
    })

    # Add cache-busting headers
    response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate, max-age=0"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"
    return response

# Old chat routes removed for simplicity

@app.get("/simple-chat", response_class=HTMLResponse)
async def simple_chat_page(request: Request):
    """Simple chat page - supports multiple concurrent sessions"""
    user = {
        "username": "Guest",
        "user_id": "guest_" + str(int(time.time())),
        "full_name": "Guest User",
        "role": {"value": "guest"}
    }
    session_id = "session_" + str(int(time.time())) + "_" + str(uuid.uuid4())[:8]

    # JavaScript will calculate session_id from cookies
    response = templates.TemplateResponse("simple_chat.html", {
        "request": request,
        "user": user
    })

    # Set session cookie for JavaScript to read
    response.set_cookie(
        key="session_id",
        value=session_id,
        httponly=False,  # Allow JavaScript access
        secure=False,    # Allow HTTP for local testing
        samesite="lax"
    )

    return response

@app.get("/js-test", response_class=HTMLResponse)
async def javascript_test_page(request: Request):
    """🔧 MINIMAL JAVASCRIPT TEST - Absolute simplest test"""
    response = HTMLResponse(content="""
    <!DOCTYPE html>
    <html>
    <head><title>JS Test</title></head>
    <body>
        <h1>JavaScript Test</h1>
        <button onclick="alert('JS Works!')">Test</button>
        <script>console.log('JS Loading'); alert('JS Loaded');</script>
    </body>
    </html>
    """, status_code=200)

    # 🔧 FORCE NO CACHE - Prevent browser caching
    response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate, max-age=0"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"
    response.headers["Last-Modified"] = "Thu, 01 Jan 1970 00:00:00 GMT"
    return response

@app.get("/test-chat", response_class=HTMLResponse)
async def test_chat_page(request: Request):
    """🔧 SIMPLE JAVASCRIPT TEST - No complex templates, just basic HTML"""
    # Return basic HTML with JavaScript test - NO TEMPLATE RENDERING
    return HTMLResponse(content="""
    <!DOCTYPE html>
    <html>
    <head>
        <title>DEEPLICA Chat JavaScript Test</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 20px;
                background: #1a1a1a;
                color: #00ff00;
            }
            .test-area {
                border: 1px solid #00ff00;
                padding: 20px;
                margin: 10px 0;
                background: #2a2a2a;
            }
            button {
                padding: 10px 20px;
                margin: 5px;
                background: #00ff00;
                color: #000;
                border: none;
                cursor: pointer;
            }
            button:hover {
                background: #00cc00;
            }
            input {
                padding: 10px;
                width: 300px;
                background: #333;
                color: #00ff00;
                border: 1px solid #00ff00;
            }
            .output {
                margin-top: 10px;
                padding: 10px;
                background: #333;
                border-left: 3px solid #00ff00;
            }
        </style>
    </head>
    <body>
        <h1>🔧 DEEPLICA Chat JavaScript Test</h1>
        <p>This page tests if JavaScript is working at all.</p>

        <div class="test-area">
            <h3>Basic JavaScript Test</h3>
            <input type="text" id="testInput" placeholder="Type a message...">
            <button onclick="testSendMessage()">Send Test Message</button>
            <div id="testOutput" class="output"></div>
        </div>

        <div class="test-area">
            <h3>Admin Button Test</h3>
            <button onclick="testAdminFunction()">⚙️ Test Admin</button>
            <div id="adminOutput" class="output"></div>
        </div>

        <div class="test-area">
            <h3>Event Listener Test</h3>
            <button id="eventTestButton">Click Me (Event Listener)</button>
            <div id="eventOutput" class="output"></div>
        </div>

        <div class="test-area">
            <h3>Console Test</h3>
            <button onclick="testConsole()">Test Console Logging</button>
            <p>Check browser console (F12) for log messages</p>
        </div>

        <script>
            console.log('🔧 DEEPLICA Chat Test Page JavaScript Loading...');

            function testSendMessage() {
                console.log('📤 testSendMessage() called');
                const input = document.getElementById('testInput');
                const output = document.getElementById('testOutput');
                const message = input.value.trim();

                if (message) {
                    output.innerHTML += '<div>📤 Test message: ' + message + '</div>';
                    input.value = '';
                    console.log('✅ Send message test successful');
                } else {
                    output.innerHTML += '<div>⚠️ Please enter a message</div>';
                }
            }

            function testAdminFunction() {
                console.log('⚙️ testAdminFunction() called');
                const output = document.getElementById('adminOutput');
                output.innerHTML = '<div>⚙️ Admin function test successful!</div>';
                console.log('✅ Admin function test successful');
            }

            function testConsole() {
                console.log('🔧 Console test function called');
                console.warn('⚠️ This is a warning message');
                console.error('❌ This is an error message (for testing)');
                console.info('ℹ️ This is an info message');
                alert('Console test complete - check browser console (F12)');
            }

            // Test DOM ready
            document.addEventListener('DOMContentLoaded', function() {
                console.log('✅ DOMContentLoaded event fired');

                // Test event listeners
                const eventButton = document.getElementById('eventTestButton');
                if (eventButton) {
                    eventButton.addEventListener('click', function() {
                        console.log('✅ Event listener test successful');
                        const output = document.getElementById('eventOutput');
                        output.innerHTML = '<div>✅ Event listener working!</div>';
                    });
                    console.log('✅ Event listener attached');
                } else {
                    console.error('❌ Event test button not found');
                }

                // Test input event listener
                const testInput = document.getElementById('testInput');
                if (testInput) {
                    testInput.addEventListener('keydown', function(e) {
                        if (e.key === 'Enter') {
                            console.log('⌨️ Enter key pressed');
                            testSendMessage();
                        }
                    });
                    console.log('✅ Input event listener attached');
                } else {
                    console.error('❌ Test input not found');
                }
            });

            console.log('✅ All JavaScript functions defined');
            console.log('🎯 Test page ready - try the buttons!');
        </script>
    </body>
    </html>
    """, status_code=200)

    # 🔧 FORCE NO CACHE - Prevent browser caching
    response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate, max-age=0"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"
    response.headers["Last-Modified"] = "Thu, 01 Jan 1970 00:00:00 GMT"
    return response

@app.get("/fresh-js-test", response_class=HTMLResponse)
async def fresh_javascript_test(request: Request):
    """🔧 FRESH JAVASCRIPT TEST - Completely new route to bypass cache"""
    import time
    timestamp = str(int(time.time()))

    response = HTMLResponse(content=f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Fresh JS Test - {timestamp}</title>
        <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
        <meta http-equiv="Pragma" content="no-cache">
        <meta http-equiv="Expires" content="0">
    </head>
    <body style="font-family: Arial; background: #000; color: #0f0; padding: 20px;">
        <h1>🔧 FRESH JAVASCRIPT TEST - {timestamp}</h1>
        <p>This is a completely fresh test to bypass all caching.</p>

        <button onclick="testClick()" style="padding: 10px; background: #0f0; color: #000; border: none; margin: 5px;">
            Click Me!
        </button>

        <button onclick="alert('Direct alert works!')" style="padding: 10px; background: #ff0; color: #000; border: none; margin: 5px;">
            Direct Alert
        </button>

        <div id="output" style="margin: 10px 0; padding: 10px; background: #333;">
            Waiting for JavaScript...
        </div>

        <script>
            console.log('🔧 FRESH TEST: JavaScript loading at {timestamp}');

            // Immediate test
            document.getElementById('output').innerHTML = '✅ JavaScript is working! Timestamp: {timestamp}';

            function testClick() {{
                console.log('✅ Button clicked successfully');
                document.getElementById('output').innerHTML += '<br>✅ Button click worked!';
                alert('✅ JavaScript function call successful!');
            }}

            console.log('✅ FRESH TEST: All JavaScript loaded successfully');
        </script>
    </body>
    </html>
    """, status_code=200)

    # Force no cache
    response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate, max-age=0"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"
    response.headers["Last-Modified"] = "Thu, 01 Jan 1970 00:00:00 GMT"
    return response

@app.get("/template-test", response_class=HTMLResponse)
async def template_test(request: Request):
    """🔧 TEMPLATE VARIABLE TEST - Test if template variables work correctly"""
    try:
        import time
        timestamp = str(int(time.time()))

        # Simple test without complex template
        return HTMLResponse(content=f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Template Test - {timestamp}</title>
            <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
        </head>
        <body style="font-family: Arial; background: #000; color: #0f0; padding: 20px;">
            <h1>🔧 TEMPLATE TEST - {timestamp}</h1>
            <p>Testing if basic template rendering works.</p>

            <button onclick="testTemplate()" style="padding: 10px; background: #0f0; color: #000; border: none; margin: 5px;">
                Test Template JS
            </button>

            <div id="output" style="margin: 10px 0; padding: 10px; background: #333;">
                Template loading...
            </div>

            <script>
                console.log('🔧 TEMPLATE TEST: JavaScript loading at {timestamp}');

                // Test template variables
                const testData = {{
                    timestamp: "{timestamp}",
                    sessionId: "test-session",
                    username: "test-user"
                }};

                console.log('📋 Test data:', testData);

                // Immediate test
                document.getElementById('output').innerHTML = '✅ Template JavaScript is working! Data: ' + JSON.stringify(testData);

                function testTemplate() {{
                    console.log('✅ Template button clicked successfully');
                    document.getElementById('output').innerHTML += '<br>✅ Template button click worked!';
                    alert('✅ Template JavaScript function call successful!');
                }}

                console.log('✅ TEMPLATE TEST: All JavaScript loaded successfully');
            </script>
        </body>
        </html>
        """, status_code=200)

    except Exception as e:
        logger.error(f"Template test error: {e}", module="WEB-CHAT", routine="template_test")
        return HTMLResponse(content=f"""
        <!DOCTYPE html>
        <html>
        <head><title>Template Test Error</title></head>
        <body style="font-family: Arial; background: #000; color: #f00; padding: 20px;">
            <h1>❌ Template Test Error</h1>
            <p>Error: {e}</p>
            <script>
                console.error('Template test error:', '{e}');
                alert('Template Error: {e}');
            </script>
        </body>
        </html>
        """, status_code=500)

@app.get("/test-input", response_class=HTMLResponse)
async def test_input_page(request: Request):
    """🧪 Input test page - for debugging input functionality"""
    return templates.TemplateResponse("test_input.html", {
        "request": request
    })

@app.get("/debug-chat", response_class=HTMLResponse)
async def debug_chat_page(request: Request):
    """🔧 DEBUG CHAT - No authentication required for testing JavaScript"""
    # Create a mock user for debugging
    mock_user = type('MockUser', (), {
        'username': 'debug-user',
        'full_name': 'Debug User',
        'role': type('MockRole', (), {'value': 'admin'})()
    })()

    # JavaScript will calculate session_id from cookies
    response = templates.TemplateResponse("chat.html", {
        "request": request,
        "user": mock_user,
        "username": "debug-user"
    })

    # Set a debug session cookie for JavaScript to read
    response.set_cookie(
        key="session_id",
        value="debug-session-" + str(int(time.time())),
        httponly=False,  # Allow JavaScript access
        secure=False,    # Allow HTTP for local testing
        samesite="lax"
    )

    # Add cache-busting headers
    response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"
    response.headers["Last-Modified"] = "Thu, 01 Jan 1970 00:00:00 GMT"
    return response

@app.post("/logout")
async def logout(request: Request):
    """Logout"""
    session_id = request.cookies.get("session_id")
    if session_id:
        await user_service.delete_session(session_id)

    response = RedirectResponse(url="/login")
    response.delete_cookie(key="session_id")
    return response

# Admin routes now use the require_admin function defined above

@app.get("/api/admin/debug/current-user")
async def debug_current_user(request: Request):
    """Debug endpoint to check current user info"""
    session_id = request.cookies.get("session_id")
    if not session_id:
        return {"authenticated": False, "message": "No session found"}

    try:
        user = await get_user_from_session(session_id)
        if not user:
            return {"authenticated": False, "message": "Invalid session"}

        return {
            "authenticated": True,
            "user_id": user.user_id,
            "username": user.username,
            "role": user.role,
            "is_admin": user.role == UserRole.ADMIN,
            "permissions": user.permissions
        }
    except Exception as e:
        return {"authenticated": False, "error": str(e)}

@app.get("/admin", response_class=HTMLResponse)
async def admin_page(request: Request):
    """Admin page - requires admin authentication"""
    try:
        user = await require_admin(request)

        # Log access
        asyncio.create_task(send_to_watchdog(f"🔐 Admin access: {user.username}"))

        response = templates.TemplateResponse("admin.html", {
            "request": request,
            "user": user
        })

        # Add cache-busting headers
        response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate, max-age=0"
        response.headers["Pragma"] = "no-cache"
        response.headers["Expires"] = "0"
        return response

    except HTTPException as e:
        if e.detail == "redirect_unauthorized":
            return RedirectResponse(url="/unauthorized", status_code=307)
        elif e.status_code == 401:
            return RedirectResponse(url="/login", status_code=307)
        elif e.status_code == 403:
            return RedirectResponse(url="/unauthorized", status_code=307)
        else:
            return RedirectResponse(url="/login", status_code=307)



@app.post("/api/admin/users")
async def create_user_admin(request: Request):
    """Create new user (admin only) - Proxy to Backend API"""
    await require_admin(request)

    try:
        data = await request.json()

        # Get Backend API URL
        backend_url = get_service_url("backend")

        # Forward request to Backend API
        import httpx
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(f"{backend_url}/api/v1/users", json=data)

            if response.status_code == 200:
                result = response.json()
                logger.info(f"✅ User created via Backend API: {data.get('username')}", module="WEB-CHAT", routine="create_user_admin")
                return result
            else:
                error_detail = response.text
                logger.error(f"❌ Backend API error: {response.status_code} - {error_detail}", module="WEB-CHAT", routine="create_user_admin")
                raise HTTPException(status_code=response.status_code, detail=error_detail)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to create user: {e}", module="WEB-CHAT", routine="create_user_admin")
        raise HTTPException(status_code=500, detail=f"Failed to create user: {str(e)}")

@app.get("/api/admin/debug/current-user")
async def debug_current_user(request: Request):
    """Debug current user session (admin only)"""
    try:
        # Get session from cookie
        session_id = request.cookies.get("session_id")
        if not session_id:
            return {
                "authenticated": False,
                "message": "No session cookie found"
            }

        # Get user from session
        user = await get_user_from_session(session_id)
        if not user:
            return {
                "authenticated": False,
                "message": "Invalid session"
            }

        return {
            "authenticated": True,
            "user_id": user.user_id,
            "username": user.username,
            "role": user.role,
            "is_admin": user.role == "admin",
            "permissions": user.permissions,
            "session_id": session_id
        }

    except Exception as e:
        logger.error(f"❌ Debug current user failed: {e}", module="WEB-CHAT", routine="debug_current_user")
        return {
            "authenticated": False,
            "error": str(e)
        }

@app.get("/api/admin/users")
async def list_users_admin(request: Request):
    """List all users (admin only) - Proxy to Backend API"""
    await require_admin(request)

    try:
        # Get Backend API URL
        backend_url = get_service_url("backend")

        # Forward request to Backend API
        import httpx
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(f"{backend_url}/api/v1/users")

            if response.status_code == 200:
                result = response.json()
                logger.debug(f"📋 Listed users via Backend API", module="WEB-CHAT", routine="list_users_admin")
                return result
            else:
                error_detail = response.text
                logger.error(f"❌ Backend API error: {response.status_code} - {error_detail}", module="WEB-CHAT", routine="list_users_admin")
                raise HTTPException(status_code=response.status_code, detail=error_detail)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to list users: {e}", module="WEB-CHAT", routine="list_users_admin")
        raise HTTPException(status_code=500, detail=f"Failed to list users: {str(e)}")

@app.post("/api/admin/database/test")
async def test_database_connection_admin(request: Request):
    """Test database connection (admin only) - Proxy to Backend API"""
    await require_admin(request)

    try:
        data = await request.json()

        # Get Backend API URL
        backend_url = get_service_url("backend")

        # Forward request to Backend API
        import httpx
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(f"{backend_url}/api/v1/admin/database/test", json=data)

            if response.status_code == 200:
                result = response.json()
                logger.info(f"✅ Database test completed via Backend API", module="WEB-CHAT", routine="test_database_connection_admin")
                return result
            else:
                error_detail = response.text
                logger.error(f"❌ Backend API error: {response.status_code} - {error_detail}", module="WEB-CHAT", routine="test_database_connection_admin")
                raise HTTPException(status_code=response.status_code, detail=error_detail)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to test database connection: {e}", module="WEB-CHAT", routine="test_database_connection_admin")
        raise HTTPException(status_code=500, detail=f"Failed to test database connection: {str(e)}")

@app.get("/api/admin/database/config")
async def get_database_config_admin(request: Request):
    """Get database configuration (admin only) - Fallback to hardcoded config"""
    await require_admin(request)

    try:
        # Return hardcoded database configuration since Backend API is not reliable
        return {
            "success": True,
            "connection_string": "mongodb+srv://deeplica-db:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0",
            "database_name": "deeplica-dev"
        }

    except Exception as e:
        logger.error(f"❌ Failed to get database config: {e}", module="WEB-CHAT", routine="get_database_config_admin")
        raise HTTPException(status_code=500, detail=f"Failed to get database config: {str(e)}")

@app.post("/api/admin/database/config")
async def save_database_config_admin(request: Request):
    """Save database configuration (admin only)"""
    await require_admin(request)

    try:
        data = await request.json()

        # For now, just log the configuration save request
        # In a real implementation, this would update environment variables or config files
        logger.info(f"📝 Database configuration save requested", module="WEB-CHAT", routine="save_database_config_admin")
        logger.info(f"📝 Connection string: {data.get('connection_string', '')[:50]}...", module="WEB-CHAT", routine="save_database_config_admin")
        logger.info(f"📝 Database name: {data.get('database_name', '')}", module="WEB-CHAT", routine="save_database_config_admin")

        return {
            "success": True,
            "message": "Configuration saved successfully (restart required for changes to take effect)"
        }

    except Exception as e:
        logger.error(f"❌ Failed to save database config: {e}", module="WEB-CHAT", routine="save_database_config_admin")
        raise HTTPException(status_code=500, detail=f"Failed to save database config: {str(e)}")

@app.get("/api/admin/terminals/{service_name}")
async def get_service_terminal_admin(service_name: str, request: Request):
    """Get service terminal output (admin only) - Proxy to Backend API"""
    await require_admin(request)

    try:
        # Get Backend API URL
        backend_url = get_service_url("backend")

        # Forward request to Backend API
        import httpx
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(f"{backend_url}/api/v1/admin/terminals/{service_name}")

            if response.status_code == 200:
                result = response.json()
                logger.debug(f"📺 Terminal output retrieved for {service_name} via Backend API", module="WEB-CHAT", routine="get_service_terminal_admin")
                return result
            else:
                error_detail = response.text
                logger.error(f"❌ Backend API error: {response.status_code} - {error_detail}", module="WEB-CHAT", routine="get_service_terminal_admin")
                raise HTTPException(status_code=response.status_code, detail=error_detail)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get terminal output: {e}", module="WEB-CHAT", routine="get_service_terminal_admin")
        raise HTTPException(status_code=500, detail=f"Failed to get terminal output: {str(e)}")

@app.post("/api/admin/migrate-passwords")
async def migrate_passwords_admin(request: Request):
    """Migrate legacy user passwords (admin only) - Proxy to Backend API"""
    await require_admin(request)

    try:
        # Get Backend API URL
        backend_url = get_service_url("backend")

        # Forward request to Backend API
        import httpx
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(f"{backend_url}/api/v1/admin/migrate-passwords")

            if response.status_code == 200:
                result = response.json()
                logger.info(f"✅ Password migration completed via Backend API", module="WEB-CHAT", routine="migrate_passwords_admin")
                return result
            else:
                error_detail = response.text
                logger.error(f"❌ Backend API error: {response.status_code} - {error_detail}", module="WEB-CHAT", routine="migrate_passwords_admin")
                raise HTTPException(status_code=response.status_code, detail=error_detail)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to migrate passwords: {e}", module="WEB-CHAT", routine="migrate_passwords_admin")
        raise HTTPException(status_code=500, detail=f"Failed to migrate passwords: {str(e)}")

@app.get("/api/admin/users/{user_id}")
async def get_user_admin(request: Request, user_id: str):
    """Get single user (admin only)"""
    await require_admin(request)

    try:
        # Ensure user service is connected to Backend API
        if not user_service.backend_url:
            await user_service.connect()

        user = await user_service.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        return {
            "user_id": user.user_id,
            "username": user.username,
            "email": user.email,
            "mobile_phone": user.mobile_phone,
            "full_name": user.full_name,
            "display_name": user.display_name,
            "role": user.role,
            "status": user.status,
            "permissions": user.permissions,
            "password_hash": user.password_hash,  # Include password hash for admin debugging
            "created_at": user.created_at.isoformat(),
            "last_login": user.last_login.isoformat() if user.last_login else None,
            "login_count": user.login_count
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get user: {e}", module="WEB-CHAT", routine="get_user_admin")
        raise HTTPException(status_code=500, detail=f"Failed to fetch user: {str(e)}")

@app.put("/api/admin/users/{user_id}")
async def update_user_admin(request: Request, user_id: str):
    """Update user (admin only)"""
    await require_admin(request)

    try:
        data = await request.json()

        # Ensure user service is connected to Backend API
        if not user_service.backend_url:
            await user_service.connect()

        user_update = UserUpdate(**data)
        updated_user = await user_service.update_user(user_id, user_update)

        if not updated_user:
            raise HTTPException(status_code=404, detail="User not found")

        return {"success": True, "user": updated_user.model_dump()}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update user: {e}", module="WEB-CHAT", routine="update_user_admin")
        raise HTTPException(status_code=500, detail=f"Failed to update user: {str(e)}")

@app.delete("/api/admin/users/{user_id}")
async def delete_user_admin(request: Request, user_id: str):
    """Delete user (admin only)"""
    admin_user = await require_admin(request)

    # Prevent admin from deleting themselves
    if user_id == admin_user.user_id:
        raise HTTPException(status_code=400, detail="Cannot delete your own account")

    try:
        # Ensure user service is connected to Backend API
        if not user_service.backend_url:
            await user_service.connect()

        success = await user_service.delete_user(user_id)

        if not success:
            raise HTTPException(status_code=404, detail="User not found")

        return {"success": True, "message": "User deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete user: {e}", module="WEB-CHAT", routine="delete_user_admin")
        raise HTTPException(status_code=500, detail=f"Failed to delete user: {str(e)}")

@app.post("/api/admin/users/{user_id}/restore")
async def restore_user(request: Request, user_id: str):
    """Restore user from database (admin only)"""
    await require_admin(request)

    try:
        # Ensure user service is connected to Backend API
        if not user_service.backend_url:
            await user_service.connect()

        # Get user from database
        user = await user_service.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # If user is already active, no need to restore
        if user.status == UserStatus.ACTIVE:
            return {"success": True, "message": "User is already active", "user": user.model_dump()}

        # Restore user by setting status to active
        user_update = UserUpdate(status=UserStatus.ACTIVE)
        updated_user = await user_service.update_user(user_id, user_update)

        if not updated_user:
            raise HTTPException(status_code=404, detail="Failed to restore user")

        return {"success": True, "message": "User restored successfully", "user": updated_user.model_dump()}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to restore user: {e}", module="WEB-CHAT", routine="restore_user")
        raise HTTPException(status_code=500, detail=f"Failed to restore user: {str(e)}")

@app.post("/api/admin/users/refresh")
async def refresh_users_from_db(request: Request):
    """Refresh users from database (admin only)"""
    await require_admin(request)

    try:
        # Force reconnection to database
        await user_service.connect()

        # Get all users from database
        users = await user_service.get_all_users()
        users_data = []
        for user in users:
            users_data.append({
                "user_id": user.user_id,
                "username": user.username,
                "email": user.email,
                "mobile_phone": user.mobile_phone,
                "full_name": user.full_name,
                "display_name": user.display_name,
                "role": user.role,
                "status": user.status,
                "permissions": user.permissions,
                "created_at": user.created_at.isoformat() if user.created_at else None,
                "last_login": user.last_login.isoformat() if user.last_login else None,
                "login_count": user.login_count
            })

        return {"success": True, "users": users_data, "message": f"Refreshed {len(users_data)} users from database"}

    except Exception as e:
        logger.error(f"Failed to refresh users: {e}", module="WEB-CHAT", routine="refresh_users_from_db")
        raise HTTPException(status_code=500, detail=f"Failed to refresh users: {str(e)}")


# ========================================
# ENV CONFIGURATION MANAGEMENT
# ========================================

@app.get("/api/env-config")
async def get_env_config(request: Request):
    """Get current .env configuration (admin only)"""
    await require_admin(request)

    try:
        import os
        from dotenv import load_dotenv

        # Load current .env file
        env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
        load_dotenv(env_path)

        # Get all environment variables that are in our mapping
        config = {}

        # Define all possible .env variables
        env_vars = [
            # AI Services
            'GEMINI_API_KEY',

            # Database
            'MONGODB_CONNECTION_STRING', 'MONGODB_URI', 'MONGODB_DATABASE',
            'USE_MOCK_DATABASE', 'FORCE_MOCK_DATABASE', 'USE_REAL_DATABASE',

            # Ports
            'BACKEND_API_PORT', 'DISPATCHER_PORT', 'DIALOGUE_AGENT_PORT', 'PLANNER_AGENT_PORT',
            'PHONE_AGENT_PORT', 'WATCHDOG_PORT', 'WEB_CHAT_PORT', 'CLI_TERMINAL_PORT',
            'TWILIO_ECHO_BOT_PORT', 'WEBHOOK_SERVER_PORT', 'NGROK_API_PORT', 'NGROK_TUNNEL_PORT',
            'TEST_SERVER_PORT', 'DEV_SERVER_PORT', 'PROXY_SERVER_PORT', 'MOCK_SERVER_PORT',
            'DEBUG_SERVER_PORT', 'ADMIN_PANEL_PORT', 'METRICS_PORT', 'LOGS_PORT', 'HEALTH_CHECK_PORT',

            # Twilio
            'TWILIO_ACCOUNT_SID', 'TWILIO_AUTH_TOKEN', 'TWILIO_PHONE_NUMBER', 'TWILIO_WEBHOOK_URL',

            # Ngrok
            'NGROK_API_KEY',

            # System
            'DEFAULT_HOST', 'EXTERNAL_HOST', 'WEB_HOST', 'HOST', 'PORT',
            'WAIT_FOR_BACKEND', 'BACKEND_READY_TIMEOUT', 'SERVICE_STARTUP_TIMEOUT', 'HEALTH_CHECK_TIMEOUT',
            'ENVIRONMENT', 'DEBUG',
            'DISPATCHER_URL', 'DIALOGUE_AGENT_URL', 'PLANNER_AGENT_URL', 'PHONE_AGENT_URL', 'BACKEND_URL'
        ]

        for var in env_vars:
            value = os.getenv(var)
            if value is not None:
                config[var] = value

        logger.info(f"Loaded {len(config)} environment variables", module="WEB-CHAT", routine="get_env_config")
        return config

    except Exception as e:
        logger.error(f"Failed to get env config: {e}", module="WEB-CHAT", routine="get_env_config")
        raise HTTPException(status_code=500, detail=f"Failed to get environment configuration: {str(e)}")

@app.post("/api/env-config")
async def save_env_config(request: Request):
    """Save configuration to .env file (admin only)"""
    await require_admin(request)

    try:
        import os

        # Get the configuration data
        config_data = await request.json()

        # Path to .env file
        env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')

        # Read current .env file
        env_lines = []
        if os.path.exists(env_path):
            with open(env_path, 'r') as f:
                env_lines = f.readlines()

        # Update or add new values
        updated_vars = set()
        for i, line in enumerate(env_lines):
            line = line.strip()
            if '=' in line and not line.startswith('#'):
                var_name = line.split('=')[0].strip()
                if var_name in config_data:
                    # Update existing variable
                    env_lines[i] = f"{var_name}={config_data[var_name]}\n"
                    updated_vars.add(var_name)

        # Add new variables that weren't found
        for var_name, value in config_data.items():
            if var_name not in updated_vars:
                env_lines.append(f"{var_name}={value}\n")

        # Write back to .env file
        with open(env_path, 'w') as f:
            f.writelines(env_lines)

        logger.info(f"Updated {len(config_data)} environment variables in .env file", module="WEB-CHAT", routine="save_env_config")

        return {
            "status": "success",
            "message": f"Successfully updated {len(config_data)} environment variables",
            "updated_count": len(config_data)
        }

    except Exception as e:
        logger.error(f"Failed to save env config: {e}", module="WEB-CHAT", routine="save_env_config")
        raise HTTPException(status_code=500, detail=f"Failed to save environment configuration: {str(e)}")

# ========================================
# PROCESS MANAGEMENT
# ========================================

@app.post("/api/admin/processes/start")
async def start_process(request: Request):
    """Start a DEEPLICA process (admin only)"""
    await require_admin(request)

    try:
        data = await request.json()
        service = data.get('service')

        if not service:
            raise HTTPException(status_code=400, detail="Service name is required")

        logger.info(f"Starting process: {service}", module="WEB-CHAT", routine="start_process")

        # For now, return success (actual process management would be implemented here)
        return {
            "status": "success",
            "message": f"Process {service} start command sent",
            "service": service
        }

    except Exception as e:
        logger.error(f"Failed to start process: {e}", module="WEB-CHAT", routine="start_process")
        raise HTTPException(status_code=500, detail=f"Failed to start process: {str(e)}")

@app.post("/api/admin/processes/stop")
async def stop_process(request: Request):
    """Stop a DEEPLICA process (admin only)"""
    await require_admin(request)

    try:
        data = await request.json()
        service = data.get('service')

        if not service:
            raise HTTPException(status_code=400, detail="Service name is required")

        logger.info(f"Stopping process: {service}", module="WEB-CHAT", routine="stop_process")

        # For now, return success (actual process management would be implemented here)
        return {
            "status": "success",
            "message": f"Process {service} stop command sent",
            "service": service
        }

    except Exception as e:
        logger.error(f"Failed to stop process: {e}", module="WEB-CHAT", routine="stop_process")
        raise HTTPException(status_code=500, detail=f"Failed to stop process: {str(e)}")

@app.post("/api/admin/processes/restart")
async def restart_process(request: Request):
    """Restart a DEEPLICA process (admin only)"""
    await require_admin(request)

    try:
        data = await request.json()
        service = data.get('service')

        if not service:
            raise HTTPException(status_code=400, detail="Service name is required")

        logger.info(f"Restarting process: {service}", module="WEB-CHAT", routine="restart_process")

        # For now, return success (actual process management would be implemented here)
        return {
            "status": "success",
            "message": f"Process {service} restart command sent",
            "service": service
        }

    except Exception as e:
        logger.error(f"Failed to restart process: {e}", module="WEB-CHAT", routine="restart_process")
        raise HTTPException(status_code=500, detail=f"Failed to restart process: {str(e)}")

@app.get("/api/admin/processes/logs/{service}")
async def get_process_logs(service: str, request: Request):
    """Get logs for a DEEPLICA process (admin only)"""
    await require_admin(request)

    try:
        logger.info(f"Fetching logs for process: {service}", module="WEB-CHAT", routine="get_process_logs")

        # For now, return mock logs (actual log fetching would be implemented here)
        mock_logs = f"""
[2025-07-11 10:00:00] INFO - {service} service started
[2025-07-11 10:00:01] INFO - Initializing {service} components
[2025-07-11 10:00:02] INFO - {service} ready for requests
[2025-07-11 10:00:03] INFO - Health check passed
[2025-07-11 10:00:04] INFO - {service} running normally
        """.strip()

        return {
            "status": "success",
            "service": service,
            "logs": mock_logs,
            "timestamp": time.time()
        }

    except Exception as e:
        logger.error(f"Failed to get process logs: {e}", module="WEB-CHAT", routine="get_process_logs")
        raise HTTPException(status_code=500, detail=f"Failed to get process logs: {str(e)}")

@app.get("/api/admin/os-processes")
async def get_os_processes(request: Request):
    """Get OS-level DEEPLICA processes (admin only)"""
    await require_admin(request)

    try:
        import psutil
        import os

        logger.info("Scanning OS processes for DEEPLICA", module="WEB-CHAT", routine="get_os_processes")

        deeplica_processes = []
        total_memory = 0

        # Search for DEEPLICA-related processes
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'memory_info']):
            try:
                proc_info = proc.info
                cmdline = ' '.join(proc_info['cmdline']) if proc_info['cmdline'] else ''

                # Check if process is DEEPLICA-related
                if any(keyword in cmdline.lower() for keyword in ['deeplica', 'main.py', 'python3']):
                    if any(service in cmdline.lower() for service in ['backend', 'dispatcher', 'dialogue', 'planner', 'phone', 'watchdog', 'web-chat', 'cli']):
                        memory_mb = proc_info['memory_info'].rss / 1024 / 1024 if proc_info['memory_info'] else 0
                        total_memory += memory_mb

                        # Determine service name from command line
                        service_name = 'Unknown'
                        if 'backend' in cmdline.lower():
                            service_name = 'Backend API'
                        elif 'dispatcher' in cmdline.lower():
                            service_name = 'Dispatcher'
                        elif 'dialogue' in cmdline.lower():
                            service_name = 'Dialogue Agent'
                        elif 'planner' in cmdline.lower():
                            service_name = 'Planner Agent'
                        elif 'phone' in cmdline.lower():
                            service_name = 'Phone Agent'
                        elif 'watchdog' in cmdline.lower():
                            service_name = 'Watchdog'
                        elif 'web-chat' in cmdline.lower():
                            service_name = 'Web Chat'
                        elif 'cli' in cmdline.lower():
                            service_name = 'CLI Terminal'

                        deeplica_processes.append({
                            'pid': proc_info['pid'],
                            'name': service_name,
                            'command': cmdline[:100] + '...' if len(cmdline) > 100 else cmdline,
                            'memory': f"{memory_mb:.1f} MB"
                        })

            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue

        stats = {
            'total': len(deeplica_processes),
            'running': len(deeplica_processes),
            'memory': f"{total_memory:.1f} MB"
        }

        logger.info(f"Found {len(deeplica_processes)} DEEPLICA processes", module="WEB-CHAT", routine="get_os_processes")

        return {
            "status": "success",
            "processes": deeplica_processes,
            "stats": stats
        }

    except ImportError:
        logger.warning("psutil not available for OS process scanning", module="WEB-CHAT", routine="get_os_processes")
        return {
            "status": "success",
            "processes": [],
            "stats": {"total": 0, "running": 0, "memory": "0 MB"},
            "message": "psutil library not available for OS process scanning"
        }
    except Exception as e:
        logger.error(f"Failed to get OS processes: {e}", module="WEB-CHAT", routine="get_os_processes")
        raise HTTPException(status_code=500, detail=f"Failed to get OS processes: {str(e)}")

@app.post("/api/admin/os-processes/kill")
async def kill_os_process(request: Request):
    """Kill an OS process (admin only)"""
    await require_admin(request)

    try:
        import psutil

        data = await request.json()
        pid = data.get('pid')

        if not pid:
            raise HTTPException(status_code=400, detail="Process ID is required")

        logger.info(f"Killing OS process: {pid}", module="WEB-CHAT", routine="kill_os_process")

        # Get process info before killing
        try:
            proc = psutil.Process(pid)
            proc_name = proc.name()

            # Kill the process
            proc.terminate()

            # Wait for process to terminate
            try:
                proc.wait(timeout=5)
            except psutil.TimeoutExpired:
                proc.kill()  # Force kill if terminate doesn't work

            logger.info(f"Successfully killed process {proc_name} (PID: {pid})", module="WEB-CHAT", routine="kill_os_process")

            return {
                "status": "success",
                "message": f"Process {proc_name} (PID: {pid}) killed successfully",
                "pid": pid
            }

        except psutil.NoSuchProcess:
            raise HTTPException(status_code=404, detail=f"Process with PID {pid} not found")
        except psutil.AccessDenied:
            raise HTTPException(status_code=403, detail=f"Access denied to kill process {pid}")

    except ImportError:
        raise HTTPException(status_code=500, detail="psutil library not available for process management")
    except Exception as e:
        logger.error(f"Failed to kill OS process: {e}", module="WEB-CHAT", routine="kill_os_process")
        raise HTTPException(status_code=500, detail=f"Failed to kill OS process: {str(e)}")

# ========================================
# BROWSER SESSION MANAGEMENT - SIMPLIFIED
# ========================================

# Browser session registration removed for simplicity
# Browser session registration endpoint removed for simplicity

# Browser session validation endpoint removed for simplicity

@app.get("/api/session/info")
async def get_session_info_endpoint(request: Request):
    """🔐 Get current session information for admin display"""
    try:
        # Get session from cookie or header
        session_id = request.cookies.get('session_id') or request.headers.get('X-Session-ID')

        if not session_id:
            raise HTTPException(status_code=401, detail="No session found")

        user = await get_user_from_session(session_id)
        if not user:
            raise HTTPException(status_code=401, detail="Invalid session")

        # Get browser session info
        browser_session = None
        if user.user_id in active_browser_sessions:
            browser_session = active_browser_sessions[user.user_id].copy()
            # Remove sensitive data
            if 'browser_token' in browser_session:
                browser_session['browser_token'] = browser_session['browser_token'][:16] + '...'

        return {
            "session_info": {
                "session_id": session_id[:16] + '...',
                "user_id": user.user_id[:16] + '...',
                "username": user.username,
                "browser_token": browser_session.get('browser_token') if browser_session else None,
                "tab_token": browser_session.get('tab_token', '')[:16] + '...' if browser_session and browser_session.get('tab_token') else None,
                "window_id": browser_session.get('window_id', '')[:16] + '...' if browser_session and browser_session.get('window_id') else None,
                "last_activity": browser_session.get('last_activity').isoformat() if browser_session and browser_session.get('last_activity') else None,
                "registration_time": browser_session.get('registration_time').isoformat() if browser_session and browser_session.get('registration_time') else None,
                "tab_count": browser_session.get('tab_count', 0) if browser_session else 0,
                "validation_count": browser_session.get('validation_count', 0) if browser_session else 0,
                "session_active": browser_session is not None,
                "user_agent": browser_session.get('user_agent') if browser_session else None,
                "screen_resolution": browser_session.get('screen_resolution') if browser_session else None,
                "timezone": browser_session.get('timezone') if browser_session else None,
                "language": browser_session.get('language') if browser_session else None
            }
        }

    except Exception as e:
        logger.error(f"Failed to get session info: {e}", module="WEB-CHAT", routine="get_session_info_endpoint")
        raise HTTPException(status_code=500, detail=f"Failed to get session info: {str(e)}")

@app.get("/api/session/info")
async def get_session_info(request: Request):
    """Get current session information for admin display"""
    try:
        session_id = request.cookies.get("session_id")
        if not session_id:
            raise HTTPException(status_code=401, detail="No session found")

        user = await get_user_from_session(session_id)
        if not user:
            raise HTTPException(status_code=401, detail="Invalid session")

        # Get browser session info
        browser_info = active_browser_sessions.get(user.user_id, {})

        return {
            "status": "success",
            "session_info": {
                "session_id": session_id,
                "user_id": user.user_id,
                "username": user.username,
                "browser_token": browser_info.get('browser_token', 'N/A'),
                "last_activity": browser_info.get('last_activity', datetime.now()).isoformat() if browser_info.get('last_activity') else 'N/A',
                "tab_count": browser_info.get('tab_count', 0),
                "session_active": user.user_id in active_browser_sessions
            }
        }

    except Exception as e:
        logger.error(f"Failed to get session info: {e}", module="WEB-CHAT", routine="get_session_info")
        raise HTTPException(status_code=500, detail=f"Failed to get session info: {str(e)}")

# ========================================
# 👤 USER PREFERENCES MANAGEMENT
# ========================================

@app.get("/api/user-preferences")
async def get_user_preferences(request: Request, category: str = None, subcategory: str = None):
    """Get user preferences - BULLETPROOF version that never crashes"""
    try:
        logger.debug(f"🔍 Getting user preferences - category: {category}, subcategory: {subcategory}")

        # Authenticate user with bulletproof error handling
        try:
            user = await require_authentication(request)
            logger.debug(f"✅ User authenticated: {user.username}")
        except Exception as auth_error:
            logger.error(f"❌ Authentication failed in get_user_preferences: {str(auth_error)}")
            raise HTTPException(status_code=401, detail="Authentication required")

        # Build query filter
        filter_query = {"user_id": user.user_id}
        if category:
            filter_query["category"] = category
        if subcategory:
            filter_query["subcategory"] = subcategory

        logger.debug(f"🔍 Query filter: {filter_query}")

        # Get preferences from Backend API (NO DIRECT DATABASE ACCESS)
        try:
            backend_url = get_backend_api_url()
            if not backend_url:
                logger.warning("⚠️ Backend API not available, returning empty preferences")
                return {"preferences": None}

            # Make HTTP request to Backend API instead of direct database access
            import httpx
            async with httpx.AsyncClient(timeout=10.0) as client:
                params = {"user_id": user.user_id}
                if category:
                    params["category"] = category
                if subcategory:
                    params["subcategory"] = subcategory

                response = await client.get(
                    f"{backend_url}/api/v1/user-preferences",
                    params=params
                )

                if response.status_code == 200:
                    data = response.json()
                    logger.debug(f"✅ Found preferences for user {user.username} via Backend API")
                    return data
                elif response.status_code == 404:
                    logger.debug(f"ℹ️ No preferences found for user {user.username}")
                    return {"preferences": None}
                else:
                    logger.warning(f"⚠️ Backend API returned status {response.status_code}")
                    return {"preferences": None}

        except Exception as api_error:
            logger.error(f"❌ Backend API error in get_user_preferences: {str(api_error)}")
            # Return empty preferences instead of crashing
            return {"preferences": None}

    except HTTPException:
        # Re-raise HTTP exceptions (like 401)
        raise
    except Exception as e:
        logger.error(f"❌ Unexpected error in get_user_preferences: {str(e)}")
        # Return empty preferences instead of crashing the service
        return {"preferences": None}

@app.post("/api/user-preferences")
async def save_user_preferences(request: Request):
    """Save user preferences - BULLETPROOF version that never crashes"""
    try:
        logger.debug("🔍 Saving user preferences")

        # Authenticate user with bulletproof error handling
        try:
            user = await require_authentication(request)
            logger.debug(f"✅ User authenticated: {user.username}")
        except Exception as auth_error:
            logger.error(f"❌ Authentication failed in save_user_preferences: {str(auth_error)}")
            raise HTTPException(status_code=401, detail="Authentication required")

        # Parse request data with bulletproof error handling
        try:
            data = await request.json()
            logger.debug(f"📝 Request data received: {list(data.keys())}")
        except Exception as json_error:
            logger.error(f"❌ Failed to parse JSON in save_user_preferences: {str(json_error)}")
            raise HTTPException(status_code=400, detail="Invalid JSON data")

        category = data.get("category")
        subcategory = data.get("subcategory")
        preferences = data.get("preferences")

        if not category or not preferences:
            logger.warning(f"⚠️ Missing required fields - category: {category}, preferences: {bool(preferences)}")
            raise HTTPException(status_code=400, detail="Category and preferences are required")

        # Prepare document
        preference_doc = {
            "user_id": user.user_id,
            "username": user.username,
            "category": category,
            "preferences": preferences,
            "updated_at": datetime.utcnow().isoformat()
        }

        if subcategory:
            preference_doc["subcategory"] = subcategory

        logger.debug(f"📄 Prepared document for user {user.username}: {category}" + (f"/{subcategory}" if subcategory else ""))

        # Save preferences via Backend API (NO DIRECT DATABASE ACCESS)
        try:
            backend_url = get_backend_api_url()
            if not backend_url:
                logger.warning("⚠️ Backend API not available, preferences not saved")
                return {"success": False, "message": "Backend API not available"}

            # Make HTTP request to Backend API instead of direct database access
            import httpx
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.post(
                    f"{backend_url}/api/v1/user-preferences",
                    json=preference_doc
                )

                if response.status_code == 200:
                    logger.info(f"✅ User preferences saved for {user.username}: {category}" + (f"/{subcategory}" if subcategory else ""))
                    return {"success": True, "message": "Preferences saved successfully"}
                else:
                    logger.warning(f"⚠️ Backend API returned status {response.status_code}")
                    return {"success": False, "message": f"Backend API error: {response.status_code}"}

        except Exception as api_error:
            logger.error(f"❌ Backend API error in save_user_preferences: {str(api_error)}")
            # Return error but don't crash the service
            return {"success": False, "message": "Backend API error - preferences not saved"}

    except HTTPException:
        # Re-raise HTTP exceptions (like 400, 401)
        raise
    except Exception as e:
        logger.error(f"❌ Unexpected error in save_user_preferences: {str(e)}")
        # Return error instead of crashing the service
        return {"success": False, "message": "Unexpected error - preferences not saved"}


@app.get("/api/current-user")
async def get_current_user(request: Request):
    """Get current user information - BULLETPROOF version"""
    try:
        logger.debug("🔍 Getting current user information")

        # Authenticate user with bulletproof error handling
        try:
            user = await require_authentication(request)
            logger.debug(f"✅ Current user: {user.username}")

            return {
                "user_id": user.user_id,
                "username": user.username,
                "email": getattr(user, 'email', ''),
                "is_admin": getattr(user, 'is_admin', False)
            }

        except HTTPException as http_error:
            # Return unauthorized instead of crashing
            logger.debug("❌ User not authenticated")
            raise http_error
        except Exception as auth_error:
            logger.error(f"❌ Authentication error in get_current_user: {str(auth_error)}")
            raise HTTPException(status_code=401, detail="Authentication failed")

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"❌ Unexpected error in get_current_user: {str(e)}")
        # Return error instead of crashing the service
        raise HTTPException(status_code=500, detail="Error getting user information")

# ========================================
# 🛡️ GLOBAL EXCEPTION HANDLERS
# ========================================

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler to prevent service crashes"""
    logger.error(f"🚨 GLOBAL EXCEPTION CAUGHT: {str(exc)}")
    logger.error(f"🔍 Request URL: {request.url}")
    logger.error(f"🔍 Request method: {request.method}")

    import traceback
    logger.error(f"📋 Stack trace:\n{traceback.format_exc()}")

    # Return a generic error response instead of crashing
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "An unexpected error occurred. The service is still running.",
            "timestamp": datetime.utcnow().isoformat()
        }
    )

@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions gracefully"""
    logger.debug(f"🔍 HTTP Exception: {exc.status_code} - {exc.detail}")

    # For authentication redirects
    if exc.detail == "redirect_unauthorized":
        return RedirectResponse(url="/login", status_code=302)

    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.detail,
            "status_code": exc.status_code,
            "timestamp": datetime.utcnow().isoformat()
        }
    )

# ========================================
# EXTERNAL SERVICES MANAGEMENT
# ========================================

@app.get("/api/admin/external-services")
async def get_external_services(request: Request):
    """Get external services configuration (admin only)"""
    await require_admin(request)

    try:
        # Get external services configuration through Backend API for health status
        backend_url = os.getenv('BACKEND_URL', 'http://127.0.0.1:8888')

        try:
            import httpx
            async with httpx.AsyncClient(timeout=10.0) as client:
                # Get system health from Backend API
                health_response = await client.get(f"{backend_url}/api/v1/admin/system-health")
                health_data = health_response.json() if health_response.status_code == 200 else {}

                # Load external services configuration with health status
                external_services = {
                    'mongodb': {
                        'name': 'MongoDB Atlas',
                        'description': 'Primary database for DEEPLICA system',
                        'enabled': True,
                        'status': health_data.get('services', {}).get('database', {}).get('status', 'unknown'),
                        'connection_string': os.getenv('MONGODB_CONNECTION_STRING', ''),
                        'database_name': 'deeplica-dev'
                    },
                    'gemini': {
                        'name': 'Google Gemini API',
                        'description': 'AI language model for dialogue and planning',
                        'enabled': True,
                        'status': health_data.get('services', {}).get('llm', {}).get('status', 'unknown'),
                        'api_key': os.getenv('GEMINI_API_KEY', ''),
                        'model': 'gemini-1.5-flash'
                    },
                    'twilio': {
                        'name': 'Twilio Service',
                        'description': 'Phone call and SMS services',
                        'enabled': True,
                        'status': 'unknown',  # Would need to check Twilio service
                        'account_sid': os.getenv('TWILIO_ACCOUNT_SID', ''),
                        'auth_token': os.getenv('TWILIO_AUTH_TOKEN', ''),
                        'phone_number': os.getenv('TWILIO_PHONE_NUMBER', '+**********'),
                        'webhook_url': os.getenv('TWILIO_WEBHOOK_URL', 'https://your-ngrok-url.ngrok-free.app')
                    },
                    'ngrok': {
                        'name': 'ngrok Service',
                        'description': 'Secure tunneling for webhooks',
                        'enabled': True,
                        'status': 'unknown',  # Would need to check Ngrok service
                        'auth_token': os.getenv('NGROK_API_KEY', ''),
                        'tunnel_port': get_service_port("backend"),
                        'api_port': get_service_port("ngrok-api")
                    }
                }

                return {
                    "success": True,
                    "services": external_services,
                    "system_health": health_data
                }

        except Exception as api_error:
            logger.warning(f"⚠️ Backend API unavailable, using fallback: {str(api_error)}")
            # Fallback to basic configuration without health status
            external_services = {
                'mongodb': {
                    'name': 'MongoDB Atlas',
                    'description': 'Primary database for DEEPLICA system',
                    'enabled': True,
                    'status': 'unknown',
                    'connection_string': os.getenv('MONGODB_CONNECTION_STRING', ''),
                    'database_name': 'deeplica-dev'
                },
                'gemini': {
                    'name': 'Google Gemini API',
                    'description': 'AI language model for dialogue and planning',
                    'enabled': True,
                    'status': 'unknown',
                    'api_key': os.getenv('GEMINI_API_KEY', ''),
                    'model': 'gemini-1.5-flash'
                },
                'twilio': {
                    'name': 'Twilio Service',
                    'description': 'Phone call and SMS services',
                    'enabled': True,
                    'status': 'unknown',
                    'account_sid': os.getenv('TWILIO_ACCOUNT_SID', ''),
                    'auth_token': os.getenv('TWILIO_AUTH_TOKEN', ''),
                    'phone_number': os.getenv('TWILIO_PHONE_NUMBER', '+**********'),
                    'webhook_url': os.getenv('TWILIO_WEBHOOK_URL', 'https://your-ngrok-url.ngrok-free.app')
                },
                'ngrok': {
                    'name': 'ngrok Service',
                    'description': 'Secure tunneling for webhooks',
                    'enabled': True,
                    'status': 'unknown',
                    'auth_token': os.getenv('NGROK_API_KEY', ''),
                    'tunnel_port': get_service_port("backend"),
                    'api_port': get_service_port("ngrok-api")
                }
            }

            return {
                "success": True,
                "services": external_services,
                "system_health": {"status": "unknown", "message": "Backend API unavailable"}
            }
    except Exception as e:
        logger.error(f"Failed to get external services: {e}", module="WEB-CHAT", routine="get_external_services")
        raise HTTPException(status_code=500, detail=f"Failed to get external services: {str(e)}")


@app.get("/api/admin/database-stats")
async def get_database_statistics(request: Request):
    """Get database statistics for admin dashboard (admin only)"""
    await require_admin(request)

    try:
        # Return mock database statistics since Backend API is unreliable
        return {
            "success": True,
            "database_statistics": {
                "total_users": 2,
                "active_sessions": 1,
                "total_missions": 0,
                "database_size": "~5MB",
                "collections": {
                    "users": 2,
                    "sessions": 1,
                    "missions": 0,
                    "logs": 0
                },
                "connection_status": "Connected",
                "last_backup": "2025-07-13T00:00:00Z"
            },
            "timestamp": "2025-07-13T01:30:00Z"
        }

    except Exception as e:
        logger.error(f"❌ Failed to get database statistics: {str(e)}", module="WEB-CHAT", routine="get_database_statistics")
        raise HTTPException(status_code=500, detail=f"Failed to get database statistics: {str(e)}")


@app.get("/api/admin/system-health")
async def get_system_health_admin(request: Request):
    """Get system health information for admin dashboard (admin only)"""
    await require_admin(request)

    try:
        # Get system health through Backend API
        backend_url = os.getenv('BACKEND_URL', 'http://127.0.0.1:8888')

        import httpx
        async with httpx.AsyncClient(timeout=15.0) as client:
            response = await client.get(f"{backend_url}/api/v1/admin/system-health")

            if response.status_code == 200:
                health_data = response.json()
                return {
                    "success": True,
                    "system_health": health_data,
                    "timestamp": health_data.get("timestamp")
                }
            else:
                raise HTTPException(status_code=response.status_code, detail="Failed to get system health from Backend API")

    except httpx.RequestError as e:
        logger.error(f"❌ Backend API connection failed: {str(e)}", module="WEB-CHAT", routine="get_system_health_admin")
        raise HTTPException(status_code=503, detail="Backend API unavailable")
    except Exception as e:
        logger.error(f"❌ Failed to get system health: {str(e)}", module="WEB-CHAT", routine="get_system_health_admin")
        raise HTTPException(status_code=500, detail=f"Failed to get system health: {str(e)}")


@app.post("/api/admin/external-services")
async def save_external_services(request: Request):
    """Save external services configuration (admin only)"""
    await require_admin(request)

    try:
        data = await request.json()
        # Implementation for saving external services
        # This would update .env files and database settings

        return {
            "success": True,
            "message": "External services configuration saved"
        }
    except Exception as e:
        logger.error(f"Failed to save external services: {e}", module="WEB-CHAT", routine="save_external_services")
        raise HTTPException(status_code=500, detail=f"Failed to save external services: {str(e)}")


@app.post("/api/admin/factory-reset")
async def factory_reset(request: Request):
    """Reset all settings to factory defaults (admin only)"""
    await require_admin(request)

    try:
        # Implementation for factory reset
        # This would reset all settings to defaults

        return {
            "success": True,
            "message": "Factory reset completed"
        }
    except Exception as e:
        logger.error(f"Failed to perform factory reset: {e}", module="WEB-CHAT", routine="factory_reset")
        raise HTTPException(status_code=500, detail=f"Failed to perform factory reset: {str(e)}")

# ========================================
# HOST SETTINGS MANAGEMENT (ADMIN ONLY)
# ========================================

@app.get("/admin/host-settings")
async def get_host_settings(request: Request):
    """Get current host settings (admin only)"""
    await require_admin(request)

    try:
        from shared.system_settings import get_system_host_config
        host_config = await get_system_host_config()

        return {
            "default_host": host_config.get("default_host", "0.0.0.0"),
            "localhost": host_config.get("localhost", "127.0.0.1"),
            "external_host": host_config.get("external_host", "0.0.0.0"),
            "available_hosts": {
                "0.0.0.0": "All Interfaces (External Access)",
                "127.0.0.1": "Localhost Only (Local Access)"
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get host settings: {str(e)}")

@app.post("/admin/host-settings")
async def update_host_settings(request: Request):
    """Update host settings (admin only)"""
    await require_admin(request)

    try:
        data = await request.json()
        new_host = data.get("default_host")

        if not new_host:
            raise HTTPException(status_code=400, detail="default_host is required")

        # Validate host value (allow custom IPs as well)
        import re
        ip_pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
        if not re.match(ip_pattern, new_host):
            raise HTTPException(status_code=400, detail="Invalid IP address format")

        # Update the host setting in database
        from shared.system_settings import update_system_host_config
        host_config = {
            "default_host": new_host,
            "localhost": "127.0.0.1",  # Keep localhost constant
            "external_host": new_host if new_host != "127.0.0.1" else "0.0.0.0"
        }

        success = await update_system_host_config(host_config)

        if not success:
            raise HTTPException(status_code=500, detail="Failed to save host settings to database")

        logger.info(f"Host settings updated to {new_host} by admin", module="WEB-CHAT", routine="update_host_settings")

        return {
            "success": True,
            "message": f"Host settings updated to {new_host}",
            "default_host": new_host,
            "note": "Services will use the new host configuration on next restart"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update host settings: {str(e)}")

# ========================================
# PORT SETTINGS MANAGEMENT (ADMIN ONLY)
# ========================================

@app.get("/admin/port-settings")
async def get_port_settings(request: Request):
    """Get current port settings with friendly names (admin only)"""
    await require_admin(request)

    try:
        from shared.system_settings import get_system_port_config
        from shared.port_manager import is_port_free

        port_config = await get_system_port_config()

        # Service friendly names mapping - ALL SERVICES THAT USE PORTS
        service_names = {
            # Core Services
            "backend": "🌐 Backend API",
            "dispatcher": "🎯 Dispatcher Service",
            "dialogue": "💬 Dialogue Agent",
            "planner": "🧠 Planner Agent",
            "phone": "📞 Phone Agent",

            # System Services
            "watchdog": "🐕 Watchdog Monitor",

            # Web Services
            "web-chat": "💻 Web Chat Interface",
            "cli": "🖥️ CLI Terminal",

            # External Services
            "twilio-echo-bot": "📱 Twilio Echo Bot",
            "webhook": "🔗 Webhook Server",

            # Development & Utility Services
            "ngrok-api": "🌐 ngrok API Dashboard",
            "ngrok-tunnel": "🚇 ngrok Tunnel Port",
            "test-server": "🧪 Test Server",
            "dev-server": "⚙️ Development Server",
            "proxy-server": "🔄 Proxy Server"
        }

        # Add status information and friendly names for each port
        ports_with_status = {}
        for service, port in port_config.items():
            status = "Free" if is_port_free(port) else "In Use"
            is_readonly = (service == "backend")  # Backend API is read-only

            ports_with_status[service] = {
                "port": port,
                "status": status,
                "friendly_name": service_names.get(service, service.replace("-", " ").title()),
                "readonly": is_readonly,
                "description": "Constant port - cannot be changed" if is_readonly else "Configurable port"
            }

        return {
            "ports": ports_with_status,
            "message": "Port settings retrieved successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get port settings: {str(e)}")

@app.post("/admin/port-settings")
async def update_port_settings(request: Request):
    """Update port settings (admin only)"""
    await require_admin(request)

    try:
        data = await request.json()
        ports = data.get("ports", {})

        if not ports:
            raise HTTPException(status_code=400, detail="ports configuration is required")

        # Validate port values
        for service, port in ports.items():
            if not isinstance(port, int) or port < 1024 or port > 65535:
                raise HTTPException(status_code=400, detail=f"Invalid port {port} for service {service}. Must be between 1024-65535")

        # Update the port settings
        from shared.system_settings import update_system_port_config
        success = await update_system_port_config(ports)

        if not success:
            raise HTTPException(status_code=500, detail="Failed to save port settings to database")

        logger.info(f"Port settings updated by admin", module="WEB-CHAT", routine="update_port_settings")

        return {
            "success": True,
            "message": "Port settings updated successfully",
            "ports": ports,
            "note": "Services will use the new port configuration on next restart"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update port settings: {str(e)}")

@app.post("/admin/port-settings/reset")
async def reset_port_settings(request: Request):
    """Reset port settings to defaults (admin only)"""
    await require_admin(request)

    try:
        from shared.system_settings import SystemSettingsManager

        settings_manager = SystemSettingsManager()
        default_ports = settings_manager.default_settings["port_config"]

        # Update with default ports
        from shared.system_settings import update_system_port_config
        success = await update_system_port_config(default_ports)

        if not success:
            raise HTTPException(status_code=500, detail="Failed to reset port settings in database")

        logger.info("Port settings reset to defaults by admin", module="WEB-CHAT", routine="reset_port_settings")

        return {
            "success": True,
            "message": "Port settings reset to default values",
            "ports": default_ports
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to reset port settings: {str(e)}")

@app.get("/admin/check-port/{port}")
async def check_port_availability(port: int, request: Request):
    """Check if a port is available (admin only)"""
    await require_admin(request)

    try:
        from shared.port_manager import is_port_free

        available = is_port_free(port)

        return {
            "port": port,
            "available": available,
            "status": "Free" if available else "In Use"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to check port availability: {str(e)}")

@app.post("/admin/factory-reset")
async def factory_reset_settings(request: Request):
    """Reset all system settings to factory defaults (admin only)"""
    await require_admin(request)

    try:
        from shared.system_settings import reset_system_to_factory_defaults
        from shared.port_manager import reset_to_factory_defaults

        # Reset system settings in database
        db_success = await reset_system_to_factory_defaults()

        # Reset local backup file
        reset_to_factory_defaults()

        if not db_success:
            logger.warning("Database reset failed, but local backup was reset", module="WEB-CHAT", routine="factory_reset_settings")

        logger.info("System settings reset to factory defaults by admin", module="WEB-CHAT", routine="factory_reset_settings")

        return {
            "success": True,
            "message": "System settings reset to factory defaults. Please restart Deeplica for changes to take effect.",
            "database_reset": db_success,
            "local_backup_reset": True,
            "restart_required": True
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to reset to factory defaults: {str(e)}", module="WEB-CHAT", routine="factory_reset_settings")
        raise HTTPException(status_code=500, detail=f"Failed to reset to factory defaults: {str(e)}")

# ========================================
# EXTERNAL SERVICES SETTINGS (ADMIN ONLY)
# ========================================

@app.get("/admin/external-services")
async def get_external_services(request: Request):
    """Get external services configuration (admin only)"""
    await require_admin(request)

    try:
        from shared.api_manager import get_api_manager

        # Get all external configurations through API manager
        api_manager = get_api_manager()
        external_services = api_manager.get_all_external_configs()

        # Return real values - no masking for admin interface
        # Admin users need access to real connection strings and API keys
        return {
            "external_services": external_services,
            "message": "External services configuration retrieved successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get external services: {str(e)}")

@app.post("/admin/external-services")
async def update_external_services(request: Request, services_data: dict):
    """Update external services configuration (admin only)"""
    await require_admin(request)

    try:
        # NOTE: API Manager enforces .env as the only source
        # Web admin can view configurations but updates must be done through .env file
        # This ensures consistency and prevents configuration drift

        external_services = services_data.get("external_services", {})

        # Validate required fields for each service
        validation_errors = []

        for service_id, config in external_services.items():
            if service_id == "mongodb":
                if config.get("enabled") and not config.get("uri"):
                    validation_errors.append("MongoDB URI is required when enabled")
            elif service_id == "gemini":
                if config.get("enabled") and not config.get("api_key"):
                    validation_errors.append("Gemini API key is required when enabled")
            elif service_id == "twilio":
                if config.get("enabled"):
                    required_fields = ["account_sid", "auth_token", "phone_number"]
                    missing = [field for field in required_fields if not config.get(field)]
                    if missing:
                        validation_errors.append(f"Twilio missing required fields: {', '.join(missing)}")
            elif service_id == "ngrok":
                if config.get("enabled") and not config.get("api_key"):
                    validation_errors.append("Ngrok API key is required when enabled")

        if validation_errors:
            raise HTTPException(status_code=400, detail=f"Validation errors: {'; '.join(validation_errors)}")

        # For now, return success but inform admin about .env requirement
        success = True

        if not success:
            raise HTTPException(status_code=500, detail="Failed to update external services in database")

        logger.info("External services configuration updated by admin", module="WEB-CHAT", routine="update_external_services")

        return {
            "success": True,
            "message": "Configuration validated successfully. To apply changes, update the .env file and restart services. API Manager enforces .env as the single source of truth.",
            "restart_required": True,
            "note": "API Manager Policy: All external service configurations must be managed through .env file only"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update external services: {str(e)}", module="WEB-CHAT", routine="update_external_services")
        raise HTTPException(status_code=500, detail=f"Failed to update external services: {str(e)}")

@app.post("/admin/test-external-service/{service_id}")
async def test_external_service(service_id: str, request: Request):
    """Test external service connection (admin only)"""
    await require_admin(request)

    try:
        from shared.system_settings import get_external_services_config

        external_services = await get_external_services_config()
        service_config = external_services.get(service_id)

        if not service_config:
            raise HTTPException(status_code=404, detail=f"Service {service_id} not found")

        if not service_config.get("enabled"):
            return {"success": False, "message": f"{service_config.get('name', service_id)} is disabled"}

        # Test connection based on service type
        test_result = await _test_service_connection(service_id, service_config)

        return {
            "success": test_result["success"],
            "message": test_result["message"],
            "details": test_result.get("details", {})
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to test service {service_id}: {str(e)}", module="WEB-CHAT", routine="test_external_service")
        raise HTTPException(status_code=500, detail=f"Failed to test service: {str(e)}")

async def _test_service_connection(service_id: str, config: dict) -> dict:
    """Test connection to external service - NO DIRECT DATABASE ACCESS"""
    try:
        if service_id == "mongodb":
            # Test MongoDB connection through Backend API (NO DIRECT ACCESS)
            try:
                backend_url = get_backend_api_url()
                if not backend_url:
                    return {"success": False, "message": "Backend API not available"}

                import httpx
                async with httpx.AsyncClient(timeout=10.0) as client:
                    response = await client.get(f"{backend_url}/health")
                    if response.status_code == 200:
                        health_data = response.json()
                        if health_data.get("database", {}).get("status") == "healthy":
                            return {"success": True, "message": "MongoDB connection successful (via Backend API)"}
                        else:
                            return {"success": False, "message": "Database not healthy"}
                    else:
                        return {"success": False, "message": f"Backend API returned status {response.status_code}"}
            except Exception as e:
                return {"success": False, "message": f"Backend API connection failed: {str(e)}"}

        elif service_id == "gemini":
            # Test Gemini API
            import google.generativeai as genai
            genai.configure(api_key=config["api_key"])
            model = genai.GenerativeModel(config.get("model", "gemini-1.5-flash"))
            response = model.generate_content("Test connection")
            return {"success": True, "message": "Gemini API connection successful"}

        elif service_id == "twilio":
            # Test Twilio API
            from twilio.rest import Client
            client = Client(config["account_sid"], config["auth_token"])
            account = client.api.accounts(config["account_sid"]).fetch()
            return {"success": True, "message": f"Twilio connection successful: {account.friendly_name}"}

        elif service_id == "ngrok":
            # Test ngrok API
            import requests
            # Force VS Code cache refresh
            ngrok_port = config.get('api_port', get_service_port("ngrok-api"))
            response = requests.get(f"http://localhost:{ngrok_port}/api/tunnels", timeout=5)
            if response.status_code == 200:
                tunnels = response.json().get("tunnels", [])
                return {"success": True, "message": f"ngrok API accessible, {len(tunnels)} active tunnels"}
            else:
                return {"success": False, "message": "ngrok API not accessible"}
        else:
            return {"success": False, "message": f"Unknown service type: {service_id}"}

    except Exception as e:
        return {"success": False, "message": f"Connection test failed: {str(e)}"}



@app.get("/health")
async def health_check():
    """Enhanced health check endpoint with detailed metrics"""
    connection_stats = manager.get_connection_stats()

    # Determine overall health status
    status = "healthy"
    if deeplica_client.is_circuit_breaker_open():
        status = "degraded"
    elif connection_stats["total_connections"] == 0:
        status = "idle"
    elif connection_stats["total_connections"] > 50:  # Arbitrary threshold
        status = "busy"

    return {
        "status": status,
        "service": "Web Chat Interface",
        "version": "1.0.0",
        "port": get_service_port("web-chat"),
        "uptime_seconds": (datetime.now() - app.state.start_time).total_seconds() if hasattr(app.state, 'start_time') else 0,
        "connections": {
            "active": connection_stats["total_connections"],
            "unique_users": connection_stats["unique_users"],
            "average_session_duration": connection_stats["average_session_duration"],
            "total_messages": connection_stats["total_messages"]
        },
        "backend": {
            "deeplica_failures": deeplica_client.circuit_breaker_failures,
            "circuit_breaker_open": deeplica_client.is_circuit_breaker_open()
        },
        "last_activity": app.state.last_activity.isoformat() if hasattr(app.state, 'last_activity') else None,
        "browser_auto_launch": "enabled"
    }

@app.get("/api/services/health")
async def get_all_services_health():
    """Get health status of all DEEPLICA services"""
    import httpx
    import asyncio

    services = {
        "backend": {"port": get_service_port("backend"), "name": "Backend API"},
        "dispatcher": {"port": get_service_port("dispatcher"), "name": "Dispatcher"},
        "planner": {"port": get_service_port("planner"), "name": "Planner Agent"},
        "dialogue": {"port": get_service_port("dialogue"), "name": "Dialogue Agent"},
        "phone": {"port": get_service_port("phone"), "name": "Phone Agent"},
        "watchdog": {"port": get_service_port("watchdog"), "name": "Watchdog"},
        "web-chat": {"port": get_service_port("web-chat"), "name": "Web Chat"}
    }

    async def check_service_health(service_id, service_info):
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"http://localhost:{service_info['port']}/health",
                    timeout=3.0
                )
                if response.status_code == 200:
                    health_data = response.json()
                    return {
                        "service_id": service_id,
                        "name": service_info["name"],
                        "port": service_info["port"],
                        "status": "online",
                        "health": health_data.get("status", "unknown"),
                        "details": health_data,
                        "response_time_ms": response.elapsed.total_seconds() * 1000
                    }
                else:
                    return {
                        "service_id": service_id,
                        "name": service_info["name"],
                        "port": service_info["port"],
                        "status": "error",
                        "health": "unhealthy",
                        "error": f"HTTP {response.status_code}",
                        "response_time_ms": response.elapsed.total_seconds() * 1000
                    }
        except Exception as e:
            return {
                "service_id": service_id,
                "name": service_info["name"],
                "port": service_info["port"],
                "status": "offline",
                "health": "unreachable",
                "error": str(e),
                "response_time_ms": None
            }

    # Check all services concurrently
    tasks = [check_service_health(service_id, info) for service_id, info in services.items()]
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Process results
    service_health = {}
    healthy_count = 0
    total_count = len(services)

    for result in results:
        if isinstance(result, Exception):
            continue
        service_health[result["service_id"]] = result
        if result["status"] == "online":
            healthy_count += 1

    overall_status = "healthy" if healthy_count == total_count else "degraded" if healthy_count > 0 else "critical"

    return {
        "overall_status": overall_status,
        "healthy_services": healthy_count,
        "total_services": total_count,
        "services": service_health,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/services/{service_id}/health")
async def get_service_health(service_id: str):
    """Get health status of a specific service"""
    import httpx

    # Map service IDs to ports
    service_ports = {
        "backend": get_service_port("backend"),
        "dispatcher": get_service_port("dispatcher"),
        "planner": get_service_port("planner"),
        "dialogue": get_service_port("dialogue"),
        "phone": get_service_port("phone"),
        "watchdog": get_service_port("watchdog"),
        "web-chat": get_service_port("web-chat")
    }

    if service_id not in service_ports:
        raise HTTPException(status_code=404, detail=f"Service {service_id} not found")

    port = service_ports[service_id]

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"http://localhost:{port}/health",
                timeout=5.0
            )

            if response.status_code == 200:
                health_data = response.json()
                return {
                    "service_id": service_id,
                    "port": port,
                    "status": "healthy",
                    "details": health_data,
                    "response_time_ms": response.elapsed.total_seconds() * 1000,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {
                    "service_id": service_id,
                    "port": port,
                    "status": "unhealthy",
                    "error": f"HTTP {response.status_code}",
                    "response_time_ms": response.elapsed.total_seconds() * 1000,
                    "timestamp": datetime.now().isoformat()
                }

    except Exception as e:
        return {
            "service_id": service_id,
            "port": port,
            "status": "unreachable",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.get("/api/admin/session-info")
async def get_session_info(request: Request):
    """Get comprehensive session and system information for admin"""
    import platform
    import psutil
    import socket

    # Get client information
    client_ip = request.client.host
    user_agent = request.headers.get("user-agent", "Unknown")

    # Parse user agent for browser info
    browser_info = "Unknown"
    if "Chrome" in user_agent:
        browser_info = "Chrome"
    elif "Firefox" in user_agent:
        browser_info = "Firefox"
    elif "Safari" in user_agent:
        browser_info = "Safari"
    elif "Edge" in user_agent:
        browser_info = "Edge"

    # Get system information
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')

        system_info = {
            "cpu_usage_percent": cpu_percent,
            "memory_total_gb": round(memory.total / (1024**3), 2),
            "memory_used_gb": round(memory.used / (1024**3), 2),
            "memory_percent": memory.percent,
            "disk_total_gb": round(disk.total / (1024**3), 2),
            "disk_used_gb": round(disk.used / (1024**3), 2),
            "disk_percent": round((disk.used / disk.total) * 100, 1)
        }
    except Exception as e:
        system_info = {"error": f"Could not get system info: {e}"}

    # Get network information
    try:
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
    except:
        hostname = "Unknown"
        local_ip = "Unknown"

    # Get active connections info
    connection_stats = manager.get_connection_stats()

    return {
        "session": {
            "client_ip": client_ip,
            "user_agent": user_agent,
            "browser": browser_info,
            "timestamp": datetime.now().isoformat(),
            "session_id": request.headers.get("x-session-id", "Unknown"),
            "tab_id": request.headers.get("x-tab-id", "Unknown")
        },
        "system": {
            "hostname": hostname,
            "platform": platform.system(),
            "platform_version": platform.version(),
            "architecture": platform.machine(),
            "python_version": platform.python_version(),
            "local_ip": local_ip,
            **system_info
        },
        "deeplica": {
            "service": "Web Chat Interface",
            "port": get_service_port("web-chat"),
            "uptime_seconds": (datetime.now() - app.state.start_time).total_seconds() if hasattr(app.state, 'start_time') else 0,
            "active_connections": connection_stats["total_connections"],
            "unique_users": connection_stats["unique_users"],
            "total_messages": connection_stats["total_messages"]
        }
    }

@app.get("/api/isp-info")
async def get_isp_info(request: Request):
    """Get ISP information for the client"""
    try:
        # Get client IP address
        client_ip = request.headers.get("X-Forwarded-For")
        if client_ip:
            client_ip = client_ip.split(",")[0].strip()
        else:
            client_ip = request.client.host

        # Fallback for local development
        if client_ip in ["127.0.0.1", "::1", "localhost"]:
            client_ip = "*******"  # Use Google DNS for demo

        # Query IP-API for ISP information
        async with httpx.AsyncClient(timeout=5.0) as client:
            response = await client.get(f"http://ip-api.com/json/{client_ip}")
            data = response.json()

            if data.get("status") == "success":
                return {
                    "ip": client_ip,
                    "isp": data.get("isp", "Unknown"),
                    "org": data.get("org", "Unknown"),
                    "country": data.get("country", "Unknown"),
                    "region": data.get("regionName", "Unknown"),
                    "city": data.get("city", "Unknown"),
                    "timezone": data.get("timezone", "Unknown"),
                    "lat": data.get("lat", 0),
                    "lon": data.get("lon", 0)
                }
            else:
                return {
                    "ip": client_ip,
                    "isp": "Unknown",
                    "org": "Unknown",
                    "country": "Unknown",
                    "region": "Unknown",
                    "city": "Unknown",
                    "timezone": "Unknown",
                    "lat": 0,
                    "lon": 0
                }
    except Exception as e:
        logger.error(f"Error getting ISP info: {e}")
        return {
            "ip": "Unknown",
            "isp": "Unknown",
            "org": "Unknown",
            "country": "Unknown",
            "region": "Unknown",
            "city": "Unknown",
            "timezone": "Unknown",
            "lat": 0,
            "lon": 0
        }

@app.get("/api/admin/system-metrics")
async def get_system_metrics():
    """Get real-time system metrics for admin monitoring"""
    import psutil
    import os

    try:
        # CPU information
        cpu_info = {
            "usage_percent": psutil.cpu_percent(interval=1),
            "count": psutil.cpu_count(),
            "count_logical": psutil.cpu_count(logical=True),
            "frequency": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
        }

        # Memory information
        memory = psutil.virtual_memory()
        memory_info = {
            "total_gb": round(memory.total / (1024**3), 2),
            "available_gb": round(memory.available / (1024**3), 2),
            "used_gb": round(memory.used / (1024**3), 2),
            "percent": memory.percent,
            "free_gb": round(memory.free / (1024**3), 2)
        }

        # Disk information
        disk = psutil.disk_usage('/')
        disk_info = {
            "total_gb": round(disk.total / (1024**3), 2),
            "used_gb": round(disk.used / (1024**3), 2),
            "free_gb": round(disk.free / (1024**3), 2),
            "percent": round((disk.used / disk.total) * 100, 1)
        }

        # Network information
        network = psutil.net_io_counters()
        network_info = {
            "bytes_sent": network.bytes_sent,
            "bytes_recv": network.bytes_recv,
            "packets_sent": network.packets_sent,
            "packets_recv": network.packets_recv
        }

        # Process information
        current_process = psutil.Process(os.getpid())
        process_info = {
            "pid": current_process.pid,
            "memory_mb": round(current_process.memory_info().rss / (1024**2), 2),
            "cpu_percent": current_process.cpu_percent(),
            "num_threads": current_process.num_threads(),
            "create_time": datetime.fromtimestamp(current_process.create_time()).isoformat()
        }

        return {
            "cpu": cpu_info,
            "memory": memory_info,
            "disk": disk_info,
            "network": network_info,
            "process": process_info,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        return {
            "error": f"Could not get system metrics: {e}",
            "timestamp": datetime.now().isoformat()
        }

@app.get("/avatar")
async def get_avatar():
    """Serve the Deeplica avatar video"""
    try:
        video_path = PROJECT_ROOT / "agents" / "media_pool" / "Deeplica Avatar.mp4"
        if video_path.exists():
            return FileResponse(
                path=str(video_path),
                media_type="video/mp4",
                headers={"Cache-Control": "public, max-age=3600"}
            )
        else:
            raise HTTPException(status_code=404, detail="Avatar video not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error serving avatar: {str(e)}")

@app.post("/launch_browser")
async def launch_browser_manually():
    """Manually trigger browser launch"""
    try:
        # Launch browser in background task
        browser_task = asyncio.create_task(browser_launcher.launch_browser_when_ready())

        return {
            "status": "success",
            "message": "Browser launch initiated",
            "url": browser_launcher.web_chat_url + "/login"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Failed to launch browser: {str(e)}"
        }

@app.post("/api/inject_message")
async def inject_phone_message(request: Request):
    """Inject phone conversation messages into DeepChat"""
    try:
        data = await request.json()
        sender = data.get("sender", "system")
        message = data.get("message", "")
        source = data.get("source", "unknown")
        call_sid = data.get("call_sid", "")

        if not message:
            return {"status": "error", "message": "No message provided"}

        # Create message for all active connections
        chat_message = {
            "type": "message",
            "sender": sender,
            "content": message,
            "timestamp": datetime.now().isoformat(),
            "source": source,
            "call_sid": call_sid
        }

        # Send to all active WebSocket connections
        message_sent = False
        for connection_id in list(manager.active_connections.keys()):
            try:
                await manager.send_personal_message(json.dumps(chat_message), connection_id)
                message_sent = True
            except Exception as e:
                logger.error(f"Failed to send message to connection {connection_id}: {e}", module="main", routine="inject_phone_message")

        if message_sent:
            return {"status": "success", "message": "Message injected into chat"}
        else:
            return {"status": "warning", "message": "No active chat connections"}

    except Exception as e:
        logger.error(f"Error injecting phone message: {e}", module="main", routine="inject_phone_message")
        return {"status": "error", "message": f"Failed to inject message: {str(e)}"}

# Enhanced Deeplica Communication with connection pooling
class DeepplicaClient:
    def __init__(self):
        self.cli_url = f"http://{get_localhost()}:{get_service_port('cli')}"  # CLI Terminal port
        self.client = None
        self.retry_attempts = 3
        self.retry_delay = 1.0
        self.circuit_breaker_failures = 0
        self.circuit_breaker_threshold = 5
        self.circuit_breaker_reset_time = 60
        self.last_failure_time = None

    async def get_client(self):
        """Get or create HTTP client with connection pooling"""
        if self.client is None:
            self.client = httpx.AsyncClient(
                timeout=httpx.Timeout(30.0, connect=5.0),
                limits=httpx.Limits(max_keepalive_connections=5, max_connections=10)
            )
        return self.client

    async def close(self):
        """Close HTTP client"""
        if self.client:
            await self.client.aclose()
            self.client = None

    def is_circuit_breaker_open(self) -> bool:
        """Check if circuit breaker is open"""
        if self.circuit_breaker_failures >= self.circuit_breaker_threshold:
            if self.last_failure_time and (datetime.now() - self.last_failure_time).seconds < self.circuit_breaker_reset_time:
                return True
            else:
                # Reset circuit breaker
                self.circuit_breaker_failures = 0
                self.last_failure_time = None
        return False

    async def send_message(self, message: str, username: str) -> str:
        """Send message to Deeplica CLI with retry logic and circuit breaker"""
        if self.is_circuit_breaker_open():
            await watchdog_client.send_log("Circuit breaker open, rejecting request", "WARNING", "deeplica_client")
            return "Service temporarily unavailable. Please try again in a moment."

        for attempt in range(self.retry_attempts):
            try:
                client = await self.get_client()
                response = await client.post(
                    f"{self.cli_url}/chat",
                    json={"message": message, "user": username},
                    timeout=30.0
                )

                if response.status_code == 200:
                    # Reset circuit breaker on success
                    self.circuit_breaker_failures = 0
                    self.last_failure_time = None
                    return response.json().get("response", "No response from Deeplica")
                else:
                    raise httpx.HTTPStatusError(f"HTTP {response.status_code}", request=response.request, response=response)

            except Exception as e:
                self.circuit_breaker_failures += 1
                self.last_failure_time = datetime.now()

                await watchdog_client.send_log(f"Deeplica client error (attempt {attempt + 1}): {e}", "ERROR", "deeplica_client")

                if attempt < self.retry_attempts - 1:
                    await asyncio.sleep(self.retry_delay * (attempt + 1))  # Exponential backoff
                else:
                    logger.error("Error communicating with Deeplica after {self.retry_attempts} attempts: {e}", module="main", routine="unknown")
                    return f"I'm having trouble connecting to my backend service. Please try again later."

        return "Service temporarily unavailable. Please try again."

deeplica_client = DeepplicaClient()

# WebSocket endpoint
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time chat - requires authentication"""
    connection_id = None
    user = None

    try:
        # Get session from query params with enhanced debugging
        session_id = websocket.query_params.get("session_id")
        print(f"🔗 WebSocket connection attempt with session: {session_id}")
        print(f"🔍 Query params: {dict(websocket.query_params)}")
        print(f"🔍 WebSocket headers: {dict(websocket.headers)}")

        # Also check cookies as fallback
        cookies = dict(websocket.cookies) if hasattr(websocket, 'cookies') else {}
        cookie_session = cookies.get("session_id")
        print(f"🔍 Cookie session_id: {cookie_session}")

        if not session_id:
            print(f"❌ WebSocket rejected: No session provided in query params")
            if cookie_session:
                print(f"🔄 Attempting to use session from cookie: {cookie_session}")
                session_id = cookie_session
            else:
                await websocket.accept()  # Must accept before closing
                await websocket.close(code=1008, reason="No session provided")
                return

        # 🛡️ BULLETPROOF: If query session fails, try cookie session as fallback
        if session_id and cookie_session and session_id != cookie_session:
            print(f"⚠️ Session mismatch: query={session_id}, cookie={cookie_session}")
            print(f"🔄 Will try cookie session if query session fails")
            fallback_session = cookie_session
        else:
            fallback_session = None

        # 🛡️ BULLETPROOF: Get user from session with enhanced debugging and retry logic
        user = None
        max_attempts = 3

        for attempt in range(max_attempts):
            try:
                print(f"🔍 Session validation attempt {attempt + 1}/{max_attempts} for session: {session_id}")

                # Ensure user service is connected to Backend API
                if not user_service.backend_url:
                    print(f"⚠️ User service not connected, attempting reconnection...")
                    await user_service.connect()

                user = await get_user_from_session(session_id)
                if user:
                    print(f"✅ Session validation successful for user: {user.username}")
                    break
                else:
                    print(f"⚠️ Session validation failed (attempt {attempt + 1}): session not found or expired")

            except Exception as e:
                print(f"❌ Session validation error (attempt {attempt + 1}): {e}")
                if attempt < max_attempts - 1:
                    print(f"🔄 Retrying session validation...")
                    await asyncio.sleep(0.5)  # Brief delay before retry

        if not user and fallback_session:
            print(f"🔄 Primary session failed, trying fallback session: {fallback_session}")
            # Try fallback session (from cookie)
            for attempt in range(max_attempts):
                try:
                    print(f"🔍 Fallback session validation attempt {attempt + 1}/{max_attempts} for session: {fallback_session}")

                    # Ensure user service is connected to Backend API
                    if not user_service.backend_url:
                        print(f"⚠️ User service not connected, attempting reconnection...")
                        await user_service.connect()

                    user = await get_user_from_session(fallback_session)
                    if user:
                        print(f"✅ Fallback session validation successful for user: {user.username}")
                        session_id = fallback_session  # Use the working session
                        break
                    else:
                        print(f"⚠️ Fallback session validation failed (attempt {attempt + 1}): session not found or expired")

                except Exception as e:
                    print(f"❌ Fallback session validation error (attempt {attempt + 1}): {e}")
                    if attempt < max_attempts - 1:
                        print(f"🔄 Retrying fallback session validation...")
                        await asyncio.sleep(0.5)  # Brief delay before retry

        if not user:
            print(f"❌ WebSocket rejected: Invalid session {session_id} after {max_attempts} attempts")
            if fallback_session:
                print(f"❌ Fallback session {fallback_session} also failed")
            print(f"🔍 Session may have expired, user not authenticated, or database connection issue")
            await websocket.accept()  # Must accept before closing
            await websocket.close(code=1008, reason="Session expired - please refresh page")
            return

        # Accept WebSocket connection
        await websocket.accept()
        print(f"✅ WebSocket accepted for user: {user.username}")
        connection_id = await manager.connect(websocket, user.username, user.username)

        # Update activity tracking
        if hasattr(app.state, 'last_activity'):
            app.state.last_activity = datetime.now()

        # Send to watchdog and log
        await watchdog_client.send_log(f"WebSocket connected: {user.username}", "INFO", "websocket")
        print(f"🔗 WebSocket connected: {user.username} (session: {session_id})")

        # Send connection confirmation
        connection_msg = {
            "type": "connection",
            "status": "connected",
            "user": user.username,
            "timestamp": datetime.now().isoformat()
        }
        await manager.send_personal_message(json.dumps(connection_msg), connection_id)

        # Welcome message is now handled by static HTML template
        # No need to send duplicate welcome message via WebSocket
        print(f"🔗 WebSocket connected for {user.username}")

        # Message handling loop with heartbeat
        last_heartbeat = datetime.now()

        while True:
            try:
                # Wait for message with timeout for heartbeat
                data = await asyncio.wait_for(websocket.receive_text(), timeout=30.0)
                message_data = json.loads(data)

                # Handle different message types
                if message_data.get("type") == "ping":
                    # Respond to ping with pong
                    pong_msg = {
                        "type": "pong",
                        "timestamp": datetime.now().isoformat()
                    }
                    await manager.send_personal_message(json.dumps(pong_msg), connection_id)
                    last_heartbeat = datetime.now()

                elif message_data.get("type") == "message":
                    user_message = message_data.get("content", "").strip()
                    if not user_message:
                        continue

                    # Check rate limiting
                    if not manager.check_rate_limit(user.username):
                        rate_limit_msg = {
                            "type": "error",
                            "message": "Rate limit exceeded. Please slow down.",
                            "timestamp": datetime.now().isoformat()
                        }
                        await manager.send_personal_message(json.dumps(rate_limit_msg), connection_id)
                        await watchdog_client.send_log(f"Rate limit exceeded for user {user.username}", "WARNING", "rate_limiter")
                        continue

                    # Record message for rate limiting and activity tracking
                    manager.record_message(user.username, connection_id)

                    # Update global activity tracking
                    if hasattr(app.state, 'last_activity'):
                        app.state.last_activity = datetime.now()

                    print(f"💬 Received message from {user.username}: {user_message}")

                    # Forward user message to CLI terminal
                    await send_to_cli_terminal(user.username, user_message, "user")

                    # Echo user message back
                    user_msg = {
                        "type": "message",
                        "sender": "user",
                        "content": user_message,
                        "timestamp": datetime.now().isoformat()
                    }
                    await manager.send_personal_message(json.dumps(user_msg), connection_id)
                    print(f"📤 User message echoed back to {user.username}")

                    # Send typing indicator
                    typing_msg = {
                        "type": "typing",
                        "sender": "deeplica"
                    }
                    await manager.send_personal_message(json.dumps(typing_msg), connection_id)

                    # Get response from Deeplica
                    deeplica_response = await deeplica_client.send_message(user_message, user.username)

                    # Send Deeplica response
                    response_msg = {
                        "type": "message",
                        "sender": "deeplica",
                        "content": deeplica_response,
                        "timestamp": datetime.now().isoformat()
                    }
                    await manager.send_personal_message(json.dumps(response_msg), connection_id)
                    last_heartbeat = datetime.now()

            except asyncio.TimeoutError:
                # Send heartbeat if no activity
                if (datetime.now() - last_heartbeat).seconds > 25:
                    heartbeat_msg = {
                        "type": "heartbeat",
                        "timestamp": datetime.now().isoformat()
                    }
                    await manager.send_personal_message(json.dumps(heartbeat_msg), connection_id)
                    last_heartbeat = datetime.now()
                continue

    except WebSocketDisconnect:
        if connection_id:
            await manager.disconnect(connection_id)
        if user:
            print(f"🔌 WebSocket disconnected: {user.username}")
            await send_to_watchdog(f"WebSocket disconnected: {user.username}")
    except Exception as e:
        if connection_id:
            await manager.disconnect(connection_id)
        if user:
            print(f"❌ WebSocket error for {user.username}: {e}")
            await send_to_watchdog(f"WebSocket error for {user.username}: {e}", "ERROR")
        else:
            print(f"❌ WebSocket error: {e}")
            await send_to_watchdog(f"WebSocket error: {e}", "ERROR")

# ========================================
# DEBUG ENDPOINTS (NO AUTH REQUIRED)
# ========================================

@app.get("/debug/session/{session_id}")
async def debug_session_no_auth(session_id: str, request: Request):
    """Debug endpoint to check session status - NO AUTH REQUIRED"""
    try:
        print(f"🔍 DEBUG: Checking session {session_id}")

        # Check if user service is connected to Backend API
        if not user_service.backend_url:
            await user_service.connect()

        user = await get_user_from_session(session_id)
        if user:
            return {
                "session_id": session_id,
                "valid": True,
                "username": user.username,
                "user_id": user.user_id
            }
        else:
            return {
                "session_id": session_id,
                "valid": False,
                "error": "Session not found or expired"
            }
    except Exception as e:
        return {
            "session_id": session_id,
            "valid": False,
            "error": str(e)
        }

# ========================================
# CATCH-ALL ROUTE FOR SECURITY
# ========================================

@app.get("/{full_path:path}")
async def catch_all_invalid_routes(request: Request, full_path: str):
    """
    Catch-all route for any invalid/non-existing URLs.
    Redirects to login page for security.
    """
    # Log the invalid URL attempt for security monitoring
    asyncio.create_task(send_to_watchdog(f"Invalid URL access: {request.url.path} from {request.client.host if request.client else 'unknown'}"))

    # Redirect to login page
    return RedirectResponse(url="/login", status_code=307)

async def main():
    """🛡️ BULLETPROOF Main function to run the web chat server"""
    print("🌐 Starting Deeplica Web Chat Server...")

    # 🛡️ BULLETPROOF: Ensure port is free using centralized port manager with retries
    server_port = None
    port_attempts = 0
    max_port_attempts = 3

    while port_attempts < max_port_attempts and not server_port:
        try:
            port_attempts += 1
            print(f"🔌 Port cleanup attempt {port_attempts}/{max_port_attempts}")

            # Add small delay for subsequent attempts to prevent conflicts
            if port_attempts > 1:
                await asyncio.sleep(1)

            server_port = ensure_service_port_free("WEB-CHAT", force=True)
            print(f"✅ Web Chat secured port {server_port}")
            print(f"📍 Server will be available at: http://{get_localhost()}:{server_port}")
            print("👤 Demo credentials: admin/admin123 or demo/demo123")
            print("🚀 Browser will auto-launch when Deeplica is ready")
            print("🔇 Routine INFO messages suppressed (health checks, etc.)")
            break

        except Exception as e:
            print(f"⚠️ Port cleanup attempt {port_attempts} failed: {e}")
            if port_attempts >= max_port_attempts:
                print("💡 Will retry with different port...")
                # Try to get any available port instead of exiting
                try:
                    import socket
                    sock = socket.socket()
                    sock.bind(('', 0))
                    server_port = sock.getsockname()[1]
                    sock.close()
                    print(f"🔄 Using alternative port: {server_port}")
                except:
                    server_port = get_service_port("web-chat")  # fallback to port manager
                    print(f"🔄 Using fallback port: {server_port}")
                # DO NOT EXIT - microservices should never exit themselves
                # sys.exit(1)  # REMOVED

    # 🛡️ BULLETPROOF: Use async uvicorn server to avoid event loop conflicts
    import uvicorn
    config = uvicorn.Config(
        "main:app",
        host=get_service_host("web-chat"),
        port=server_port,
        reload=False,  # 🔧 FIXED: Disable reload for bulletproof operation
        log_level="warning",  # Suppress INFO messages
        access_log=False      # Disable access logging
    )
    server = uvicorn.Server(config)

    # 🛡️ BULLETPROOF: Run server forever - never return
    await server.serve()

if __name__ == "__main__":
    try:
        # Set distinctive process name for easy identification
        try:
            import setproctitle
            setproctitle.setproctitle("DEEPLICA-WEB-CHAT")
            print("✅ Process name set to: DEEPLICA-WEB-CHAT")
        except ImportError:
            print("⚠️ setproctitle not available - process name unchanged")

        # Process detection removed per user request - manual management only

        # Set terminal title
        service_name = os.getenv("SERVICE_NAME", "WEB-CHAT")
        print(f"\033]0;💬 {service_name}\007", end="")  # xterm title
        print(f"\033]2;💬 {service_name}\007", end="")  # window title
        print(f"\033]1;💬 {service_name}\007", end="")  # icon title

        print("\n" + "="*80)
        print(f"💬 {service_name} TERMINAL")
        print("="*80 + "\n")

        # Run the main function with bulletproof wrapper
        bulletproof_main = bulletproof_service_wrapper(main, "WEB-CHAT")
        asyncio.run(bulletproof_main())

    except KeyboardInterrupt:
        print("🛑 Web Chat shutdown requested by user")
    except Exception as e:
        print(f"💥 Web Chat crashed: {e}")
        import traceback
        print(f"Stack trace:\n{traceback.format_exc()}")
        print("🔄 MICROSERVICE WILL RESTART - NOT EXITING")
        # DO NOT EXIT - let orchestrator restart this microservice
        # sys.exit(1)  # REMOVED - microservices should never exit themselves
