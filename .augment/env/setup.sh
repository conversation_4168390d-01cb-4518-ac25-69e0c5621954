#!/bin/bash

# DEEPLICA Development Environment Setup - Final Version
set -e

echo "🚀 Setting up DEEPLICA Development Environment"
echo "=============================================="

# Update system packages
echo "📦 Updating system packages..."
sudo apt-get update -y

# Install Python 3 and pip if not already installed
echo "🐍 Installing Python 3 and pip..."
sudo apt-get install -y python3 python3-pip python3-venv python3-dev

# Upgrade pip
echo "📦 Upgrading pip..."
python3 -m pip install --upgrade pip

# Create virtual environment
echo "🔧 Creating virtual environment..."
python3 -m venv venv

# Activate virtual environment and add to profile
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Add virtual environment activation to profile
echo "# DEEPLICA virtual environment" >> $HOME/.profile
echo "source $(pwd)/venv/bin/activate" >> $HOME/.profile

# Install base requirements
echo "📦 Installing base requirements..."
pip install -r requirements.txt

# Install development requirements
echo "📦 Installing development requirements..."
pip install -r requirements-dev.txt

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file..."
    if [ -f .env.example ]; then
        cp .env.example .env
    else
        # Create basic .env file
        cat > .env << EOF
# DEEPLICA Environment Configuration
GEMINI_API_KEY=your_api_key_here
MONGODB_URL=mongodb://localhost:27017/deeplica
DEBUG=true
EOF
    fi
    echo "⚠️  Please edit .env file and set your GEMINI_API_KEY"
fi

# Install additional system dependencies that might be needed
echo "📦 Installing additional system dependencies..."
sudo apt-get install -y curl wget git

# Verify Python installation
echo "✅ Verifying Python installation..."
python3 --version
pip --version

echo ""
echo "🎉 Development Environment Setup Complete!"
echo "Virtual environment activated and added to ~/.profile"
echo "Ready to run tests!"