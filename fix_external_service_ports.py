#!/usr/bin/env python3
"""
🔧 Fix ALL External Service Ports
Make Ngrok, MongoDB, Webhook, Twilio ports ALL managed by port manager.
NO MORE CONSTANT PORTS - ALL are configurable.
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Dict, <PERSON><PERSON>

def fix_external_service_ports():
    """Fix all external service ports to use port manager"""
    
    print("🔧 Fixing ALL external service ports to use port manager...")
    
    # External service port replacements
    external_replacements = [
        # Ngrok API (4040)
        (r':4040/', ':${get_service_port("ngrok-api")}/'),
        (r'localhost:4040', 'localhost:${get_service_port("ngrok-api")}'),
        (r'http://localhost:4040', 'http://localhost:${get_service_port("ngrok-api")}'),
        (r'NGROK_API_PORT=4040', 'NGROK_API_PORT=${get_service_port("ngrok-api")}'),
        (r'port.*=.*4040', 'port = get_service_port("ngrok-api")'),
        
        # Ngrok Tunnel (8080)
        (r':8080/', ':${get_service_port("ngrok-tunnel")}/'),
        (r'localhost:8080', 'localhost:${get_service_port("ngrok-tunnel")}'),
        (r'http://localhost:8080', 'http://localhost:${get_service_port("ngrok-tunnel")}'),
        (r'NGROK_TUNNEL_PORT=8080', 'NGROK_TUNNEL_PORT=${get_service_port("ngrok-tunnel")}'),
        (r'ngrok http 8080', 'ngrok http ${get_service_port("ngrok-tunnel")}'),
        (r'NGROK_PORT.*8080', 'NGROK_PORT = get_service_port("ngrok-tunnel")'),
        
        # Twilio Echo Bot (8009)
        (r':8009/', ':${get_service_port("twilio-echo-bot")}/'),
        (r'localhost:8009', 'localhost:${get_service_port("twilio-echo-bot")}'),
        (r'http://localhost:8009', 'http://localhost:${get_service_port("twilio-echo-bot")}'),
        (r'TWILIO_ECHO_BOT_PORT=8009', 'TWILIO_ECHO_BOT_PORT=${get_service_port("twilio-echo-bot")}'),
        (r'port.*=.*8009', 'port = get_service_port("twilio-echo-bot")'),
        
        # Webhook Server (8010)
        (r':8010/', ':${get_service_port("webhook-server")}/'),
        (r'localhost:8010', 'localhost:${get_service_port("webhook-server")}'),
        (r'http://localhost:8010', 'http://localhost:${get_service_port("webhook-server")}'),
        (r'WEBHOOK_SERVER_PORT=8010', 'WEBHOOK_SERVER_PORT=${get_service_port("webhook-server")}'),
        (r'port.*=.*8010', 'port = get_service_port("webhook-server")'),
        
        # MongoDB references (add proxy port management)
        (r'mongodb://localhost:27017', 'mongodb://localhost:${get_service_port("mongodb-proxy")}'),
        (r'MONGODB_PORT=27017', 'MONGODB_PORT=${get_service_port("mongodb-proxy")}'),
        
        # Comments about constant ports
        (r'# CONSTANT.*4040', '# Ngrok API port - managed by port_manager.py'),
        (r'# CONSTANT.*8080', '# Ngrok tunnel port - managed by port_manager.py'),
        (r'# CONSTANT.*8009', '# Twilio port - managed by port_manager.py'),
        (r'# CONSTANT.*8010', '# Webhook port - managed by port_manager.py'),
    ]
    
    # Python-specific replacements (for .py files)
    python_replacements = [
        # Ngrok API
        (r'4040', 'get_service_port("ngrok-api")'),
        
        # Ngrok Tunnel  
        (r'8080', 'get_service_port("ngrok-tunnel")'),
        
        # Twilio Echo Bot
        (r'8009', 'get_service_port("twilio-echo-bot")'),
        
        # Webhook Server
        (r'8010', 'get_service_port("webhook-server")'),
    ]
    
    # Shell script replacements
    shell_replacements = [
        # Ngrok commands
        (r'ngrok http 8080', 'ngrok http $(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'ngrok-tunnel\'))")'),
        (r'ngrok http 4040', 'ngrok http $(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'ngrok-api\'))")'),
        
        # Port assignments in shell
        (r'PORT=8009', 'PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'twilio-echo-bot\'))")'),
        (r'PORT=8010', 'PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'webhook-server\'))")'),
        (r'PORT=4040', 'PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'ngrok-api\'))")'),
        (r'PORT=8080', 'PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port(\'ngrok-tunnel\'))")'),
    ]
    
    files_modified = 0
    
    # Process all files
    for root, dirs, files in os.walk('.'):
        # Skip hidden directories and __pycache__
        dirs[:] = [d for d in dirs if not d.startswith('.') and d != '__pycache__']
        
        for file in files:
            if file.endswith(('.py', '.sh', '.json', '.env')):
                file_path = Path(root) / file
                
                # Skip our own scripts
                if file_path.name.startswith('fix_') or file_path.name.startswith('update_'):
                    continue
                
                if fix_file_external_ports(file_path, external_replacements, python_replacements, shell_replacements):
                    files_modified += 1
    
    print(f"✅ Fixed external service ports in {files_modified} files")
    return files_modified

def fix_file_external_ports(file_path: Path, external_replacements: List[Tuple[str, str]], 
                           python_replacements: List[Tuple[str, str]], 
                           shell_replacements: List[Tuple[str, str]]) -> bool:
    """Fix external service ports in a single file"""
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        original_content = content
        modified = False
        
        # Check if file needs port manager import (for Python files)
        needs_import = False
        if file_path.suffix == '.py':
            # Check if file contains external service port references
            if any(port in content for port in ['4040', '8009', '8010', '8080']):
                if 'get_service_port' not in content:
                    needs_import = True
        
        # Add import if needed
        if needs_import and 'from shared.port_manager import' not in content:
            lines = content.split('\n')
            
            # Find the best place to add the import
            import_pos = 0
            for i, line in enumerate(lines):
                if line.startswith('import ') or line.startswith('from '):
                    import_pos = i + 1
                elif line.strip() == '' and import_pos > 0:
                    break
            
            if import_pos == 0:
                import_pos = 0
            
            lines.insert(import_pos, 'from shared.port_manager import get_service_port')
            content = '\n'.join(lines)
            modified = True
            print(f"✅ {file_path}: Added port manager import")
        
        # Apply appropriate replacements based on file type
        if file_path.suffix == '.py':
            # For Python files, use direct function calls
            for pattern, replacement in python_replacements:
                # Only replace if it's clearly a port number (not part of other numbers)
                if re.search(rf'\b{pattern}\b', content):
                    content = re.sub(rf'\b{pattern}\b', replacement, content)
                    modified = True
                    print(f"✅ {file_path}: Fixed Python pattern {pattern}")
        
        elif file_path.suffix == '.sh':
            # For shell scripts, use shell command substitution
            for pattern, replacement in shell_replacements:
                if re.search(pattern, content):
                    content = re.sub(pattern, replacement, content)
                    modified = True
                    print(f"✅ {file_path}: Fixed shell pattern {pattern}")
        
        # Apply general external replacements for all file types
        for pattern, replacement in external_replacements:
            if re.search(pattern, content):
                content = re.sub(pattern, replacement, content)
                modified = True
                print(f"✅ {file_path}: Fixed external pattern {pattern[:30]}...")
        
        # Write back if modified
        if modified:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
    
    except Exception as e:
        print(f"⚠️ Error processing {file_path}: {e}")
    
    return False

def update_ngrok_configuration():
    """Update ngrok configuration to use dynamic ports"""
    
    print("🔧 Updating ngrok configuration for dynamic ports...")
    
    # Update start_ngrok.py to be fully dynamic
    ngrok_file = Path("start_ngrok.py")
    if ngrok_file.exists():
        try:
            with open(ngrok_file, 'r') as f:
                content = f.read()
            
            # Replace the default port logic
            content = re.sub(
                r'port = os\.getenv\(\'NGROK_PORT\', str\(get_service_port\(\'ngrok-tunnel\'\)\)\)',
                'port = os.getenv(\'NGROK_PORT\', str(get_service_port(\'ngrok-tunnel\')))',
                content
            )
            
            # Add comment about dynamic port management
            if 'ALL PORTS MANAGED DYNAMICALLY' not in content:
                header = '''"""
🌐 Ngrok Tunnel Starter
ALL PORTS MANAGED DYNAMICALLY by shared/port_manager.py
Ngrok will use the port assigned by the port manager.
"""

'''
                content = header + content
            
            with open(ngrok_file, 'w') as f:
                f.write(content)
            
            print(f"✅ Updated {ngrok_file} for dynamic port management")
        
        except Exception as e:
            print(f"⚠️ Error updating {ngrok_file}: {e}")

if __name__ == "__main__":
    print("🔧 Starting external service port fixes...")
    files_modified = fix_external_service_ports()
    update_ngrok_configuration()
    print(f"✅ External service port fixes completed! Modified {files_modified} files")
    print("🔄 ALL external service ports are now managed by port_manager.py")
