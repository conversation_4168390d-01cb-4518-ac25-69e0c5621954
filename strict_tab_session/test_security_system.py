#!/usr/bin/env python3
"""
🧪 STRICT SINGLE-TAB SESSION ENFORCEMENT - Comprehensive Test Suite

This script provides comprehensive testing of the strict single-tab session
enforcement system with security validation and attack simulation.

Test Categories:
- Basic functionality tests
- Security violation tests
- Edge case handling tests
- Performance and load tests
- Browser compatibility tests
- Attack simulation tests

<AUTHOR> Security Team
@version 1.0.0
@date 2025-07-11
"""

import asyncio
import aiohttp
import json
import time
import uuid
import random
from datetime import datetime
from typing import Dict, List, Any

class SecurityTestSuite:
    """Comprehensive security test suite for strict tab session enforcement"""
    
    def __init__(self, base_url: str = "https://127.0.0.1:5000"):
        self.base_url = base_url
        self.test_results = []
        self.session = None
        
    async def run_all_tests(self):
        """Run all security tests"""
        print("🧪 Starting Strict Tab Session Security Test Suite")
        print("=" * 60)
        
        async with aiohttp.ClientSession(
            connector=aiohttp.TCPConnector(ssl=False)  # For testing only
        ) as session:
            self.session = session
            
            # Basic functionality tests
            await self.test_basic_functionality()
            
            # Security violation tests
            await self.test_security_violations()
            
            # Edge case tests
            await self.test_edge_cases()
            
            # Attack simulation tests
            await self.test_attack_simulations()
            
            # Performance tests
            await self.test_performance()
            
        # Generate test report
        self.generate_test_report()

    async def test_basic_functionality(self):
        """Test basic session functionality"""
        print("\n🔧 Testing Basic Functionality")
        print("-" * 40)
        
        # Test 1: Valid login with proper Tab ID
        await self.test_valid_login()
        
        # Test 2: Session validation
        await self.test_session_validation()
        
        # Test 3: Logout functionality
        await self.test_logout()

    async def test_valid_login(self):
        """Test valid login process"""
        test_name = "Valid Login with Tab ID"
        
        try:
            # Generate valid Tab ID
            tab_id = str(uuid.uuid4())
            
            # Get login page for CSRF token
            async with self.session.get(f"{self.base_url}/login") as response:
                if response.status != 200:
                    raise Exception(f"Failed to get login page: {response.status}")
                
                # Extract CSRF token (simplified for testing)
                csrf_token = "test_csrf_token"
            
            # Attempt login
            login_data = {
                'username': 'admin',
                'password': 'admin123',
                'csrf_token': csrf_token
            }
            
            headers = {
                'X-Tab-ID': tab_id,
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            async with self.session.post(
                f"{self.base_url}/login",
                data=login_data,
                headers=headers
            ) as response:
                result = await response.json()
                
                if response.status == 200 and result.get('success'):
                    self.record_test_result(test_name, True, "Login successful")
                    return result.get('session_token'), tab_id
                else:
                    self.record_test_result(test_name, False, f"Login failed: {result}")
                    return None, None
                    
        except Exception as e:
            self.record_test_result(test_name, False, f"Exception: {e}")
            return None, None

    async def test_session_validation(self):
        """Test session validation"""
        test_name = "Session Validation"
        
        try:
            session_token, tab_id = await self.test_valid_login()
            
            if not session_token or not tab_id:
                self.record_test_result(test_name, False, "No valid session to test")
                return
            
            headers = {
                'X-Tab-ID': tab_id,
                'X-Session-Token': session_token
            }
            
            async with self.session.post(
                f"{self.base_url}/api/validate-session",
                headers=headers
            ) as response:
                result = await response.json()
                
                if response.status == 200 and result.get('valid'):
                    self.record_test_result(test_name, True, "Session validation successful")
                else:
                    self.record_test_result(test_name, False, f"Validation failed: {result}")
                    
        except Exception as e:
            self.record_test_result(test_name, False, f"Exception: {e}")

    async def test_logout(self):
        """Test logout functionality"""
        test_name = "Logout Functionality"
        
        try:
            session_token, tab_id = await self.test_valid_login()
            
            if not session_token or not tab_id:
                self.record_test_result(test_name, False, "No valid session to test")
                return
            
            headers = {
                'X-Tab-ID': tab_id,
                'X-Session-Token': session_token
            }
            
            async with self.session.post(
                f"{self.base_url}/api/logout",
                headers=headers
            ) as response:
                result = await response.json()
                
                if response.status == 200 and result.get('success'):
                    self.record_test_result(test_name, True, "Logout successful")
                else:
                    self.record_test_result(test_name, False, f"Logout failed: {result}")
                    
        except Exception as e:
            self.record_test_result(test_name, False, f"Exception: {e}")

    async def test_security_violations(self):
        """Test security violation detection"""
        print("\n🚨 Testing Security Violations")
        print("-" * 40)
        
        # Test 1: Invalid Tab ID format
        await self.test_invalid_tab_id()
        
        # Test 2: Tab ID mismatch
        await self.test_tab_id_mismatch()
        
        # Test 3: Missing headers
        await self.test_missing_headers()
        
        # Test 4: Multi-tab simulation
        await self.test_multi_tab_simulation()

    async def test_invalid_tab_id(self):
        """Test invalid Tab ID rejection"""
        test_name = "Invalid Tab ID Rejection"
        
        try:
            invalid_tab_ids = [
                "invalid-tab-id",
                "12345",
                "not-a-uuid",
                "",
                "a" * 100
            ]
            
            for invalid_id in invalid_tab_ids:
                headers = {
                    'X-Tab-ID': invalid_id,
                    'X-Session-Token': 'dummy_token'
                }
                
                async with self.session.post(
                    f"{self.base_url}/api/validate-session",
                    headers=headers
                ) as response:
                    if response.status == 400 or response.status == 401:
                        # Expected rejection
                        continue
                    else:
                        self.record_test_result(
                            test_name, False, 
                            f"Invalid Tab ID '{invalid_id}' was accepted"
                        )
                        return
            
            self.record_test_result(test_name, True, "All invalid Tab IDs properly rejected")
            
        except Exception as e:
            self.record_test_result(test_name, False, f"Exception: {e}")

    async def test_tab_id_mismatch(self):
        """Test Tab ID mismatch detection"""
        test_name = "Tab ID Mismatch Detection"
        
        try:
            # Create valid session
            session_token, original_tab_id = await self.test_valid_login()
            
            if not session_token:
                self.record_test_result(test_name, False, "No valid session to test")
                return
            
            # Use different Tab ID
            different_tab_id = str(uuid.uuid4())
            
            headers = {
                'X-Tab-ID': different_tab_id,
                'X-Session-Token': session_token
            }
            
            async with self.session.post(
                f"{self.base_url}/api/validate-session",
                headers=headers
            ) as response:
                if response.status == 401:
                    self.record_test_result(test_name, True, "Tab ID mismatch properly detected")
                else:
                    self.record_test_result(test_name, False, "Tab ID mismatch not detected")
                    
        except Exception as e:
            self.record_test_result(test_name, False, f"Exception: {e}")

    async def test_missing_headers(self):
        """Test missing header rejection"""
        test_name = "Missing Headers Rejection"
        
        try:
            # Test missing Tab ID
            async with self.session.post(
                f"{self.base_url}/api/validate-session",
                headers={'X-Session-Token': 'dummy_token'}
            ) as response:
                if response.status != 400:
                    self.record_test_result(test_name, False, "Missing Tab ID not rejected")
                    return
            
            # Test missing Session Token
            async with self.session.post(
                f"{self.base_url}/api/validate-session",
                headers={'X-Tab-ID': str(uuid.uuid4())}
            ) as response:
                if response.status != 400:
                    self.record_test_result(test_name, False, "Missing Session Token not rejected")
                    return
            
            self.record_test_result(test_name, True, "Missing headers properly rejected")
            
        except Exception as e:
            self.record_test_result(test_name, False, f"Exception: {e}")

    async def test_multi_tab_simulation(self):
        """Simulate multi-tab session attempt"""
        test_name = "Multi-Tab Session Prevention"
        
        try:
            # Create first session
            session_token1, tab_id1 = await self.test_valid_login()
            
            if not session_token1:
                self.record_test_result(test_name, False, "Failed to create first session")
                return
            
            # Attempt to create second session for same user
            tab_id2 = str(uuid.uuid4())
            
            login_data = {
                'username': 'admin',  # Same user
                'password': 'admin123',
                'csrf_token': 'test_csrf_token'
            }
            
            headers = {
                'X-Tab-ID': tab_id2,
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            async with self.session.post(
                f"{self.base_url}/login",
                data=login_data,
                headers=headers
            ) as response:
                result = await response.json()
                
                if response.status == 403 and result.get('security_violation'):
                    self.record_test_result(test_name, True, "Multi-tab session properly prevented")
                else:
                    self.record_test_result(test_name, False, "Multi-tab session was allowed")
                    
        except Exception as e:
            self.record_test_result(test_name, False, f"Exception: {e}")

    async def test_edge_cases(self):
        """Test edge cases and browser compatibility"""
        print("\n🔄 Testing Edge Cases")
        print("-" * 40)
        
        # Test rate limiting
        await self.test_rate_limiting()

    async def test_rate_limiting(self):
        """Test rate limiting functionality"""
        test_name = "Rate Limiting"
        
        try:
            tab_id = str(uuid.uuid4())
            session_token = "dummy_token"
            
            headers = {
                'X-Tab-ID': tab_id,
                'X-Session-Token': session_token
            }
            
            # Send rapid requests
            blocked_count = 0
            for i in range(20):
                async with self.session.post(
                    f"{self.base_url}/api/validate-session",
                    headers=headers
                ) as response:
                    if response.status == 429:
                        blocked_count += 1
            
            if blocked_count > 0:
                self.record_test_result(test_name, True, f"Rate limiting active: {blocked_count} requests blocked")
            else:
                self.record_test_result(test_name, False, "Rate limiting not working")
                
        except Exception as e:
            self.record_test_result(test_name, False, f"Exception: {e}")

    async def test_attack_simulations(self):
        """Simulate various attack scenarios"""
        print("\n⚔️ Testing Attack Simulations")
        print("-" * 40)
        
        # Test session hijacking attempt
        await self.test_session_hijacking()

    async def test_session_hijacking(self):
        """Simulate session hijacking attempt"""
        test_name = "Session Hijacking Prevention"
        
        try:
            # Create legitimate session
            session_token, tab_id = await self.test_valid_login()
            
            if not session_token:
                self.record_test_result(test_name, False, "No valid session to test")
                return
            
            # Attempt to use session with different Tab ID (hijacking simulation)
            hijacked_tab_id = str(uuid.uuid4())
            
            headers = {
                'X-Tab-ID': hijacked_tab_id,
                'X-Session-Token': session_token
            }
            
            async with self.session.post(
                f"{self.base_url}/api/validate-session",
                headers=headers
            ) as response:
                if response.status == 401:
                    self.record_test_result(test_name, True, "Session hijacking attempt blocked")
                else:
                    self.record_test_result(test_name, False, "Session hijacking attempt succeeded")
                    
        except Exception as e:
            self.record_test_result(test_name, False, f"Exception: {e}")

    async def test_performance(self):
        """Test system performance under load"""
        print("\n⚡ Testing Performance")
        print("-" * 40)
        
        await self.test_concurrent_requests()

    async def test_concurrent_requests(self):
        """Test concurrent request handling"""
        test_name = "Concurrent Request Handling"
        
        try:
            session_token, tab_id = await self.test_valid_login()
            
            if not session_token:
                self.record_test_result(test_name, False, "No valid session to test")
                return
            
            headers = {
                'X-Tab-ID': tab_id,
                'X-Session-Token': session_token
            }
            
            # Send concurrent requests
            start_time = time.time()
            tasks = []
            
            for i in range(10):
                task = self.session.post(
                    f"{self.base_url}/api/validate-session",
                    headers=headers
                )
                tasks.append(task)
            
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            success_count = sum(1 for r in responses if hasattr(r, 'status') and r.status == 200)
            duration = end_time - start_time
            
            self.record_test_result(
                test_name, True, 
                f"Handled {success_count}/10 concurrent requests in {duration:.2f}s"
            )
            
        except Exception as e:
            self.record_test_result(test_name, False, f"Exception: {e}")

    def record_test_result(self, test_name: str, passed: bool, details: str):
        """Record test result"""
        result = {
            'test_name': test_name,
            'passed': passed,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        
        self.test_results.append(result)
        
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}: {details}")

    def generate_test_report(self):
        """Generate comprehensive test report"""
        print("\n" + "=" * 60)
        print("🧪 STRICT TAB SESSION SECURITY TEST REPORT")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r['passed'])
        failed_tests = total_tests - passed_tests
        
        print(f"\n📊 Test Summary:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {passed_tests}")
        print(f"   Failed: {failed_tests}")
        print(f"   Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ Failed Tests:")
            for result in self.test_results:
                if not result['passed']:
                    print(f"   - {result['test_name']}: {result['details']}")
        
        print(f"\n✅ Security Assessment:")
        if failed_tests == 0:
            print("   🛡️ EXCELLENT: All security tests passed")
        elif failed_tests <= 2:
            print("   ⚠️ GOOD: Minor issues detected")
        else:
            print("   🚨 CRITICAL: Major security vulnerabilities found")
        
        # Save detailed report
        with open('security_test_report.json', 'w') as f:
            json.dump({
                'summary': {
                    'total_tests': total_tests,
                    'passed_tests': passed_tests,
                    'failed_tests': failed_tests,
                    'success_rate': (passed_tests/total_tests)*100
                },
                'test_results': self.test_results,
                'generated_at': datetime.now().isoformat()
            }, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: security_test_report.json")

async def main():
    """Run the security test suite"""
    test_suite = SecurityTestSuite()
    await test_suite.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
