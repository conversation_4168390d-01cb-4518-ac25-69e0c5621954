#!/usr/bin/env python3
"""
🔒 DEEPLICA TAB-SPECIFIC TOKEN SECURITY TEST

This test verifies that the tab-specific URL token system works correctly:
1. User "admin" can login and access chat with valid tab token
2. Copying the URL to another tab/browser should fail authentication
3. Tab tokens are properly generated and verified
4. Invalid tab tokens redirect to unauthorized page
"""

import requests
import json
import time
import re
from pathlib import Path
import sys
from urllib.parse import urlparse, parse_qs

# Add project root to path
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

from shared.port_manager import get_service_port

def extract_tab_token_from_url(url):
    """Extract tab token from URL"""
    parsed = urlparse(url)
    params = parse_qs(parsed.query)
    return params.get('tab_token', [None])[0]

def test_admin_login_and_tab_token():
    """Test 1: Admin user can login and get tab token in URL"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔐 Test 1: Testing admin login and tab token generation on port {web_chat_port}...")
        
        # Create session for admin user
        session = requests.Session()
        
        # Login as admin
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=False,
            timeout=10
        )
        
        if login_response.status_code not in [302, 303, 307]:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
        
        print(f"✅ Admin login successful")
        
        # Follow redirect to chat
        chat_response = session.get(
            f"http://localhost:{web_chat_port}/chat",
            allow_redirects=True,
            timeout=10
        )
        
        if chat_response.status_code != 200:
            print(f"❌ Chat access failed: {chat_response.status_code}")
            return False
        
        # Check if URL contains tab token
        final_url = chat_response.url
        tab_token = extract_tab_token_from_url(final_url)
        
        if not tab_token:
            print(f"❌ No tab token found in URL: {final_url}")
            return False
        
        print(f"✅ Tab token found in URL: {tab_token[:20]}...")
        print(f"✅ Final URL: {final_url}")
        
        # Check if page contains tab token verification JavaScript
        if 'verifyTabToken' in chat_response.text:
            print(f"✅ Tab token verification JavaScript found in page")
        else:
            print(f"❌ Tab token verification JavaScript missing")
            return False
        
        return True, session, final_url, tab_token
        
    except Exception as e:
        print(f"❌ Error in test 1: {e}")
        return False

def test_tab_token_url_copying():
    """Test 2: Copying URL with tab token to new session should fail"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔒 Test 2: Testing tab token URL copying prevention...")
        
        # First, get a valid session and URL with tab token
        result = test_admin_login_and_tab_token()
        if not result or result is True:
            print(f"❌ Could not get valid session for URL copying test")
            return False
        
        success, original_session, chat_url_with_token, tab_token = result
        
        # Create a NEW session (simulating different browser tab)
        new_session = requests.Session()
        
        # Login with the new session first
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        login_response = new_session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=False,
            timeout=10
        )
        
        if login_response.status_code not in [302, 303, 307]:
            print(f"❌ Second session login failed: {login_response.status_code}")
            return False
        
        print(f"✅ Second session login successful")
        
        # Now try to access the original URL with tab token using the NEW session
        print(f"🔒 Attempting to access copied URL with different session...")
        print(f"🔗 URL: {chat_url_with_token}")
        
        copied_url_response = new_session.get(
            chat_url_with_token,
            allow_redirects=False,
            timeout=10
        )
        
        # This should fail and redirect to unauthorized
        if copied_url_response.status_code in [401, 403]:
            print(f"✅ Tab token correctly rejected copied URL (status: {copied_url_response.status_code})")
            return True
        elif copied_url_response.status_code in [302, 307]:
            # Check if redirected to unauthorized
            location = copied_url_response.headers.get('location', '')
            if 'unauthorized' in location.lower():
                print(f"✅ Tab token correctly redirected to unauthorized page")
                return True
            else:
                print(f"❌ Unexpected redirect to: {location}")
                return False
        else:
            print(f"❌ Tab token failed to prevent URL copying - status: {copied_url_response.status_code}")
            print(f"❌ Response: {copied_url_response.text[:200]}")
            return False
        
    except Exception as e:
        print(f"❌ Error in test 2: {e}")
        return False

def test_tab_token_generation_uniqueness():
    """Test 3: Each session should get unique tab tokens"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔒 Test 3: Testing tab token uniqueness...")
        
        tokens = []
        
        # Create multiple sessions and collect tab tokens
        for i in range(3):
            session = requests.Session()
            
            # Login
            login_data = {
                'username': 'admin',
                'password': 'admin123'
            }
            
            login_response = session.post(
                f"http://localhost:{web_chat_port}/login",
                data=login_data,
                allow_redirects=False,
                timeout=10
            )
            
            if login_response.status_code not in [302, 303, 307]:
                print(f"❌ Login {i+1} failed: {login_response.status_code}")
                continue
            
            # Access chat
            chat_response = session.get(
                f"http://localhost:{web_chat_port}/chat",
                allow_redirects=True,
                timeout=10
            )
            
            if chat_response.status_code == 200:
                tab_token = extract_tab_token_from_url(chat_response.url)
                if tab_token:
                    tokens.append(tab_token)
                    print(f"✅ Session {i+1} tab token: {tab_token[:20]}...")
        
        # Check uniqueness
        if len(tokens) >= 2:
            unique_tokens = set(tokens)
            if len(unique_tokens) == len(tokens):
                print(f"✅ All tab tokens are unique ({len(tokens)} tokens)")
                return True
            else:
                print(f"❌ Duplicate tab tokens found: {len(unique_tokens)} unique out of {len(tokens)}")
                return False
        else:
            print(f"❌ Could not collect enough tokens for uniqueness test")
            return False
        
    except Exception as e:
        print(f"❌ Error in test 3: {e}")
        return False

def test_admin_page_tab_tokens():
    """Test 4: Admin page should also have tab token protection"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔧 Test 4: Testing admin page tab token protection...")
        
        # Create session for admin user
        session = requests.Session()
        
        # Login as admin
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=False,
            timeout=10
        )
        
        if login_response.status_code not in [302, 303, 307]:
            print(f"❌ Admin login failed: {login_response.status_code}")
            return False
        
        # Access admin page
        admin_response = session.get(
            f"http://localhost:{web_chat_port}/admin",
            allow_redirects=True,
            timeout=10
        )
        
        if admin_response.status_code != 200:
            print(f"❌ Admin page access failed: {admin_response.status_code}")
            return False
        
        # Check if URL contains tab token
        final_url = admin_response.url
        tab_token = extract_tab_token_from_url(final_url)
        
        if not tab_token:
            print(f"❌ No tab token found in admin URL: {final_url}")
            return False
        
        print(f"✅ Admin page tab token found: {tab_token[:20]}...")
        print(f"✅ Admin URL: {final_url}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in test 4: {e}")
        return False

def main():
    """Run all tab token security tests"""
    print("🔒 DEEPLICA TAB-SPECIFIC TOKEN SECURITY TEST")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 4
    
    # Test 1: Admin login and tab token generation
    print("\n🧪 TEST 1: Admin Login and Tab Token Generation")
    result = test_admin_login_and_tab_token()
    if result and result is not True:
        tests_passed += 1
    elif result is True:
        tests_passed += 1
    
    # Test 2: Tab token URL copying prevention
    print("\n🧪 TEST 2: Tab Token URL Copying Prevention")
    if test_tab_token_url_copying():
        tests_passed += 1
    
    # Test 3: Tab token uniqueness
    print("\n🧪 TEST 3: Tab Token Uniqueness")
    if test_tab_token_generation_uniqueness():
        tests_passed += 1
    
    # Test 4: Admin page tab tokens
    print("\n🧪 TEST 4: Admin Page Tab Token Protection")
    if test_admin_page_tab_tokens():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 60)
    print(f"🔒 TEST RESULTS: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✅ ALL TESTS PASSED - Tab-specific token security is working!")
        print("🎉 Security features:")
        print("   - Tab tokens are generated for each session")
        print("   - URLs cannot be copied between browser tabs")
        print("   - Each tab gets a unique token")
        print("   - Both chat and admin pages are protected")
    else:
        print("❌ SOME TESTS FAILED - Tab token security needs fixes")
        
        if tests_passed >= 3:
            print("🔧 RECOMMENDATION: Most features work, minor issues remain")
        elif tests_passed >= 2:
            print("🔧 RECOMMENDATION: Basic functionality works, some security gaps")
        else:
            print("🔧 RECOMMENDATION: Major security issues - check implementation")
    
    print(f"\n🌐 Manual test instructions:")
    print(f"1. Open http://localhost:8007 in browser")
    print(f"2. Login with: admin / admin123")
    print(f"3. Notice the tab_token parameter in the URL")
    print(f"4. Copy the URL and paste it in a NEW browser tab")
    print(f"5. The new tab should show unauthorized page")

if __name__ == '__main__':
    main()
