#!/usr/bin/env python3
"""
🧪 Complete Dynamic Port Management Tests
Comprehensive verification that ALL ports are dynamically assigned by port manager.
NO CONSTANT PORTS - ALL are configurable including Backend API.
"""

import os
import sys
import json
import re
from pathlib import Path
from shared.port_manager import get_service_port

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.port_manager import (
    get_service_port, get_all_ports, is_port_configurable,
    get_all_configurable_services, port_manager
)

def test_no_constant_ports():
    """Test that NO ports are constant - ALL are configurable"""
    print("🧪 Testing that NO constant ports exist...")
    
    try:
        all_ports = get_all_ports()
        
        # Test that ALL ports are configurable
        for service, port in all_ports.items():
            assert is_port_configurable(service.lower()), f"Service {service} should be configurable"
        
        # Test that even Backend API is configurable
        assert is_port_configurable("backend"), "Backend API should be configurable"
        assert is_port_configurable("twilio-echo-bot"), "Twilio should be configurable"
        assert is_port_configurable("webhook-server"), "Webhook should be configurable"
        assert is_port_configurable("ngrok-api"), "Ngrok API should be configurable"
        assert is_port_configurable("ngrok-tunnel"), "Ngrok tunnel should be configurable"
        
        print("✅ ALL ports are configurable - no constant ports exist")
        return True
    except Exception as e:
        print(f"❌ Constant ports test failed: {e}")
        return False

def test_backend_api_dynamic():
    """Test that Backend API port is fully dynamic"""
    print("🧪 Testing Backend API dynamic port management...")
    
    try:
        # Backend API should be configurable
        backend_port = get_service_port("backend")
        assert isinstance(backend_port, int), "Backend port should be an integer"
        assert 1024 <= backend_port <= 65535, "Backend port should be in valid range"
        
        # Test that backend port can be changed (by checking it's in configurable services)
        configurable_services = get_all_configurable_services()
        assert "BACKEND-API" in configurable_services, "Backend API should be in configurable services"
        
        print(f"✅ Backend API port ({backend_port}) is fully dynamic and configurable")
        return True
    except Exception as e:
        print(f"❌ Backend API dynamic test failed: {e}")
        return False

def test_external_services_dynamic():
    """Test that ALL external services are dynamic"""
    print("🧪 Testing external services dynamic port management...")
    
    try:
        external_services = [
            ("twilio-echo-bot", "Twilio Echo Bot"),
            ("webhook-server", "Webhook Server"),
            ("ngrok-api", "Ngrok API"),
            ("ngrok-tunnel", "Ngrok Tunnel"),
            ("mongodb-proxy", "MongoDB Proxy")
        ]
        
        for service_key, service_name in external_services:
            try:
                port = get_service_port(service_key)
                assert isinstance(port, int), f"{service_name} port should be an integer"
                assert 1024 <= port <= 65535, f"{service_name} port should be in valid range"
                assert is_port_configurable(service_key), f"{service_name} should be configurable"
                print(f"✅ {service_name} port ({port}) is dynamic and configurable")
            except Exception as e:
                print(f"⚠️ {service_name} test issue: {e}")
        
        return True
    except Exception as e:
        print(f"❌ External services dynamic test failed: {e}")
        return False

def test_no_hardcoded_ports_in_code():
    """Test that no hardcoded ports remain in the codebase"""
    print("🧪 Scanning for hardcoded ports in codebase...")
    
    # Ports that should NOT be hardcoded anywhere
    forbidden_patterns = [
        r'(?<!#.*):get_service_port("backend")(?!\s*#.*port_manager)',  # get_service_port("backend") not in comments about port manager
        r'(?<!#.*):get_service_port("dispatcher")(?!\s*#.*port_manager)',  # get_service_port("dispatcher") not in comments about port manager
        r'(?<!#.*):get_service_port("dialogue")(?!\s*#.*port_manager)',  # get_service_port("dialogue") not in comments about port manager
        r'(?<!#.*):get_service_port("planner")(?!\s*#.*port_manager)',  # get_service_port("planner") not in comments about port manager
        r'(?<!#.*):get_service_port("phone")(?!\s*#.*port_manager)',  # get_service_port("phone") not in comments about port manager
        r'(?<!#.*):get_service_port("twilio-echo-bot")(?!\s*#.*port_manager)',  # get_service_port("twilio-echo-bot") not in comments about port manager
        r'(?<!#.*):get_service_port("webhook-server")(?!\s*#.*port_manager)',  # get_service_port("webhook-server") not in comments about port manager
        r'(?<!#.*):get_service_port("ngrok-api")(?!\s*#.*port_manager)',  # get_service_port("ngrok-api") not in comments about port manager
        r'(?<!#.*):get_service_port("ngrok-tunnel")(?!\s*#.*port_manager)',  # get_service_port("ngrok-tunnel") not in comments about port manager
    ]
    
    issues_found = []
    
    # Scan key files
    scan_files = [
        "backend/app/main.py",
        "dispatcher/app/main.py",
        "agents/dialogue/app/main.py",
        "agents/planner/app/main.py",
        "agents/phone/app/main.py",
        "watchdog/main.py",
        "web_chat/main.py",
        "startup_orchestrator.py",
        "start_ngrok.py"
    ]
    
    for file_path in scan_files:
        if Path(file_path).exists():
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                
                for pattern in forbidden_patterns:
                    matches = re.finditer(pattern, content)
                    for match in matches:
                        issues_found.append({
                            'file': file_path,
                            'pattern': match.group(),
                            'issue': 'Hardcoded port found'
                        })
            except Exception as e:
                print(f"⚠️ Error scanning {file_path}: {e}")
    
    if issues_found:
        print(f"❌ Found {len(issues_found)} hardcoded port issues:")
        for issue in issues_found:
            print(f"   📁 {issue['file']}: {issue['pattern']}")
        return False
    else:
        print("✅ No hardcoded ports found in key files")
        return True

def test_port_manager_version():
    """Test that port manager is updated to latest version"""
    print("🧪 Testing port manager version and configuration...")
    
    try:
        # Check that port manager has been updated
        settings_file = Path("shared/deeplica_port_settings.json")
        if settings_file.exists():
            with open(settings_file, 'r') as f:
                data = json.load(f)
            
            # Check version
            version = data.get('version', '1.0')
            assert version >= '4.0', f"Port manager version should be 4.0+, got {version}"
            
            # Check that all_ports_configurable flag exists
            all_configurable = data.get('all_ports_configurable', False)
            assert all_configurable, "all_ports_configurable should be True"
            
            print(f"✅ Port manager version {version} with all ports configurable")
        
        return True
    except Exception as e:
        print(f"❌ Port manager version test failed: {e}")
        return False

def test_service_imports():
    """Test that all services import port manager correctly"""
    print("🧪 Testing service port manager imports...")
    
    service_files = [
        "backend/app/main.py",
        "dispatcher/app/main.py",
        "agents/dialogue/app/main.py",
        "agents/planner/app/main.py",
        "agents/phone/app/main.py",
        "watchdog/main.py",
        "web_chat/main.py"
    ]
    
    missing_imports = []
    
    for service_file in service_files:
        if Path(service_file).exists():
            try:
                with open(service_file, 'r') as f:
                    content = f.read()
                
                has_import = (
                    'from shared.port_manager import' in content or
                    'import shared.port_manager' in content
                )
                
                if not has_import:
                    missing_imports.append(service_file)
                else:
                    print(f"✅ {service_file}: Has port manager import")
            except Exception as e:
                print(f"⚠️ Error checking {service_file}: {e}")
    
    if missing_imports:
        print(f"❌ Services missing port manager imports: {missing_imports}")
        return False
    else:
        print("✅ All services have port manager imports")
        return True

def test_comprehensive_port_coverage():
    """Test that all expected services have port assignments"""
    print("🧪 Testing comprehensive port coverage...")
    
    expected_services = [
        "backend", "dispatcher", "dialogue", "planner", "phone", "watchdog",
        "web-chat", "cli", "twilio-echo-bot", "webhook-server", 
        "ngrok-api", "ngrok-tunnel", "mongodb-proxy"
    ]
    
    try:
        all_ports = get_all_ports()
        
        for service in expected_services:
            try:
                port = get_service_port(service)
                assert isinstance(port, int), f"{service} port should be integer"
                print(f"✅ {service}: port {port}")
            except Exception as e:
                print(f"❌ {service}: {e}")
                return False
        
        print("✅ All expected services have port assignments")
        return True
    except Exception as e:
        print(f"❌ Comprehensive port coverage test failed: {e}")
        return False

def run_complete_dynamic_tests():
    """Run all dynamic port management tests"""
    print("🧪 COMPLETE DYNAMIC PORT MANAGEMENT TESTS")
    print("=" * 60)
    
    tests = [
        test_no_constant_ports,
        test_backend_api_dynamic,
        test_external_services_dynamic,
        test_no_hardcoded_ports_in_code,
        test_port_manager_version,
        test_service_imports,
        test_comprehensive_port_coverage
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
        print()
    
    print("=" * 60)
    print(f"📊 TEST RESULTS: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Complete dynamic port management is working correctly")
        print("✅ NO constant ports exist - ALL ports are configurable")
        print("✅ Backend API and external services are fully dynamic")
        return True
    else:
        print("⚠️ Some tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = run_complete_dynamic_tests()
    sys.exit(0 if success else 1)
