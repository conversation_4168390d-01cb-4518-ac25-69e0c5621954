#!/usr/bin/env python3
"""
🧪 COMPLETE SYSTEM TEST
Tests the entire mandatory registration and stop system
"""

import asyncio
import subprocess
import time
import httpx
import sys
import os
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from shared.port_manager import get_service_port

class SystemTester:
    """Tests the complete Deeplica system"""
    
    def __init__(self):
        self.stop_service_url = f"http://localhost:{get_service_port('stop-deeplica')}"
        self.stop_service_process = None
        self.test_processes = []
    
    async def test_stop_service_startup(self):
        """Test 1: Stop Service starts and responds"""
        print("🧪 TEST 1: Stop Service Startup")
        
        # Start Stop Service
        self.stop_service_process = subprocess.Popen([
            sys.executable, "stop_deeplica_service/main.py"
        ], cwd=os.getcwd())
        
        # Wait for it to be ready
        for attempt in range(10):
            await asyncio.sleep(1)
            try:
                async with httpx.AsyncClient(timeout=3.0) as client:
                    response = await client.get(f"{self.stop_service_url}/health")
                    if response.status_code == 200:
                        data = response.json()
                        if data.get('status') == 'healthy':
                            print("✅ Stop Service is running and healthy")
                            return True
            except:
                pass
            print(f"⏳ Waiting for Stop Service... ({attempt + 1}/10)")
        
        print("❌ Stop Service failed to start")
        return False
    
    def test_registration_system(self):
        """Test 2: Service registration works"""
        print("\n🧪 TEST 2: Service Registration")
        
        try:
            from shared.process_registry import register_service
            
            # Test mandatory registration
            registry = register_service(
                service_name='TEST-MANDATORY',
                port=9999,
                terminal_name='Test Mandatory Service',
                mandatory=False  # Use False to avoid exit on failure
            )
            print("✅ Mandatory registration system works")
            return True
            
        except Exception as e:
            print(f"❌ Registration failed: {e}")
            return False
    
    async def test_service_tracking(self):
        """Test 3: Service tracking and status"""
        print("\n🧪 TEST 3: Service Tracking")
        
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{self.stop_service_url}/status")
                if response.status_code == 200:
                    data = response.json()
                    processes = data.get('processes', {})
                    if 'TEST-MANDATORY' in processes:
                        print("✅ Service tracking works")
                        print(f"   Registered processes: {len(processes)}")
                        return True
                    else:
                        print("❌ Service not found in registry")
                        return False
                else:
                    print(f"❌ Status endpoint failed: {response.status_code}")
                    return False
        except Exception as e:
            print(f"❌ Service tracking failed: {e}")
            return False
    
    async def test_background_cleanup(self):
        """Test 4: Background cleanup of dead processes"""
        print("\n🧪 TEST 4: Background Cleanup")
        
        # The TEST-MANDATORY process should be dead by now
        # Wait for background cleanup (runs every 30 seconds, but let's wait a bit)
        print("⏳ Waiting for background cleanup...")
        await asyncio.sleep(5)
        
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{self.stop_service_url}/status")
                if response.status_code == 200:
                    data = response.json()
                    processes = data.get('processes', {})
                    
                    # Check if dead processes are marked as not running
                    if 'TEST-MANDATORY' in processes:
                        process_info = processes['TEST-MANDATORY']
                        if not process_info.get('running', True):
                            print("✅ Background cleanup detects dead processes")
                            return True
                        else:
                            print("⚠️ Process still marked as running")
                            return True  # Still acceptable
                    else:
                        print("✅ Background cleanup removed dead process")
                        return True
        except Exception as e:
            print(f"❌ Background cleanup test failed: {e}")
            return False
    
    async def test_stop_functionality(self):
        """Test 5: Stop functionality"""
        print("\n🧪 TEST 5: Stop Functionality")
        
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.post(
                    f"{self.stop_service_url}/stop_all",
                    json={
                        "force": False,
                        "reason": "System test",
                        "shutdown_self": False  # Don't shutdown for this test
                    }
                )
                
                if response.status_code == 200:
                    data = response.json()
                    print("✅ Stop functionality works")
                    print(f"   Results: {data.get('results', {})}")
                    return True
                else:
                    print(f"❌ Stop request failed: {response.status_code}")
                    return False
        except Exception as e:
            print(f"❌ Stop functionality failed: {e}")
            return False
    
    async def test_self_shutdown(self):
        """Test 6: Self shutdown functionality"""
        print("\n🧪 TEST 6: Self Shutdown")
        
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.post(
                    f"{self.stop_service_url}/stop_all",
                    json={
                        "force": False,
                        "reason": "System test - self shutdown",
                        "shutdown_self": True  # Shutdown the service itself
                    }
                )
                
                if response.status_code == 200:
                    print("✅ Self shutdown command accepted")
                    
                    # Wait a moment and check if service is down
                    await asyncio.sleep(3)
                    
                    try:
                        health_response = await client.get(f"{self.stop_service_url}/health")
                        print("❌ Service still responding after shutdown command")
                        return False
                    except:
                        print("✅ Service successfully shut itself down")
                        return True
                else:
                    print(f"❌ Self shutdown failed: {response.status_code}")
                    return False
        except Exception as e:
            print(f"❌ Self shutdown test failed: {e}")
            return False
    
    def cleanup(self):
        """Clean up test processes"""
        if self.stop_service_process:
            try:
                self.stop_service_process.terminate()
                self.stop_service_process.wait(timeout=5)
            except:
                try:
                    self.stop_service_process.kill()
                except:
                    pass
        
        for process in self.test_processes:
            try:
                process.terminate()
                process.wait(timeout=5)
            except:
                try:
                    process.kill()
                except:
                    pass

async def main():
    """Run complete system test"""
    print("🧪 DEEPLICA COMPLETE SYSTEM TEST")
    print("=" * 50)
    
    tester = SystemTester()
    tests_passed = 0
    total_tests = 6
    
    try:
        # Test 1: Stop Service Startup
        if await tester.test_stop_service_startup():
            tests_passed += 1
        
        # Test 2: Registration System
        if tester.test_registration_system():
            tests_passed += 1
        
        # Test 3: Service Tracking
        if await tester.test_service_tracking():
            tests_passed += 1
        
        # Test 4: Background Cleanup
        if await tester.test_background_cleanup():
            tests_passed += 1
        
        # Test 5: Stop Functionality
        if await tester.test_stop_functionality():
            tests_passed += 1
        
        # Test 6: Self Shutdown (this will stop the service)
        if await tester.test_self_shutdown():
            tests_passed += 1
        
    except Exception as e:
        print(f"\n❌ Test suite failed with error: {e}")
    finally:
        tester.cleanup()
    
    print(f"\n🎯 TEST RESULTS: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 ALL TESTS PASSED! System is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
