"""
Dialogue Agent Service - Handles user interaction tasks.
Microservice that processes dialogue and conversation tasks.
"""

import os
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

import sys

# Add project root to path for shared modules
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.unified_logging import get_dialogue_logger
from shared.port_manager import get_service_port, get_service_host, ensure_service_port_free
from shared.port_cache import get_port_cache
from shared.resilience_utils import bulletproof_retry, wait_for_service, bulletproof_service_wrapper

# Initialize unified logger
logger = get_dialogue_logger()

# Initialize port cache for efficient port management
port_cache = get_port_cache("dialogue")

from agents.dialogue.app.agent import DialogueAgent
from agents.dialogue.app.models import TaskRequest, TaskResponse
from agents.dialogue.app.llm_service import Backend<PERSON><PERSON>lient
from shared.backend_readiness import ensure_backend_ready_before_startup


# Port cleanup function removed per user request - manual management only


async def wait_for_backend_and_initialize(app: FastAPI):
    """Wait for Backend API and initialize services - runs in background, NEVER crashes the main service"""
    import httpx
    import asyncio

    # 🔧 FIXED: Use correct backend port and simplified connection
    backend_port = get_service_port('backend')
    backend_url = f"http://localhost:{backend_port}"
    logger.info(f"⏳ Waiting for Backend API at {backend_url} (port {backend_port})", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize()")
    logger.info("🔄 Will keep trying INDEFINITELY until Backend API is ready...", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize()")
    logger.info("💪 This service will NEVER crash - it will wait as long as needed!", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize()")

    attempt = 1
    while True:  # Keep trying forever until successful
        try:
            # 🔧 FIXED: Simplified and more reliable connection check
            try:
                async with httpx.AsyncClient(timeout=10.0) as client:
                    try:
                        # First check health endpoint
                        health_response = await client.get(f"{backend_url}/health")
                        if health_response.status_code == 200:
                            health_data = health_response.json()
                            if health_data.get("status") == "healthy":
                                logger.debug(f"✅ Backend API health check passed (attempt {attempt})", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")

                                # Then check ready endpoint
                                ready_response = await client.get(f"{backend_url}/ready")
                                if ready_response.status_code == 200:
                                    ready_data = ready_response.json()
                                    if ready_data.get("status") == "ready":
                                        logger.info(f"✅ Backend API is FULLY READY! (attempt {attempt})", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize()")
                                        break
                                    else:
                                        logger.debug(f"Backend API ready endpoint responded but status not ready: {ready_data}", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")
                                else:
                                    logger.debug(f"Backend API ready endpoint returned {ready_response.status_code}", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")
                            else:
                                logger.debug(f"Backend API health check failed: {health_data}", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")
                        else:
                            logger.debug(f"Backend API health endpoint returned {health_response.status_code}", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")

                    except httpx.ConnectError as e:
                        logger.debug("Backend API connection failed (port not open yet): {e}", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")
                    except httpx.TimeoutException as e:
                        logger.debug("Backend API timeout (still starting up): {e}", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")
                    except httpx.ReadTimeout as e:
                        logger.debug("Backend API read timeout (still initializing): {e}", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")
                    except httpx.WriteTimeout as e:
                        logger.debug("Backend API write timeout: {e}", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")
                    except httpx.PoolTimeout as e:
                        logger.debug("Backend API pool timeout: {e}", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")
                    except httpx.HTTPStatusError as e:
                        logger.debug("Backend API HTTP error: {e}", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")
                    except httpx.RequestError as e:
                        logger.debug("Backend API request error: {e}", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")
                    except OSError as e:
                        logger.debug("Backend API OS error (network/socket): {e}", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")
                    except Exception as e:
                        logger.debug("Backend API unexpected response error: {e}", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")

            except ImportError as e:
                logger.debug("Failed to import httpx: {e}", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")
            except Exception as e:
                logger.debug("Failed to create HTTP client: {e}", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")

        except Exception as e:
            logger.debug("Unexpected error in connection attempt: {e}", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")

        # Log progress every 15 attempts (30 seconds)
        if attempt % 15 == 0:
            logger.info("⏳ Still waiting for Backend API... (attempt {attempt}, {attempt * 2 // 60} minutes elapsed)", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")
            logger.info("continuing to wait...", module="DIALOGUE-AGENT:wait_for_backend_and_initialize", routine="SYSTEM | 💪 Dialogue Agent will NEVER give up")

        try:
            logger.debug("⏳ Retrying in 2 seconds... (attempt {attempt})", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")
            await asyncio.sleep(2)
        except Exception as e:
            logger.debug("Sleep interrupted: {e}", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")
            # Use time.sleep as fallback
            try:
                import time
                time.sleep(2)
            except Exception as sleep_error:
                logger.debug("Fallback sleep failed: {sleep_error}", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")
                pass  # Continue anyway

        attempt += 1

    logger.info("✅ Backend API is healthy and ready - proceeding with Dialogue Agent initialization!", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize()")

    # Initialize services with retry loop (avoid recursion)
    service_retry_attempt = 1
    while True:
        try:
            llm_service = BackendLLMClient()
            dialogue_agent = DialogueAgent(llm_service)
            app.state.agent = dialogue_agent
            app.state.ready = True
            logger.info("✅ Dialogue Agent initialized successfully", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize()")

            # 🛡️ BULLETPROOF: Keep the background task alive indefinitely
            logger.info("🔄 Background task will now run indefinitely to keep service alive", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")

            # Keep the background task alive by waiting indefinitely
            try:
                while True:
                    await asyncio.sleep(60)  # Sleep for 1 minute intervals
                    # Optional: Add health checks or maintenance tasks here
                    logger.debug("🔄 Background task heartbeat - service running", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")
            except asyncio.CancelledError:
                logger.info("🛑 Background task cancelled - service shutting down", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")
                raise  # Re-raise to properly handle cancellation
        except Exception as e:
            logger.error("❌ Failed to initialize Dialogue Agent (attempt {service_retry_attempt}): {e}", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize()")
            logger.info("🔄 Will retry initialization...", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize()")

            try:
                await asyncio.sleep(5)
            except Exception as sleep_error:
                logger.debug("Sleep interrupted during service retry: {sleep_error}", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")
                try:
                    import time
                    time.sleep(5)
                except Exception:
                    pass

            service_retry_attempt += 1

            # Log progress every 10 attempts
            if service_retry_attempt % 10 == 0:
                logger.info("⏳ Still trying to initialize Dialogue Agent... (attempt {service_retry_attempt})", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")
                logger.info("💪 Will NEVER give up on service initialization!", module="DIALOGUE-AGENT", routine="wait_for_backend_and_initialize")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager - starts immediately, handles dependencies in background"""
    import asyncio

    # Startup
    logger.info("🚀 Starting Dialogue Agent Service", module="DIALOGUE-AGENT", routine="lifespan()")
    logger.info("💪 Service will start immediately and handle dependencies in background - NEVER crashes!", module="DIALOGUE-AGENT", routine="lifespan()")

    # Initialize basic state
    app.state.ready = False
    app.state.agent = None

    # Start background task to wait for dependencies - this will NEVER crash the service
    try:
        background_task = asyncio.create_task(wait_for_backend_and_initialize(app))
        app.state.background_task = background_task
        logger.info("✅ Dialogue Agent service started - waiting for dependencies in background", module="DIALOGUE-AGENT", routine="lifespan()")
    except Exception as e:
        logger.error("❌ Failed to start background task: {e}", module="DIALOGUE-AGENT", routine="lifespan()")
        # Even if background task fails, don't crash the service
        app.state.ready = False
        logger.info("⚠️ Background task failed but service will continue running", module="DIALOGUE-AGENT", routine="lifespan()")

    yield

    # Shutdown
    logger.info("🛑 Shutting down Dialogue Agent Service", module="DIALOGUE-AGENT", routine="lifespan()")
    if hasattr(app.state, 'background_task'):
        try:
            app.state.background_task.cancel()
        except Exception as e:
            logger.debug("Error canceling background task: {e}", module="DIALOGUE-AGENT", routine="lifespan")


# Create FastAPI app
app = FastAPI(
    title="Dialogue Agent Service",
    description="User Interaction and Conversation Agent",
    version="0.1.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/health")
async def health_check():
    """Health check endpoint - always responds, shows dependency status"""
    backend_ready = getattr(app.state, 'ready', False)
    return {
        "status": "healthy",
        "service": "dialogue-agent",
        "version": "0.1.0",
        "backend_ready": backend_ready,
        "message": "Service is running" + (" and fully ready" if backend_ready else " but waiting for Backend API")
    }


@app.post("/execute")
async def execute_task(request: TaskRequest):
    """Execute a dialogue task"""
    logger.info("📥 Received task: {request.task_id} (type: {request.task_type})", module="DIALOGUE-AGENT", routine="execute_task()")
    
    try:
        agent = app.state.agent
        
        # Execute the task asynchronously and callback to dispatcher
        # Return immediate response to avoid timeout
        import asyncio
        asyncio.create_task(agent.execute_task_async(request))
        
        return {"status": "accepted", "task_id": request.task_id}
        
    except Exception as e:
        logger.error("❌ Failed to execute task {request.task_id}: {e}", module="DIALOGUE-AGENT", routine="execute_task()")
        raise HTTPException(status_code=500, detail=str(e))


async def main():
    """🛡️ BULLETPROOF Main startup function - GUARANTEED 100% UPTIME"""
    # Logger is already configured via unified_logging system

    # 🛡️ BULLETPROOF: Infinite retry loop - service NEVER exits
    retry_count = 0
    while True:
        try:
            retry_count += 1
            if retry_count > 1:
                logger.info(f"🔄 BULLETPROOF: Dialogue Agent startup attempt #{retry_count}", module="DIALOGUE-AGENT", routine="main")

            # Get configuration from environment
            server_host = os.getenv("DIALOGUE_AGENT_HOST", get_service_host("dialogue"))

            # 🔧 REMOVED: Duplicate detection logic to simplify startup and avoid errors
            logger.info("🚀 Starting DIALOGUE service...", module="DIALOGUE-AGENT", routine="main")

            # Ensure port is free before starting
            logger.info("🔌 Ensuring dialogue agent port is free...", module="DIALOGUE-AGENT", routine="main")
            server_port = ensure_service_port_free("dialogue", force=True)
            logger.info(f"✅ Dialogue agent will use port {server_port}", module="DIALOGUE-AGENT", routine="main")

            debug = os.getenv("DEBUG", "false").lower() == "true"

            # 🔧 FIXED: Simplified backend readiness check with bulletproof connection
            logger.info("🔍 Checking Backend API readiness...", module="DIALOGUE-AGENT", routine="main")

            import httpx
            backend_port = get_service_port('backend')
            backend_url = f"http://localhost:{backend_port}"

            # Test direct connection to backend
            try:
                async with httpx.AsyncClient(timeout=5.0) as client:
                    response = await client.get(f"{backend_url}/health")
                    if response.status_code == 200:
                        logger.info(f"✅ Backend API is healthy on port {backend_port}", module="DIALOGUE-AGENT", routine="main")

                        # Also check /ready endpoint
                        ready_response = await client.get(f"{backend_url}/ready")
                        if ready_response.status_code == 200:
                            ready_data = ready_response.json()
                            if ready_data.get("status") == "ready":
                                logger.info("✅ Backend API is fully ready!", module="DIALOGUE-AGENT", routine="main")
                            else:
                                logger.info("⚠️ Backend API healthy but not fully ready - proceeding anyway", module="DIALOGUE-AGENT", routine="main")
                        else:
                            logger.info("⚠️ Backend API healthy but /ready endpoint not available - proceeding anyway", module="DIALOGUE-AGENT", routine="main")
                    else:
                        logger.warning(f"⚠️ Backend API returned status {response.status_code} - proceeding anyway", module="DIALOGUE-AGENT", routine="main")
            except Exception as e:
                logger.warning(f"⚠️ Could not connect to Backend API: {e} - proceeding anyway", module="DIALOGUE-AGENT", routine="main")
                logger.info("💪 BULLETPROOF: Dialogue service will start and wait for backend in background", module="DIALOGUE-AGENT", routine="main")

            # Port cleanup removed per user request - manual management only

            logger.info(f"🌟 Starting Dialogue Agent on {server_host}:{server_port} (debug={debug})", module="DIALOGUE-AGENT", routine="main")

            # AGGRESSIVELY suppress ALL HTTP request logging
            import logging

            # Disable ALL uvicorn logging completely
            for logger_name in ["uvicorn", "uvicorn.access", "uvicorn.error", "uvicorn.asgi"]:
                logger_obj = logging.getLogger(logger_name)
                logger_obj.setLevel(logging.CRITICAL)
                logger_obj.disabled = True
                logger_obj.propagate = False

            uvicorn.run(
                "agents.dialogue.app.main:app",
                host=server_host,
                port=server_port,
                reload=debug,
                log_level="error",    # Only show errors
                access_log=False,     # Disable access logging
                use_colors=False      # Disable colors for cleaner output
            )

            # If we reach here, uvicorn completed - THIS SHOULD NEVER HAPPEN!
            logger.error("💥 CRITICAL: Dialogue Agent uvicorn completed unexpectedly - servers should NEVER complete!", module="DIALOGUE-AGENT", routine="main")
            logger.error("💥 CRITICAL: This indicates a serious problem - restarting immediately", module="DIALOGUE-AGENT", routine="main")
            import time
            time.sleep(2)
            continue  # Restart the service immediately
        except OSError as e:
            if "Address already in use" in str(e):
                logger.error(f"❌ Port {server_port} is already in use. Another Dialogue Agent may be running.", module="DIALOGUE-AGENT", routine="main")
                logger.info("💡 Try stopping other DEEPLICA services first with '🛑 STOP DEEPLICA'", module="DIALOGUE-AGENT", routine="main")
                # 🛡️ BULLETPROOF: Never exit - try alternative port
                logger.info("🔄 BULLETPROOF: Will try alternative port - service NEVER crashes", module="DIALOGUE-AGENT", routine="main")
                try:
                    from shared.port_manager import DeepLicaPortManager
                    port_mgr = DeepLicaPortManager("dialogue-alt")
                    server_port = port_mgr.allocate_port("dialogue-alt")
                    logger.info(f"🔄 Using alternative port: {server_port}", module="DIALOGUE-AGENT", routine="main")
                except:
                    import time
                    time.sleep(10)
                    logger.info("🔄 Retrying after delay", module="DIALOGUE-AGENT", routine="main")
            else:
                logger.error(f"❌ Network error starting Dialogue Agent: {e}", module="DIALOGUE-AGENT", routine="main")
                # 🛡️ BULLETPROOF: Never exit - wait and retry
                logger.info("🔄 BULLETPROOF: Will retry after network error - service NEVER crashes", module="DIALOGUE-AGENT", routine="main")
                import time
                time.sleep(10)
            continue  # Retry the entire main loop
        except Exception as e:
            logger.error(f"❌ Unexpected error starting Dialogue Agent: {e}", module="DIALOGUE-AGENT", routine="main")
            # 🛡️ BULLETPROOF: Never exit - wait and retry
            logger.info("🔄 BULLETPROOF: Will retry after unexpected error - service NEVER crashes", module="DIALOGUE-AGENT", routine="main")
            import time
            time.sleep(10)
            continue  # Retry the entire main loop

    # 🛡️ BULLETPROOF: This line should NEVER be reached!
    logger.error("💥 CRITICAL: Dialogue Agent main function exited infinite loop - THIS SHOULD NEVER HAPPEN!", module="DIALOGUE-AGENT", routine="main")
    # No return - this should never be reached


if __name__ == "__main__":
    # Set distinctive process name for easy identification
    try:
        import setproctitle
        setproctitle.setproctitle("DEEPLICA-DIALOGUE-AGENT")
        logger.info("DIALOGUE-AGENT", module="DIALOGUE-AGENT:startup", routine="PROCESS | Process name set to: DEEPLICA")
    except ImportError:
        logger.warning("process name unchanged", module="DIALOGUE-AGENT:startup", routine="PROCESS | setproctitle not available")

    # Process detection removed per user request - manual management only

    # Set terminal title and clear identification banner - FORCE RENAME EVEN IF ALREADY NAMED
    service_name = os.getenv("SERVICE_NAME", "DIALOGUE-AGENT")

    # Multiple methods to ensure terminal gets renamed
    print(f"\033]0;💬 {service_name}\007", end="")  # xterm title
    print(f"\033]2;💬 {service_name}\007", end="")  # window title
    print(f"\033]1;💬 {service_name}\007", end="")  # icon title

    # Also try VS Code specific terminal naming
    import sys
    if hasattr(sys, 'ps1') or hasattr(sys, 'ps2'):
        try:
            import os
            os.system(f'echo -ne "\\033]0;💬 {service_name}\\007"')
        except:
            pass

    print("\n" + "="*80)
    print(f"💬 {service_name} TERMINAL")
    print("="*80 + "\n")

    # Wrap main function with bulletproof wrapper to ensure it never crashes
    bulletproof_main = bulletproof_service_wrapper(main, "DIALOGUE-AGENT")
    exit_code = asyncio.run(bulletproof_main())

    # Check if running in debugger (VS Code)
    import sys
    if hasattr(sys, 'gettrace') and sys.gettrace() is not None:
        print(f"\n🔍 Running in debugger mode - exit code would be: {exit_code}")
        if exit_code != 0:
            print("💡 In production, this would exit with error code")
    # Don't exit in production - let the service restart itself
    # else:
    #     sys.exit(exit_code)  # REMOVED - services should never exit themselves
