#!/usr/bin/env python3
"""
🔌 Test DHCP-like Port Manager
Test the enhanced port manager with conflict resolution
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'shared'))

try:
    print("🔌 Testing DHCP-like Port Manager")
    print("=" * 50)

    from shared.port_manager import port_manager

    print("✅ Port manager imported successfully")

    # Test 1: Allocate port for phone agent
    print("\n📞 Test 1: Allocating port for PHONE-AGENT")
    phone_port = port_manager.allocate_port("PHONE-AGENT")
    print(f"✅ Phone Agent allocated port: {phone_port}")

    # Test 2: Try to allocate same service again (should reuse)
    print("\n🔄 Test 2: Re-allocating port for PHONE-AGENT (should reuse)")
    phone_port2 = port_manager.allocate_port("PHONE-AGENT")
    print(f"✅ Phone Agent port (reuse): {phone_port2}")

    # Test 3: Allocate port for different service
    print("\n🤖 Test 3: Allocating port for DIALOGUE-AGENT")
    dialogue_port = port_manager.allocate_port("DIALOGUE-AGENT")
    print(f"✅ Dialogue Agent allocated port: {dialogue_port}")

    # Test 4: Check for conflicts
    print("\n🚨 Test 4: Checking for conflicts")
    if phone_port == dialogue_port:
        print("❌ CONFLICT DETECTED: Same port allocated to different services!")
    else:
        print("✅ No conflicts: Different ports allocated")

    # Test 5: List all leases
    print("\n📋 Test 5: Current port leases")
    leases = port_manager.list_all_leases()
    for service, lease_info in leases.items():
        print(f"  {service}: {lease_info}")

    # Test 6: Test ensure_port_free
    print("\n🔌 Test 6: Testing ensure_port_free for PHONE-AGENT")
    ensured_port = port_manager.ensure_port_free("PHONE-AGENT")
    print(f"✅ Ensured port for Phone Agent: {ensured_port}")

    # Test 7: Validate no conflicts
    print("\n✅ Test 7: Final validation")
    port_manager._validate_and_fix_conflicts()

    print("\n🎉 Port Manager tests completed!")

except Exception as e:
    print(f"❌ Port manager error: {e}")
    import traceback
    traceback.print_exc()
