#!/usr/bin/env python3
"""
DEEPLICA Password Migration Script
Migrate all existing passwords to PBKDF2 with salt (more secure)
This is the SELECTED ONE SYSTEM WIDE standard
"""

import asyncio
import sys
import os
from motor.motor_asyncio import AsyncIOMotorClient
from datetime import datetime

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from shared_models.password_utils import PasswordHasher, hash_password, verify_password, needs_rehash


class PasswordMigrator:
    """Migrate passwords to PBKDF2 with salt standard"""
    
    def __init__(self):
        self.client = None
        self.db = None
        
    async def connect_to_database(self):
        """Connect to MongoDB Atlas"""
        try:
            # MongoDB Atlas connection string
            connection_string = "mongodb+srv://deeplica-db:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"
            
            self.client = AsyncIOMotorClient(connection_string)
            self.db = self.client["deeplica-dev"]
            
            # Test connection
            await self.client.admin.command('ping')
            print("✅ Connected to MongoDB Atlas")
            
        except Exception as e:
            print(f"❌ Failed to connect to database: {e}")
            raise
    
    async def migrate_user_passwords(self):
        """Migrate all user passwords to PBKDF2 with salt"""
        try:
            collection = self.db["users"]
            
            # Find all users
            users = await collection.find({}).to_list(None)
            print(f"📋 Found {len(users)} users to check")
            
            migrated_count = 0
            updated_count = 0
            
            for user in users:
                username = user.get("username", "unknown")
                stored_hash = user.get("password_hash", "")
                
                print(f"\n🔍 Checking user: {username}")
                print(f"   Current hash format: {stored_hash[:20]}...")
                
                # Check if password needs migration
                if needs_rehash(stored_hash):
                    print(f"   ⚠️ Password needs migration to PBKDF2")
                    
                    # For known default passwords, migrate them
                    if username.lower() == "admin":
                        new_hash = hash_password("admin123")
                        await collection.update_one(
                            {"_id": user["_id"]},
                            {"$set": {"password_hash": new_hash}}
                        )
                        print(f"   ✅ Migrated admin password to PBKDF2")
                        migrated_count += 1
                        
                    elif username.lower() == "guest":
                        new_hash = hash_password("guest123")
                        await collection.update_one(
                            {"_id": user["_id"]},
                            {"$set": {"password_hash": new_hash}}
                        )
                        print(f"   ✅ Migrated guest password to PBKDF2")
                        migrated_count += 1
                        
                    elif username.lower() == "testadmin":
                        new_hash = hash_password("admin123")
                        await collection.update_one(
                            {"_id": user["_id"]},
                            {"$set": {"password_hash": new_hash}}
                        )
                        print(f"   ✅ Migrated testadmin password to PBKDF2")
                        migrated_count += 1
                        
                    else:
                        print(f"   ⚠️ Unknown user - password will be migrated on next login")
                        
                else:
                    print(f"   ✅ Password already in PBKDF2 format")
                    updated_count += 1
            
            print(f"\n📊 Migration Summary:")
            print(f"   🔄 Migrated: {migrated_count} users")
            print(f"   ✅ Already updated: {updated_count} users")
            print(f"   📋 Total users: {len(users)}")
            
        except Exception as e:
            print(f"❌ Failed to migrate passwords: {e}")
            raise
    
    async def verify_migrations(self):
        """Verify that migrations were successful"""
        try:
            collection = self.db["users"]
            
            # Test known users
            test_users = [
                ("admin", "admin123"),
                ("guest", "guest123"),
                ("testadmin", "admin123")
            ]
            
            print(f"\n🧪 Verifying password migrations...")
            
            for username, password in test_users:
                user_doc = await collection.find_one({
                    "username": {"$regex": f"^{username}$", "$options": "i"}
                })
                
                if user_doc:
                    stored_hash = user_doc["password_hash"]
                    is_valid = verify_password(password, stored_hash)
                    is_pbkdf2 = stored_hash.startswith('pbkdf2_sha256$')
                    
                    print(f"   {username}: {'✅' if is_valid else '❌'} Valid, {'✅' if is_pbkdf2 else '❌'} PBKDF2")
                else:
                    print(f"   {username}: ❌ User not found")
            
        except Exception as e:
            print(f"❌ Failed to verify migrations: {e}")
            raise
    
    async def close_connection(self):
        """Close database connection"""
        if self.client:
            self.client.close()
            print("🔌 Database connection closed")


async def main():
    """Main migration function"""
    print("🔐 DEEPLICA Password Migration to PBKDF2 with Salt")
    print("=" * 60)
    
    migrator = PasswordMigrator()
    
    try:
        # Connect to database
        await migrator.connect_to_database()
        
        # Migrate passwords
        await migrator.migrate_user_passwords()
        
        # Verify migrations
        await migrator.verify_migrations()
        
        print("\n✅ Password migration completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Migration failed: {e}")
        return 1
        
    finally:
        await migrator.close_connection()
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
