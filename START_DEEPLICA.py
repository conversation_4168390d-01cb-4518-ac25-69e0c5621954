#!/usr/bin/env python3
"""
🏭 START DEEPLICA - Production Orchestrator with Real Database

PRODUCTION MODE - USING REAL MONGODB ATLAS DATABASE

✅ FEATURES:
- Real MongoDB Atlas database connection
- Production-ready service orchestration
- Services designed to NEVER crash
- Each service waits for dependencies internally
- Orchestrator monitors and restarts crashed services
- Automatic browser launch when ready
- Comprehensive logging and debugging
- Health monitoring with recovery
- Exponential backoff for restarts
- Full database persistence and reliability

🔗 DATABASE: MongoDB Atlas (Real Production Database)
"""

import asyncio
import os
import sys
import subprocess
import signal
import time
from typing import Dict, List
from pathlib import Path

# Add project root to path
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, PROJECT_ROOT)

from shared.port_manager import get_service_port, ensure_service_port_free
from shared.unified_logging import get_logger

# Initialize logger
logger = get_logger("START-DEEPLICA")

class DeepplicaOrchestrator:
    """Production-ready ultra-resilient orchestrator"""
    
    def __init__(self):
        self.services = {
            "backend": {
                "name": "🌐 Backend API",
                "directory": "backend",
                "command": [sys.executable, "app/main.py"],
                "port": get_service_port("BACKEND-API"),
                "health_url": f"http://localhost:{get_service_port('BACKEND-API')}/health",
                "process": None,
                "restart_count": 0,
                "last_restart": 0,
                "essential": True
            },
            "dispatcher": {
                "name": "🎯 Dispatcher",
                "directory": "dispatcher",
                "command": [sys.executable, "-m", "app.main"],
                "port": get_service_port("DISPATCHER"),
                "health_url": f"http://localhost:{get_service_port('DISPATCHER')}/health",
                "process": None,
                "restart_count": 0,
                "last_restart": 0,
                "essential": True
            },
            "webchat": {
                "name": "💬 Web Chat",
                "directory": "web_chat",
                "command": [sys.executable, "main.py"],
                "port": get_service_port("WEB-CHAT"),
                "health_url": f"http://localhost:{get_service_port('WEB-CHAT')}/health",
                "process": None,
                "restart_count": 0,
                "last_restart": 0,
                "essential": True
            },
            "watchdog": {
                "name": "🐕 Watchdog",
                "directory": "watchdog",
                "command": [sys.executable, "main.py"],
                "port": get_service_port("WATCHDOG"),
                "health_url": f"http://localhost:{get_service_port('WATCHDOG')}/health",
                "process": None,
                "restart_count": 0,
                "last_restart": 0,
                "essential": False
            }
        }
        
        self.shutdown_requested = False
        self.browser_launched = False
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("🏭 DEEPLICA PRODUCTION ORCHESTRATOR INITIALIZED")
        logger.info("🔗 USING REAL MONGODB ATLAS DATABASE")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"🛑 RECEIVED SIGNAL {signum} - SHUTTING DOWN")
        self.shutdown_requested = True
    
    async def start_all_services(self) -> bool:
        """Start all services with maximum resilience"""
        logger.info("🚀 STARTING DEEPLICA - PRODUCTION MODE WITH REAL DATABASE")
        logger.info("=" * 80)
        
        try:
            # Phase 1: Clean up ports
            await self._cleanup_ports()
            
            # Phase 2: Start services in dependency order
            startup_order = ["backend", "dispatcher", "webchat", "watchdog"]
            
            for service_id in startup_order:
                if self.shutdown_requested:
                    logger.warning("🛑 SHUTDOWN REQUESTED - ABORTING STARTUP")
                    return False
                
                await self._start_service(service_id)
                await asyncio.sleep(3)  # Brief pause between services
            
            logger.info("🎉 ALL SERVICES LAUNCHED SUCCESSFULLY!")
            logger.info("🔄 Starting continuous monitoring and auto-restart...")
            
            # Phase 3: Monitor services continuously
            await self._monitor_services_forever()
            
            return True
            
        except Exception as e:
            logger.error(f"💥 ORCHESTRATOR ERROR: {e}")
            return False
    
    async def _cleanup_ports(self):
        """Clean up ports for all services"""
        logger.info("🧹 CLEANING UP PORTS...")
        
        for service_id, service in self.services.items():
            logger.info(f"🔍 Checking port {service['port']} for {service['name']}")
            
            if not ensure_service_port_free(service_id.upper().replace("_", "-")):
                logger.warning(f"⚠️ Port {service['port']} was in use - cleaned up")
            else:
                logger.info(f"✅ Port {service['port']} is free")
        
        logger.info("✅ PORT CLEANUP COMPLETE")
    
    async def _start_service(self, service_id: str):
        """Start a single service with resilience"""
        service = self.services[service_id]
        logger.info(f"🚀 STARTING {service['name']}")
        
        try:
            # Set working directory
            cwd = Path(PROJECT_ROOT) / service["directory"]
            
            # Set environment
            env = os.environ.copy()
            env["PYTHONPATH"] = PROJECT_ROOT
            env["SERVICE_NAME"] = service_id.upper().replace("_", "-")

            # FORCE REAL DATABASE MODE FOR ALL SERVICES - USE MONGODB ATLAS
            env["USE_MOCK_DATABASE"] = "false"
            env["FORCE_MOCK_DATABASE"] = "false"
            env["USE_REAL_DATABASE"] = "true"
            env["MONGODB_CONNECTION_STRING"] = "mongodb+srv://deeplica-db:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"
            env["MONGODB_DATABASE"] = "deeplica-dev"
            env["WAIT_FOR_BACKEND"] = "true"  # Let services handle their own waiting

            # Ensure .env file is loaded
            env["DOTENV_PATH"] = os.path.join(PROJECT_ROOT, ".env")

            # Log real database enforcement
            logger.info(f"🏭 ENFORCING REAL DATABASE MODE for {service['name']}")
            
            # Launch process
            logger.info(f"📝 Command: {' '.join(service['command'])}")
            logger.info(f"📁 Working directory: {cwd}")
            
            process = subprocess.Popen(
                service["command"],
                cwd=str(cwd),
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1
            )
            
            service["process"] = process
            service["last_restart"] = time.time()
            
            logger.info(f"✅ {service['name']} launched with PID: {process.pid}")
            
            # Start output monitoring in background
            asyncio.create_task(self._monitor_service_output(service_id))
            
        except Exception as e:
            logger.error(f"💥 FAILED TO START {service['name']}: {e}")
            service["restart_count"] += 1
    
    async def _monitor_service_output(self, service_id: str):
        """Monitor service output in background"""
        service = self.services[service_id]
        process = service["process"]
        
        if not process:
            return
        
        try:
            while process.poll() is None and not self.shutdown_requested:
                line = process.stdout.readline()
                if line:
                    line = line.strip()
                    # Only log important messages to avoid spam
                    if any(keyword in line.lower() for keyword in ['error', 'exception', 'failed', 'ready', 'started', 'browser']):
                        logger.info(f"[{service_id.upper()}] {line}")
                        
                        # Auto-launch browser when web chat is ready
                        if service_id == "webchat" and "browser will auto-launch" in line.lower() and not self.browser_launched:
                            await self._launch_browser()
                
                await asyncio.sleep(0.1)
        except Exception as e:
            logger.debug(f"Output monitoring error for {service_id}: {e}")
    
    async def _launch_browser(self):
        """Launch browser to DeepChat"""
        if self.browser_launched:
            return
            
        try:
            import webbrowser
            url = f"http://localhost:{get_service_port('WEB-CHAT')}"
            webbrowser.open(url)
            self.browser_launched = True
            logger.info(f"🌐 BROWSER LAUNCHED: {url}")
        except Exception as e:
            logger.warning(f"⚠️ Could not auto-launch browser: {e}")
    
    async def _monitor_services_forever(self):
        """Monitor all services and restart if needed"""
        logger.info("👁️ STARTING CONTINUOUS SERVICE MONITORING")

        # Track service stability
        stable_services = set()

        while not self.shutdown_requested:
            for service_id, service in self.services.items():
                process = service["process"]

                if process and process.poll() is not None:
                    # Service has exited
                    exit_code = process.returncode

                    # Check if this is expected behavior (services completing normally)
                    if exit_code == 0:
                        # Normal completion - this is expected for our current service design
                        logger.debug(f"🔄 {service['name']} completed normally (exit code: 0)")

                        # For essential services, restart with longer intervals to reduce cycling
                        if service.get("essential", True):
                            time_since_last = time.time() - service["last_restart"]
                            min_wait = 30 if service_id in stable_services else 15

                            if time_since_last < min_wait:
                                wait_time = min_wait - time_since_last
                                logger.info(f"⏳ {service['name']} waiting {wait_time:.1f}s before restart")
                                await asyncio.sleep(wait_time)

                            # Restart the service
                            await self._restart_service(service_id)
                            stable_services.add(service_id)
                        else:
                            logger.info(f"✅ {service['name']} completed successfully")

                    elif exit_code == -9:
                        # Service was killed (likely by system or user)
                        logger.warning(f"⚠️ {service['name']} was killed (signal 9)")
                        await self._restart_service(service_id)

                    else:
                        # Unexpected exit code
                        logger.error(f"💥 {service['name']} crashed (exit code: {exit_code})")
                        await self._restart_service(service_id)

                elif process and service_id == "backend":
                    # Periodically check backend health (less frequently to reduce noise)
                    if time.time() % 60 < 1:  # Check every 60 seconds
                        await self._check_service_health(service_id)

            await asyncio.sleep(10)  # Check every 10 seconds (reduced frequency)
    
    async def _restart_service(self, service_id: str):
        """Restart a service with intelligent backoff"""
        service = self.services[service_id]
        service["restart_count"] += 1

        # Implement exponential backoff for problematic services
        if service["restart_count"] > 50:
            logger.error(f"❌ {service['name']} has restarted {service['restart_count']} times - disabling")
            return

        # Calculate backoff delay based on restart count
        if service["restart_count"] > 10:
            backoff_delay = min(60, service["restart_count"] * 2)  # Max 60 seconds
            logger.info(f"🔄 RESTARTING {service['name']} (attempt {service['restart_count']}) with {backoff_delay}s backoff")
            await asyncio.sleep(backoff_delay)
        else:
            logger.info(f"🔄 RESTARTING {service['name']} (attempt {service['restart_count']})")

        # Clean up old process
        if service["process"]:
            try:
                service["process"].terminate()
                await asyncio.sleep(1)  # Give it time to terminate
            except:
                pass

        # Restart
        await self._start_service(service_id)
    
    async def _check_service_health(self, service_id: str):
        """Check service health via HTTP"""
        service = self.services[service_id]
        
        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.get(service["health_url"], timeout=5) as response:
                    if response.status == 200:
                        logger.debug(f"✅ {service['name']} health check passed")
                    else:
                        logger.warning(f"⚠️ {service['name']} health check failed: HTTP {response.status}")
        except Exception as e:
            logger.debug(f"🔄 {service['name']} health check error: {e}")
    
    async def shutdown_all_services(self):
        """Shutdown all services gracefully"""
        logger.info("🛑 SHUTTING DOWN ALL SERVICES...")
        
        for service_id, service in self.services.items():
            process = service["process"]
            if process:
                try:
                    logger.info(f"🛑 Stopping {service['name']}...")
                    process.terminate()
                    
                    # Wait for graceful shutdown
                    try:
                        process.wait(timeout=10)
                    except subprocess.TimeoutExpired:
                        logger.warning(f"⚠️ Force killing {service['name']}")
                        process.kill()
                        process.wait()
                    
                    logger.info(f"✅ {service['name']} stopped")
                except Exception as e:
                    logger.error(f"❌ Error stopping {service['name']}: {e}")
        
        logger.info("✅ ALL SERVICES STOPPED")

async def main():
    """Main function"""
    orchestrator = DeepplicaOrchestrator()
    
    try:
        success = await orchestrator.start_all_services()
        if success:
            logger.info("🎉 DEEPLICA STARTED SUCCESSFULLY!")
        else:
            logger.error("❌ DEEPLICA STARTUP FAILED!")
            return 1
    except KeyboardInterrupt:
        logger.info("🛑 INTERRUPTED BY USER")
    except Exception as e:
        logger.error(f"💥 ORCHESTRATOR ERROR: {e}")
        return 1
    finally:
        await orchestrator.shutdown_all_services()
    
    return 0

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(result)
    except Exception as e:
        print(f"💥 FATAL ERROR: {e}")
        sys.exit(1)
