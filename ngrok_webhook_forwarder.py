#!/usr/bin/env python3
"""
🌐 NGROK WEBHOOK FORWARDER
Runs on constant port 8080 and forwards webhook calls to the phone webhook monitor
"""

import os
import sys
from pathlib import Path
from flask import Flask, request, Response
import requests

# Add project root to path
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

# Import port management
from shared.port_manager import get_service_port

app = Flask(__name__)

# Constant port for ngrok service
NGROK_PORT = 8080

def get_phone_webhook_url():
    """Get the phone webhook monitor URL"""
    try:
        phone_webhook_port = get_service_port('webhook-server')
        return f"http://localhost:{phone_webhook_port}"
    except:
        return "http://localhost:8010"  # fallback

@app.route('/webhook/voice', methods=['POST', 'GET'])
def forward_voice_webhook():
    """Forward voice webhook to phone webhook monitor"""
    try:
        phone_webhook_url = get_phone_webhook_url()
        
        print(f"🔄 Forwarding voice webhook to {phone_webhook_url}/webhook/voice")
        print(f"📋 Method: {request.method}")
        print(f"📋 Headers: {dict(request.headers)}")
        print(f"📋 Form data: {dict(request.form)}")
        
        # Forward the request
        if request.method == 'POST':
            response = requests.post(
                f"{phone_webhook_url}/webhook/voice",
                data=request.form,
                headers={'Content-Type': 'application/x-www-form-urlencoded'},
                timeout=10
            )
        else:
            response = requests.get(
                f"{phone_webhook_url}/webhook/voice",
                params=request.args,
                timeout=10
            )
        
        print(f"✅ Forwarded successfully, response: {response.status_code}")
        
        return Response(
            response.content,
            status=response.status_code,
            headers=dict(response.headers)
        )
        
    except Exception as e:
        print(f"❌ Error forwarding webhook: {e}")
        
        # Fallback TwiML response
        fallback_twiml = '''<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="alice" language="en-US">
        Hello Eran! This is DEEPLICA. The webhook forwarder encountered an issue. 
        Please wait 3 seconds then say Eran 5 times. Thank you!
    </Say>
    <Pause length="18"/>
    <Say voice="alice" language="en-US">Goodbye!</Say>
</Response>'''
        
        return Response(fallback_twiml, mimetype='text/xml')

@app.route('/webhook/recording', methods=['POST'])
def forward_recording_webhook():
    """Forward recording webhook to phone webhook monitor"""
    try:
        phone_webhook_url = get_phone_webhook_url()
        
        print(f"🔄 Forwarding recording webhook to {phone_webhook_url}/webhook/recording")
        
        response = requests.post(
            f"{phone_webhook_url}/webhook/recording",
            data=request.form,
            headers={'Content-Type': 'application/x-www-form-urlencoded'},
            timeout=10
        )
        
        print(f"✅ Recording forwarded successfully")
        
        return Response(
            response.content,
            status=response.status_code,
            headers=dict(response.headers)
        )
        
    except Exception as e:
        print(f"❌ Error forwarding recording webhook: {e}")
        return Response('OK', mimetype='text/plain')

@app.route('/webhook/transcription', methods=['POST'])
def forward_transcription_webhook():
    """Forward transcription webhook to phone webhook monitor"""
    try:
        phone_webhook_url = get_phone_webhook_url()
        
        print(f"🔄 Forwarding transcription webhook to {phone_webhook_url}/webhook/transcription")
        
        response = requests.post(
            f"{phone_webhook_url}/webhook/transcription",
            data=request.form,
            headers={'Content-Type': 'application/x-www-form-urlencoded'},
            timeout=10
        )
        
        print(f"✅ Transcription forwarded successfully")
        
        return Response(
            response.content,
            status=response.status_code,
            headers=dict(response.headers)
        )
        
    except Exception as e:
        print(f"❌ Error forwarding transcription webhook: {e}")
        return Response('OK', mimetype='text/plain')

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    phone_webhook_url = get_phone_webhook_url()
    return {
        "status": "healthy", 
        "service": "ngrok-webhook-forwarder",
        "port": NGROK_PORT,
        "forwarding_to": phone_webhook_url
    }, 200

@app.route('/', methods=['GET'])
def root():
    """Root endpoint"""
    phone_webhook_url = get_phone_webhook_url()
    return {
        "message": "DEEPLICA Ngrok Webhook Forwarder",
        "status": "running",
        "port": NGROK_PORT,
        "forwarding_to": phone_webhook_url,
        "endpoints": [
            "/webhook/voice",
            "/webhook/recording", 
            "/webhook/transcription",
            "/health"
        ]
    }, 200

if __name__ == '__main__':
    print("🌐 DEEPLICA NGROK WEBHOOK FORWARDER")
    print("=" * 50)
    print(f"🚀 Starting forwarder on CONSTANT port {NGROK_PORT}...")
    print(f"🔄 Forwarding to phone webhook monitor at {get_phone_webhook_url()}")
    print("📞 Webhook endpoints:")
    print(f"   • Voice: http://localhost:{NGROK_PORT}/webhook/voice")
    print(f"   • Recording: http://localhost:{NGROK_PORT}/webhook/recording")
    print(f"   • Transcription: http://localhost:{NGROK_PORT}/webhook/transcription")
    print(f"📊 Health: http://localhost:{NGROK_PORT}/health")
    print()
    print("🌐 This service runs on CONSTANT port 8080 for ngrok")
    print("🔄 All webhooks are forwarded to the phone webhook monitor")
    print()
    
    app.run(
        host='0.0.0.0',
        port=NGROK_PORT,
        debug=False,
        threaded=True
    )
