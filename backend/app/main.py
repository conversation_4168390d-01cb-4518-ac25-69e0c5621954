"""
Deeplica v0 Backend - FastAPI Application
Main entry point for the mission orchestration system.
"""

import os
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Import process registry for mandatory registration
import sys
import os

# Add the project root to Python path (works from any directory)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Add backend directory to path for local imports
backend_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if backend_dir not in sys.path:
    sys.path.insert(0, backend_dir)

from app.services.database import DatabaseService
from app.api.routes import router


from shared.unified_logging import get_backend_logger
from shared.port_manager import get_service_port, get_service_host, ensure_service_port_free
from shared.port_cache import get_port_cache
from shared.resilience_utils import bulletproof_retry, wait_for_service, bulletproof_service_wrapper

# Initialize unified logger
logger = get_backend_logger()

# Initialize port cache for efficient port management
port_cache = get_port_cache("backend")


def get_dispatcher_client(app):
    """Get dispatcher client with lazy initialization to avoid circular dependency"""
    if app.state.dispatcher_client is None:
        try:
            from app.services.dispatcher_client import DispatcherClient
            app.state.dispatcher_client = DispatcherClient()
            logger.info("✅ Dispatcher client initialized lazily", module="BACKEND-API", routine="get_dispatcher_client")
        except Exception as e:
            logger.warning(f"⚠️ Failed to initialize dispatcher client: {e}", module="BACKEND-API", routine="get_dispatcher_client")
            return None
    return app.state.dispatcher_client


async def initialize_backend_services(app: FastAPI):
    """Initialize backend services ONLY - does NOT start other microservices"""
    import asyncio
    from app.services.llm_service import LLMService
    from app.services.dispatcher_client import DispatcherClient

    logger.info("🧠 SMART INIT: Initializing optimized database connection...", module="main", routine="initialize_backend_services")
    logger.info("💪 EVERLASTING: Will keep trying INDEFINITELY until MongoDB is ready...", module="main", routine="initialize_backend_services")
    logger.info("🛡️ BULLETPROOF: Backend API will NEVER crash - it will wait as long as needed!", module="main", routine="initialize_backend_services")

    # 🧠 SMART DATABASE INITIALIZATION with optimized retry strategy
    retry_count = 0
    max_quick_retries = 10  # Quick retries for temporary issues

    while True:
        try:
            retry_count += 1
            if retry_count > 1:
                logger.info(f"🧠 SMART RETRY: Database connection attempt #{retry_count}", module="main", routine="initialize_backend_services")

            # 🧠 OPTIMIZED: Create database service with enhanced configuration
            db_service = DatabaseService()
            await db_service.connect()
            app.state.db = db_service
            app.state.db_health_check_interval = 60  # Health check every minute
            app.state.degraded_mode = False

            logger.info("✅ SMART INIT: Database connected with optimized configuration", module="main", routine="initialize_backend_services")
            break

        except Exception as e:
            logger.debug(f"🔄 Connection attempt #{retry_count} failed: {type(e).__name__}: {e}", module="database", routine="initialize_backend_services")

            # 🧠 INTELLIGENT BACKOFF: Adaptive retry strategy
            if retry_count <= max_quick_retries:
                # Quick retries for temporary network issues
                wait_time = min(retry_count * 1.5, 15)  # 1.5, 3, 4.5, 6, 7.5, 9, 10.5, 12, 13.5, 15
                logger.debug(f"🧠 QUICK RETRY: Retrying in {wait_time:.1f} seconds (quick retry #{retry_count})", module="database", routine="initialize_backend_services")
            else:
                # Slower retries for persistent issues
                wait_time = min((retry_count - max_quick_retries) * 10, 60)  # 10, 20, 30, 40, 50, 60
                logger.debug(f"🧠 SLOW RETRY: Retrying in {wait_time} seconds (persistent issue retry)", module="database", routine="initialize_backend_services")

            await asyncio.sleep(wait_time)

    # Initialize singleton services with bulletproof error handling
    service_retry_count = 0
    while True:
        try:
            service_retry_count += 1
            if service_retry_count > 1:
                logger.info(f"🔄 Service initialization attempt #{service_retry_count}", module="main", routine="initialize_backend_services")

            from app.services.llm_service import LLMService
            # Don't initialize DispatcherClient during startup to avoid circular dependency
            # It will be initialized lazily when needed

            app.state.llm_service = LLMService()
            app.state.dispatcher_client = None  # Initialize lazily to avoid circular dependency

            # Backend is now ready - declare it immediately
            app.state.ready = True
            logger.info("✅ Backend API is READY and accepting requests!", module="main", routine="initialize_backend_services")
            logger.info("🔄 Other microservices should now be able to connect to this backend", module="main", routine="initialize_backend_services")
            logger.info("📋 Backend provides: Database, LLM, Core APIs", module="main", routine="initialize_backend_services")
            logger.info("⚠️ Note: Start other microservices manually - backend does NOT start them", module="main", routine="initialize_backend_services")
            break

        except Exception as e:
            logger.error(f"❌ Failed to initialize services (attempt #{service_retry_count}): {e}", module="main", routine="initialize_backend_services", exc_info=True)

            # Exponential backoff for service initialization
            wait_time = min(service_retry_count * 3, 60)  # Max 60 seconds
            logger.info(f"🔄 Will retry service initialization in {wait_time} seconds...", module="main", routine="initialize_backend_services")
            await asyncio.sleep(wait_time)
            # Continue the infinite loop


# Removed start_other_services_in_background - backend does NOT start other services


async def smart_health_monitor(app: FastAPI):
    """🧠 SMART HEALTH MONITORING - Advanced health tracking and self-healing"""
    import asyncio
    from datetime import datetime, timedelta

    health_check_count = 0
    last_health_report = datetime.now()

    while True:
        try:
            await asyncio.sleep(30)  # Health check every 30 seconds
            health_check_count += 1

            # Update health metrics
            if hasattr(app.state, 'health_metrics'):
                app.state.health_metrics["last_health_check"] = datetime.now()

                # Check database health
                if hasattr(app.state, 'db') and app.state.db:
                    try:
                        # Simple ping to check database connectivity
                        await app.state.db.ping()
                        app.state.health_metrics["database_health"] = "healthy"
                    except Exception as e:
                        app.state.health_metrics["database_health"] = f"unhealthy: {type(e).__name__}"
                        logger.warning(f"🏥 HEALTH: Database health check failed: {e}", module="main", routine="smart_health_monitor")

                # Report health status every 5 minutes
                if datetime.now() - last_health_report >= timedelta(minutes=5):
                    uptime = datetime.now() - app.state.health_metrics["startup_time"]
                    logger.info(f"🏥 HEALTH REPORT: Uptime {uptime}, DB: {app.state.health_metrics['database_health']}, Requests: {app.state.health_metrics.get('request_count', 0)}, Errors: {app.state.health_metrics.get('error_count', 0)}", module="main", routine="smart_health_monitor")
                    last_health_report = datetime.now()

        except asyncio.CancelledError:
            logger.info("🏥 HEALTH: Health monitor cancelled (normal during shutdown)", module="main", routine="smart_health_monitor")
            break
        except Exception as e:
            logger.warning(f"🏥 HEALTH: Health monitor error: {e}", module="main", routine="smart_health_monitor")
            # Continue monitoring - don't let errors stop health checks


async def background_heartbeat():
    """🧠 SMART HEARTBEAT - Optimized background task with intelligent monitoring"""
    import asyncio

    heartbeat_count = 0
    while True:
        try:
            await asyncio.sleep(300)  # 5 minutes
            heartbeat_count += 1
            logger.info(f"💓 EVERLASTING HEARTBEAT #{heartbeat_count} - service optimized and running", module="main", routine="background_heartbeat")
        except asyncio.CancelledError:
            logger.info("💓 HEARTBEAT: Background task was cancelled (normal during shutdown)", module="main", routine="background_heartbeat")
            break
        except Exception as e:
            logger.warning(f"💓 HEARTBEAT: Background heartbeat error: {e}", module="main", routine="background_heartbeat")
            # Continue the loop - don't let errors stop the heartbeat


@asynccontextmanager
async def lifespan(app: FastAPI):
    """🧠 SMART LIFESPAN MANAGER - Optimized application lifespan with health monitoring"""
    import asyncio
    from datetime import datetime

    # Startup
    logger.info("🚀 Starting Deeplica V1 Backend", module="main", routine="lifespan")
    logger.info("💪 Backend API will start immediately and handle database connection in background - NEVER crashes!", module="main", routine="lifespan")

    # 🧠 SMART STATE INITIALIZATION: Initialize optimized app state with health metrics
    logger.info("🧠 SMART INIT: Initializing optimized app state...", module="main", routine="lifespan")
    app.state.ready = False
    app.state.db = None
    app.state.llm_service = None
    app.state.dispatcher_client = None

    # 🧠 HEALTH METRICS: Initialize comprehensive health tracking
    app.state.health_metrics = {
        "startup_time": datetime.now(),
        "request_count": 0,
        "error_count": 0,
        "last_health_check": datetime.now(),
        "database_health": "initializing",
        "service_health": "starting"
    }

    logger.info("🧠 SMART INIT: App state and health metrics initialized", module="main", routine="lifespan")

    # Start background task to initialize services - this will NEVER crash the service
    logger.info("🐛 DEBUG: About to start background task...", module="main", routine="lifespan")
    try:
        # Wrap the background task in bulletproof error handling
        logger.info("🐛 DEBUG: Defining bulletproof_initialize function...", module="main", routine="lifespan")
        async def bulletproof_initialize():
            logger.info("🐛 DEBUG: Inside bulletproof_initialize function...", module="main", routine="lifespan")
            try:
                logger.info("🐛 DEBUG: About to call initialize_backend_services...", module="main", routine="lifespan")
                await initialize_backend_services(app)
                logger.info("🐛 DEBUG: initialize_backend_services completed successfully", module="main", routine="lifespan")

                # 🛡️ BULLETPROOF: Keep the background task alive indefinitely
                logger.info("🔄 Background task will now run indefinitely to keep service alive", module="main", routine="lifespan")

                # 🧠 SMART BACKGROUND TASKS: Start optimized monitoring tasks
                app.state.health_metrics["service_health"] = "healthy"

                # Start smart health monitoring
                health_task = asyncio.create_task(smart_health_monitor(app))
                heartbeat_task = asyncio.create_task(background_heartbeat())

                logger.info("🧠 SMART TASKS: Health monitoring and heartbeat tasks started", module="main", routine="lifespan")

                # Keep the background tasks alive indefinitely
                try:
                    # Wait for tasks to complete (they should run forever)
                    await asyncio.gather(health_task, heartbeat_task)

                except asyncio.CancelledError:
                    logger.info("🛑 Background task cancelled - service shutting down", module="main", routine="lifespan")
                    raise  # Re-raise to properly handle cancellation
                except Exception as e:
                    logger.error(f"💥 CRITICAL: Background task infinite loop failed: {type(e).__name__}: {e}", module="main", routine="lifespan")
                    # Even if the infinite loop fails, try to restart it
                    logger.info("🔄 Restarting infinite loop...", module="main", routine="lifespan")
                    raise  # This will trigger the retry logic

            except Exception as e:
                logger.error(f"🐛 DEBUG: Exception in bulletproof_initialize: {type(e).__name__}: {e}", module="main", routine="lifespan")
                logger.error(f"💥 Background initialization failed: {e}", module="main", routine="lifespan")
                # Set ready to False but don't crash
                app.state.ready = False
                logger.info("🔄 Backend API will continue running and retry initialization", module="main", routine="lifespan")

                # Keep trying indefinitely even after failure
                try:
                    while True:
                        await asyncio.sleep(30)  # Retry every 30 seconds
                        logger.info("🔄 Retrying backend initialization...", module="main", routine="lifespan")
                        try:
                            await initialize_backend_services(app)
                            logger.info("✅ Backend initialization retry successful!", module="main", routine="lifespan")
                            # Continue with normal heartbeat
                            while True:
                                await asyncio.sleep(60)
                                logger.debug("🔄 Background task heartbeat - service running", module="main", routine="lifespan")
                        except Exception as retry_error:
                            logger.error(f"🔄 Retry failed: {retry_error}", module="main", routine="lifespan")
                            continue  # Keep retrying
                except asyncio.CancelledError:
                    logger.info("🛑 Background task cancelled during retry - service shutting down", module="main", routine="lifespan")
                    raise

        logger.info("🐛 DEBUG: Creating background task...", module="main", routine="lifespan")
        background_task = asyncio.create_task(bulletproof_initialize())
        app.state.background_task = background_task

        # Add a callback to track when the background task completes (it should NEVER complete)
        def on_background_task_done(task):
            if task.cancelled():
                logger.info("🛑 Background task was cancelled (normal during shutdown)", module="main", routine="lifespan")
            elif task.exception():
                logger.error(f"💥 CRITICAL: Background task failed with exception: {task.exception()}", module="main", routine="lifespan")
            else:
                logger.error("💥 CRITICAL: Background task completed successfully - THIS SHOULD NEVER HAPPEN!", module="main", routine="lifespan")
                logger.error("💥 CRITICAL: Background tasks should run indefinitely to keep service alive", module="main", routine="lifespan")

        background_task.add_done_callback(on_background_task_done)

        logger.info("✅ Backend API started - initializing database connection in background", module="main", routine="lifespan")
        logger.info("🐛 DEBUG: Background task created successfully", module="main", routine="lifespan")
    except Exception as e:
        logger.error(f"🐛 DEBUG: Exception creating background task: {type(e).__name__}: {e}", module="main", routine="lifespan")
        logger.error(f"Failed to start background task: {type(e).__name__}: {e}", module="main", routine="lifespan", exc_info=True)
        # Even if background task fails, don't crash the service
        app.state.ready = False
        logger.info("Background task failed but Backend API will continue running", module="main", routine="lifespan")
        # Don't re-raise - allow the service to continue

    logger.info("🐛 DEBUG: About to yield in lifespan...", module="main", routine="lifespan")

    try:
        yield
        logger.info("🐛 DEBUG: Returned from yield in lifespan (shutdown phase)...", module="main", routine="lifespan")
    except Exception as e:
        logger.error(f"🐛 DEBUG: Exception during lifespan yield: {type(e).__name__}: {e}", module="main", routine="lifespan")

    # Shutdown - BULLETPROOF
    try:
        logger.info("🛑 Shutting down Deeplica v0 Backend", module="main", routine="lifespan")
        if hasattr(app.state, 'background_task'):
            try:
                app.state.background_task.cancel()
            except Exception as e:
                logger.debug(f"Error canceling background task: {type(e).__name__}: {e}", module="BACKEND-API", routine="lifespan")
        # Removed orchestrator shutdown - backend does NOT manage other services
        if hasattr(app.state, 'dispatcher_client') and app.state.dispatcher_client:
            try:
                await app.state.dispatcher_client.close()
            except Exception as e:
                logger.debug(f"Error closing dispatcher client: {type(e).__name__}: {e}", module="BACKEND-API", routine="lifespan")
        if hasattr(app.state, 'db') and app.state.db:
            try:
                await app.state.db.disconnect()
            except Exception as e:
                logger.debug(f"Error disconnecting database: {type(e).__name__}: {e}", module="BACKEND-API", routine="lifespan")
    except Exception as shutdown_error:
        logger.error(f"💥 LIFESPAN SHUTDOWN ERROR: {shutdown_error}", module="main", routine="lifespan")
        # Don't re-raise - allow graceful shutdown


# Create FastAPI app
app = FastAPI(
    title="Deeplica v0 API",
    description="AI Mission Orchestration System",
    version="0.1.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(router, prefix="/api/v1")


@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "service": "Deeplica v0 Backend",
        "status": "healthy",
        "version": "0.1.0"
    }


@app.get("/health")
async def health_check():
    """Detailed health check - shows database readiness status"""
    try:
        # Check database connection and readiness
        database_ready = getattr(app.state, 'ready', False)
        db_connected = hasattr(app.state, 'db') and app.state.db is not None

        return {
            "status": "healthy",
            "service": "Deeplica V1 Backend API",
            "version": "0.1.0",
            "database_ready": database_ready,
            "database_connected": db_connected,
            "message": "Backend API is running" + (" and fully ready" if database_ready else " but initializing database connection")
        }
    except Exception as e:
        logger.error(f"Health check failed in backend.app.main.health_check: {type(e).__name__}: {e}", module="BACKEND-API", routine="health_check")
        # Don't crash on health check failure
        return {
            "status": "healthy",
            "service": "Deeplica V1 Backend API",
            "version": "0.1.0",
            "database_ready": False,
            "database_connected": False,
            "message": "Backend API is running but health check encountered an error"
        }


@app.get("/debug/state")
async def debug_state():
    """Debug endpoint to check app state"""
    try:
        database_ready = getattr(app.state, 'ready', False)
        db_service = getattr(app.state, 'db', None)
        dispatcher_client_raw = getattr(app.state, 'dispatcher_client', None)
        dispatcher_client_lazy = get_dispatcher_client(app)

        return {
            "database_ready": database_ready,
            "db_service_exists": db_service is not None,
            "dispatcher_client_raw": dispatcher_client_raw is not None,
            "dispatcher_client_lazy": dispatcher_client_lazy is not None,
            "app_state_keys": list(vars(app.state).keys()) if hasattr(app, 'state') else []
        }
    except Exception as e:
        return {
            "error": str(e),
            "type": type(e).__name__
        }


@app.get("/ready")
async def readiness_check():
    """Readiness check - returns 200 when Backend API is ready for service connections"""
    try:
        # Check basic readiness - be more lenient about LLM service
        llm_ready = hasattr(app.state, 'llm_service') and app.state.llm_service is not None

        # Database can be connecting but we allow services to connect after reasonable time
        database_ready = getattr(app.state, 'ready', False)
        db_connected = hasattr(app.state, 'db') and app.state.db is not None

        # Check how long we've been trying to connect to database
        startup_time = getattr(app.state, 'startup_time', None)
        if startup_time is None:
            import time
            app.state.startup_time = time.time()
            startup_time = app.state.startup_time

        import time
        elapsed_time = time.time() - startup_time

        # Return ready immediately if both database and LLM are ready
        if database_ready and llm_ready:
            return {
                "status": "ready",
                "message": "Backend API is ready to accept connections",
                "database_status": "connected",
                "llm_status": "ready",
                "elapsed_time": round(elapsed_time, 1)
            }

        # Allow services to connect if database is connected and we have basic services
        # OR if we've been running for more than 10 seconds (reduced from 30)
        elif (db_connected and llm_ready) or elapsed_time > 10:
            return {
                "status": "ready",
                "message": "Backend API is ready to accept connections",
                "database_status": "connected" if database_ready else "connecting",
                "llm_status": "ready" if llm_ready else "initializing",
                "elapsed_time": round(elapsed_time, 1)
            }
        else:
            # Return 503 if not ready yet
            from fastapi import HTTPException
            raise HTTPException(
                status_code=503,
                detail=f"Backend API still initializing (llm_ready={llm_ready}, database_ready={database_ready}, elapsed={round(elapsed_time, 1)}s, waiting for 10s minimum)"
            )
    except Exception as e:
        logger.error(f"Readiness check failed: {e}", module="BACKEND-API", routine="readiness_check()")
        from fastapi import HTTPException
        raise HTTPException(status_code=503, detail="Backend API is not ready")


@app.get("/system/status")
async def system_status():
    """System status endpoint for CLI compatibility"""
    try:
        # Check all system components
        database_ready = getattr(app.state, 'ready', False)
        db_connected = hasattr(app.state, 'db') and app.state.db is not None
        llm_ready = hasattr(app.state, 'llm_service') and app.state.llm_service is not None

        return {
            "status": "operational" if database_ready else "initializing",
            "components": {
                "database": {
                    "status": "ready" if database_ready else "connecting",
                    "connected": db_connected
                },
                "llm_service": {
                    "status": "ready" if llm_ready else "initializing"
                },
                "api": {
                    "status": "ready"
                }
            },
            "message": "System is " + ("fully operational" if database_ready else "initializing - please wait")
        }
    except Exception as e:
        logger.error(f"System status check failed in backend.app.main.system_status: {type(e).__name__}: {e}", module="BACKEND-API", routine="system_status")
        return {
            "status": "error",
            "components": {
                "database": {"status": "unknown", "connected": False},
                "llm_service": {"status": "unknown"},
                "api": {"status": "ready"}
            },
            "message": "System status check encountered an error"
        }





if __name__ == "__main__":
    # Set distinctive process name for easy identification
    try:
        import setproctitle
        setproctitle.setproctitle("DEEPLICA-BACKEND-API")
        logger.info("BACKEND-API", module="BACKEND-API:startup", routine="PROCESS | Process name set to: DEEPLICA")
    except ImportError:
        logger.warning("process name unchanged", module="BACKEND-API:startup", routine="PROCESS | setproctitle not available")

    # Set terminal title and clear identification banner - FORCE RENAME EVEN IF ALREADY NAMED
    service_name = os.getenv("SERVICE_NAME", "BACKEND-API")

    # Multiple methods to ensure terminal gets renamed
    print(f"\033]0;🌐 {service_name}\007", end="")  # xterm title
    print(f"\033]2;🌐 {service_name}\007", end="")  # window title
    print(f"\033]1;🌐 {service_name}\007", end="")  # icon title

    # Also try VS Code specific terminal naming
    import sys
    if hasattr(sys, 'ps1') or hasattr(sys, 'ps2'):
        # Running in interactive mode
        try:
            import os
            os.system(f'echo -ne "\\033]0;🌐 {service_name}\\007"')
        except:
            pass

    from datetime import datetime
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"{timestamp} - [INFO] - Svc: {service_name}, Mod: BACKEND-API, Cod: startup, msg: {'='*80}")
    print(f"{timestamp} - [INFO] - Svc: {service_name}, Mod: BACKEND-API, Cod: startup, msg: 🌐 {service_name} TERMINAL")
    print(f"{timestamp} - [INFO] - Svc: {service_name}, Mod: BACKEND-API, Cod: startup, msg: {'='*80}")

def main():
    """🚀 OPTIMIZED EVERLASTING BACKEND API - Enterprise-grade resilience"""
    import signal
    import sys
    import threading
    import time
    from datetime import datetime, timedelta

    # 🧠 SMART HEALTH MONITORING
    class HealthMonitor:
        def __init__(self):
            self.last_heartbeat = datetime.now()
            self.restart_count = 0
            self.consecutive_failures = 0
            self.total_uptime = timedelta()
            self.start_time = datetime.now()

        def record_heartbeat(self):
            self.last_heartbeat = datetime.now()

        def record_restart(self):
            self.restart_count += 1
            self.consecutive_failures += 1

        def record_success(self):
            self.consecutive_failures = 0

        def get_health_status(self):
            uptime = datetime.now() - self.start_time
            return {
                "uptime": str(uptime),
                "restart_count": self.restart_count,
                "consecutive_failures": self.consecutive_failures,
                "last_heartbeat": self.last_heartbeat.isoformat(),
                "health_score": max(0, 100 - (self.consecutive_failures * 10))
            }

    health_monitor = HealthMonitor()

    # 🛡️ ADVANCED SIGNAL HANDLING - Block uvicorn signals
    def bulletproof_signal_handler(signum, frame):
        logger.warning(f"🛡️ EVERLASTING-API received signal {signum} - IGNORING (bulletproof mode)", module="BACKEND-API", routine="signal_handler")
        logger.warning(f"🛡️ EVERLASTING-API will NEVER stop - use the cleanup script to terminate", module="BACKEND-API", routine="signal_handler")
        health_monitor.record_heartbeat()
        # Don't propagate signal to uvicorn
        return

    # Install bulletproof signal handlers
    signal.signal(signal.SIGTERM, bulletproof_signal_handler)
    signal.signal(signal.SIGINT, bulletproof_signal_handler)
    if hasattr(signal, 'SIGHUP'):
        signal.signal(signal.SIGHUP, bulletproof_signal_handler)

    # 🧠 SMART RESTART LOGIC with exponential backoff
    def calculate_restart_delay(consecutive_failures):
        """Smart restart delay based on failure count"""
        if consecutive_failures == 0:
            return 1  # Immediate restart on first failure
        elif consecutive_failures <= 3:
            return min(consecutive_failures * 2, 10)  # 2, 4, 6 seconds
        elif consecutive_failures <= 10:
            return min(consecutive_failures * 5, 60)  # Up to 60 seconds
        else:
            return 120  # Max 2 minutes for persistent failures

    logger.info("🚀 EVERLASTING BACKEND API - Enterprise-grade resilience initialized", module="BACKEND-API", routine="startup")
    logger.info("🛡️ BULLETPROOF: Advanced signal handlers installed - uvicorn signals blocked", module="BACKEND-API", routine="startup")
    logger.info("Entering main block...", module="BACKEND-API", routine="startup")

    # INFINITE RETRY LOOP - MICROSERVICE SHOULD NEVER GIVE UP
    retry_count = 0
    while True:
        try:
            retry_count += 1
            if retry_count > 1:
                logger.info(f"🔄 Backend API startup attempt #{retry_count}", module="BACKEND-API", routine="startup")

            logger.info("Starting try block...", module="BACKEND-API", routine="startup")
            # Configure logging
            # TEMPORARILY DISABLED FOR DEBUGGING
            # os.makedirs("logs", exist_ok=True)
            # logger.add(
            #     "logs/deeplica_v0.log",
            #     rotation="1 day",
            #     retention="30 days",
            #     level="INFO",
            #     format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
            # )

            # Get configuration from environment
            server_host = os.getenv("HOST", get_service_host("backend"))

            # Ensure backend port is free before starting
            logger.info("🔌 Ensuring backend API port is free...", module="BACKEND-API", routine="startup")

            # Process detection removed per user request - manual management only

            # Handle PORT environment variable safely
            port_env = os.getenv("PORT")
            if port_env and port_env.isdigit():
                server_port = int(port_env)
            else:
                server_port = get_service_port("backend")

            logger.info(f"✅ Backend API will use port {server_port}", module="BACKEND-API", routine="startup")

            debug = os.getenv("DEBUG", "false").lower() == "true"

            logger.info(f"Starting Backend API server on {server_host}:{server_port} (debug={debug})", module="BACKEND-API", routine="startup")



            try:
                # AGGRESSIVELY suppress ALL HTTP request logging
                import logging

                # Disable ALL uvicorn logging completely
                logging.getLogger("uvicorn").disabled = True
                logging.getLogger("uvicorn").propagate = False
                logging.getLogger("uvicorn.access").disabled = True
                logging.getLogger("uvicorn.access").propagate = False
                logging.getLogger("uvicorn.error").disabled = True
                logging.getLogger("uvicorn.error").propagate = False

                # Also disable any other HTTP-related loggers
                logging.getLogger("httpx").disabled = True
                logging.getLogger("httpcore").disabled = True

                # Set all uvicorn loggers to CRITICAL level (highest)
                for logger_name in ["uvicorn", "uvicorn.access", "uvicorn.error", "uvicorn.asgi"]:
                    logger_obj = logging.getLogger(logger_name)
                    logger_obj.setLevel(logging.CRITICAL)
                    logger_obj.disabled = True

                # BULLETPROOF UVICORN STARTUP - NEVER CRASH
                logger.info("🚀 Starting uvicorn server with bulletproof error handling...", module="BACKEND-API", routine="startup")

                # Port cleanup removed per user request - manual management only

                logger.info("🚀 EVERLASTING: Starting optimized uvicorn server...", module="BACKEND-API", routine="startup")
                try:
                    # 🧠 SMART UVICORN CONFIGURATION - Optimized for stability
                    logger.info(f"🧠 SMART CONFIG: host={server_host}, port={server_port}", module="BACKEND-API", routine="startup")

                    # 🛡️ BULLETPROOF UVICORN: Direct uvicorn.run() with signal handling
                    logger.info("🛡️ EVERLASTING: Starting uvicorn with bulletproof configuration", module="BACKEND-API", routine="startup")

                    # 🧠 SMART RESTART: Calculate intelligent restart delay
                    restart_delay = calculate_restart_delay(health_monitor.consecutive_failures)
                    if restart_delay > 1:
                        logger.warning(f"🧠 SMART RESTART: Waiting {restart_delay} seconds before restart (failure #{health_monitor.consecutive_failures})", module="BACKEND-API", routine="startup")
                        time.sleep(restart_delay)

                    # Use direct uvicorn.run() - simpler and more reliable
                    uvicorn.run(
                        "backend.app.main:app",
                        host=server_host,
                        port=server_port,
                        reload=False,           # Stability over development convenience
                        log_level="error",      # Minimal logging for performance
                        access_log=False,       # Disable access logging
                        use_colors=False,       # Clean output
                    )

                    # If we reach here, uvicorn completed unexpectedly
                    logger.warning("🛡️ EVERLASTING: Uvicorn completed unexpectedly - restarting", module="BACKEND-API", routine="startup")
                    health_monitor.record_restart()
                except OSError as e:
                    logger.info(f"🐛 DEBUG: Caught OSError: {type(e).__name__}: {e}", module="BACKEND-API", routine="startup")
                    if "Address already in use" in str(e):
                        logger.error(f"Port {server_port} is already in use! Error: {type(e).__name__}: {e}", module="BACKEND-API", routine="startup")
                        logger.error("🛑 Port cleanup removed per user request - manual management only", module="BACKEND-API", routine="startup")
                        logger.error("🛠️ Use manual cleanup tools from launch.json to resolve port conflicts", module="BACKEND-API", routine="startup")
                        raise e
                    else:
                        raise e
                except Exception as uvicorn_error:
                    logger.error(f"🐛 DEBUG: Caught Exception in uvicorn.run(): {type(uvicorn_error).__name__}: {uvicorn_error}", module="BACKEND-API", routine="startup")
                    logger.error(f"💥 Uvicorn crashed: {type(uvicorn_error).__name__}: {uvicorn_error}", module="BACKEND-API", routine="startup")
                    import traceback
                    logger.error(f"Stack trace:\n{traceback.format_exc()}", module="BACKEND-API", routine="startup")
                    logger.info("🐛 DEBUG: About to re-raise uvicorn_error...", module="BACKEND-API", routine="startup")
                    raise uvicorn_error  # Re-raise to trigger retry loop

                # If we reach here, uvicorn.run() completed - THIS SHOULD NEVER HAPPEN!
                logger.error("💥 CRITICAL: Backend API uvicorn completed unexpectedly - servers should NEVER complete!", module="BACKEND-API", routine="startup")
                logger.error("💥 CRITICAL: This indicates a serious problem - restarting immediately", module="BACKEND-API", routine="startup")
                # Continue the infinite loop to restart

            except Exception as inner_error:
                logger.error(f"🐛 DEBUG: Caught Exception in inner try block: {type(inner_error).__name__}: {inner_error}", module="BACKEND-API", routine="startup")
                logger.error(f"💥 Inner try block failed: {type(inner_error).__name__}: {inner_error}", module="BACKEND-API", routine="startup")
                import traceback
                logger.error(f"🐛 DEBUG: Inner try block stack trace:\n{traceback.format_exc()}", module="BACKEND-API", routine="startup")
                logger.info("🐛 DEBUG: About to re-raise inner_error...", module="BACKEND-API", routine="startup")
                raise inner_error  # Re-raise to trigger outer exception handling

        except KeyboardInterrupt:
            logger.info("🐛 DEBUG: Caught KeyboardInterrupt", module="BACKEND-API", routine="startup")
            logger.info("🛑 Backend API shutdown requested", module="BACKEND-API", routine="startup")
            # 🛡️ BULLETPROOF: Even keyboard interrupt doesn't stop the service
            logger.info("🔄 BULLETPROOF: Ignoring shutdown request - service NEVER stops", module="BACKEND-API", routine="startup")
            import time
            time.sleep(2)
            # Continue the infinite loop
        except Exception as e:
            logger.error(f"🐛 DEBUG: Caught Exception in outer try block: {type(e).__name__}: {e}", module="BACKEND-API", routine="startup")
            logger.error(f"💥 Backend API startup failed (attempt #{retry_count}): {type(e).__name__}: {e}", module="BACKEND-API", routine="startup")
            import traceback
            logger.error(f"Stack trace:\n{traceback.format_exc()}", module="BACKEND-API", routine="startup")

            # Wait before retrying
            logger.info("🐛 DEBUG: About to wait before retry...", module="BACKEND-API", routine="startup")
            import time
            wait_time = min(retry_count * 2, 30)  # Exponential backoff, max 30 seconds
            logger.info(f"🔄 Will retry in {wait_time} seconds...", module="BACKEND-API", routine="startup")
            logger.info(f"🐛 DEBUG: Sleeping for {wait_time} seconds...", module="BACKEND-API", routine="startup")
            time.sleep(wait_time)
            logger.info("🐛 DEBUG: Sleep completed, continuing infinite loop...", module="BACKEND-API", routine="startup")
            # Continue the infinite loop


# Execute main function when run directly
if __name__ == "__main__":
    # Run main function directly
    main()
