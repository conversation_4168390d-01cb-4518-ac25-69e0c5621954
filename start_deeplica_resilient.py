#!/usr/bin/env python3
"""
🛡️ RESILIENT START DEEPLICA - Ultra-Stable Service Orchestrator

This script provides maximum resilience for DEEPLICA startup:
- Services are designed to NEVER crash
- Each service waits for dependencies internally
- Orchestrator only launches and monitors, doesn't control timing
- Automatic restart on crashes
- Health monitoring with recovery
"""

import asyncio
import os
import sys
import subprocess
import signal
import time
from typing import Dict, List
from pathlib import Path

# Add project root to path
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, PROJECT_ROOT)

from shared.port_manager import get_service_port, ensure_service_port_free
from shared.unified_logging import get_logger

# Initialize logger
logger = get_logger("RESILIENT-ORCHESTRATOR")

class ResilientDeepplicaOrchestrator:
    """Ultra-resilient orchestrator that never gives up"""
    
    def __init__(self):
        self.services = {
            "backend": {
                "name": "🌐 Backend API",
                "directory": "backend",
                "command": [sys.executable, "app/main.py"],
                "port": get_service_port("BACKEND-API"),
                "health_url": f"http://localhost:{get_service_port('BACKEND-API')}/health",
                "process": None,
                "restart_count": 0,
                "last_restart": 0
            },
            "dispatcher": {
                "name": "🎯 Dispatcher",
                "directory": "dispatcher",
                "command": [sys.executable, "-m", "app.main"],
                "port": get_service_port("DISPATCHER"),
                "health_url": f"http://localhost:{get_service_port('DISPATCHER')}/health",
                "process": None,
                "restart_count": 0,
                "last_restart": 0
            },
            "webchat": {
                "name": "💬 Web Chat",
                "directory": "web_chat",
                "command": [sys.executable, "main.py"],
                "port": get_service_port("WEB-CHAT"),
                "health_url": f"http://localhost:{get_service_port('WEB-CHAT')}/health",
                "process": None,
                "restart_count": 0,
                "last_restart": 0
            },
            "watchdog": {
                "name": "🐕 Watchdog",
                "directory": "watchdog",
                "command": [sys.executable, "main.py"],
                "port": get_service_port("WATCHDOG"),
                "health_url": f"http://localhost:{get_service_port('WATCHDOG')}/health",
                "process": None,
                "restart_count": 0,
                "last_restart": 0
            }
        }
        
        self.shutdown_requested = False
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("🛡️ RESILIENT ORCHESTRATOR INITIALIZED")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"🛑 RECEIVED SIGNAL {signum} - SHUTTING DOWN")
        self.shutdown_requested = True
    
    async def start_all_services(self) -> bool:
        """Start all services with maximum resilience"""
        logger.info("🚀 STARTING RESILIENT DEEPLICA ORCHESTRATOR")
        logger.info("=" * 80)
        
        try:
            # Phase 1: Clean up ports
            await self._cleanup_ports()
            
            # Phase 2: Start services in dependency order
            startup_order = ["backend", "dispatcher", "webchat", "watchdog"]
            
            for service_id in startup_order:
                if self.shutdown_requested:
                    logger.warning("🛑 SHUTDOWN REQUESTED - ABORTING STARTUP")
                    return False
                
                await self._start_service(service_id)
                await asyncio.sleep(3)  # Brief pause between services
            
            logger.info("🎉 ALL SERVICES LAUNCHED!")
            logger.info("🔄 Starting continuous monitoring...")
            
            # Phase 3: Monitor services continuously
            await self._monitor_services_forever()
            
            return True
            
        except Exception as e:
            logger.error(f"💥 ORCHESTRATOR ERROR: {e}")
            return False
    
    async def _cleanup_ports(self):
        """Clean up ports for all services"""
        logger.info("🧹 CLEANING UP PORTS...")
        
        for service_id, service in self.services.items():
            logger.info(f"🔍 Checking port {service['port']} for {service['name']}")
            
            if not ensure_service_port_free(service_id.upper().replace("_", "-")):
                logger.warning(f"⚠️ Port {service['port']} was in use - cleaned up")
            else:
                logger.info(f"✅ Port {service['port']} is free")
        
        logger.info("✅ PORT CLEANUP COMPLETE")
    
    async def _start_service(self, service_id: str):
        """Start a single service with resilience"""
        service = self.services[service_id]
        logger.info(f"🚀 STARTING {service['name']}")
        
        try:
            # Set working directory
            cwd = Path(PROJECT_ROOT) / service["directory"]
            
            # Set environment
            env = os.environ.copy()
            env["PYTHONPATH"] = PROJECT_ROOT
            env["SERVICE_NAME"] = service_id.upper().replace("_", "-")
            env["USE_MOCK_DATABASE"] = "true"
            env["WAIT_FOR_BACKEND"] = "true"  # Let services handle their own waiting
            
            # Launch process
            logger.info(f"📝 Command: {' '.join(service['command'])}")
            logger.info(f"📁 Working directory: {cwd}")
            
            process = subprocess.Popen(
                service["command"],
                cwd=str(cwd),
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1
            )
            
            service["process"] = process
            service["last_restart"] = time.time()
            
            logger.info(f"✅ {service['name']} launched with PID: {process.pid}")
            
            # Start output monitoring in background
            asyncio.create_task(self._monitor_service_output(service_id))
            
        except Exception as e:
            logger.error(f"💥 FAILED TO START {service['name']}: {e}")
            service["restart_count"] += 1
    
    async def _monitor_service_output(self, service_id: str):
        """Monitor service output in background"""
        service = self.services[service_id]
        process = service["process"]
        
        if not process:
            return
        
        try:
            while process.poll() is None and not self.shutdown_requested:
                line = process.stdout.readline()
                if line:
                    line = line.strip()
                    # Only log important messages to avoid spam
                    if any(keyword in line.lower() for keyword in ['error', 'exception', 'failed', 'ready', 'started']):
                        logger.info(f"[{service_id.upper()}] {line}")
                
                await asyncio.sleep(0.1)
        except Exception as e:
            logger.debug(f"Output monitoring error for {service_id}: {e}")
    
    async def _monitor_services_forever(self):
        """Monitor all services and restart if needed"""
        logger.info("👁️ STARTING CONTINUOUS SERVICE MONITORING")
        
        while not self.shutdown_requested:
            for service_id, service in self.services.items():
                process = service["process"]
                
                if process and process.poll() is not None:
                    # Service has crashed
                    logger.error(f"💥 {service['name']} CRASHED (exit code: {process.returncode})")
                    
                    # Wait before restart to avoid rapid cycling
                    time_since_last = time.time() - service["last_restart"]
                    if time_since_last < 10:
                        logger.info(f"⏳ Waiting before restart (last restart {time_since_last:.1f}s ago)")
                        await asyncio.sleep(10 - time_since_last)
                    
                    # Restart the service
                    await self._restart_service(service_id)
                
                elif process:
                    # Service is running - check health periodically
                    if service_id == "backend" and time.time() % 30 < 1:  # Check every 30 seconds
                        await self._check_service_health(service_id)
            
            await asyncio.sleep(5)  # Check every 5 seconds
    
    async def _restart_service(self, service_id: str):
        """Restart a crashed service"""
        service = self.services[service_id]
        service["restart_count"] += 1
        
        if service["restart_count"] > 10:
            logger.error(f"❌ {service['name']} has crashed {service['restart_count']} times - giving up")
            return
        
        logger.info(f"🔄 RESTARTING {service['name']} (attempt {service['restart_count']})")
        
        # Clean up old process
        if service["process"]:
            try:
                service["process"].terminate()
            except:
                pass
        
        # Restart
        await self._start_service(service_id)
    
    async def _check_service_health(self, service_id: str):
        """Check service health via HTTP"""
        service = self.services[service_id]
        
        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.get(service["health_url"], timeout=5) as response:
                    if response.status == 200:
                        logger.debug(f"✅ {service['name']} health check passed")
                    else:
                        logger.warning(f"⚠️ {service['name']} health check failed: HTTP {response.status}")
        except Exception as e:
            logger.debug(f"🔄 {service['name']} health check error: {e}")
    
    async def shutdown_all_services(self):
        """Shutdown all services gracefully"""
        logger.info("🛑 SHUTTING DOWN ALL SERVICES...")
        
        for service_id, service in self.services.items():
            process = service["process"]
            if process:
                try:
                    logger.info(f"🛑 Stopping {service['name']}...")
                    process.terminate()
                    
                    # Wait for graceful shutdown
                    try:
                        process.wait(timeout=10)
                    except subprocess.TimeoutExpired:
                        logger.warning(f"⚠️ Force killing {service['name']}")
                        process.kill()
                        process.wait()
                    
                    logger.info(f"✅ {service['name']} stopped")
                except Exception as e:
                    logger.error(f"❌ Error stopping {service['name']}: {e}")
        
        logger.info("✅ ALL SERVICES STOPPED")

async def main():
    """Main function"""
    orchestrator = ResilientDeepplicaOrchestrator()
    
    try:
        success = await orchestrator.start_all_services()
        if success:
            logger.info("🎉 DEEPLICA STARTED SUCCESSFULLY!")
        else:
            logger.error("❌ DEEPLICA STARTUP FAILED!")
            return 1
    except KeyboardInterrupt:
        logger.info("🛑 INTERRUPTED BY USER")
    except Exception as e:
        logger.error(f"💥 ORCHESTRATOR ERROR: {e}")
        return 1
    finally:
        await orchestrator.shutdown_all_services()
    
    return 0

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(result)
    except Exception as e:
        print(f"💥 FATAL ERROR: {e}")
        sys.exit(1)
