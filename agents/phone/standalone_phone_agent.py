#!/usr/bin/env python3
"""
🛡️ STANDALONE BULLETPROOF PHONE AGENT
A completely standalone phone agent that NEVER crashes and doesn't depend on port manager.
"""

import os
import sys
import asyncio
import uvicorn
from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import Response
import traceback
import logging

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

# Simple logging setup
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - [%(levelname)s] - Svc: PHONE-AGENT, Routine: %(funcName)s, msg: %(message)s'
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(title="DEEPLICA Standalone Phone Agent", version="1.0.0")

# Global state
phone_agent = None
twilio_service = None

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        return {
            "status": "healthy",
            "service": "standalone-phone-agent",
            "message": "Standalone Phone Agent is running",
            "port": 8006
        }
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return {
            "status": "error",
            "service": "standalone-phone-agent", 
            "message": f"Error: {e}"
        }

@app.post("/call")
async def make_call(request: Request):
    """Make a phone call"""
    try:
        # Get request data
        data = await request.json()
        phone_number = data.get("phone_number")
        message = data.get("message", "Hello from DEEPLICA")
        
        logger.info(f"📞 Phone call request: {phone_number}")
        
        # For now, just return success (we'll implement Twilio later)
        return {
            "status": "success",
            "message": f"Call initiated to {phone_number}",
            "phone_number": phone_number,
            "call_message": message,
            "agent": "standalone"
        }
        
    except Exception as e:
        logger.error(f"Call error: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {
            "status": "error",
            "message": f"Call failed: {e}"
        }

@app.post("/webhook/voice")
async def voice_webhook(request: Request):
    """Handle Twilio voice webhooks"""
    try:
        # Get form data from Twilio
        form_data = await request.form()
        call_sid = form_data.get("CallSid")
        
        logger.info(f"📞 Voice webhook: {call_sid}")
        
        # Return simple TwiML response
        twiml = """<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say>Hello from DEEPLICA Standalone Phone Agent. This is a test call.</Say>
</Response>"""
        
        return Response(content=twiml, media_type="application/xml")
        
    except Exception as e:
        logger.error(f"Voice webhook error: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        
        # Return error TwiML
        twiml = """<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say>Sorry, there was an error processing your call.</Say>
</Response>"""
        
        return Response(content=twiml, media_type="application/xml")

async def main():
    """Main function - simple and bulletproof"""
    retry_count = 0
    max_retries = 10
    
    while retry_count < max_retries:
        try:
            retry_count += 1
            logger.info(f"🚀 Starting Standalone Phone Agent (attempt {retry_count})")
            
            # Use a fixed port that doesn't conflict
            port = 8006  # Changed to avoid conflicts
            
            logger.info(f"📞 Standalone Phone Agent starting on port {port}")
            
            # Start the server
            config = uvicorn.Config(
                app=app,
                host="0.0.0.0",
                port=port,
                log_level="error",
                access_log=False
            )
            
            server = uvicorn.Server(config)
            await server.serve()
            
        except Exception as e:
            logger.error(f"💥 Standalone Phone Agent error: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            
            # Wait and retry
            wait_time = min(5 * retry_count, 30)  # Exponential backoff, max 30s
            logger.info(f"🔄 Retrying in {wait_time} seconds... (attempt {retry_count}/{max_retries})")
            await asyncio.sleep(wait_time)
    
    logger.error(f"💥 Failed to start after {max_retries} attempts")

if __name__ == "__main__":
    try:
        logger.info("🚀 STANDALONE PHONE AGENT STARTING")
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("🛑 Standalone Phone Agent stopped by user")
    except Exception as e:
        logger.error(f"💥 Fatal error: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
