#!/usr/bin/env python3
"""
Clean CLI Interface Script - Move ALL debug/info/error reporting from CLI to watchdog.
This script will create a clean, user-focused CLI interface without any technical noise.
"""

import os
import re
from typing import List, <PERSON><PERSON>

def create_watchdog_logger_integration():
    """Create a watchdog logger integration for CLI"""
    return '''
import requests
import json
from typing import Optional
import sys
import os

# Add project root to path for shared modules
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.port_manager import get_service_port, get_localhost

class WatchdogLogger:
    """Send CLI events to watchdog for monitoring without cluttering user interface"""
    
    def __init__(self):
        self.watchdog_url = f"http://{get_localhost()}:{get_service_port('watchdog')}"  # Watchdog service
        self.service_name = "CLI-TERMINAL"
    
    def _send_to_watchdog(self, level: str, routine: str, category: str, message: str, data: Optional[dict] = None):
        """Send log message to watchdog service"""
        try:
            payload = {
                "service": self.service_name,
                "routine": routine,
                "level": level,
                "category": category,
                "message": message,
                "data": data or {}
            }
            requests.post(f"{self.watchdog_url}/log", json=payload, timeout=1)
        except:
            # Never let watchdog logging interfere with CLI operation
            pass
    
    def info(self, routine: str, category: str, message: str, data: Optional[dict] = None):
        """Log info message to watchdog"""
        self._send_to_watchdog("INFO", routine, category, message, data)
    
    def error(self, routine: str, category: str, message: str, data: Optional[dict] = None):
        """Log error message to watchdog"""
        self._send_to_watchdog("ERROR", routine, category, message, data)
    
    def debug(self, routine: str, category: str, message: str, data: Optional[dict] = None):
        """Log debug message to watchdog"""
        self._send_to_watchdog("DEBUG", routine, category, message, data)

# Global watchdog logger instance
watchdog_logger = WatchdogLogger()
'''

def clean_cli_terminal_ui():
    """Clean the CLI terminal UI by removing all debug/info/error prints and replacing with watchdog logging"""
    
    # Read the current file
    with open("cli/terminal_ui.py", "r") as f:
        content = f.read()
    
    # Add watchdog logger integration at the top
    watchdog_integration = create_watchdog_logger_integration()
    
    # Find the imports section and add watchdog logger after it
    import_pattern = r'(from cli\.api_client import.*?\n)'
    content = re.sub(import_pattern, r'\1\n' + watchdog_integration + '\n', content)
    
    # Replace all debug/info print statements with watchdog logging
    replacements = [
        # Remove progress prints - replace with watchdog logging
        (r'print\(f"\\n📊 Progress: \{completed\}/\{total\} tasks \(\{percentage:.1f\}%\)"\)', 
         'watchdog_logger.info("print_mission_response", "PROGRESS", f"Mission progress: {completed}/{total} tasks ({percentage:.1f}%)", {"completed": completed, "total": total, "percentage": percentage})'),
        
        # Replace response prints with clean user messages only
        (r'print\(f"\\n🤖 \{message\}"\)', 'print(f"🤖 {message}")'),
        (r'print\(f"\\n✅ \{message\}"\)', 'print(f"✅ {message}")'),
        
        # Replace mission creation print
        (r'print\(f"\\n🧠 Planning your mission\.\.\."\)', 'print("🧠 Planning your mission...")'),
        
        # Replace mission completion print
        (r'print\(f"\\n🎉 Mission completed!"\)', 'print("🎉 Mission completed!")'),
        
        # Replace status prints with watchdog logging
        (r'print\(f"\\n📊 Mission Status: \{self\.current_mission_id\[:8\]\}\.\.\."\)', 
         'watchdog_logger.info("handle_status_command", "STATUS", f"Checking mission status: {self.current_mission_id[:8]}...")'),
        
        # Replace error prints with watchdog logging
        (r'print\(f"\\n⚠️ Mission \{self\.current_mission_id\[:8\]\}\.\.\..*?\)"\)', 
         'watchdog_logger.error("handle_status_command", "MISSION", f"Mission {self.current_mission_id[:8]}... no longer exists")'),
        
        # Replace missions list print
        (r'print\(f"\\n📋 Recent Missions \(\{len\(missions\)\}\):"\)', 
         'print(f"📋 Recent Missions ({len(missions)}):")'),
        
        # Replace separator prints with clean versions
        (r'print\("\\n" \+ "="\*50\)', 'print("\\n" + "="*50)'),
        
        # Replace timeout warning with watchdog logging
        (r'print\(f"\\n⚠️ Mission is taking longer than expected\..*?\)"\)', 
         'watchdog_logger.info("_wait_for_mission_stable_state", "TIMEOUT", "Mission taking longer than expected")'),
        
        # Replace startup messages with watchdog logging
        (r'print\(f"\[CLI-TERMINAL:main\] STARTUP.*?\)"\)', 
         'watchdog_logger.info("main", "STARTUP", "Backend readiness timeout - starting CLI anyway")'),
        
        # Clean up spacing prints
        (r'print\(\)  # Add spacing', '# Clean spacing managed internally'),
        
        # Replace error handling prints with watchdog logging
        (r'print\("Type \'exit\' to quit.*?\)"\)', 
         'watchdog_logger.error("run", "ERROR", "Unexpected error in main loop", {"error": str(e)}); print("Something went wrong. Please try again or type \'exit\' to quit.")'),
    ]
    
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
    
    # Remove all commented debug lines
    content = re.sub(r'\s*# \[CLI-TERMINAL:.*?\n', '\n', content)
    
    # Clean up the banner printing to be minimal
    banner_replacement = '''
    def print_banner(self):
        """Print clean, minimal banner"""
        print("\\n" + "="*60)
        print("🤖 DEEPLICA - AI Mission Orchestration System")
        print("="*60)
        print("Ready to help! Tell me what you'd like to accomplish.\\n")
'''
    
    # Replace the existing print_banner method
    content = re.sub(r'def print_banner\(self\):.*?print\("=".*?\n', banner_replacement, content, flags=re.MULTILINE | re.DOTALL)
    
    return content

def main():
    """Main function to clean CLI interface"""
    print("🧹 [CLEAN-CLI:main] SYSTEM | Starting CLI interface cleanup...")
    print("=" * 80)
    
    try:
        # Clean the CLI terminal UI
        cleaned_content = clean_cli_terminal_ui()
        
        # Write the cleaned content back
        with open("cli/terminal_ui.py", "w") as f:
            f.write(cleaned_content)
        
        print("✅ [CLEAN-CLI:main] SUCCESS | CLI terminal UI cleaned successfully")
        print("📊 [CLEAN-CLI:main] SYSTEM | All debug/info/error reporting moved to watchdog")
        print("🎯 [CLEAN-CLI:main] SYSTEM | CLI now provides clean, user-focused interface")
        
    except Exception as e:
        print(f"❌ [CLEAN-CLI:main] ERROR | Error cleaning CLI: {e}")
    
    print("=" * 80)
    print("🎉 [CLEAN-CLI:main] SUCCESS | CLI cleanup complete!")

if __name__ == "__main__":
    main()
