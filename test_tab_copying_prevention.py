#!/usr/bin/env python3
"""
🔒 TAB COPYING PREVENTION TEST

Tests that URLs cannot be copied between browser tabs, even in the same browser.
This verifies the tab-specific token system properly prevents URL sharing.
"""

import requests
import json
import time
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

from shared.port_manager import get_service_port

def test_chat_url_copying_prevention():
    """Test that chat URLs cannot be copied between browser sessions"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔒 Testing chat URL copying prevention on port {web_chat_port}...")
        
        # Session 1: Get a valid chat URL with tab token
        session1 = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        
        # Login with session 1
        login_response1 = session1.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=15
        )
        
        if login_response1.status_code != 200:
            print(f"❌ Session 1 login failed: {login_response1.status_code}")
            return False
        
        # Get chat URL with session 1
        chat_response1 = session1.get(
            f"http://localhost:{web_chat_port}/chat",
            allow_redirects=True,
            timeout=15
        )
        
        if chat_response1.status_code != 200:
            print(f"❌ Session 1 chat access failed: {chat_response1.status_code}")
            return False
        
        chat_url1 = chat_response1.url
        print(f"✅ Session 1 got chat URL: {chat_url1[:80]}...")
        
        # Session 2: Try to use Session 1's chat URL (simulating URL copying)
        session2 = requests.Session()
        
        # First, login with session 2 to get valid authentication
        login_response2 = session2.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=15
        )
        
        if login_response2.status_code != 200:
            print(f"❌ Session 2 login failed: {login_response2.status_code}")
            return False
        
        # Now try to access Session 1's chat URL with Session 2
        # This simulates copying the URL to a different browser tab
        chat_response2 = session2.get(chat_url1, allow_redirects=True, timeout=15)
        
        # Check if the URL copying was properly blocked
        if chat_response2.status_code == 200:
            # Check if it redirected to login or setup
            final_url = chat_response2.url
            content = chat_response2.text.lower()
            
            if ('/login' in final_url or 'login' in content or 
                'setup=1' in final_url or 'error=' in final_url):
                print(f"✅ Chat URL copying correctly blocked (redirected)")
                print(f"🔗 Redirected to: {final_url}")
                return True
            else:
                print(f"❌ Chat URL copying NOT blocked - this is a security issue!")
                print(f"🔗 Final URL: {final_url}")
                return False
        else:
            print(f"✅ Chat URL copying blocked (status: {chat_response2.status_code})")
            return True
        
    except Exception as e:
        print(f"❌ Error testing chat URL copying prevention: {e}")
        return False

def test_admin_url_copying_prevention():
    """Test that admin URLs cannot be copied between browser sessions"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔧 Testing admin URL copying prevention on port {web_chat_port}...")
        
        # Session 1: Get a valid admin URL with tab token
        session1 = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        
        # Login with session 1
        login_response1 = session1.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=15
        )
        
        if login_response1.status_code != 200:
            print(f"❌ Session 1 admin login failed: {login_response1.status_code}")
            return False
        
        # Get admin URL with session 1
        admin_response1 = session1.get(
            f"http://localhost:{web_chat_port}/admin",
            allow_redirects=True,
            timeout=15
        )
        
        if admin_response1.status_code != 200:
            print(f"❌ Session 1 admin access failed: {admin_response1.status_code}")
            return False
        
        admin_url1 = admin_response1.url
        print(f"✅ Session 1 got admin URL: {admin_url1[:80]}...")
        
        # Session 2: Try to use Session 1's admin URL (simulating URL copying)
        session2 = requests.Session()
        
        # First, login with session 2 to get valid authentication
        login_response2 = session2.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=15
        )
        
        if login_response2.status_code != 200:
            print(f"❌ Session 2 admin login failed: {login_response2.status_code}")
            return False
        
        # Now try to access Session 1's admin URL with Session 2
        # This simulates copying the admin URL to a different browser tab
        admin_response2 = session2.get(admin_url1, allow_redirects=True, timeout=15)
        
        # Check if the admin URL copying was properly blocked
        if admin_response2.status_code == 200:
            # Check if it redirected to login or setup
            final_url = admin_response2.url
            content = admin_response2.text.lower()
            
            if ('/login' in final_url or 'login' in content or 
                'setup=1' in final_url or 'error=' in final_url):
                print(f"✅ Admin URL copying correctly blocked (redirected)")
                print(f"🔗 Redirected to: {final_url}")
                return True
            else:
                print(f"❌ Admin URL copying NOT blocked - this is a security issue!")
                print(f"🔗 Final URL: {final_url}")
                return False
        else:
            print(f"✅ Admin URL copying blocked (status: {admin_response2.status_code})")
            return True
        
    except Exception as e:
        print(f"❌ Error testing admin URL copying prevention: {e}")
        return False

def test_token_format_with_tab_id():
    """Test that tokens contain proper tab IDs"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔍 Testing token format with tab ID on port {web_chat_port}...")
        
        # Login first
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=15
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login failed for token format test")
            return False
        
        # Generate a token with specific tab ID
        test_tab_id = f"test_unique_tab_{int(time.time())}_verification"
        
        api_response = session.post(
            f"http://localhost:{web_chat_port}/api/generate-tab-token",
            json={"tab_id": test_tab_id},
            timeout=15
        )
        
        if api_response.status_code != 200:
            print(f"❌ Token generation failed for format test")
            return False
        
        result = api_response.json()
        token = result.get('tab_token')
        
        if not token:
            print(f"❌ No token in response")
            return False
        
        # Decode and verify token format
        try:
            import base64
            decoded = base64.urlsafe_b64decode(token.encode()).decode()
            parts = decoded.split(':')
            
            if len(parts) != 5:
                print(f"❌ Token should have 5 parts, got {len(parts)}")
                return False
            
            user_id, fingerprint, token_tab_id, timestamp, signature = parts
            
            # Verify the tab ID is correctly embedded in the token
            if token_tab_id != test_tab_id:
                print(f"❌ Tab ID mismatch in token:")
                print(f"   Expected: {test_tab_id}")
                print(f"   Got: {token_tab_id}")
                return False
            
            print(f"✅ Token format is correct with embedded tab ID")
            print(f"🔍 Tab ID in token: {token_tab_id}")
            
            return True
            
        except Exception as e:
            print(f"❌ Token format validation error: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing token format: {e}")
        return False

def test_same_browser_different_tabs_simulation():
    """Simulate what happens when URLs are copied between tabs in same browser"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔄 Testing same browser different tabs simulation on port {web_chat_port}...")
        
        # This test simulates the JavaScript behavior that should prevent URL copying
        # We can't actually test multiple browser tabs, but we can test the token logic
        
        # Get a valid session
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=15
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login failed for tab simulation test")
            return False
        
        # Generate token for "Tab 1"
        tab1_id = f"tab1_unique_{int(time.time())}_aaaa"
        
        api_response1 = session.post(
            f"http://localhost:{web_chat_port}/api/generate-tab-token",
            json={"tab_id": tab1_id},
            timeout=15
        )
        
        if api_response1.status_code != 200:
            print(f"❌ Tab 1 token generation failed")
            return False
        
        result1 = api_response1.json()
        token1 = result1.get('tab_token')
        
        # Generate token for "Tab 2" 
        tab2_id = f"tab2_unique_{int(time.time())}_bbbb"
        
        api_response2 = session.post(
            f"http://localhost:{web_chat_port}/api/generate-tab-token",
            json={"tab_id": tab2_id},
            timeout=15
        )
        
        if api_response2.status_code != 200:
            print(f"❌ Tab 2 token generation failed")
            return False
        
        result2 = api_response2.json()
        token2 = result2.get('tab_token')
        
        # Verify tokens are different
        if token1 == token2:
            print(f"❌ Different tabs generated same token - security issue!")
            return False
        
        print(f"✅ Different tabs generate different tokens")
        print(f"🔍 Tab 1 token: {token1[:30]}...")
        print(f"🔍 Tab 2 token: {token2[:30]}...")
        
        # Verify tab IDs are embedded correctly
        import base64
        
        decoded1 = base64.urlsafe_b64decode(token1.encode()).decode()
        parts1 = decoded1.split(':')
        embedded_tab1_id = parts1[2] if len(parts1) >= 3 else None
        
        decoded2 = base64.urlsafe_b64decode(token2.encode()).decode()
        parts2 = decoded2.split(':')
        embedded_tab2_id = parts2[2] if len(parts2) >= 3 else None
        
        if embedded_tab1_id != tab1_id:
            print(f"❌ Tab 1 ID not properly embedded in token")
            return False
        
        if embedded_tab2_id != tab2_id:
            print(f"❌ Tab 2 ID not properly embedded in token")
            return False
        
        print(f"✅ Tab IDs properly embedded in tokens")
        print(f"🔍 This means JavaScript can verify tab identity")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing tab simulation: {e}")
        return False

def main():
    """Run all tab copying prevention tests"""
    print("🔒 TAB COPYING PREVENTION TEST")
    print("=" * 60)
    print("Testing that URLs cannot be copied between browser tabs:")
    print("- Chat URL copying prevention")
    print("- Admin URL copying prevention") 
    print("- Token format with embedded tab IDs")
    print("- Same browser different tabs simulation")
    print()
    
    tests_passed = 0
    total_tests = 4
    
    # Test 1: Chat URL copying prevention
    print("🧪 TEST 1: Chat URL Copying Prevention")
    if test_chat_url_copying_prevention():
        tests_passed += 1
    
    # Test 2: Admin URL copying prevention
    print("\n🧪 TEST 2: Admin URL Copying Prevention")
    if test_admin_url_copying_prevention():
        tests_passed += 1
    
    # Test 3: Token format with tab ID
    print("\n🧪 TEST 3: Token Format with Tab ID")
    if test_token_format_with_tab_id():
        tests_passed += 1
    
    # Test 4: Same browser different tabs simulation
    print("\n🧪 TEST 4: Same Browser Different Tabs Simulation")
    if test_same_browser_different_tabs_simulation():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 60)
    print(f"🔒 TEST RESULTS: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✅ ALL TESTS PASSED - Tab copying prevention working!")
        print("🎉 Security features verified:")
        print("   - URLs cannot be copied between browser sessions")
        print("   - Tab IDs are properly embedded in tokens")
        print("   - Different tabs generate different tokens")
        print("   - JavaScript can verify tab identity")
    else:
        print("❌ SOME TESTS FAILED - Tab copying prevention needs fixes")
        
        if tests_passed >= 3:
            print("🔧 RECOMMENDATION: Minor security issues remain")
        elif tests_passed >= 2:
            print("🔧 RECOMMENDATION: Some security features work")
        else:
            print("🔧 RECOMMENDATION: Major security fixes needed")
    
    print(f"\n🌐 Manual test instructions:")
    print(f"1. Open http://localhost:8007 in browser")
    print(f"2. Login with: admin / admin123")
    print(f"3. Get to chat or admin page")
    print(f"4. Copy the URL from address bar")
    print(f"5. Open NEW TAB in SAME BROWSER")
    print(f"6. Paste the URL in new tab")
    print(f"7. New tab should show security error")
    print(f"8. This proves URLs can't be copied between tabs")

if __name__ == '__main__':
    main()
