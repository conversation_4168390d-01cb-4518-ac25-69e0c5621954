#!/usr/bin/env python3
"""
Test backend imports to identify any issues
"""

print("Testing backend imports...")

try:
    print("1. Testing basic imports...")
    import os
    import sys
    from pathlib import Path
    print("✅ Basic imports successful")
    
    print("2. Testing project path...")
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    print(f"✅ Project root: {project_root}")
    
    print("3. Testing dotenv...")
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ dotenv loaded")
    
    print("4. Testing MongoDB connection string...")
    connection_string = os.getenv('MONGODB_CONNECTION_STRING')
    print(f"✅ MongoDB connection string loaded: {connection_string[:50]}...")
    
    print("5. Testing FastAPI imports...")
    from fastapi import FastAPI
    print("✅ FastAPI imported")
    
    print("6. Testing uvicorn...")
    import uvicorn
    print("✅ uvicorn imported")
    
    print("7. Testing backend app imports...")
    sys.path.insert(0, str(project_root / "backend"))
    from app.main import app
    print("✅ Backend app imported successfully")
    
    print("🎉 All imports successful! Backend should be able to start.")
    
except Exception as e:
    print(f"❌ Import failed: {e}")
    import traceback
    traceback.print_exc()
