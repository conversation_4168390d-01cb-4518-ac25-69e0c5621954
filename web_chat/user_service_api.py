"""
User service for database operations and authentication.

This module handles all user-related operations by routing through the Backend API.
NO DIRECT DATABASE ACCESS - ALL OPERATIONS ROUTE THROUGH BACKEND API ONLY.
"""

import os
import uuid
import hashlib
import secrets
from datetime import datetime, timed<PERSON>ta
from typing import Optional, List, Dict, Any
import httpx

from shared_models.user import (
    User, UserCreate, UserUpdate, UserLogin, UserSession, UserResponse,
    UserRole, UserStatus, UserPermission, get_default_permissions
)
from shared_models.password_utils import hash_password, verify_password


class UserServiceAPI:
    """Service for user management and authentication - ROUTES THROUGH BACKEND API ONLY"""
    
    def __init__(self):
        # NO DIRECT DATABASE ACCESS - ALL OPERATIONS ROUTE THROUGH BACKEND API
        self.backend_url = None
        
        # Session settings
        self.session_duration_hours = 24  # Sessions last 24 hours
        
    def get_backend_api_url(self):
        """Get Backend API URL - NO DIRECT DATABASE ACCESS ALLOWED"""
        try:
            from shared.api_manager import get_service_url
            backend_url = get_service_url("backend")
            if backend_url:
                return backend_url
            
            # Fallback to environment variable or default
            backend_port = os.getenv("BACKEND_PORT", "8888")
            return f"http://127.0.0.1:{backend_port}"
        except Exception as e:
            print(f"❌ Failed to get backend API URL: {str(e)}")
            return None

    async def connect(self) -> None:
        """Initialize connection to Backend API - NO DIRECT DATABASE ACCESS"""
        try:
            self.backend_url = self.get_backend_api_url()
            if not self.backend_url:
                raise Exception("Backend API URL not available")
            
            # Test Backend API connection
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{self.backend_url}/health")
                if response.status_code == 200:
                    print(f"✅ User service connected to Backend API: {self.backend_url}")
                    
                    # Ensure default admin user exists via Backend API
                    await self._ensure_default_admin_via_api()
                else:
                    raise Exception(f"Backend API health check failed: {response.status_code}")
                    
        except Exception as e:
            print(f"❌ Failed to connect to Backend API: {str(e)}")
            raise

    async def _ensure_default_admin_via_api(self) -> None:
        """Ensure default admin user exists via Backend API - NO DIRECT DATABASE ACCESS"""
        try:
            if not self.backend_url:
                return
                
            async with httpx.AsyncClient(timeout=10.0) as client:
                # Check if admin user exists
                response = await client.get(f"{self.backend_url}/api/v1/users/admin")
                if response.status_code == 404:
                    # Create default admin user
                    admin_data = {
                        "username": "admin",
                        "email": "<EMAIL>",
                        "password": "admin123",
                        "full_name": "System Administrator",
                        "role": "admin",
                        "is_admin": True
                    }
                    
                    create_response = await client.post(f"{self.backend_url}/api/v1/users", json=admin_data)
                    if create_response.status_code == 201:
                        print("✅ Default admin user created via Backend API")
                    else:
                        print(f"⚠️ Failed to create admin user: {create_response.status_code}")
                elif response.status_code == 200:
                    print("✅ Default admin user already exists")
                    
        except Exception as e:
            print(f"⚠️ Failed to ensure admin user via API: {str(e)}")

    async def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """Authenticate user via Backend API"""
        try:
            if not self.backend_url:
                return None
                
            async with httpx.AsyncClient(timeout=10.0) as client:
                auth_data = {"username": username, "password": password}
                print(f"🔍 Authenticating {username} via {self.backend_url}/api/v1/users/authenticate")
                response = await client.post(f"{self.backend_url}/api/v1/users/authenticate", json=auth_data)

                print(f"🔍 Response status: {response.status_code}")
                if response.status_code == 200:
                    user_data = response.json()
                    # Convert to User object
                    return User(
                        user_id=user_data["user_id"],
                        username=user_data["username"],
                        email=user_data.get("email", ""),
                        mobile_phone=user_data.get("mobile_phone", ""),
                        password_hash="***",  # Not returned by API for security
                        full_name=user_data.get("full_name", ""),
                        display_name=user_data.get("display_name", user_data.get("full_name", "")),
                        role=UserRole(user_data.get("role", "user")),
                        status=UserStatus(user_data.get("status", "active")),
                        is_admin=user_data.get("is_admin", False),
                        created_at=datetime.fromisoformat(user_data["created_at"]),
                        updated_at=datetime.fromisoformat(user_data["updated_at"]),
                        last_login=datetime.fromisoformat(user_data["last_login"]) if user_data.get("last_login") else None,
                        login_count=user_data.get("login_count", 0),
                        preferences=user_data.get("preferences", {}),
                        permissions=user_data.get("permissions", get_default_permissions(UserRole(user_data.get("role", "user"))))
                    )
                else:
                    return None
                    
        except Exception as e:
            print(f"❌ Authentication error: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

    async def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username via Backend API"""
        try:
            if not self.backend_url:
                return None
                
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{self.backend_url}/api/v1/users/by-username/{username}")

                if response.status_code == 200:
                    response_data = response.json()
                    user_data = response_data.get("user", response_data)  # Handle both formats
                    return User(
                        user_id=user_data["user_id"],
                        username=user_data["username"],
                        email=user_data.get("email", ""),
                        mobile_phone=user_data.get("mobile_phone", ""),
                        password_hash="***",  # Not returned by API for security
                        full_name=user_data.get("full_name", ""),
                        display_name=user_data.get("display_name", user_data.get("full_name", "")),
                        role=UserRole(user_data.get("role", "user")),
                        status=UserStatus(user_data.get("status", "active")),
                        is_admin=user_data.get("is_admin", False),
                        created_at=datetime.fromisoformat(user_data["created_at"]),
                        updated_at=datetime.fromisoformat(user_data["updated_at"]),
                        last_login=datetime.fromisoformat(user_data["last_login"]) if user_data.get("last_login") else None,
                        login_count=user_data.get("login_count", 0),
                        preferences=user_data.get("preferences", {}),
                        permissions=get_default_permissions(UserRole(user_data.get("role", "user")))
                    )
                else:
                    return None
                    
        except Exception as e:
            print(f"❌ Error getting user: {str(e)}")
            return None

    async def create_session(self, user: User) -> str:
        """Create user session via Backend API"""
        try:
            if not self.backend_url:
                return None
                
            async with httpx.AsyncClient(timeout=10.0) as client:
                session_data = {
                    "user_id": user.user_id,
                    "username": user.username
                }
                response = await client.post(f"{self.backend_url}/api/v1/sessions", json=session_data)
                
                if response.status_code == 200:
                    session_info = response.json()
                    return session_info["session_id"]
                else:
                    return None
                    
        except Exception as e:
            print(f"❌ Error creating session: {str(e)}")
            return None

    async def get_user_from_session(self, session_id: str) -> Optional[User]:
        """Get user from session via Backend API"""
        try:
            if not self.backend_url:
                return None
                
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{self.backend_url}/api/v1/sessions/{session_id}")
                
                if response.status_code == 200:
                    session_data = response.json()
                    # Get user by username
                    return await self.get_user_by_username(session_data["username"])
                else:
                    return None
                    
        except Exception as e:
            print(f"❌ Error getting user from session: {str(e)}")
            return None

    async def delete_session(self, session_id: str) -> None:
        """Delete session via Backend API"""
        try:
            if not self.backend_url:
                return
                
            async with httpx.AsyncClient(timeout=10.0) as client:
                await client.delete(f"{self.backend_url}/api/v1/sessions/{session_id}")
                    
        except Exception as e:
            print(f"❌ Error deleting session: {str(e)}")

    async def create_user(self, user_create: UserCreate) -> User:
        """Create user via Backend API"""
        try:
            if not self.backend_url:
                raise Exception("Backend API not available")

            async with httpx.AsyncClient(timeout=10.0) as client:
                user_data = {
                    "username": user_create.username,
                    "email": user_create.email,
                    "password": user_create.password,
                    "full_name": user_create.full_name,
                    "role": user_create.role.value if hasattr(user_create.role, 'value') else str(user_create.role),
                    "is_admin": user_create.role == UserRole.ADMIN
                }

                response = await client.post(f"{self.backend_url}/api/v1/users", json=user_data)

                if response.status_code == 201:
                    user_data = response.json()
                    return User(
                        user_id=user_data["user_id"],
                        username=user_data["username"],
                        email=user_data.get("email", ""),
                        mobile_phone=user_data.get("mobile_phone", ""),
                        password_hash="***",  # Not returned by API for security
                        full_name=user_data.get("full_name", ""),
                        display_name=user_data.get("display_name", user_data.get("full_name", "")),
                        role=UserRole(user_data.get("role", "user")),
                        status=UserStatus(user_data.get("status", "active")),
                        is_admin=user_data.get("is_admin", False),
                        created_at=datetime.fromisoformat(user_data["created_at"]),
                        updated_at=datetime.fromisoformat(user_data["updated_at"]),
                        last_login=datetime.fromisoformat(user_data["last_login"]) if user_data.get("last_login") else None,
                        login_count=user_data.get("login_count", 0),
                        preferences=user_data.get("preferences", {}),
                        permissions=get_default_permissions(UserRole(user_data.get("role", "user")))
                    )
                else:
                    raise Exception(f"Failed to create user: {response.status_code}")

        except Exception as e:
            print(f"❌ Error creating user: {str(e)}")
            raise

    async def update_user(self, user_id: str, user_update: UserUpdate) -> Optional[User]:
        """Update user via Backend API"""
        try:
            if not self.backend_url:
                return None

            async with httpx.AsyncClient(timeout=10.0) as client:
                update_data = user_update.model_dump(exclude_unset=True)
                response = await client.put(f"{self.backend_url}/api/v1/users/{user_id}", json=update_data)

                if response.status_code == 200:
                    user_data = response.json()
                    return User(
                        user_id=user_data["user_id"],
                        username=user_data["username"],
                        email=user_data.get("email", ""),
                        mobile_phone=user_data.get("mobile_phone", ""),
                        password_hash="***",  # Not returned by API for security
                        full_name=user_data.get("full_name", ""),
                        display_name=user_data.get("display_name", user_data.get("full_name", "")),
                        role=UserRole(user_data.get("role", "user")),
                        status=UserStatus(user_data.get("status", "active")),
                        is_admin=user_data.get("is_admin", False),
                        created_at=datetime.fromisoformat(user_data["created_at"]),
                        updated_at=datetime.fromisoformat(user_data["updated_at"]),
                        last_login=datetime.fromisoformat(user_data["last_login"]) if user_data.get("last_login") else None,
                        login_count=user_data.get("login_count", 0),
                        preferences=user_data.get("preferences", {}),
                        permissions=get_default_permissions(UserRole(user_data.get("role", "user")))
                    )
                else:
                    return None

        except Exception as e:
            print(f"❌ Error updating user: {str(e)}")
            return None

    async def delete_user(self, user_id: str) -> bool:
        """Delete user via Backend API"""
        try:
            if not self.backend_url:
                return False

            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.delete(f"{self.backend_url}/api/v1/users/{user_id}")
                return response.status_code == 200

        except Exception as e:
            print(f"❌ Error deleting user: {str(e)}")
            return False

    async def get_all_users(self) -> List[User]:
        """Get all users via Backend API"""
        try:
            if not self.backend_url:
                return []

            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{self.backend_url}/api/v1/users")

                if response.status_code == 200:
                    users_data = response.json()
                    users = []
                    for user_data in users_data:
                        users.append(User(
                            user_id=user_data["user_id"],
                            username=user_data["username"],
                            email=user_data.get("email", ""),
                            full_name=user_data.get("full_name", ""),
                            role=UserRole(user_data.get("role", "user")),
                            status=UserStatus(user_data.get("status", "active")),
                            is_admin=user_data.get("is_admin", False),
                            created_at=datetime.fromisoformat(user_data["created_at"]),
                            updated_at=datetime.fromisoformat(user_data["updated_at"]),
                            permissions=get_default_permissions(UserRole(user_data.get("role", "user")))
                        ))
                    return users
                else:
                    return []

        except Exception as e:
            print(f"❌ Error getting all users: {str(e)}")
            return []

    async def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get user by ID via Backend API"""
        try:
            if not self.backend_url:
                return None

            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{self.backend_url}/api/v1/users/by-id/{user_id}")

                if response.status_code == 200:
                    user_data = response.json()
                    return User(
                        user_id=user_data["user_id"],
                        username=user_data["username"],
                        email=user_data.get("email", ""),
                        mobile_phone=user_data.get("mobile_phone", ""),
                        password_hash="***",  # Not returned by API for security
                        full_name=user_data.get("full_name", ""),
                        display_name=user_data.get("display_name", user_data.get("full_name", "")),
                        role=UserRole(user_data.get("role", "user")),
                        status=UserStatus(user_data.get("status", "active")),
                        is_admin=user_data.get("is_admin", False),
                        created_at=datetime.fromisoformat(user_data["created_at"]),
                        updated_at=datetime.fromisoformat(user_data["updated_at"]),
                        last_login=datetime.fromisoformat(user_data["last_login"]) if user_data.get("last_login") else None,
                        login_count=user_data.get("login_count", 0),
                        preferences=user_data.get("preferences", {}),
                        permissions=get_default_permissions(UserRole(user_data.get("role", "user")))
                    )
                else:
                    return None

        except Exception as e:
            print(f"❌ Error getting user by ID: {str(e)}")
            return None

    async def change_user_password(self, user_id: str, new_password: str) -> bool:
        """
        Change user password using PBKDF2 with salt (more secure)
        This is the SELECTED ONE SYSTEM WIDE standard
        """
        try:
            if not self.backend_url:
                return False

            async with httpx.AsyncClient(timeout=10.0) as client:
                # Hash the new password using PBKDF2 with salt
                password_hash = hash_password(new_password)

                # Update user with new password hash
                update_data = {"password_hash": password_hash}
                response = await client.put(f"{self.backend_url}/api/v1/users/{user_id}", json=update_data)

                if response.status_code == 200:
                    print(f"✅ Password changed for user {user_id} using PBKDF2 with salt")
                    return True
                else:
                    print(f"❌ Failed to change password: {response.status_code}")
                    return False

        except Exception as e:
            print(f"❌ Error changing password: {str(e)}")
            return False
