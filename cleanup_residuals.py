#!/usr/bin/env python3
"""
🧹 DEEPLICA CLEANUP RESIDUALS
Ensures no Deeplica processes or resources are left behind after shutdown
"""

import os
import psutil
import subprocess
import time
import sys
from pathlib import Path
from shared.port_manager import get_service_port

class DeepplicaCleanup:
    """Comprehensive cleanup of all Deeplica residuals"""
    
    def __init__(self):
        self.cleanup_name = "DEEPLICA-CLEANUP"
        self.deeplica_ports = [get_service_port("backend"), get_service_port("dispatcher"), get_service_port("dialogue"), get_service_port("planner"), get_service_port("phone"), get_service_port("watchdog"), get_service_port("web-chat"), get_service_port("cli")]
        self.deeplica_process_names = [
            "DEEPLICA-BACKEND-API",
            "DEEPLICA-DISPATCHER", 
            "DEEPLICA-DIALOGUE-AGENT",
            "DEEPLICA-PLANNER-AGENT",
            "DEEPLICA-PHONE-AGENT",
            "DEEPLICA-WATCHDOG",
            "DEEPLICA-CLI-TERMINAL",
            ""
        ]
        self.temp_files = [
            "watchdog/registered_processes.json",

            "logs/deeplica_v0.log"
        ]

    def find_processes_by_port(self, port: int) -> list:
        """Find all processes using a specific port"""
        processes = []
        try:
            result = subprocess.run(
                ['lsof', '-ti', f':{port}'],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0 and result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                for pid_str in pids:
                    try:
                        pid = int(pid_str)
                        if psutil.pid_exists(pid):
                            process = psutil.Process(pid)
                            processes.append(process)
                    except (ValueError, psutil.NoSuchProcess):
                        continue
                        
        except Exception as e:
            print(f"[{self.cleanup_name}] ⚠️ Error finding processes on port {port}: {e}")
            
        return processes

    def find_processes_by_name(self) -> list:
        """Find all Deeplica processes by name"""
        processes = []
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    
                    # Check if it's a Deeplica process
                    is_deeplica = False
                    for process_name in self.deeplica_process_names:
                        if process_name in cmdline:
                            is_deeplica = True
                            break
                    
                    # Also check for Python processes running Deeplica files
                    if not is_deeplica and 'python' in proc.info['name'].lower():
                        deeplica_files = [
                            'watchdog/main.py',
                            'backend/app/main.py', 
                            'dispatcher/app/main.py',
                            'agents/dialogue/app/main.py',
                            'agents/planner/app/main.py',
                            'agents/phone/app/main.py',
                            'cli/main.py',

                            'startup_orchestrator.py'
                        ]
                        
                        for file_path in deeplica_files:
                            if file_path in cmdline:
                                is_deeplica = True
                                break
                    
                    if is_deeplica:
                        # Don't kill VS Code or this cleanup script
                        if 'code' not in proc.info['name'].lower() and 'cleanup_residuals.py' not in cmdline:
                            processes.append(proc)
                            
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
        except Exception as e:
            print(f"[{self.cleanup_name}] ⚠️ Error finding processes by name: {e}")
            
        return processes

    def cleanup_processes(self, force: bool = False) -> int:
        """Clean up all Deeplica processes"""
        print(f"[{self.cleanup_name}] 🔍 Scanning for Deeplica processes...")
        
        # Find processes by port
        port_processes = []
        for port in self.deeplica_ports:
            port_procs = self.find_processes_by_port(port)
            port_processes.extend(port_procs)
        
        # Find processes by name
        name_processes = self.find_processes_by_name()
        
        # Combine and deduplicate
        all_processes = []
        seen_pids = set()
        
        for proc in port_processes + name_processes:
            if proc.pid not in seen_pids:
                all_processes.append(proc)
                seen_pids.add(proc.pid)
        
        if not all_processes:
            print(f"[{self.cleanup_name}] ✅ No Deeplica processes found")
            return 0
        
        print(f"[{self.cleanup_name}] 🛑 Found {len(all_processes)} Deeplica processes to clean up:")
        
        killed_count = 0
        for proc in all_processes:
            try:
                cmdline = ' '.join(proc.cmdline())
                print(f"[{self.cleanup_name}]   🎯 PID {proc.pid}: {cmdline[:80]}...")
                
                if not force:
                    # Try graceful termination first
                    proc.terminate()
                    try:
                        proc.wait(timeout=3)
                        print(f"[{self.cleanup_name}]   ✅ Gracefully terminated PID {proc.pid}")
                        killed_count += 1
                        continue
                    except psutil.TimeoutExpired:
                        print(f"[{self.cleanup_name}]   ⚠️ PID {proc.pid} didn't terminate gracefully, forcing...")
                
                # Force kill
                proc.kill()
                print(f"[{self.cleanup_name}]   💀 Force killed PID {proc.pid}")
                killed_count += 1
                
            except psutil.NoSuchProcess:
                print(f"[{self.cleanup_name}]   ℹ️ PID {proc.pid} already terminated")
                killed_count += 1
            except psutil.AccessDenied:
                print(f"[{self.cleanup_name}]   ❌ Access denied to PID {proc.pid}")
            except Exception as e:
                print(f"[{self.cleanup_name}]   ❌ Error killing PID {proc.pid}: {e}")
        
        return killed_count

    def cleanup_temp_files(self) -> int:
        """Clean up temporary files and registries"""
        print(f"[{self.cleanup_name}] 🗂️ Cleaning up temporary files...")
        
        cleaned_count = 0
        for file_path in self.temp_files:
            try:
                path = Path(file_path)
                if path.exists():
                    path.unlink()
                    print(f"[{self.cleanup_name}]   🗑️ Removed: {file_path}")
                    cleaned_count += 1
                else:
                    print(f"[{self.cleanup_name}]   ℹ️ Not found: {file_path}")
            except Exception as e:
                print(f"[{self.cleanup_name}]   ❌ Error removing {file_path}: {e}")
        
        return cleaned_count

    def verify_cleanup(self) -> bool:
        """Verify that cleanup was successful"""
        print(f"[{self.cleanup_name}] 🔍 Verifying cleanup...")
        
        # Check for remaining processes
        remaining_processes = []
        for port in self.deeplica_ports:
            procs = self.find_processes_by_port(port)
            remaining_processes.extend(procs)
        
        name_procs = self.find_processes_by_name()
        
        # Deduplicate
        seen_pids = set()
        unique_remaining = []
        for proc in remaining_processes + name_procs:
            if proc.pid not in seen_pids:
                unique_remaining.append(proc)
                seen_pids.add(proc.pid)
        
        if unique_remaining:
            print(f"[{self.cleanup_name}] ⚠️ {len(unique_remaining)} processes still running:")
            for proc in unique_remaining:
                try:
                    cmdline = ' '.join(proc.cmdline())
                    print(f"[{self.cleanup_name}]   🔴 PID {proc.pid}: {cmdline[:60]}...")
                except:
                    print(f"[{self.cleanup_name}]   🔴 PID {proc.pid}: <unknown>")
            return False
        else:
            print(f"[{self.cleanup_name}] ✅ No Deeplica processes remaining")
            return True

    def comprehensive_cleanup(self, force: bool = False) -> bool:
        """Perform comprehensive cleanup of all Deeplica residuals"""
        print("🧹 DEEPLICA COMPREHENSIVE CLEANUP")
        print("=" * 50)
        print(f"⏰ {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("🎯 Removing all Deeplica processes and temporary files")
        print("✅ VS Code will remain unaffected")
        print("=" * 50)
        
        try:
            # Step 1: Clean up processes
            killed_count = self.cleanup_processes(force=force)
            
            # Step 2: Wait a moment for processes to fully terminate
            if killed_count > 0:
                print(f"[{self.cleanup_name}] ⏳ Waiting for processes to fully terminate...")
                time.sleep(3)
            
            # Step 3: Clean up temporary files
            cleaned_files = self.cleanup_temp_files()
            
            # Step 4: Verify cleanup
            success = self.verify_cleanup()
            
            # Summary
            print(f"\n📊 CLEANUP SUMMARY:")
            print(f"   🛑 Processes terminated: {killed_count}")
            print(f"   🗑️ Files cleaned: {cleaned_files}")
            print(f"   ✅ Cleanup successful: {success}")
            
            if success:
                print(f"\n🎉 DEEPLICA CLEANUP COMPLETED SUCCESSFULLY")
                print(f"✅ All Deeplica residuals have been removed")
                print(f"🔗 System is clean and ready for fresh start")
            else:
                print(f"\n⚠️ DEEPLICA CLEANUP COMPLETED WITH WARNINGS")
                print(f"🔧 Some processes may still be running")
                print(f"💡 Try running with --force flag for aggressive cleanup")
            
            return success
            
        except Exception as e:
            print(f"\n❌ DEEPLICA CLEANUP FAILED")
            print(f"🚨 Error: {e}")
            return False

def main():
    """Main function"""
    cleanup = DeepplicaCleanup()
    
    # Check for force flag
    force = "--force" in sys.argv
    
    try:
        success = cleanup.comprehensive_cleanup(force=force)
        if success:
            print("\n🎯 Cleanup completed successfully")
            sys.exit(0)
        else:
            print("\n💥 Cleanup completed with warnings")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print(f"\n🛑 Cleanup cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n🚨 Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
