#!/usr/bin/env python3
"""
🔍 Service Status Test
Tests all DEEPLICA service health and status endpoints
"""

import os
import sys
import asyncio
import aiohttp
import traceback
from dotenv import load_dotenv

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

# Import port manager
from shared.port_manager import get_service_port, get_service_host

async def test_service_endpoint(session, service_name, endpoint_path="/health", timeout=10):
    """Test a single service endpoint"""
    try:
        host = get_service_host(service_name)
        port = get_service_port(service_name)
        url = f"http://{host}:{port}{endpoint_path}"
        
        print(f"🔍 Testing {service_name}: {url}")
        
        async with session.get(url, timeout=aiohttp.ClientTimeout(total=timeout)) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✅ {service_name}: {data.get('status', 'OK')} - {data.get('message', 'Healthy')}")
                return True, data
            else:
                print(f"⚠️ {service_name}: HTTP {response.status}")
                return False, {"status": f"HTTP {response.status}"}
                
    except aiohttp.ClientConnectorError:
        print(f"❌ {service_name}: Connection refused (service not running)")
        return False, {"status": "connection_refused"}
    except asyncio.TimeoutError:
        print(f"⏰ {service_name}: Timeout after {timeout}s")
        return False, {"status": "timeout"}
    except Exception as e:
        print(f"❌ {service_name}: {type(e).__name__}: {e}")
        return False, {"status": f"error: {e}"}

async def test_all_service_endpoints():
    """Test all DEEPLICA service endpoints"""
    print("🔍 TESTING ALL SERVICE STATUS ENDPOINTS")
    print("=" * 60)
    
    # Define services to test
    services = [
        ("backend", "/health"),
        ("backend", "/ready"),
        ("backend", "/system/status"),
        ("dispatcher", "/health"),
        ("dialogue", "/health"),
        ("planner", "/health"),
        ("phone", "/health"),
        ("watchdog", "/status"),
        ("web-chat", "/health"),
        ("cli", "/health"),
    ]
    
    results = {}
    
    async with aiohttp.ClientSession() as session:
        for service_name, endpoint in services:
            print(f"\n📡 Testing {service_name.upper()} service...")
            success, data = await test_service_endpoint(session, service_name, endpoint)
            
            if service_name not in results:
                results[service_name] = {}
            results[service_name][endpoint] = {
                "success": success,
                "data": data
            }
    
    return results

async def test_backend_specific_endpoints():
    """Test Backend API specific endpoints"""
    print("\n🌐 TESTING BACKEND API SPECIFIC ENDPOINTS")
    print("=" * 60)
    
    backend_endpoints = [
        "/health",
        "/ready", 
        "/system/status",
        "/api/v1/health",
        "/docs",  # FastAPI docs
    ]
    
    host = get_service_host("backend")
    port = get_service_port("backend")
    
    results = {}
    
    async with aiohttp.ClientSession() as session:
        for endpoint in backend_endpoints:
            try:
                url = f"http://{host}:{port}{endpoint}"
                print(f"🔍 Testing: {url}")
                
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status == 200:
                        if endpoint == "/docs":
                            print(f"✅ {endpoint}: Documentation accessible")
                            results[endpoint] = {"success": True, "status": "accessible"}
                        else:
                            data = await response.json()
                            print(f"✅ {endpoint}: {data.get('status', 'OK')}")
                            results[endpoint] = {"success": True, "data": data}
                    else:
                        print(f"⚠️ {endpoint}: HTTP {response.status}")
                        results[endpoint] = {"success": False, "status": response.status}
                        
            except Exception as e:
                print(f"❌ {endpoint}: {type(e).__name__}: {e}")
                results[endpoint] = {"success": False, "error": str(e)}
    
    return results

async def test_service_communication():
    """Test inter-service communication"""
    print("\n🔗 TESTING INTER-SERVICE COMMUNICATION")
    print("=" * 60)
    
    # Test if services can reach each other
    communication_tests = [
        ("dispatcher", "backend", "/health"),
        ("dialogue", "backend", "/health"),
        ("planner", "backend", "/health"),
        ("phone", "backend", "/health"),
    ]
    
    results = {}
    
    async with aiohttp.ClientSession() as session:
        for source_service, target_service, endpoint in communication_tests:
            try:
                # Check if source service is running first
                source_host = get_service_host(source_service)
                source_port = get_service_port(source_service)
                source_url = f"http://{source_host}:{source_port}/health"
                
                async with session.get(source_url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                    if response.status != 200:
                        print(f"⚠️ {source_service} not running, skipping communication test")
                        continue
                
                # Test communication to target
                target_host = get_service_host(target_service)
                target_port = get_service_port(target_service)
                target_url = f"http://{target_host}:{target_port}{endpoint}"
                
                print(f"🔗 Testing {source_service} → {target_service}: {target_url}")
                
                async with session.get(target_url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status == 200:
                        data = await response.json()
                        print(f"✅ {source_service} → {target_service}: Communication successful")
                        results[f"{source_service}_to_{target_service}"] = {"success": True, "data": data}
                    else:
                        print(f"⚠️ {source_service} → {target_service}: HTTP {response.status}")
                        results[f"{source_service}_to_{target_service}"] = {"success": False, "status": response.status}
                        
            except Exception as e:
                print(f"❌ {source_service} → {target_service}: {type(e).__name__}: {e}")
                results[f"{source_service}_to_{target_service}"] = {"success": False, "error": str(e)}
    
    return results

def print_summary(all_results, backend_results, communication_results):
    """Print test summary"""
    print("\n📊 SERVICE STATUS TEST SUMMARY")
    print("=" * 80)
    
    # Count results
    total_services = len(all_results)
    healthy_services = sum(1 for service_data in all_results.values() 
                          if any(endpoint_data["success"] for endpoint_data in service_data.values()))
    
    total_backend_endpoints = len(backend_results)
    healthy_backend_endpoints = sum(1 for result in backend_results.values() if result["success"])
    
    total_communication_tests = len(communication_results)
    successful_communication = sum(1 for result in communication_results.values() if result["success"])
    
    print(f"🏥 Service Health: {healthy_services}/{total_services} services responding")
    print(f"🌐 Backend Endpoints: {healthy_backend_endpoints}/{total_backend_endpoints} endpoints working")
    print(f"🔗 Inter-service Communication: {successful_communication}/{total_communication_tests} tests passed")
    
    print("\n📋 DETAILED RESULTS:")
    print("-" * 40)
    
    for service_name, endpoints in all_results.items():
        status = "✅" if any(ep["success"] for ep in endpoints.values()) else "❌"
        print(f"{status} {service_name.upper()}")
        for endpoint, result in endpoints.items():
            endpoint_status = "✅" if result["success"] else "❌"
            print(f"   {endpoint_status} {endpoint}")
    
    # Overall status
    if healthy_services == total_services and healthy_backend_endpoints == total_backend_endpoints:
        print("\n🎉 ALL SERVICES ARE HEALTHY AND RESPONDING!")
        return True
    else:
        print(f"\n⚠️ {total_services - healthy_services} services need attention")
        return False

async def main():
    """Main test function"""
    print("🔍 DEEPLICA SERVICE STATUS TEST")
    print("=" * 80)
    
    try:
        # Test all service endpoints
        all_results = await test_all_service_endpoints()
        
        # Test backend specific endpoints
        backend_results = await test_backend_specific_endpoints()
        
        # Test inter-service communication
        communication_results = await test_service_communication()
        
        # Print summary
        success = print_summary(all_results, backend_results, communication_results)
        
        return success
        
    except Exception as e:
        print(f"❌ Service status test failed: {e}")
        print(f"🔍 Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
