#!/usr/bin/env python3
"""
🚀 START DEEPLICA WITH MANDATORY REGISTRATION
Ensures Stop Deeplica Service starts first, then all other services register
"""

import asyncio
import subprocess
import time
import httpx
import os
import signal
import sys
from pathlib import Path
from shared.port_manager import get_service_port

class DeepplicaStarter:
    """Manages startup with mandatory registration"""
    
    def __init__(self):
        self.stop_service_port = 8006
        self.stop_service_url = f"http://localhost:{self.stop_service_port}"
        self.stop_service_process = None
        self.started_processes = []
        
    async def check_stop_service(self) -> bool:
        """Check if Stop Deeplica Service is running"""
        try:
            async with httpx.AsyncClient(timeout=3.0) as client:
                response = await client.get(f"{self.stop_service_url}/health")
                return response.status_code == 200
        except:
            return False
    
    async def start_stop_service(self) -> bool:
        """Start the Stop Deeplica Service first"""
        print("🛑 Starting Stop Deeplica Service (MANDATORY for all other services)...")
        
        try:
            # Start Stop Deeplica Service
            self.stop_service_process = subprocess.Popen([
                sys.executable, "stop_deeplica_service/main.py"
            ], cwd=os.getcwd())
            
            # Wait for it to be ready
            for attempt in range(30):  # 30 seconds max
                await asyncio.sleep(1)
                if await self.check_stop_service():
                    print("✅ Stop Deeplica Service is ready and accepting registrations")
                    return True
                print(f"⏳ Waiting for Stop Deeplica Service... ({attempt + 1}/30)")
            
            print("❌ Stop Deeplica Service failed to start within 30 seconds")
            return False
            
        except Exception as e:
            print(f"❌ Failed to start Stop Deeplica Service: {e}")
            return False
    
    def start_service_with_registration(self, service_name: str, script_path: str, port: int = None):
        """Start a service that will register itself"""
        print(f"🚀 Starting {service_name}...")
        
        env = os.environ.copy()
        env["PYTHONPATH"] = os.getcwd()
        if port:
            env["PORT"] = str(port)
        
        process = subprocess.Popen([
            sys.executable, script_path
        ], cwd=os.getcwd(), env=env)
        
        self.started_processes.append({
            "name": service_name,
            "process": process,
            "port": port
        })
        
        # Give service time to start and register
        time.sleep(2)
    
    async def start_all_services(self):
        """Start all services with mandatory registration"""
        print("🚀 DEEPLICA V3 STARTUP WITH MANDATORY REGISTRATION")
        print("=" * 60)
        
        # Step 1: Start Stop Deeplica Service FIRST
        if not await self.start_stop_service():
            print("🚨 CRITICAL: Cannot start without Stop Deeplica Service")
            return False
        
        print("\n📋 Starting all services in proper dependency order...")
        print("🔒 Each service MUST register with Stop Deeplica Service")

        # Step 2: Start all other services in dependency order
        # Backend API must start first, Watchdog must start last
        services = [
            ("Backend API", "backend/app/main.py", 8000),
            ("Dispatcher", "dispatcher/main.py", get_service_port("dispatcher")),
            ("Dialogue Agent", "agents/dialogue/app/main.py", get_service_port("dialogue")),
            ("Planner Agent", "agents/planner/app/main.py", get_service_port("planner")),
            ("Phone Agent", "agents/phone/app/main.py", get_service_port("phone")),
            ("CLI Terminal", "cli/main.py", None),
            ("Watchdog", "watchdog/main.py", get_service_port("watchdog"))  # Watchdog LAST
        ]
        
        for service_name, script_path, port in services:
            if Path(script_path).exists():
                self.start_service_with_registration(service_name, script_path, port)
            else:
                print(f"⚠️ Skipping {service_name}: {script_path} not found")
        
        print(f"\n✅ Started {len(self.started_processes)} services")
        print("🔒 All services are now registered with Stop Deeplica Service")
        
        # Step 3: Wait and monitor
        await self.monitor_services()
        
        return True
    
    async def monitor_services(self):
        """Monitor services and handle shutdown"""
        print("\n🔍 Monitoring services... (Press Ctrl+C to stop all)")
        
        try:
            while True:
                # Check if any process has died
                for service_info in self.started_processes[:]:  # Copy list to avoid modification during iteration
                    if service_info["process"].poll() is not None:
                        print(f"💀 {service_info['name']} has stopped")
                        self.started_processes.remove(service_info)
                
                # Check Stop Deeplica Service
                if self.stop_service_process and self.stop_service_process.poll() is not None:
                    print("🚨 Stop Deeplica Service has stopped - this is critical!")
                    break
                
                await asyncio.sleep(5)
                
        except KeyboardInterrupt:
            print("\n🛑 Shutdown requested...")
            await self.shutdown_all()
    
    async def shutdown_all(self):
        """Shutdown all services via Stop Deeplica Service"""
        print("🛑 Requesting shutdown of all registered services...")
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{self.stop_service_url}/stop_all",
                    json={
                        "force": False,
                        "reason": "Startup script shutdown",
                        "shutdown_self": True  # Shutdown Stop Deeplica Service too
                    }
                )
                
                if response.status_code == 200:
                    print("✅ All registered services stopped successfully")
                else:
                    print(f"⚠️ Stop request returned: {response.status_code}")
                    
        except Exception as e:
            print(f"⚠️ Could not contact Stop Deeplica Service: {e}")
            print("🔥 Force stopping all processes...")
            
            # Force stop all processes
            for service_info in self.started_processes:
                try:
                    service_info["process"].terminate()
                    service_info["process"].wait(timeout=5)
                except:
                    service_info["process"].kill()
        
        # Stop the Stop Deeplica Service last
        if self.stop_service_process:
            try:
                self.stop_service_process.terminate()
                self.stop_service_process.wait(timeout=5)
            except:
                self.stop_service_process.kill()
        
        print("✅ All services stopped")

async def main():
    """Main startup function"""
    starter = DeepplicaStarter()
    
    # Setup signal handlers
    def signal_handler(signum, frame):
        print(f"\n🛑 Received signal {signum}")
        asyncio.create_task(starter.shutdown_all())
    
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        success = await starter.start_all_services()
        if not success:
            print("❌ Startup failed")
            sys.exit(1)
    except Exception as e:
        print(f"❌ Startup error: {e}")
        await starter.shutdown_all()
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
