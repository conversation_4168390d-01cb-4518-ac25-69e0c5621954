# 📚 DEEPLICA V3 API REFERENCE

## **Complete API Documentation for All Services**

---

## 🌐 **Backend API (Port 8888 - managed by port_manager.py)**

> **⚠️ PORT MANAGEMENT**: All ports are managed by `shared/port_manager.py`. Use `get_service_port('service_name')` to get actual ports.

### **Base URL**: `http://localhost:8888` (Backend API - managed by port_manager.py)

### **Health & Status**
```http
GET /health
```
**Response**:
```json
{
  "status": "healthy",
  "service": "backend-api",
  "database_ready": true,
  "timestamp": "2025-07-06T10:30:00Z"
}
```

### **Mission Management**

#### Create Mission
```http
POST /api/v1/missions
Content-Type: application/json

{
  "user_input": "Call John at +********** and ask about the project status",
  "title": "Project Status Check",
  "description": "Follow up on project deliverables",
  "priority": "normal"
}
```

**Response**:
```json
{
  "mission_id": "uuid-here",
  "status": "created",
  "tasks": [],
  "created_at": "2025-07-06T10:30:00Z"
}
```

#### Get Mission Status
```http
GET /api/v1/missions/{mission_id}
```

**Response**:
```json
{
  "mission_id": "uuid-here",
  "status": "in_progress",
  "title": "Project Status Check",
  "description": "Follow up on project deliverables",
  "priority": "normal",
  "tasks": [
    {
      "task_id": "task-uuid",
      "task_type": "phone_call",
      "status": "completed",
      "task_data": {
        "phone_number": "+**********",
        "contact_name": "John",
        "question": "What's the status of the project?",
        "response": "Project is on track, delivery next week"
      }
    }
  ],
  "created_at": "2025-07-06T10:30:00Z",
  "updated_at": "2025-07-06T10:35:00Z"
}
```

---

## 🎯 **Dispatcher (Port 8001)**

### **Base URL**: `http://localhost:8001` (Dispatcher - managed by port_manager.py)

### **Health Check**
```http
GET /health
```

### **Task Callbacks**
```http
POST /task_callback
Content-Type: application/json

{
  "task_id": "task-uuid",
  "status": "completed",
  "result": {
    "success": true,
    "data": "Task completed successfully"
  }
}
```

### **System Status**
```http
GET /status
```

**Response**:
```json
{
  "system_status": "healthy",
  "active_tasks": 3,
  "completed_tasks": 15,
  "failed_tasks": 1,
  "services": {
    "backend": "healthy",
    "dialogue": "healthy",
    "planner": "healthy",
    "phone": "healthy",
    "watchdog": "healthy"
  }
}
```

---

## 💬 **Dialogue Agent (Port 8002)**

### **Base URL**: `http://localhost:8002` (Dialogue Agent - managed by port_manager.py)

### **Chat Processing**
```http
POST /chat
Content-Type: application/json

{
  "message": "I need to call my client about the contract",
  "conversation_id": "conv-uuid",
  "user_id": "user-123"
}
```

**Response**:
```json
{
  "response": "I can help you call your client. What's their phone number and what would you like to discuss about the contract?",
  "conversation_id": "conv-uuid",
  "intent": "phone_call_request",
  "entities": {
    "action": "call",
    "topic": "contract",
    "recipient": "client"
  }
}
```

### **Conversation History**
```http
GET /conversation/{conversation_id}
```

**Response**:
```json
{
  "conversation_id": "conv-uuid",
  "messages": [
    {
      "timestamp": "2025-07-06T10:30:00Z",
      "role": "user",
      "content": "I need to call my client about the contract"
    },
    {
      "timestamp": "2025-07-06T10:30:01Z",
      "role": "assistant",
      "content": "I can help you call your client..."
    }
  ]
}
```

---

## 🧠 **Planner Agent (Port 8003)**

### **Base URL**: `http://localhost:8003` (Planner Agent - managed by port_manager.py)

### **Create Mission Plan**
```http
POST /plan
Content-Type: application/json

{
  "mission_id": "mission-uuid",
  "user_input": "Call John and then email Sarah with the results",
  "context": {
    "user_preferences": {},
    "available_contacts": []
  }
}
```

**Response**:
```json
{
  "plan_id": "plan-uuid",
  "mission_id": "mission-uuid",
  "tasks": [
    {
      "task_id": "task-1",
      "task_type": "phone_call",
      "dependencies": [],
      "task_data": {
        "phone_number": "+**********",
        "contact_name": "John",
        "question": "Project status update"
      }
    },
    {
      "task_id": "task-2",
      "task_type": "email",
      "dependencies": ["task-1"],
      "task_data": {
        "recipient": "<EMAIL>",
        "subject": "Update from John",
        "template": "results_summary"
      }
    }
  ]
}
```

---

## 📞 **Phone Agent (Port 8004)**

### **Base URL**: `http://localhost:8004` (Phone Agent - managed by port_manager.py)

### **Execute Phone Call**
```http
POST /execute
Content-Type: application/json

{
  "task_id": "task-uuid",
  "mission_id": "mission-uuid",
  "task_type": "phone_call",
  "task_data": {
    "phone_number": "+**********",
    "contact_name": "John",
    "context": "Following up on project status",
    "question": "What's the current status of the project?"
  },
  "mission_context": {
    "mission_id": "mission-uuid",
    "user_input": "Call John about project",
    "title": "Project Status Check"
  },
  "callback_url": "http://localhost:8001/task_callback"  // Dispatcher port
}
```

**Response**:
```json
{
  "status": "accepted",
  "task_id": "task-uuid",
  "call_initiated": true,
  "estimated_duration": "2-5 minutes"
}
```

### **Twilio Webhooks**
```http
POST /voice/{task_id}
Content-Type: application/x-www-form-urlencoded

CallSid=CA123&From=%2B**********&To=%2B0987654321
```

**Response**: TwiML XML for call handling

### **Configuration Management**
```http
POST /reload_config
```

### **Emergency Stop**
```http
POST /emergency_stop
```

---

## 🐕 **Watchdog (Port 8005)**

### **Base URL**: `http://localhost:8005` (Watchdog - managed by port_manager.py)

### **System Status**
```http
GET /system_status
```

**Response**:
```json
{
  "system_health": "healthy",
  "services": {
    "backend": {
      "status": "healthy",
      "response_time": "45ms",
      "last_check": "2025-07-06T10:30:00Z"
    },
    "dispatcher": {
      "status": "healthy",
      "response_time": "32ms",
      "last_check": "2025-07-06T10:30:00Z"
    }
  },
  "external_services": {
    "mongodb": {
      "status": "connected",
      "response_time": "120ms"
    },
    "twilio": {
      "status": "connected",
      "last_test": "2025-07-06T10:25:00Z"
    },
    "ngrok": {
      "status": "active",
      "tunnel_url": "https://abc123.ngrok-free.app"
    }
  }
}
```

### **Performance Metrics**
```http
GET /metrics
```

**Response**:
```json
{
  "uptime": "24h 15m 30s",
  "total_requests": 1547,
  "average_response_time": "85ms",
  "error_rate": "0.2%",
  "active_connections": 12,
  "memory_usage": "245MB",
  "cpu_usage": "15%"
}
```

### **Manual Recovery**
```http
POST /recover/{service_name}
```

---

## 🔧 **Common Response Codes**

### **Success Codes**
- `200 OK` - Request successful
- `201 Created` - Resource created successfully
- `202 Accepted` - Request accepted for processing

### **Error Codes**
- `400 Bad Request` - Invalid request format
- `401 Unauthorized` - Authentication required
- `404 Not Found` - Resource not found
- `500 Internal Server Error` - Server error
- `503 Service Unavailable` - Service temporarily unavailable

### **Error Response Format**
```json
{
  "error": {
    "code": "INVALID_PHONE_NUMBER",
    "message": "Phone number format is invalid",
    "details": {
      "field": "phone_number",
      "provided": "123-456",
      "expected": "+**********"
    }
  },
  "timestamp": "2025-07-06T10:30:00Z",
  "request_id": "req-uuid"
}
```

---

## 🔐 **Authentication**

### **API Key Authentication**
```http
Authorization: Bearer your-api-key-here
```

### **Service-to-Service Authentication**
Internal services use shared secrets for authentication.

---

## 📊 **Rate Limiting**

- **Default Limit**: 100 requests per minute per IP
- **Phone Calls**: 10 calls per hour per user
- **Mission Creation**: 50 missions per hour per user

---

**Next**: See [OPERATIONS_GUIDE.md](OPERATIONS_GUIDE.md) for operational procedures
