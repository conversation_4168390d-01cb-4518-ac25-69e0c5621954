"""
User service for database operations and authentication.

This module handles all user-related database operations, authentication,
and session management for the web chat service.
"""

import os
import uuid
import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
# NO DIRECT DATABASE ACCESS - ALL OPERATIONS ROUTE THROUGH BACKEND API
# from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase, AsyncIOMotorCollection

from shared_models.user import (
    User, UserCreate, UserUpdate, UserLogin, UserSession, UserResponse,
    UserRole, UserStatus, UserPermission, get_default_permissions
)


class UserService:
    """Service for user management and authentication - ROUTES THROUGH BACKEND API ONLY"""

    def __init__(self):
        # NO DIRECT DATABASE ACCESS - ALL OPERATIONS ROUTE THROUGH BACKEND API
        self.backend_url = None

        # Session settings
        self.session_duration_hours = 24  # Sessions last 24 hours

    def get_backend_api_url(self):
        """Get Backend API URL - NO DIRECT DATABASE ACCESS ALLOWED"""
        try:
            from shared.api_manager import get_service_url
            backend_url = get_service_url("backend")
            if backend_url:
                return backend_url

            # Fallback to environment variable or default
            backend_port = os.getenv("BACKEND_PORT", "8888")
            return f"http://127.0.0.1:{backend_port}"
        except Exception as e:
            print(f"❌ Failed to get backend API URL: {str(e)}")
            return None
    
    async def connect(self) -> None:
        """Initialize connection to Backend API - NO DIRECT DATABASE ACCESS"""
        try:
            self.backend_url = self.get_backend_api_url()
            if not self.backend_url:
                raise Exception("Backend API URL not available")

            # Test Backend API connection
            import httpx
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{self.backend_url}/health")
                if response.status_code == 200:
                    print(f"✅ User service connected to Backend API: {self.backend_url}")

                    # Ensure default admin user exists via Backend API
                    await self._ensure_default_admin_via_api()
                else:
                    raise Exception(f"Backend API health check failed: {response.status_code}")

        except Exception as e:
            print(f"❌ Failed to connect to Backend API: {str(e)}")
            raise
    
    async def _ensure_default_admin_via_api(self) -> None:
        """Ensure default admin user exists via Backend API - NO DIRECT DATABASE ACCESS"""
        try:
            if not self.backend_url:
                return

            import httpx
            async with httpx.AsyncClient(timeout=10.0) as client:
                # Check if admin user exists
                response = await client.get(f"{self.backend_url}/api/v1/users/admin")
                if response.status_code == 404:
                    # Create default admin user
                    admin_data = {
                        "username": "admin",
                        "email": "<EMAIL>",
                        "password": "admin123",
                        "full_name": "System Administrator",
                        "role": "admin",
                        "is_admin": True
                    }

                    create_response = await client.post(f"{self.backend_url}/api/v1/users", json=admin_data)
                    if create_response.status_code == 201:
                        print("✅ Default admin user created via Backend API")
                    else:
                        print(f"⚠️ Failed to create admin user: {create_response.status_code}")
                elif response.status_code == 200:
                    print("✅ Default admin user already exists")

        except Exception as e:
            print(f"⚠️ Failed to ensure admin user via API: {str(e)}")
    
    async def _setup_mock_data(self) -> None:
        """Setup mock data for development"""
        self._mock_users = {}
        self._mock_sessions = {}
        
        # Create default admin user
        admin_user = await self._create_user_object(UserCreate(
            username="admin",
            password="admin123",
            full_name="Administrator",
            role=UserRole.ADMIN,
            permissions=get_default_permissions(UserRole.ADMIN)
        ))
        self._mock_users[admin_user.user_id] = admin_user
        
        # Create demo user
        demo_user = await self._create_user_object(UserCreate(
            username="demo",
            password="demo123",
            full_name="Demo User",
            role=UserRole.USER,
            permissions=get_default_permissions(UserRole.USER)
        ))
        self._mock_users[demo_user.user_id] = demo_user
    
    async def _create_default_admin(self) -> None:
        """Create default admin user if none exists"""
        if self.users_collection is None:
            return
            
        # Check if any admin users exist
        admin_count = await self.users_collection.count_documents({"role": UserRole.ADMIN})
        
        if admin_count == 0:
            # Create default admin
            admin_create = UserCreate(
                username="admin",
                password="admin123",
                full_name="Administrator",
                role=UserRole.ADMIN,
                permissions=get_default_permissions(UserRole.ADMIN)
            )
            await self.create_user(admin_create)
            print("✅ Created default admin user: admin/admin123")
    
    def _hash_password(self, password: str) -> str:
        """Hash a password with salt"""
        salt = secrets.token_hex(32)
        password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
        return f"{salt}:{password_hash.hex()}"
    
    def _verify_password(self, password: str, password_hash: str) -> bool:
        """Verify a password against its hash"""
        try:
            salt, hash_hex = password_hash.split(':')
            password_hash_check = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
            return password_hash_check.hex() == hash_hex
        except:
            return False
    
    async def _create_user_object(self, user_create: UserCreate) -> User:
        """Create a User object from UserCreate"""
        user_id = str(uuid.uuid4())
        password_hash = self._hash_password(user_create.password)
        
        return User(
            user_id=user_id,
            username=user_create.username.lower(),  # Store username in lowercase
            email=user_create.email,
            mobile_phone=user_create.mobile_phone,
            password_hash=password_hash,
            full_name=user_create.full_name,
            display_name=user_create.display_name or user_create.full_name,
            role=user_create.role,
            status=user_create.status,
            permissions=user_create.permissions or get_default_permissions(user_create.role),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    
    async def create_user(self, user_create: UserCreate) -> UserResponse:
        """Create a new user"""
        # Check if username already exists (case insensitive)
        existing_user = await self.get_user_by_username(user_create.username)
        if existing_user:
            raise ValueError(f"Username '{user_create.username}' already exists")
        
        # Create user object
        user = await self._create_user_object(user_create)
        
        if self.users_collection is not None:
            # Save to database
            user_dict = user.model_dump()
            user_dict['created_at'] = user.created_at.isoformat()
            user_dict['updated_at'] = user.updated_at.isoformat()
            if user.last_login:
                user_dict['last_login'] = user.last_login.isoformat()
            
            await self.users_collection.insert_one(user_dict)
        else:
            # Mock mode
            self._mock_users[user.user_id] = user
        
        return UserResponse(
            user_id=user.user_id,
            username=user.username,
            email=user.email,
            mobile_phone=user.mobile_phone,
            full_name=user.full_name,
            display_name=user.display_name,
            role=user.role,
            status=user.status,
            permissions=user.permissions,
            created_at=user.created_at,
            last_login=user.last_login,
            login_count=user.login_count
        )
    
    async def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username (case insensitive)"""
        username_lower = username.lower()
        
        if self.users_collection is not None:
            user_dict = await self.users_collection.find_one({"username": username_lower})
            if user_dict:
                # Convert datetime strings back to datetime objects
                if 'created_at' in user_dict and isinstance(user_dict['created_at'], str):
                    user_dict['created_at'] = datetime.fromisoformat(user_dict['created_at'])
                if 'updated_at' in user_dict and isinstance(user_dict['updated_at'], str):
                    user_dict['updated_at'] = datetime.fromisoformat(user_dict['updated_at'])
                if 'last_login' in user_dict and user_dict['last_login'] and isinstance(user_dict['last_login'], str):
                    user_dict['last_login'] = datetime.fromisoformat(user_dict['last_login'])
                
                return User(**user_dict)
        else:
            # Mock mode
            for user in self._mock_users.values():
                if user.username == username_lower:
                    return user
        
        return None
    
    async def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """Authenticate user with username and password"""
        user = await self.get_user_by_username(username)
        
        if user and user.status == UserStatus.ACTIVE:
            if self._verify_password(password, user.password_hash):
                # Update login info
                user.last_login = datetime.now()
                user.login_count += 1
                await self._update_user_login_info(user)
                return user
        
        return None
    
    async def _update_user_login_info(self, user: User) -> None:
        """Update user login information"""
        if self.users_collection is not None:
            await self.users_collection.update_one(
                {"user_id": user.user_id},
                {
                    "$set": {
                        "last_login": user.last_login.isoformat(),
                        "login_count": user.login_count,
                        "updated_at": datetime.now().isoformat()
                    }
                }
            )
        # Mock mode updates happen in-place
    
    async def create_session(self, user: User) -> str:
        """Create a new session for user - ensures only ONE session per user"""
        # First, invalidate any existing sessions for this user
        await self.invalidate_user_sessions(user.user_id)

        # Generate unique session ID with user context
        session_id = f"{user.user_id}_{str(uuid.uuid4())}"
        expires_at = datetime.now() + timedelta(hours=self.session_duration_hours)

        session = UserSession(
            session_id=session_id,
            user_id=user.user_id,
            username=user.username,
            expires_at=expires_at
        )

        if self.sessions_collection is not None:
            session_dict = session.model_dump()
            session_dict['created_at'] = session.created_at.isoformat()
            session_dict['expires_at'] = session.expires_at.isoformat()
            session_dict['last_activity'] = session.last_activity.isoformat()

            await self.sessions_collection.insert_one(session_dict)
        else:
            # Mock mode
            self._mock_sessions[session_id] = session

        return session_id

    async def invalidate_user_sessions(self, user_id: str) -> None:
        """Invalidate all existing sessions for a user (ensures only one session per user)"""
        if self.sessions_collection is not None:
            # Delete all existing sessions for this user
            await self.sessions_collection.delete_many({"user_id": user_id})
        else:
            # Mock mode - remove all sessions for this user
            if hasattr(self, '_mock_sessions'):
                sessions_to_remove = [
                    session_id for session_id, session in self._mock_sessions.items()
                    if session.user_id == user_id
                ]
                for session_id in sessions_to_remove:
                    del self._mock_sessions[session_id]

    async def get_active_session_for_user(self, user_id: str) -> Optional[str]:
        """Get active session ID for a user (if any)"""
        if self.sessions_collection is not None:
            session_dict = await self.sessions_collection.find_one({"user_id": user_id})
            if session_dict:
                expires_at = datetime.fromisoformat(session_dict['expires_at'])
                if expires_at > datetime.now():
                    return session_dict['session_id']
                else:
                    # Session expired, clean it up
                    await self.sessions_collection.delete_one({"session_id": session_dict['session_id']})
        else:
            # Mock mode
            if hasattr(self, '_mock_sessions'):
                for session_id, session in self._mock_sessions.items():
                    if session.user_id == user_id and session.expires_at > datetime.now():
                        return session_id
        return None

    async def get_user_from_session(self, session_id: str) -> Optional[User]:
        """Get user from session ID"""
        if self.sessions_collection is not None:
            session_dict = await self.sessions_collection.find_one({"session_id": session_id})
            if session_dict:
                expires_at = datetime.fromisoformat(session_dict['expires_at'])
                if expires_at > datetime.now():
                    return await self.get_user_by_username(session_dict['username'])
        else:
            # Mock mode
            session = self._mock_sessions.get(session_id)
            if session and session.expires_at > datetime.now():
                return await self.get_user_by_username(session.username)
        
        return None
    
    async def delete_session(self, session_id: str) -> None:
        """Delete a session"""
        if self.sessions_collection is not None:
            await self.sessions_collection.delete_one({"session_id": session_id})
        else:
            # Mock mode
            self._mock_sessions.pop(session_id, None)

    async def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get user by user ID"""
        if self.users_collection is not None:
            user_dict = await self.users_collection.find_one({"user_id": user_id})
            if user_dict:
                # Convert datetime strings back to datetime objects
                if 'created_at' in user_dict and isinstance(user_dict['created_at'], str):
                    user_dict['created_at'] = datetime.fromisoformat(user_dict['created_at'])
                if 'updated_at' in user_dict and isinstance(user_dict['updated_at'], str):
                    user_dict['updated_at'] = datetime.fromisoformat(user_dict['updated_at'])
                if 'last_login' in user_dict and user_dict['last_login'] and isinstance(user_dict['last_login'], str):
                    user_dict['last_login'] = datetime.fromisoformat(user_dict['last_login'])

                return User(**user_dict)
        else:
            # Mock mode
            return self._mock_users.get(user_id)

        return None

    async def update_user(self, user_id: str, user_update: UserUpdate) -> Optional[UserResponse]:
        """Update user information"""
        user = await self.get_user_by_id(user_id)
        if not user:
            return None

        # Update fields
        update_data = {}
        if user_update.new_user_id is not None:
            user.user_id = user_update.new_user_id
            update_data['user_id'] = user_update.new_user_id
        if user_update.email is not None:
            user.email = user_update.email
            update_data['email'] = user_update.email
        if user_update.mobile_phone is not None:
            user.mobile_phone = user_update.mobile_phone
            update_data['mobile_phone'] = user_update.mobile_phone
        if user_update.full_name is not None:
            user.full_name = user_update.full_name
            update_data['full_name'] = user_update.full_name
        if user_update.display_name is not None:
            user.display_name = user_update.display_name
            update_data['display_name'] = user_update.display_name
        if user_update.role is not None:
            user.role = user_update.role
            update_data['role'] = user_update.role
        if user_update.status is not None:
            user.status = user_update.status
            update_data['status'] = user_update.status
        if user_update.permissions is not None:
            user.permissions = user_update.permissions
            update_data['permissions'] = user_update.permissions
        if user_update.preferences is not None:
            user.preferences = user_update.preferences
            update_data['preferences'] = user_update.preferences
        if user_update.password is not None:
            user.password_hash = self._hash_password(user_update.password)
            update_data['password_hash'] = user.password_hash

        user.updated_at = datetime.now()
        update_data['updated_at'] = user.updated_at.isoformat()

        if self.users_collection is not None:
            if user_update.new_user_id is not None:
                # If User_ID is being changed, we need to handle it specially
                # First update all fields except user_id
                temp_update_data = {k: v for k, v in update_data.items() if k != 'user_id'}
                if temp_update_data:
                    await self.users_collection.update_one(
                        {"user_id": user_id},
                        {"$set": temp_update_data}
                    )
                # Then update the user_id field
                await self.users_collection.update_one(
                    {"user_id": user_id},
                    {"$set": {"user_id": user_update.new_user_id}}
                )
                # Also update sessions to use new user_id
                await self.sessions_collection.update_many(
                    {"user_id": user_id},
                    {"$set": {"user_id": user_update.new_user_id}}
                )
            else:
                # Normal update without User_ID change
                await self.users_collection.update_one(
                    {"user_id": user_id},
                    {"$set": update_data}
                )
        else:
            # Mock mode updates happen in-place
            if user_update.new_user_id is not None:
                # Update mock data with new user_id
                old_user = self._mock_users.pop(user_id, None)
                if old_user:
                    self._mock_users[user_update.new_user_id] = user

        return UserResponse(
            user_id=user.user_id,
            username=user.username,
            email=user.email,
            mobile_phone=user.mobile_phone,
            full_name=user.full_name,
            display_name=user.display_name,
            role=user.role,
            status=user.status,
            permissions=user.permissions,
            created_at=user.created_at,
            last_login=user.last_login,
            login_count=user.login_count
        )

    async def delete_user(self, user_id: str) -> bool:
        """Delete a user"""
        user = await self.get_user_by_id(user_id)
        if not user:
            return False

        if self.users_collection is not None:
            result = await self.users_collection.delete_one({"user_id": user_id})
            # Also delete all sessions for this user
            await self.sessions_collection.delete_many({"user_id": user_id})
            return result.deleted_count > 0
        else:
            # Mock mode
            if user_id in self._mock_users:
                del self._mock_users[user_id]
                # Delete sessions
                sessions_to_delete = [sid for sid, session in self._mock_sessions.items() if session.user_id == user_id]
                for sid in sessions_to_delete:
                    del self._mock_sessions[sid]
                return True

        return False

    async def get_all_users(self) -> List[UserResponse]:
        """Get all users"""
        users = []

        if self.users_collection is not None:
            cursor = self.users_collection.find({})
            async for user_dict in cursor:
                # Convert datetime strings back to datetime objects
                if 'created_at' in user_dict and isinstance(user_dict['created_at'], str):
                    user_dict['created_at'] = datetime.fromisoformat(user_dict['created_at'])
                if 'updated_at' in user_dict and isinstance(user_dict['updated_at'], str):
                    user_dict['updated_at'] = datetime.fromisoformat(user_dict['updated_at'])
                if 'last_login' in user_dict and user_dict['last_login'] and isinstance(user_dict['last_login'], str):
                    user_dict['last_login'] = datetime.fromisoformat(user_dict['last_login'])

                user = User(**user_dict)
                users.append(UserResponse(
                    user_id=user.user_id,
                    username=user.username,
                    email=user.email,
                    mobile_phone=user.mobile_phone,
                    full_name=user.full_name,
                    display_name=user.display_name,
                    role=user.role,
                    status=user.status,
                    permissions=user.permissions,
                    created_at=user.created_at,
                    last_login=user.last_login,
                    login_count=user.login_count
                ))
        else:
            # Mock mode
            for user in self._mock_users.values():
                users.append(UserResponse(
                    user_id=user.user_id,
                    username=user.username,
                    email=user.email,
                    mobile_phone=user.mobile_phone,
                    full_name=user.full_name,
                    display_name=user.display_name,
                    role=user.role,
                    status=user.status,
                    permissions=user.permissions,
                    created_at=user.created_at,
                    last_login=user.last_login,
                    login_count=user.login_count
                ))

        return users


# Global user service instance
user_service = UserService()
