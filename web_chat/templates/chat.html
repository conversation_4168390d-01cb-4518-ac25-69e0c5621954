<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 Deeplica Chat - Working Version</title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <style>
        :root {
            --chat-font-size: 1.2rem;
            --input-font-size: 1.1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* CSS Variables for Theming */
        :root {
            --bg-gradient: linear-gradient(135deg, #f5f5f5 0%, #e9ecef 50%, #dee2e6 100%);
            --text-color: #2c3e50;
            --header-bg: white;
            --header-text: #2c3e50;
            --chat-bg: white;
            --chat-text: #2c3e50;
            --user-bubble-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --user-bubble-text: white;
            --deeplica-bubble-bg: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
            --deeplica-bubble-text: white;
            --input-bg: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            --input-text: #2c3e50;
            --input-border: #e9ecef;
            --scrollbar-track: #f1f1f1;
            --scrollbar-thumb: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --font-size: 16px;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: var(--bg-gradient);
            color: var(--text-color);
            min-height: 100vh;
            max-height: 100vh; /* Prevent overflow */
            overflow: hidden; /* Prevent page scrolling */
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: var(--header-bg);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0; /* Don't shrink header */
            transition: all 0.3s ease;
        }

        .header h1 {
            color: #00d4ff;
            font-size: 2rem;
            margin: 0;
        }

        .header-controls {
            display: flex;
            gap: 10px;
        }

        .control-btn {
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .control-btn:hover {
            background: #0056b3;
        }

        .chat-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            height: calc(100vh - 150px); /* Adjust based on header height */
            min-height: 400px;
        }

        .status {
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-weight: 600;
            text-align: center;
        }

        .status.connecting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .messages {
            flex: 1; /* Take up remaining space */
            overflow-y: auto;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            background: #fafafa;
            min-height: 200px;
            /* Custom scrollbar styling */
            scrollbar-width: thin;
            scrollbar-color: #007bff #f1f1f1;
        }

        /* Custom scrollbar for webkit browsers */
        .messages::-webkit-scrollbar {
            width: 12px;
        }

        .messages::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .messages::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            border-radius: 10px;
            border: 2px solid #f1f1f1;
        }

        .messages::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #0056b3 0%, #007bff 100%);
        }

        .signout-btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            border: none;
            padding: 10px 18px;
            border-radius: 8px;
            color: white;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .signout-btn:hover {
            background: linear-gradient(45deg, #c0392b, #e74c3c);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
        }

        .signout-btn:active {
            transform: translateY(0);
        }

        .signout-icon {
            font-size: 1rem;
        }

        .signout-text {
            font-weight: 700;
        }

        .message {
            margin: 12px 0;
            padding: 12px 16px;
            border-radius: 12px;
            font-size: var(--chat-font-size);
            line-height: 1.5;
            animation: fadeIn 0.3s ease-in;
            word-wrap: break-word;
            word-break: break-word;
            white-space: pre-wrap; /* Preserve line breaks but wrap text */
            overflow-wrap: break-word;
            max-width: 80%;
        }

        .message.user {
            background: #007bff;
            color: white;
            text-align: right;
            margin-left: 20%;
        }

        .message.deeplica {
            background: #e9ecef;
            color: #333;
            margin-right: 20%;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .input-area {
            display: flex;
            gap: 12px;
            align-items: flex-end;
            flex-shrink: 0; /* Don't shrink the input area */
            margin-top: auto; /* Push to bottom if needed */
        }

        .input-area input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: var(--input-font-size);
            font-family: inherit;
        }

        .input-area input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }

        .input-area button {
            padding: 12px 24px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: background 0.2s;
        }

        .input-area button:hover {
            background: #0056b3;
        }

        .input-area button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        /* Customization Modal Styles */
        .customization-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: modalFadeIn 0.3s ease;
        }

        @keyframes modalFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .customization-content {
            background: var(--chat-bg);
            border-radius: 20px;
            width: 95%;
            max-width: 2200px;
            height: 90%;
            max-height: 900px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            border: 2px solid rgba(0, 212, 255, 0.3);
            animation: modalSlideIn 0.3s ease;
            display: flex;
            flex-direction: column;
        }

        @keyframes modalSlideIn {
            from { transform: scale(0.8) translateY(-20px); opacity: 0; }
            to { transform: scale(1) translateY(0); opacity: 1; }
        }

        .customization-header {
            padding: 25px;
            border-bottom: 2px solid rgba(0, 212, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(102, 126, 234, 0.1));
        }

        .customization-header h2 {
            color: var(--chat-text);
            font-size: 1.8rem;
            font-weight: 700;
            margin: 0;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--chat-text);
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: rgba(231, 76, 60, 0.2);
            color: #e74c3c;
            transform: scale(1.1);
        }

        .customization-body {
            padding: 25px;
            flex: 1;
            display: flex;
            gap: 30px;
            overflow: hidden;
        }

        .customization-controls {
            flex: 1;
            overflow-y: auto;
            padding-right: 15px;
        }

        .customization-preview {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: rgba(0, 212, 255, 0.05);
            border: 2px solid rgba(0, 212, 255, 0.2);
            border-radius: 15px;
            padding: 20px;
            overflow: hidden;
        }

        .customization-section {
            margin-bottom: 30px;
        }

        .customization-section h3 {
            color: var(--chat-text);
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid rgba(0, 212, 255, 0.2);
        }

        .theme-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .theme-btn {
            padding: 12px 20px;
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 12px;
            background: rgba(0, 212, 255, 0.1);
            color: var(--chat-text);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 120px;
        }

        .theme-btn:hover, .theme-btn.active {
            background: rgba(0, 212, 255, 0.2);
            border-color: #00d4ff;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
        }

        .preset-themes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 15px;
        }

        .theme-preset {
            text-align: center;
            cursor: pointer;
            padding: 15px;
            border: 2px solid rgba(0, 212, 255, 0.2);
            border-radius: 12px;
            transition: all 0.3s ease;
            background: rgba(0, 212, 255, 0.05);
        }

        .theme-preset:hover, .theme-preset.active {
            border-color: #00d4ff;
            background: rgba(0, 212, 255, 0.1);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 212, 255, 0.2);
        }

        .theme-preview {
            display: flex;
            gap: 5px;
            margin-bottom: 10px;
            justify-content: center;
        }

        .preview-user, .preview-deeplica {
            width: 30px;
            height: 20px;
            border-radius: 8px;
        }

        .theme-preset span {
            color: var(--chat-text);
            font-size: 0.9rem;
            font-weight: 600;
        }

        .font-size-controls {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .font-size-controls button {
            padding: 8px 15px;
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            background: rgba(0, 212, 255, 0.1);
            color: var(--chat-text);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .font-size-controls button:hover {
            background: rgba(0, 212, 255, 0.2);
            transform: translateY(-1px);
        }

        #fontSizeSlider {
            flex: 1;
            min-width: 150px;
        }

        #fontSizeDisplay {
            color: var(--chat-text);
            font-weight: 600;
            min-width: 50px;
            text-align: center;
        }

        .color-controls-container {
            display: flex;
            gap: 25px;
            align-items: flex-start;
            height: 85vh;
            max-height: 800px;
            width: 100%;
            max-width: 2000px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .color-controls {
            flex: 1;
            min-width: 700px;
            max-width: 900px;
            height: 100%;
            overflow-y: auto;
            padding-right: 15px;
        }

        .color-controls::-webkit-scrollbar {
            width: 8px;
        }

        .color-controls::-webkit-scrollbar-track {
            background: var(--scrollbar-track);
            border-radius: 4px;
        }

        .color-controls::-webkit-scrollbar-thumb {
            background: var(--scrollbar-thumb);
            border-radius: 4px;
        }

        .color-controls::-webkit-scrollbar-thumb:hover {
            background: var(--scrollbar-thumb-hover, #555);
        }

        .color-controls-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .color-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .color-group label {
            color: var(--chat-text);
            font-weight: 600;
            font-size: 0.9rem;
        }

        .color-group small {
            color: var(--chat-text);
            opacity: 0.7;
            font-size: 0.75rem;
            margin-top: 2px;
        }

        .color-group input[type="color"] {
            width: 100%;
            height: 35px;
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            cursor: pointer;
            background: none;
        }

        .color-preview-side {
            flex: 1;
            min-width: 700px;
            max-width: 1000px;
            padding: 30px;
            background: rgba(0, 212, 255, 0.05);
            border: 2px solid rgba(0, 212, 255, 0.2);
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .color-preview-side h4 {
            color: var(--chat-text);
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 12px;
            text-align: center;
        }

        .preview-chat-container {
            background: var(--chat-bg);
            border-radius: 10px;
            padding: 12px;
            border: 1px solid rgba(0, 212, 255, 0.2);
            transition: all 0.3s ease;
        }

        .preview-messages {
            display: flex;
            flex-direction: column;
            gap: 8px;
            min-height: 100px;
            margin-bottom: 10px;
            padding: 8px;
            background: var(--input-bg);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .preview-input-container {
            display: flex;
            gap: 8px;
            align-items: center;
            padding: 8px;
            background: var(--input-bg);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .preview-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid var(--input-border);
            border-radius: 15px;
            font-size: var(--preview-font-size, 0.8rem);
            font-family: 'Rajdhani', sans-serif;
            background: var(--input-bg);
            color: var(--input-text);
            transition: all 0.3s ease;
        }

        .preview-send-btn {
            width: 30px;
            height: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .preview-message {
            padding: 10px 15px;
            border-radius: 12px;
            max-width: 80%;
            font-size: 0.9rem;
            line-height: 1.4;
            transition: all 0.3s ease;
        }

        .user-preview {
            margin-left: auto;
            text-align: right;
        }

        .deeplica-preview {
            margin-right: auto;
        }

        .preview-text {
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        .customization-footer {
            padding: 25px;
            border-top: 2px solid rgba(0, 212, 255, 0.2);
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.05), rgba(102, 126, 234, 0.05));
        }

        .reset-btn, .apply-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .reset-btn {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
            border: 2px solid rgba(231, 76, 60, 0.3);
        }

        .reset-btn:hover {
            background: rgba(231, 76, 60, 0.2);
            transform: translateY(-2px);
        }

        .apply-btn {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            color: white;
            border: 2px solid #00d4ff;
        }

        .apply-btn:hover {
            background: linear-gradient(135deg, #0099cc, #00d4ff);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            body { padding: 10px; }
            .header { padding: 15px; }
            .chat-container {
                padding: 15px;
                height: calc(100vh - 120px); /* Adjust for mobile */
            }
            .messages {
                min-height: 150px; /* Smaller minimum on mobile */
            }
            .message { margin-left: 0; margin-right: 0; }

            .customization-content {
                width: 95%;
                max-height: 90vh;
            }

            .customization-header, .customization-body, .customization-footer {
                padding: 15px;
            }

            .preset-themes {
                grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
                gap: 10px;
            }

            .font-size-controls {
                flex-direction: column;
                align-items: stretch;
            }

            .color-controls-container {
                flex-direction: column;
            }

            .color-controls {
                grid-template-columns: 1fr;
                min-width: auto;
            }

            .color-preview-side {
                min-width: auto;
                max-width: none;
            }

            .preview-chat-container {
                padding: 8px;
            }

            .preview-messages {
                min-height: 80px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 Deeplica Chat</h1>
        <div class="header-controls">
            <button class="control-btn" onclick="decreaseFontSize()">A-</button>
            <button class="control-btn" onclick="increaseFontSize()">A+</button>
            <button class="control-btn" onclick="openCustomization()">🎨 Theme</button>
            <button class="control-btn" onclick="goBack()">← Back</button>
            {% if user and user.role and user.role.value == "admin" %}
            <button class="control-btn" onclick="navigateToAdmin()">⚙️ Admin</button>
            {% endif %}
            <button class="control-btn" onclick="logoutUser()">🔓 Logout</button>
        </div>
    </div>

    <div class="chat-container">
        <div id="status" class="status connecting">🟡 Connecting...</div>
        <div id="messages" class="messages"></div>
        <div class="input-area">
            <input type="text" id="messageInput" placeholder="Type your message and press Enter..." onkeypress="handleKeyPress(event)">
            <button onclick="sendMessage()" id="sendButton">Send</button>
        </div>
    </div>

    <script>
        console.log('🚀 DEEPLICA CHAT - SIMPLE WORKING VERSION');

        // Get session ID from cookie - CLIENT-SIDE CALCULATION
        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
            return null;
        }

        function calculateSessionToken(sessionId) {
            // Client-side token calculation - should match server logic
            // For enhanced security, this could include:
            // 1. Timestamp validation
            // 2. HMAC signature verification
            // 3. Additional entropy mixing

            // For now, return the session_id directly
            // This can be enhanced with crypto.subtle.digest() for hashing
            return sessionId;
        }

        async function enhancedTokenCalculation(sessionId) {
            // Future enhancement: Use Web Crypto API for secure hashing
            // const encoder = new TextEncoder();
            // const data = encoder.encode(sessionId + timestamp + secret);
            // const hashBuffer = await crypto.subtle.digest('SHA-256', data);
            // return Array.from(new Uint8Array(hashBuffer)).map(b => b.toString(16).padStart(2, '0')).join('');
            return sessionId;
        }

        // Get session from cookie and calculate token
        const rawSessionId = getCookie('session_id');
        const sessionId = rawSessionId ? calculateSessionToken(rawSessionId) : '{{ session_id if session_id else "debug-session" }}';
        const username = '{{ user.username if user and user.username else "debug-user" }}';

        console.log('🔑 Raw Session ID from cookie:', rawSessionId);
        console.log('📋 Calculated Session Token:', sessionId);
        console.log('👤 Username:', username);

        let ws = null;
        let isConnected = false;

        const messagesDiv = document.getElementById('messages');
        const statusDiv = document.getElementById('status');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');

        function updateStatus(status, text) {
            statusDiv.className = `status ${status}`;
            statusDiv.textContent = text;
            console.log('📊 Status updated:', status, text);

            // Enable/disable send button based on connection
            sendButton.disabled = (status !== 'connected');
        }

        function addMessage(sender, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            // Create message content with proper text wrapping
            const messageContent = document.createElement('div');
            messageContent.style.whiteSpace = 'pre-wrap'; // Preserve line breaks and wrap text
            messageContent.style.wordWrap = 'break-word';
            messageContent.style.overflowWrap = 'break-word';
            messageContent.textContent = `${sender === 'user' ? 'You' : 'Deeplica'}: ${content}`;

            messageDiv.appendChild(messageContent);
            messagesDiv.appendChild(messageDiv);

            // Smooth scroll to bottom to show latest message
            messagesDiv.scrollTo({
                top: messagesDiv.scrollHeight,
                behavior: 'smooth'
            });
            console.log('💬 Message added:', sender, content);
        }

        function connectWebSocket() {
            console.log('🔗 Starting WebSocket connection...');

            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws?session_id=${sessionId}`;

            console.log('🌐 WebSocket URL:', wsUrl);
            updateStatus('connecting', '🟡 Connecting...');

            try {
                ws = new WebSocket(wsUrl);
                console.log('✅ WebSocket object created');

                ws.onopen = function() {
                    console.log('🎉 WebSocket opened');
                    isConnected = true;
                    updateStatus('connected', '🟢 Connected');
                };

                ws.onmessage = function(event) {
                    console.log('📨 WebSocket message:', event.data);
                    try {
                        const data = JSON.parse(event.data);
                        if (data.type === 'message') {
                            addMessage(data.sender, data.content);
                        }
                    } catch (e) {
                        console.error('❌ Error parsing message:', e);
                    }
                };

                ws.onclose = function(event) {
                    console.log('🔴 WebSocket closed:', event.code, event.reason);
                    isConnected = false;
                    updateStatus('disconnected', '🔴 Disconnected');
                };

                ws.onerror = function(error) {
                    console.error('❌ WebSocket error:', error);
                    updateStatus('disconnected', '❌ Connection Error');
                };

            } catch (error) {
                console.error('❌ Error creating WebSocket:', error);
                updateStatus('disconnected', '❌ Failed to Connect');
            }
        }

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            console.log('📤 Sending message:', message);

            if (!isConnected) {
                console.log('❌ Not connected');
                alert('Not connected to Deeplica');
                return;
            }

            // Don't add message here - server will echo it back to avoid duplicates

            const data = {
                type: 'message',
                content: message
            };

            try {
                ws.send(JSON.stringify(data));
                messageInput.value = '';
                console.log('✅ Message sent successfully');
            } catch (error) {
                console.error('❌ Error sending message:', error);
            }
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // Font size control
        let currentFontLevel = 2; // Start at medium size
        const fontLevels = [
            { chat: '0.9rem', input: '0.9rem' },  // Level 0 - Small
            { chat: '1.1rem', input: '1.0rem' },  // Level 1 - Medium-Small
            { chat: '1.3rem', input: '1.2rem' },  // Level 2 - Medium (default)
            { chat: '1.6rem', input: '1.4rem' },  // Level 3 - Large
            { chat: '2.0rem', input: '1.8rem' }   // Level 4 - Extra Large
        ];

        function updateFontSize() {
            const level = fontLevels[currentFontLevel];
            document.documentElement.style.setProperty('--chat-font-size', level.chat);
            document.documentElement.style.setProperty('--input-font-size', level.input);
            localStorage.setItem('deeplica-font-level', currentFontLevel);
            console.log('Font size updated to level', currentFontLevel, level);
        }

        function increaseFontSize() {
            if (currentFontLevel < fontLevels.length - 1) {
                currentFontLevel++;
                updateFontSize();
            }
        }

        function decreaseFontSize() {
            if (currentFontLevel > 0) {
                currentFontLevel--;
                updateFontSize();
            }
        }

        // Navigation functions
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '/login';
            }
        }

        function navigateToAdmin() {
            window.location.href = '/admin';
        }

        function logoutUser() {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/logout';
            document.body.appendChild(form);
            form.submit();
        }

        // Load saved font preference
        const savedFontLevel = localStorage.getItem('deeplica-font-level');
        if (savedFontLevel !== null) {
            currentFontLevel = parseInt(savedFontLevel);
            updateFontSize();
        }

        // Initialize
        console.log('🚀 Initializing simple chat...');
        connectWebSocket();
        messageInput.focus();

        console.log('✅ Simple chat initialization complete');
    </script>
</body>
</html>

        // Font size control
        let currentFontLevel = 2; // Start at medium size
        const fontLevels = [
            { chat: '0.9rem', input: '0.9rem' },  // Level 0 - Small
            { chat: '1.1rem', input: '1.0rem' },  // Level 1 - Medium-Small
            { chat: '1.3rem', input: '1.2rem' },  // Level 2 - Medium (default)
            { chat: '1.6rem', input: '1.4rem' },  // Level 3 - Large
            { chat: '2.0rem', input: '1.8rem' }   // Level 4 - Extra Large
        ];

        function updateFontSize() {
            const level = fontLevels[currentFontLevel];
            document.documentElement.style.setProperty('--chat-font-size', level.chat);
            document.documentElement.style.setProperty('--input-font-size', level.input);
            localStorage.setItem('deeplica-font-level', currentFontLevel);
            console.log('Font size updated to level', currentFontLevel, level);
        }

        function increaseFontSize() {
            if (currentFontLevel < fontLevels.length - 1) {
                currentFontLevel++;
                updateFontSize();
            }
        }

        function decreaseFontSize() {
            if (currentFontLevel > 0) {
                currentFontLevel--;
                updateFontSize();
            }
        }

        // Navigation functions
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '/login';
            }
        }

        function navigateToAdmin() {
            window.location.href = '/admin';
        }

        function logoutUser() {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/logout';
            document.body.appendChild(form);
            form.submit();
        }

        // Load saved font preference
        const savedFontLevel = localStorage.getItem('deeplica-font-level');
        if (savedFontLevel !== null) {
            currentFontLevel = parseInt(savedFontLevel);
            updateFontSize();
        }

        // Initialize
        console.log('🚀 Initializing simple chat...');
        connectWebSocket();
        messageInput.focus();

        console.log('✅ Simple chat initialization complete');
    </script>
</body>
</html>
            gap: 12px;
            max-width: 85%;
            align-self: flex-start;
        }

        .typing-dots {
            background: white;
            border: 1px solid #e5e5e5;
            padding: 12px 16px;
            border-radius: 18px;
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #999;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        /* Input Area */
        .input-area {
            background: white;
            border-top: 1px solid #e5e5e5;
            padding: 20px;
        }

        .input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
            max-width: 800px;
            margin: 0 auto;
        }

        .message-input {
            flex: 1;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 12px 16px;
            font-size: var(--input-font-size);
            resize: none;
            min-height: 80px;
            max-height: 180px;
            line-height: 1.6;
            background: #ffffff;
            transition: all 0.2s ease;
            font-family: inherit;
        }

        .message-input:focus {
            outline: none;
            border-color: #10a37f;
            background: white;
            box-shadow: 0 0 0 3px rgba(16, 163, 127, 0.1);
        }

        .message-input::placeholder {
            color: #9ca3af;
            transition: color 0.2s ease;
        }

        .message-input:focus::placeholder {
            color: #d1d5db;
        }

        .send-button {
            background: linear-gradient(135deg, #10a37f, #059669);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 18px 32px;
            cursor: pointer;
            font-size: 1.6rem;
            font-weight: 600;
            transition: all 0.2s ease;
            min-width: 100px;
            box-shadow: 0 2px 4px rgba(16, 163, 127, 0.2);
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .send-icon {
            font-size: 18px;
            line-height: 1;
        }

        .send-text {
            line-height: 1;
        }

        .send-button:hover:not(:disabled) {
            background: linear-gradient(135deg, #0d8f6f, #047857);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(16, 163, 127, 0.3);
        }

        .send-button:active:not(:disabled) {
            transform: translateY(0);
        }

        .send-button:disabled {
            background: #e5e7eb;
            color: #9ca3af;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* Send button loading state */
        .send-button.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 16px;
            height: 16px;
            margin: -8px 0 0 -8px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Enhanced Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(20px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        /* Message animations */
        .message.user {
            animation: slideInRight 0.3s ease-out;
        }

        .message.deeplica {
            animation: slideIn 0.3s ease-out;
        }

        /* Connection status animations */
        .connection-status.connecting {
            animation: pulse 2s infinite;
        }

        .connection-status.failed {
            animation: shake 0.5s ease-in-out;
        }

        /* Help message styling */
        .help-message {
            margin: 20px 0;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }

        .help-message:hover {
            opacity: 1;
        }

        /* Keyboard shortcut styling */
        kbd {
            background: #f1f5f9;
            border: 1px solid #cbd5e1;
            border-radius: 4px;
            padding: 2px 6px;
            font-family: monospace;
            font-size: 0.8rem;
            color: #475569;
        }

        /* Enhanced scrollbar */
        .messages-area::-webkit-scrollbar {
            width: 6px;
        }

        .messages-area::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .messages-area::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .messages-area::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* Message selection */
        .message-content {
            user-select: text;
            -webkit-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
        }

        /* Copy button for messages */
        .message:hover .copy-button {
            opacity: 1;
        }

        .copy-button {
            opacity: 0;
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(0, 0, 0, 0.1);
            border: none;
            border-radius: 4px;
            padding: 4px 6px;
            cursor: pointer;
            font-size: 0.7rem;
            transition: all 0.2s ease;
        }

        .copy-button:hover {
            background: rgba(0, 0, 0, 0.2);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header {
                padding: 10px 15px;
            }
            
            .messages-area {
                padding: 15px;
            }
            
            .input-area {
                padding: 15px;
            }
            
            .message {
                max-width: 95%;
            }
        }

        /* Connection Status */
        .connection-status {
            position: fixed;
            top: 70px;
            right: 20px;
            padding: 12px 18px;
            border-radius: 8px;
            font-size: 1.4rem;
            font-weight: 700;
            z-index: 1000;
            transition: all 0.3s;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .connection-status.connected {
            background: rgba(40, 167, 69, 0.3);
            color: white;
            border: 1px solid rgba(40, 167, 69, 0.5);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        .connection-status.disconnected {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }

        .connection-status.connecting {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fcd34d;
        }

        .connection-status.failed {
            background: #fecaca;
            color: #7f1d1d;
            border: 1px solid #f87171;
        }

        /* Keyframe animations for avatar glow effect */
        @keyframes borderGlow {
            0% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* Deeplica Avatar Video - Login Page Style */
        .avatar-container {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            border-radius: 15px;
            overflow: hidden;
            border: 1px solid rgba(0, 212, 255, 0.3);
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.2);
            transition: all 0.3s ease;
            animation: borderGlow 2s ease-in-out infinite alternate;
            width: 300px;
            height: 150px;
        }

        .avatar-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 15px;
            display: block;
        }

        .avatar-container:hover {
            transform: translateX(-50%) scale(1.05);
            box-shadow: 0 0 25px rgba(0, 212, 255, 0.4);
        }

        /* Hide avatar when disconnected */
        .avatar-container.disconnected {
            opacity: 0.5;
            filter: grayscale(100%);
            border-color: rgba(255, 255, 255, 0.2);
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .avatar-container {
                width: 250px;
                height: 125px;
                top: 15px;
            }
        }

        @media (max-width: 400px) {
            .avatar-container {
                width: 200px;
                height: 100px;
                top: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-left">
            <div class="logo">🤖</div>
            <div class="title">Deeplica Chat</div>
        </div>
        <div class="header-right">
            <div class="user-info">{{ user.full_name if user else 'Debug User' }}</div>
            <div class="header-controls">
                <button class="control-icon-btn font-small" onclick="decreaseFontSize()" title="Decrease font size">
                    A
                </button>
                <button class="control-icon-btn font-large" onclick="increaseFontSize()" title="Increase font size">
                    A
                </button>
                <button class="control-icon-btn customize-btn" onclick="openCustomization()" title="Customize Theme">
                    🎨
                </button>
                <button class="control-icon-btn back-btn" onclick="goBack()" title="Go Back">
                    ←
                </button>
                {% if user and user.role and user.role.value == "admin" %}
                <button class="control-icon-btn" onclick="navigateToAdmin()" title="Admin Panel" id="adminButton">
                    ⚙️
                </button>
                {% endif %}
                <button class="control-icon-btn logout-btn" onclick="logoutUser()" title="Log Out">
                    🔓
                </button>
            </div>
        </div>
    </div>

    <!-- Connection Status -->
    <div id="connectionStatus" class="connection-status connecting">
        Connecting...
    </div>

    <!-- Deeplica Avatar Video -->
    <div class="avatar-container">
        <video id="deepplicaAvatar" class="avatar-video" autoplay muted loop>
            <source src="/media/Deeplica Avatar.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
    </div>

    <!-- Chat Container -->
    <div class="chat-container">
        <!-- Messages Area -->
        <div id="messagesArea" class="messages-area">
            <!-- Welcome message for debugging -->
            <div class="message deeplica">
                <div class="message-bubble">
                    <div>👋 Hello {{ username if username else 'Debug User' }}! I'm Deeplica, your AI assistant. How can I help you today?</div>
                    <div class="message-time">Just now</div>
                </div>
            </div>
        </div>

        <!-- Typing Indicator -->
        <div id="typingIndicator" class="typing-indicator">
            <div class="message-avatar">🤖</div>
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        </div>

        <!-- Input Area -->
        <div class="input-area">
            <div class="input-container">
                <textarea
                    id="messageInput"
                    class="message-input"
                    placeholder="Message Deeplica... (Press Enter to send, Shift+Enter for new line)"
                    rows="1"
                ></textarea>
                <button id="sendButton" class="send-button">
                    <span class="send-icon">📤</span>
                    <span class="send-text">Send</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // IMMEDIATE EXECUTION TEST
        console.log('🚀 DEEPLICA CHAT SCRIPT STARTED - IMMEDIATE EXECUTION');
        console.log('⏰ Timestamp:', new Date().toISOString());

        // Get user session from cookie
        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
            return null;
        }

        // CLIENT-SIDE SESSION CALCULATION - ROBUST VERSION
        function getCookieValue(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
            return null;
        }

        function generateSessionToken(sessionId) {
            // Client-side token calculation - matches server logic
            return sessionId; // Can be enhanced with crypto.subtle for hashing
        }

        // Get session from cookie and generate token
        const cookieSessionId = getCookieValue('session_id');
        const sessionId = cookieSessionId ? generateSessionToken(cookieSessionId) : '{{ session_id if session_id else "debug-session-" + Date.now() }}';
        const username = '{{ user.username if user and user.username else "debug-user" }}';

        console.log('🎯 DEEPLICA CHAT JAVASCRIPT LOADED');
        console.log('🔑 Cookie Session ID:', cookieSessionId);
        console.log('📋 Generated Session Token:', sessionId);
        console.log('👤 Username:', username);
        console.log('🌐 Current URL:', window.location.href);
        console.log('📄 Document ready state:', document.readyState);

        // More robust session validation - allow debug mode
        if (!sessionId || sessionId === 'None' || sessionId === '') {
            console.error('❌ No session ID found');
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                console.warn('🔧 DEBUG MODE: Continuing without session for localhost testing');
                // Don't redirect in debug mode
            } else {
                console.error('📋 Session ID value:', sessionId);
                window.location.href = '/login';
                return;
            }
        }

        console.log('✅ Session validation passed, proceeding with chat initialization');
        
        // WebSocket connection
        let ws = null;
        let isConnected = false;

        // DOM elements - will be initialized when DOM is ready
        let messagesArea, messageInput, sendButton, typingIndicator, connectionStatus;

        // WebSocket connection with improved stability
        let reconnectAttempts = 0;
        let maxReconnectAttempts = 5;
        let heartbeatInterval = null;

        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws?session_id=${sessionId}`;

            console.log('🔗 Connecting to WebSocket:', wsUrl);
            console.log('📋 Session ID:', sessionId);
            console.log('👤 Username:', username);
            updateConnectionStatus('connecting');

            try {
                ws = new WebSocket(wsUrl);
                console.log('✅ WebSocket object created successfully');

                ws.onopen = function() {
                    console.log('🎉 WebSocket onopen event fired');
                    isConnected = true;
                    reconnectAttempts = 0;
                    updateConnectionStatus('connected');
                    console.log('✅ Connected to Deeplica - status updated');

                    // Start heartbeat
                    startHeartbeat();
                    console.log('💓 Heartbeat started');
                };
            } catch (error) {
                console.error('❌ Error creating WebSocket:', error);
                updateConnectionStatus('failed');
                return;
            }

            ws.onmessage = function(event) {
                console.log('📨 Received WebSocket message:', event.data);
                try {
                    const data = JSON.parse(event.data);
                    console.log('📋 Parsed message data:', data);
                    handleMessage(data);
                } catch (e) {
                    console.error('❌ Error parsing WebSocket message:', e);
                    console.error('📄 Raw message data:', event.data);
                }
            };

            ws.onclose = function(event) {
                isConnected = false;
                stopHeartbeat();
                updateConnectionStatus('disconnected');
                console.log('Disconnected from Deeplica. Code:', event.code, 'Reason:', event.reason);

                // Handle different close codes
                if (event.code === 1008) {
                    console.error('Authentication failed, redirecting to login');
                    window.location.href = '/login';
                } else if (reconnectAttempts < maxReconnectAttempts) {
                    reconnectAttempts++;
                    const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000);
                    console.log(`Reconnecting in ${delay}ms (attempt ${reconnectAttempts}/${maxReconnectAttempts})`);
                    setTimeout(connectWebSocket, delay);
                } else {
                    console.error('Max reconnection attempts reached');
                    updateConnectionStatus('failed');
                }
            };

            ws.onerror = function(error) {
                console.error('WebSocket error:', error);
                updateConnectionStatus('disconnected');
            };
        }

        // Heartbeat to keep connection alive
        function startHeartbeat() {
            heartbeatInterval = setInterval(() => {
                if (isConnected && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({ type: 'ping' }));
                }
            }, 25000); // Send ping every 25 seconds
        }

        function stopHeartbeat() {
            if (heartbeatInterval) {
                clearInterval(heartbeatInterval);
                heartbeatInterval = null;
            }
        }

        // Update connection status
        function updateConnectionStatus(status) {
            console.log(`🔄 Updating connection status to: ${status}`);

            if (!connectionStatus) {
                console.error('❌ Connection status element not found!');
                return;
            }

            // Update connection status display
            connectionStatus.className = `connection-status ${status}`;

            // Control avatar video based on connection status
            const avatarContainer = document.querySelector('.avatar-container');
            const avatarVideo = document.getElementById('deepplicaAvatar');

            switch(status) {
                case 'connected':
                    connectionStatus.textContent = '🟢 Connected';
                    sendButton.disabled = false;
                    // Play avatar video when connected
                    if (avatarVideo) {
                        avatarVideo.play().catch(e => console.log('Avatar video autoplay prevented:', e));
                        avatarContainer?.classList.remove('disconnected');
                    }
                    console.log('✅ Status set to Connected - send button enabled, avatar playing');
                    break;
                case 'disconnected':
                    connectionStatus.textContent = '🔴 Disconnected';
                    sendButton.disabled = true;
                    // Pause avatar video when disconnected
                    if (avatarVideo) {
                        avatarVideo.pause();
                        avatarContainer?.classList.add('disconnected');
                    }
                    console.log('🔴 Status set to Disconnected - send button disabled, avatar paused');
                    break;
                case 'connecting':
                    connectionStatus.textContent = '🟡 Connecting...';
                    sendButton.disabled = true;
                    // Keep avatar playing while connecting
                    if (avatarVideo) {
                        avatarVideo.play().catch(e => console.log('Avatar video autoplay prevented:', e));
                        avatarContainer?.classList.remove('disconnected');
                    }
                    console.log('🟡 Status set to Connecting - send button disabled, avatar playing');
                    break;
                case 'failed':
                    connectionStatus.textContent = '❌ Connection Failed';
                    sendButton.disabled = true;
                    // Pause avatar video when failed
                    if (avatarVideo) {
                        avatarVideo.pause();
                        avatarContainer?.classList.add('disconnected');
                    }
                    console.log('❌ Status set to Failed - send button disabled, avatar paused');
                    break;
                default:
                    console.warn('⚠️ Unknown connection status:', status);
            }
        }

        // Handle incoming messages
        function handleMessage(data) {
            switch(data.type) {
                case 'connection':
                    console.log('Connection confirmed:', data.status);
                    if (data.status === 'connected') {
                        updateConnectionStatus('connected');
                    }
                    break;

                case 'typing':
                    showTypingIndicator();
                    break;

                case 'message':
                    hideTypingIndicator();
                    addMessage(data.sender, data.content, data.timestamp);
                    break;

                case 'pong':
                    console.log('Received pong');
                    break;

                case 'heartbeat':
                    console.log('Received heartbeat');
                    break;

                default:
                    console.log('Unknown message type:', data.type);
            }
        }

        // 🔧 FIXED: Simple and reliable addMessage function with proper text wrapping
        function addMessage(sender, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            const time = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

            // Create message content with proper text wrapping
            const messageContent = document.createElement('div');
            messageContent.className = 'message-bubble';
            messageContent.style.whiteSpace = 'pre-wrap'; // Preserve line breaks and wrap text
            messageContent.style.wordWrap = 'break-word';
            messageContent.style.overflowWrap = 'break-word';

            messageContent.innerHTML = `
                <div style="white-space: pre-wrap; word-wrap: break-word; overflow-wrap: break-word;">${escapeHtml(content)}</div>
                <div class="message-time">${time}</div>
            `;

            messageDiv.appendChild(messageContent);
            messagesArea.appendChild(messageDiv);

            // Smooth scroll to bottom to show latest message
            messagesArea.scrollTo({
                top: messagesArea.scrollHeight,
                behavior: 'smooth'
            });
        }

        // Format message content with basic markdown
        function formatMessageContent(content) {
            let formatted = escapeHtml(content);

            // Bold text **text**
            formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

            // Italic text *text*
            formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');

            // Code `code`
            formatted = formatted.replace(/`(.*?)`/g, '<code style="background: #f1f5f9; padding: 2px 4px; border-radius: 3px; font-family: monospace;">$1</code>');

            // Line breaks
            formatted = formatted.replace(/\n/g, '<br>');

            return formatted;
        }

        // Get status text
        function getStatusText(status) {
            switch(status) {
                case 'sending': return 'Sending...';
                case 'sent': return 'Sent';
                case 'error': return 'Failed';
                default: return '';
            }
        }

        // Show typing indicator
        function showTypingIndicator() {
            typingIndicator.style.display = 'flex';
            scrollToBottom();
        }

        // Hide typing indicator
        function hideTypingIndicator() {
            typingIndicator.style.display = 'none';
        }

        // Scroll to bottom
        function scrollToBottom() {
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }

        // Escape HTML
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Copy message to clipboard
        function copyMessage(button) {
            const messageContent = button.parentElement;
            const text = messageContent.textContent.replace('📋', '').trim();

            navigator.clipboard.writeText(text).then(() => {
                button.textContent = '✅';
                setTimeout(() => {
                    button.textContent = '📋';
                }, 1000);
                showNotification('Message copied to clipboard', 'success');
            }).catch(() => {
                showNotification('Failed to copy message', 'error');
            });
        }

        // Export chat history
        function exportChatHistory() {
            const messages = Array.from(messagesArea.querySelectorAll('.message')).map(msg => {
                const sender = msg.classList.contains('user') ? 'You' : 'Deeplica';
                const content = msg.querySelector('.message-content').textContent.replace('📋', '').trim();
                const time = msg.querySelector('.message-time').textContent;
                return `[${time}] ${sender}: ${content}`;
            }).join('\n');

            const blob = new Blob([messages], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `deeplica-chat-${new Date().toISOString().split('T')[0]}.txt`;
            a.click();
            URL.revokeObjectURL(url);

            showNotification('Chat history exported', 'success');
        }

        // Clear chat history
        function clearChatHistory() {
            if (confirm('Are you sure you want to clear the chat history?')) {
                messagesArea.innerHTML = '';
                showNotification('Chat history cleared', 'info');
            }
        }

        // Add context menu for messages
        document.addEventListener('contextmenu', function(e) {
            if (e.target.closest('.message-content')) {
                e.preventDefault();
                // Could add a custom context menu here
            }
        });

        // 🔧 FIXED: Simple and reliable sendMessage function
        function sendMessage() {
            console.log('🚀 sendMessage() called');
            const message = messageInput.value.trim();
            console.log('📝 Message content:', message);
            console.log('🔗 Connection status:', isConnected);

            if (!message) {
                console.log('❌ Empty message, not sending');
                return;
            }

            if (!isConnected) {
                console.log('❌ Not connected, cannot send message');
                return;
            }

            console.log('📤 Sending message:', message);
            addMessage('user', message);

            const data = {
                type: 'message',
                content: message
            };

            try {
                ws.send(JSON.stringify(data));
                messageInput.value = '';
                autoResizeTextarea();
                showTypingIndicator();
                console.log('✅ Message sent successfully');
            } catch (error) {
                console.error('❌ Error sending message:', error);
            }
        }

        // Show notification
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 16px;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                animation: slideIn 0.3s ease-out;
                max-width: 300px;
            `;

            switch(type) {
                case 'error':
                    notification.style.background = '#ef4444';
                    break;
                case 'success':
                    notification.style.background = '#10b981';
                    break;
                default:
                    notification.style.background = '#3b82f6';
            }

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'fadeOut 0.3s ease-in';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Auto-resize textarea
        function adjustTextareaHeight() {
            messageInput.style.height = 'auto';
            messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
        }

        // Event listeners will be attached after DOM initialization

        // Message history management
        let messageHistory = [];
        let historyIndex = -1;

        // Load message history from localStorage
        function loadMessageHistory() {
            try {
                const saved = localStorage.getItem(`deeplica_history_${username}`);
                if (saved) {
                    messageHistory = JSON.parse(saved);
                }
            } catch (e) {
                console.warn('Failed to load message history:', e);
            }
        }

        // Save message to history
        function saveToHistory(message) {
            if (message.trim()) {
                messageHistory.unshift(message);
                // Keep only last 50 messages
                messageHistory = messageHistory.slice(0, 50);
                try {
                    localStorage.setItem(`deeplica_history_${username}`, JSON.stringify(messageHistory));
                } catch (e) {
                    console.warn('Failed to save message history:', e);
                }
            }
        }

        // Navigate message history
        function navigateHistory(direction) {
            if (messageHistory.length === 0) return;

            if (direction === 'up') {
                historyIndex = Math.min(historyIndex + 1, messageHistory.length - 1);
            } else {
                historyIndex = Math.max(historyIndex - 1, -1);
            }

            if (historyIndex >= 0) {
                messageInput.value = messageHistory[historyIndex];
            } else {
                messageInput.value = '';
            }
            adjustTextareaHeight();
        }

        // Enhanced keyboard shortcuts will be attached after DOM initialization

        // User presence indicator
        let isTyping = false;
        let typingTimeout = null;

        // Message timestamps and read receipts
        function addTimestamp() {
            const now = new Date();
            return now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        }

        // Auto-scroll behavior
        let isUserScrolling = false;
        let scrollTimeout = null;

        // Enhanced scroll to bottom
        function scrollToBottom(force = false) {
            if (!isUserScrolling || force) {
                messagesArea.scrollTop = messagesArea.scrollHeight;
            }
        }

        // 🔧 FIXED: Robust initialization function
        function initializeChat() {
            console.log('🚀 Initializing Deeplica Chat...');

            // Get DOM elements
            messagesArea = document.getElementById('messagesArea');
            messageInput = document.getElementById('messageInput');
            sendButton = document.getElementById('sendButton');
            typingIndicator = document.getElementById('typingIndicator');
            connectionStatus = document.getElementById('connectionStatus');

            console.log('🔍 DOM elements check:');
            console.log('  messagesArea:', !!messagesArea, messagesArea?.id);
            console.log('  messageInput:', !!messageInput, messageInput?.id);
            console.log('  sendButton:', !!sendButton, sendButton?.id);
            console.log('  typingIndicator:', !!typingIndicator, typingIndicator?.id);
            console.log('  connectionStatus:', !!connectionStatus, connectionStatus?.id);

            // Verify elements
            if (!messagesArea || !messageInput || !sendButton || !connectionStatus) {
                console.error('❌ Required DOM elements not found');
                console.error('🔍 Missing elements:', {
                    messagesArea: !messagesArea,
                    messageInput: !messageInput,
                    sendButton: !sendButton,
                    connectionStatus: !connectionStatus
                });

                // List all available elements for debugging
                console.error('🔍 Available elements:',
                    Array.from(document.querySelectorAll('[id]')).map(el => el.id));
                return;
            }

            console.log('✅ DOM elements found, attaching event handlers...');

            // 🔧 FIX: Attach send button click handler
            sendButton.addEventListener('click', function(e) {
                console.log('📤 Send button clicked');
                e.preventDefault();
                sendMessage();
            });

            // 🔧 FIX: Attach keyboard event handler for Enter key
            messageInput.addEventListener('keydown', function(e) {
                console.log('⌨️ Key pressed:', e.key, 'Shift:', e.shiftKey);
                if (e.key === 'Enter' && !e.shiftKey) {
                    console.log('📤 Enter key pressed - sending message');
                    e.preventDefault();
                    sendMessage();
                }
            });

            // Auto-resize textarea
            messageInput.addEventListener('input', autoResizeTextarea);

            console.log('✅ Event handlers attached, connecting WebSocket...');

            console.log('⌨️ Keyboard event handler already attached above');

            // User presence indicator
            messageInput.addEventListener('input', function() {
                if (!isTyping && isConnected) {
                    isTyping = true;
                    ws.send(JSON.stringify({ type: 'typing_start' }));
                }

                // Clear existing timeout
                if (typingTimeout) {
                    clearTimeout(typingTimeout);
                }

                // Set new timeout
                typingTimeout = setTimeout(() => {
                    if (isTyping && isConnected) {
                        isTyping = false;
                        ws.send(JSON.stringify({ type: 'typing_stop' }));
                    }
                }, 2000);
            });

            // Auto-scroll behavior
            messagesArea.addEventListener('scroll', function() {
                const { scrollTop, scrollHeight, clientHeight } = messagesArea;
                const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;

                isUserScrolling = !isAtBottom;

                // Clear existing timeout
                if (scrollTimeout) {
                    clearTimeout(scrollTimeout);
                }

                // Auto-scroll back to bottom after 3 seconds of no scrolling
                scrollTimeout = setTimeout(() => {
                    isUserScrolling = false;
                }, 3000);
            });

            loadMessageHistory();
            connectWebSocket();

            if (messageInput) {
                messageInput.focus();
            }
        }

        // 🔧 FIXED: Auto-resize textarea function
        function autoResizeTextarea() {
            messageInput.style.height = 'auto';
            messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
        }

        // Ensure DOM is fully loaded before initializing
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeChat);
        } else {
            // DOM is already loaded
            initializeChat();
        }

        // Add helpful keyboard shortcuts info
        setTimeout(() => {
            if (messagesArea.children.length === 0) {
                const helpMessage = document.createElement('div');
                helpMessage.className = 'help-message';
                helpMessage.innerHTML = `
                    <div style="text-align: center; color: #666; font-size: 0.9rem; padding: 20px;">
                        💡 <strong>Tips:</strong><br>
                        • Press <kbd>Ctrl+↑</kbd>/<kbd>Ctrl+↓</kbd> to navigate message history<br>
                        • Press <kbd>Esc</kbd> to clear input<br>
                        • Use <strong>**bold**</strong>, <em>*italic*</em>, and <code>\`code\`</code> formatting
                    </div>
                `;
                messagesArea.appendChild(helpMessage);
            }
        }, 1000);

        // Add subtle parallax effect to avatar video (like login page)
        document.addEventListener('mousemove', function(e) {
            const video = document.querySelector('.avatar-video');
            if (video) {
                const x = (e.clientX / window.innerWidth) * 5 - 2.5;
                const y = (e.clientY / window.innerHeight) * 5 - 2.5;
                video.style.transform = `translate(${x}px, ${y}px) scale(1.02)`;
            }
        });

        // Font size control functions
        let currentFontLevel = 4; // Start at level 4 (2.8rem)
        const fontLevels = [
            { chat: '0.8rem', input: '0.8rem' },  // Level 0
            { chat: '1.0rem', input: '1.0rem' },  // Level 1
            { chat: '1.4rem', input: '1.4rem' },  // Level 2
            { chat: '1.8rem', input: '1.8rem' },  // Level 3
            { chat: '2.8rem', input: '2.8rem' },  // Level 4 (current)
            { chat: '3.6rem', input: '3.6rem' },  // Level 5
            { chat: '4.4rem', input: '4.4rem' }   // Level 6
        ];

        function updateFontSize() {
            const level = fontLevels[currentFontLevel];
            const root = document.documentElement;

            // Update CSS variables
            root.style.setProperty('--chat-font-size', level.chat);
            root.style.setProperty('--input-font-size', level.input);

            // Save preference to localStorage
            localStorage.setItem('deepchat-font-level', currentFontLevel);
            console.log('Font size updated to level', currentFontLevel, level);
        }

        function increaseFontSize() {
            if (currentFontLevel < fontLevels.length - 1) {
                currentFontLevel++;
                updateFontSize();
            }
        }

        function decreaseFontSize() {
            if (currentFontLevel > 0) {
                currentFontLevel--;
                updateFontSize();
            }
        }

        // Load saved font preference on page load
        function loadFontPreference() {
            const saved = localStorage.getItem('deepchat-font-level');
            if (saved !== null) {
                currentFontLevel = parseInt(saved);
                updateFontSize();
            }
        }

        // Navigation functions
        function goBack() {
            // Smart back navigation
            if (window.history.length > 1) {
                window.history.back();
            } else {
                // Fallback to login page
                window.location.href = '/login';
            }
        }

        function navigateToAdmin() {
            console.log('⚙️ Navigating to admin panel...');
            window.location.href = '/admin';
        }

        function logoutUser() {
            console.log('🔓 Logging out user...');
            // Create a form and submit it to logout
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/logout';
            document.body.appendChild(form);
            form.submit();
        }

        // Load font preference when page loads
        setTimeout(() => {
            loadFontPreference();
        }, 100);

        // 🎨 CUSTOMIZATION SYSTEM

        // Predefined theme configurations
        const themePresets = {
            default: {
                name: 'Default',
                userBubbleBg: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                userBubbleText: '#ffffff',
                deepplicaBubbleBg: 'linear-gradient(135deg, #00d4ff 0%, #0099cc 100%)',
                deepplicaBubbleText: '#ffffff'
            },
            ocean: {
                name: 'Ocean',
                userBubbleBg: 'linear-gradient(135deg, #2980b9 0%, #3498db 100%)',
                userBubbleText: '#ffffff',
                deepplicaBubbleBg: 'linear-gradient(135deg, #1abc9c 0%, #16a085 100%)',
                deepplicaBubbleText: '#ffffff'
            },
            sunset: {
                name: 'Sunset',
                userBubbleBg: 'linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)',
                userBubbleText: '#ffffff',
                deepplicaBubbleBg: 'linear-gradient(135deg, #f39c12 0%, #e67e22 100%)',
                deepplicaBubbleText: '#ffffff'
            },
            forest: {
                name: 'Forest',
                userBubbleBg: 'linear-gradient(135deg, #27ae60 0%, #2ecc71 100%)',
                userBubbleText: '#ffffff',
                deepplicaBubbleBg: 'linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%)',
                deepplicaBubbleText: '#ffffff'
            },
            royal: {
                name: 'Royal',
                userBubbleBg: 'linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%)',
                userBubbleText: '#ffffff',
                deepplicaBubbleBg: 'linear-gradient(135deg, #f1c40f 0%, #f39c12 100%)',
                deepplicaBubbleText: '#ffffff'
            },
            neon: {
                name: 'Neon',
                userBubbleBg: 'linear-gradient(135deg, #ff006e 0%, #8338ec 100%)',
                userBubbleText: '#ffffff',
                deepplicaBubbleBg: 'linear-gradient(135deg, #06ffa5 0%, #00d4ff 100%)',
                deepplicaBubbleText: '#000000'
            },
            vintage: {
                name: 'Vintage',
                userBubbleBg: 'linear-gradient(135deg, #d4a574 0%, #b8860b 100%)',
                userBubbleText: '#ffffff',
                deepplicaBubbleBg: 'linear-gradient(135deg, #8b4513 0%, #a0522d 100%)',
                deepplicaBubbleText: '#ffffff'
            },
            ice: {
                name: 'Ice',
                userBubbleBg: 'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)',
                userBubbleText: '#ffffff',
                deepplicaBubbleBg: 'linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%)',
                deepplicaBubbleText: '#ffffff'
            },
            fire: {
                name: 'Fire',
                userBubbleBg: 'linear-gradient(135deg, #fd79a8 0%, #e84393 100%)',
                userBubbleText: '#ffffff',
                deepplicaBubbleBg: 'linear-gradient(135deg, #fdcb6e 0%, #e17055 100%)',
                deepplicaBubbleText: '#ffffff'
            },
            cosmic: {
                name: 'Cosmic',
                userBubbleBg: 'linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%)',
                userBubbleText: '#000000',
                deepplicaBubbleBg: 'linear-gradient(135deg, #ffd93d 0%, #ff6b6b 100%)',
                deepplicaBubbleText: '#000000'
            },
            matrix: {
                name: 'Matrix',
                userBubbleBg: 'linear-gradient(135deg, #00ff41 0%, #00d4aa 100%)',
                userBubbleText: '#000000',
                deepplicaBubbleBg: 'linear-gradient(135deg, #000000 0%, #1a1a1a 100%)',
                deepplicaBubbleText: '#00ff41'
            },
            cyberpunk: {
                name: 'Cyberpunk',
                userBubbleBg: 'linear-gradient(135deg, #ff0080 0%, #ff8c00 100%)',
                userBubbleText: '#ffffff',
                deepplicaBubbleBg: 'linear-gradient(135deg, #00d4ff 0%, #ff00ff 100%)',
                deepplicaBubbleText: '#ffffff'
            },
            retro: {
                name: 'Retro',
                userBubbleBg: 'linear-gradient(135deg, #ff6b9d 0%, #c44569 100%)',
                userBubbleText: '#ffffff',
                deepplicaBubbleBg: 'linear-gradient(135deg, #f8b500 0%, #feca57 100%)',
                deepplicaBubbleText: '#000000'
            },
            aurora: {
                name: 'Aurora',
                userBubbleBg: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                userBubbleText: '#ffffff',
                deepplicaBubbleBg: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                deepplicaBubbleText: '#ffffff'
            },
            midnight: {
                name: 'Midnight',
                userBubbleBg: 'linear-gradient(135deg, #2c3e50 0%, #34495e 100%)',
                userBubbleText: '#ffffff',
                deepplicaBubbleBg: 'linear-gradient(135deg, #4a69bd 0%, #1e3799 100%)',
                deepplicaBubbleText: '#ffffff'
            }
        };

        // Dark and Light mode configurations
        const modeConfigs = {
            light: {
                bgGradient: 'linear-gradient(135deg, #f5f5f5 0%, #e9ecef 50%, #dee2e6 100%)',
                textColor: '#2c3e50',
                headerBg: '#ffffff',
                headerText: '#2c3e50',
                chatBg: '#ffffff',
                chatText: '#2c3e50',
                inputBg: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
                inputText: '#2c3e50',
                inputBorder: '#e9ecef',
                scrollbarTrack: '#f1f1f1',
                scrollbarThumb: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
            },
            dark: {
                bgGradient: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%)',
                textColor: '#ffffff',
                headerBg: 'rgba(26, 26, 46, 0.9)',
                headerText: '#ffffff',
                chatBg: 'rgba(22, 33, 62, 0.9)',
                chatText: '#ffffff',
                inputBg: 'rgba(26, 26, 46, 0.8)',
                inputText: '#ffffff',
                inputBorder: 'rgba(0, 212, 255, 0.3)',
                scrollbarTrack: 'rgba(255, 255, 255, 0.1)',
                scrollbarThumb: 'linear-gradient(135deg, #00d4ff 0%, #0099cc 100%)'
            }
        };

        // Current customization state
        let currentCustomization = {
            mode: 'light',
            theme: 'default',
            fontSize: 16,
            fontFamily: "'Orbitron', monospace",
            fontWeight: '400',
            fontBold: false,
            chatBackground: '#ffffff',
            userBubbleBg: themePresets.default.userBubbleBg,
            userBubbleText: themePresets.default.userBubbleText,
            deepplicaBubbleBg: themePresets.default.deepplicaBubbleBg,
            deepplicaBubbleText: themePresets.default.deepplicaBubbleText
        };

        // Load saved customization from cookies
        function loadCustomization() {
            try {
                const saved = getCookie('deeplica_customization');
                if (saved) {
                    const parsed = JSON.parse(decodeURIComponent(saved));
                    currentCustomization = { ...currentCustomization, ...parsed };
                    applyCustomization();
                    updateCustomizationUI();
                }
            } catch (error) {
                console.error('Error loading customization:', error);
            }
        }

        // Save customization to cookies
        function saveCustomization() {
            try {
                const customizationString = JSON.stringify(currentCustomization);
                document.cookie = `deeplica_customization=${encodeURIComponent(customizationString)}; path=/; max-age=${365 * 24 * 60 * 60}`;
                console.log('🎨 Customization saved');
                closeCustomization();
            } catch (error) {
                console.error('Error saving customization:', error);
            }
        }

        // Apply current customization to the page
        function applyCustomization() {
            const root = document.documentElement;
            const mode = modeConfigs[currentCustomization.mode];

            // Apply mode-specific styles
            root.style.setProperty('--bg-gradient', mode.bgGradient);
            root.style.setProperty('--text-color', mode.textColor);
            root.style.setProperty('--header-bg', mode.headerBg);
            root.style.setProperty('--header-text', mode.headerText);
            root.style.setProperty('--chat-bg', mode.chatBg);
            root.style.setProperty('--chat-text', mode.chatText);
            root.style.setProperty('--scrollbar-track', mode.scrollbarTrack);
            root.style.setProperty('--scrollbar-thumb', mode.scrollbarThumb);

            // Apply custom chat area background (messages area and input field) or mode default
            const chatAreaBg = currentCustomization.chatBackground || mode.inputBg;
            root.style.setProperty('--input-bg', chatAreaBg);
            root.style.setProperty('--input-text', mode.inputText);
            root.style.setProperty('--input-border', mode.inputBorder);

            // Apply bubble colors
            root.style.setProperty('--user-bubble-bg', currentCustomization.userBubbleBg);
            root.style.setProperty('--user-bubble-text', currentCustomization.userBubbleText);
            root.style.setProperty('--deeplica-bubble-bg', currentCustomization.deepplicaBubbleBg);
            root.style.setProperty('--deeplica-bubble-text', currentCustomization.deepplicaBubbleText);

            // Apply font size
            root.style.setProperty('--font-size', currentCustomization.fontSize + 'px');
            root.style.setProperty('--preview-font-size', (currentCustomization.fontSize * 0.9) + 'px');

            console.log('🎨 Customization applied:', currentCustomization);
        }

        // Open customization modal
        function openCustomization() {
            document.getElementById('customizationModal').style.display = 'flex';
            updateCustomizationUI();
        }

        // Close customization modal
        function closeCustomization() {
            document.getElementById('customizationModal').style.display = 'none';
        }

        // Update live preview bubbles
        function updateLivePreview() {
            const userPreview = document.getElementById('userPreview');
            const deepplicaPreview = document.getElementById('deepplicaPreview');
            const previewMessages = document.getElementById('previewMessages');
            const previewInputContainer = document.querySelector('.preview-input-container');
            const previewInput = document.querySelector('.preview-input');

            if (userPreview) {
                userPreview.style.background = currentCustomization.userBubbleBg;
                userPreview.style.color = currentCustomization.userBubbleText;
                userPreview.style.fontSize = (currentCustomization.fontSize * 0.9) + 'px';
            }

            if (deepplicaPreview) {
                deepplicaPreview.style.background = currentCustomization.deepplicaBubbleBg;
                deepplicaPreview.style.color = currentCustomization.deepplicaBubbleText;
                deepplicaPreview.style.fontSize = (currentCustomization.fontSize * 0.9) + 'px';
            }

            // Apply chat area background to messages area and input container
            const chatAreaBg = currentCustomization.chatBackground || 'var(--input-bg)';
            if (previewMessages) {
                previewMessages.style.background = chatAreaBg;
            }
            if (previewInputContainer) {
                previewInputContainer.style.background = chatAreaBg;
            }
            if (previewInput) {
                previewInput.style.background = chatAreaBg;
                previewInput.style.fontSize = (currentCustomization.fontSize * 0.9) + 'px';
            }
        }

        // Update UI elements in customization modal
        function updateCustomizationUI() {
            // Update theme mode buttons
            document.querySelectorAll('.theme-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.mode === currentCustomization.mode);
            });

            // Update preset theme selection
            document.querySelectorAll('.theme-preset').forEach(preset => {
                preset.classList.toggle('active', preset.dataset.theme === currentCustomization.theme);
            });

            // Update font size display and slider
            const fontSizeDisplay = document.getElementById('fontSizeDisplay');
            const fontSizeSlider = document.getElementById('fontSizeSlider');
            if (fontSizeDisplay) fontSizeDisplay.textContent = currentCustomization.fontSize + 'px';
            if (fontSizeSlider) fontSizeSlider.value = currentCustomization.fontSize;

            // Update color pickers (extract colors from gradients and current settings)
            const userBgColor = extractColorFromGradient(currentCustomization.userBubbleBg);
            const deepplicaBgColor = extractColorFromGradient(currentCustomization.deepplicaBubbleBg);

            const userBubbleColorPicker = document.getElementById('userBubbleColor');
            const userTextColorPicker = document.getElementById('userTextColor');
            const deepplicaBubbleColorPicker = document.getElementById('deepplicaBubbleColor');
            const deepplicaTextColorPicker = document.getElementById('deepplicaTextColor');

            if (userBubbleColorPicker) userBubbleColorPicker.value = userBgColor;
            if (userTextColorPicker) userTextColorPicker.value = currentCustomization.userBubbleText;
            if (deepplicaBubbleColorPicker) deepplicaBubbleColorPicker.value = deepplicaBgColor;
            if (deepplicaTextColorPicker) deepplicaTextColorPicker.value = currentCustomization.deepplicaBubbleText;

            // Update live preview
            updateLivePreview();
        }

        // Extract first color from gradient for color picker
        function extractColorFromGradient(gradient) {
            const match = gradient.match(/#[0-9a-fA-F]{6}/);
            return match ? match[0] : '#667eea';
        }

        // Set theme mode (light/dark)
        function setThemeMode(mode) {
            currentCustomization.mode = mode;
            applyCustomization();
            updateCustomizationUI();
            updateLivePreview();
        }

        // Apply preset theme
        function applyPresetTheme(themeKey) {
            const theme = themePresets[themeKey];
            if (theme) {
                currentCustomization.theme = themeKey;
                currentCustomization.userBubbleBg = theme.userBubbleBg;
                currentCustomization.userBubbleText = theme.userBubbleText;
                currentCustomization.deepplicaBubbleBg = theme.deepplicaBubbleBg;
                currentCustomization.deepplicaBubbleText = theme.deepplicaBubbleText;
                applyCustomization();
                updateCustomizationUI();
                updateLivePreview();
            }
        }

        // Set font size
        function setFontSize(size) {
            currentCustomization.fontSize = parseInt(size);
            applyCustomization();
            updateCustomizationUI();
        }

        // Update custom colors
        function updateCustomColors() {
            const chatBgColorPicker = document.getElementById('chatBackgroundColor');
            const userBgColorPicker = document.getElementById('userBubbleColor');
            const userTextColorPicker = document.getElementById('userTextColor');
            const deepplicaBgColorPicker = document.getElementById('deepplicaBubbleColor');
            const deepplicaTextColorPicker = document.getElementById('deepplicaTextColor');

            if (chatBgColorPicker && userBgColorPicker && userTextColorPicker && deepplicaBgColorPicker && deepplicaTextColorPicker) {
                const chatBgColor = chatBgColorPicker.value;
                const userBgColor = userBgColorPicker.value;
                const userTextColor = userTextColorPicker.value;
                const deepplicaBgColor = deepplicaBgColorPicker.value;
                const deepplicaTextColor = deepplicaTextColorPicker.value;

                currentCustomization.chatBackground = chatBgColor;
                currentCustomization.userBubbleBg = `linear-gradient(135deg, ${userBgColor}, ${adjustBrightness(userBgColor, -20)})`;
                currentCustomization.userBubbleText = userTextColor;
                currentCustomization.deepplicaBubbleBg = `linear-gradient(135deg, ${deepplicaBgColor}, ${adjustBrightness(deepplicaBgColor, -20)})`;
                currentCustomization.deepplicaBubbleText = deepplicaTextColor;
                currentCustomization.theme = 'custom';

                applyCustomization();
                updateLivePreview();
            }
        }

        // Update custom font
        function updateCustomFont() {
            const fontFamilyPicker = document.getElementById('chatFontFamily');
            const fontSizePicker = document.getElementById('chatFontSize');
            const fontWeightPicker = document.getElementById('chatFontWeight');

            if (fontFamilyPicker && fontSizePicker && fontWeightPicker) {
                const fontFamily = fontFamilyPicker.value;
                const fontSize = fontSizePicker.value;
                const fontWeight = fontWeightPicker.value;

                currentCustomization.fontFamily = fontFamily;
                currentCustomization.fontSize = parseInt(fontSize);
                currentCustomization.fontWeight = fontWeight;

                applyCustomization();
                updateLivePreview();
                updateChatFontPreview();
            }
        }

        function updateChatFontPreview() {
            const preview = document.getElementById('chatFontPreview');
            if (preview) {
                preview.style.fontFamily = currentCustomization.fontFamily;
                preview.style.fontSize = currentCustomization.fontSize + 'px';
                preview.style.fontWeight = currentCustomization.fontWeight;
            }
        }

        // Adjust color brightness for gradient effect
        function adjustBrightness(hex, percent) {
            const num = parseInt(hex.replace("#", ""), 16);
            const amt = Math.round(2.55 * percent);
            const R = (num >> 16) + amt;
            const G = (num >> 8 & 0x00FF) + amt;
            const B = (num & 0x0000FF) + amt;
            return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
                (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
                (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
        }

        // Reset to default settings
        function resetToDefaults() {
            currentCustomization = {
                mode: 'light',
                theme: 'default',
                fontSize: 16,
                userBubbleBg: themePresets.default.userBubbleBg,
                userBubbleText: themePresets.default.userBubbleText,
                deepplicaBubbleBg: themePresets.default.deepplicaBubbleBg,
                deepplicaBubbleText: themePresets.default.deepplicaBubbleText
            };
            applyCustomization();
            updateCustomizationUI();
        }

        // Close modal when clicking outside
        document.addEventListener('click', function(e) {
            const modal = document.getElementById('customizationModal');
            if (e.target === modal) {
                closeCustomization();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeCustomization();
            }
        });

        // Initialize customization system
        loadCustomization();
    </script>

    <!-- Customization Modal -->
    <div id="customizationModal" class="customization-modal" style="display: none;">
        <div class="customization-content">
            <div class="customization-header">
                <h2>🎨 Customize Your Chat</h2>
                <button class="close-btn" onclick="closeCustomization()">✕</button>
            </div>

            <div class="customization-body">
                <!-- Theme Mode Selection -->
                <div class="customization-section">
                    <h3>🌓 Theme Mode</h3>
                    <div class="theme-buttons">
                        <button class="theme-btn" data-mode="light" onclick="setThemeMode('light')">
                            ☀️ Light Mode
                        </button>
                        <button class="theme-btn" data-mode="dark" onclick="setThemeMode('dark')">
                            🌙 Dark Mode
                        </button>
                    </div>
                </div>

                <!-- Preset Themes -->
                <div class="customization-section">
                    <h3>🎨 Preset Themes</h3>
                    <div class="preset-themes">
                        <div class="theme-preset" data-theme="default" onclick="applyPresetTheme('default')">
                            <div class="theme-preview">
                                <div class="preview-user" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);"></div>
                                <div class="preview-deeplica" style="background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);"></div>
                            </div>
                            <span>Default</span>
                        </div>

                        <div class="theme-preset" data-theme="ocean" onclick="applyPresetTheme('ocean')">
                            <div class="theme-preview">
                                <div class="preview-user" style="background: linear-gradient(135deg, #2980b9 0%, #3498db 100%);"></div>
                                <div class="preview-deeplica" style="background: linear-gradient(135deg, #1abc9c 0%, #16a085 100%);"></div>
                            </div>
                            <span>Ocean</span>
                        </div>

                        <div class="theme-preset" data-theme="sunset" onclick="applyPresetTheme('sunset')">
                            <div class="theme-preview">
                                <div class="preview-user" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);"></div>
                                <div class="preview-deeplica" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);"></div>
                            </div>
                            <span>Sunset</span>
                        </div>

                        <div class="theme-preset" data-theme="forest" onclick="applyPresetTheme('forest')">
                            <div class="theme-preview">
                                <div class="preview-user" style="background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);"></div>
                                <div class="preview-deeplica" style="background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);"></div>
                            </div>
                            <span>Forest</span>
                        </div>

                        <div class="theme-preset" data-theme="royal" onclick="applyPresetTheme('royal')">
                            <div class="theme-preview">
                                <div class="preview-user" style="background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);"></div>
                                <div class="preview-deeplica" style="background: linear-gradient(135deg, #f1c40f 0%, #f39c12 100%);"></div>
                            </div>
                            <span>Royal</span>
                        </div>
                    </div>
                </div>

                <!-- Font Size -->
                <div class="customization-section">
                    <h3>📝 Font Size</h3>
                    <div class="font-size-controls">
                        <button onclick="decreaseFontSize()">A-</button>
                        <span id="fontSizeDisplay">16px</span>
                        <button onclick="increaseFontSize()">A+</button>
                        <input type="range" id="fontSizeSlider" min="12" max="24" value="16" onchange="setFontSize(this.value)">
                    </div>
                </div>

                <!-- Custom Colors -->
                <div class="customization-section">
                    <h3>🎨 Custom Colors</h3>
                    <div class="color-controls-container">
                        <div class="color-controls">
                            <!-- Compact Grid Layout -->
                            <div style="display: grid; gap: 12px;">

                                <!-- Font Controls Section -->
                                <div style="border: 1px solid var(--input-border); border-radius: 6px; padding: 12px; background: rgba(255,255,255,0.05);">
                                    <h4 style="margin: 0 0 10px 0; font-size: 0.9rem; color: var(--input-text);">🔤 Font Settings</h4>

                                    <!-- Font Family Row -->
                                    <div style="display: grid; grid-template-columns: 2fr 1fr 1fr 80px; gap: 8px; margin-bottom: 8px;">
                                        <div>
                                            <label style="font-size: 0.8rem; color: var(--input-text); display: block; margin-bottom: 2px;">Font Family:</label>
                                            <select id="chatFontFamily" onchange="updateCustomFont()" style="width: 100%; padding: 4px; border: 1px solid var(--input-border); border-radius: 4px; background: var(--input-bg); color: var(--input-text); font-size: 0.8rem;">
                                                <option value="'Orbitron', monospace">🌟 Orbitron</option>
                                                <option value="'Rajdhani', sans-serif">🤖 Rajdhani</option>
                                                <option value="'Calibri', sans-serif">📊 Calibri</option>
                                                <option value="'Segoe UI', sans-serif">🖥️ Segoe UI</option>
                                                <option value="'Roboto', sans-serif">🤖 Roboto</option>
                                                <option value="'Arial', sans-serif">📄 Arial</option>
                                                <option value="'Helvetica', sans-serif">🎨 Helvetica</option>
                                                <option value="'Fira Code', monospace">💻 Fira Code</option>
                                                <option value="'Monaco', monospace">🖥️ Monaco</option>
                                                <option value="'Consolas', monospace">⌨️ Consolas</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label style="font-size: 0.8rem; color: var(--input-text); display: block; margin-bottom: 2px;">Size:</label>
                                            <select id="chatFontSize" onchange="updateCustomFont()" style="width: 100%; padding: 4px; border: 1px solid var(--input-border); border-radius: 4px; background: var(--input-bg); color: var(--input-text); font-size: 0.8rem;">
                                                <option value="12px">12px</option>
                                                <option value="14px">14px</option>
                                                <option value="16px" selected>16px</option>
                                                <option value="18px">18px</option>
                                                <option value="20px">20px</option>
                                                <option value="22px">22px</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label style="font-size: 0.8rem; color: var(--input-text); display: block; margin-bottom: 2px;">Weight:</label>
                                            <select id="chatFontWeight" onchange="updateCustomFont()" style="width: 100%; padding: 4px; border: 1px solid var(--input-border); border-radius: 4px; background: var(--input-bg); color: var(--input-text); font-size: 0.8rem;">
                                                <option value="300">Light</option>
                                                <option value="400" selected>Normal</option>
                                                <option value="500">Medium</option>
                                                <option value="600">Semi Bold</option>
                                                <option value="700">Bold</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label style="font-size: 0.8rem; color: var(--input-text); display: block; margin-bottom: 2px;">Bold:</label>
                                            <div style="display: flex; align-items: center; justify-content: center; height: 26px;">
                                                <input type="checkbox" id="chatFontBold" onchange="updateCustomFont()" style="transform: scale(1.2);">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Font Preview -->
                                    <div>
                                        <label style="font-size: 0.8rem; color: var(--input-text); display: block; margin-bottom: 2px;">Preview:</label>
                                        <div id="chatFontPreview" style="padding: 6px; border: 1px solid var(--input-border); border-radius: 4px; background: var(--input-bg); color: var(--input-text); font-family: 'Orbitron', monospace; font-size: 14px; font-weight: 400;">
                                            The quick brown fox jumps over the lazy dog. 1234567890
                                        </div>
                                    </div>
                                </div>

                                <!-- Color Controls Section -->
                                <div style="border: 1px solid var(--input-border); border-radius: 6px; padding: 12px; background: rgba(255,255,255,0.05);">
                                    <h4 style="margin: 0 0 10px 0; font-size: 0.9rem; color: var(--input-text);">🎨 Message Colors</h4>

                                    <!-- Color Grid -->
                                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px;">
                                        <div>
                                            <label style="font-size: 0.8rem; color: var(--input-text); display: block; margin-bottom: 2px;">💬 Your Message BG:</label>
                                            <input type="color" id="userBubbleColor" value="#667eea" onchange="updateCustomColors()" style="width: 100%; height: 30px; border: 1px solid var(--input-border); border-radius: 4px;">
                                        </div>
                                        <div>
                                            <label style="font-size: 0.8rem; color: var(--input-text); display: block; margin-bottom: 2px;">📝 Your Message Text:</label>
                                            <input type="color" id="userTextColor" value="#ffffff" onchange="updateCustomColors()" style="width: 100%; height: 30px; border: 1px solid var(--input-border); border-radius: 4px;">
                                        </div>
                                        <div>
                                            <label style="font-size: 0.8rem; color: var(--input-text); display: block; margin-bottom: 2px;">🤖 Deeplica Message BG:</label>
                                            <input type="color" id="deepplicaBubbleColor" value="#00d4ff" onchange="updateCustomColors()" style="width: 100%; height: 30px; border: 1px solid var(--input-border); border-radius: 4px;">
                                        </div>
                                        <div>
                                            <label style="font-size: 0.8rem; color: var(--input-text); display: block; margin-bottom: 2px;">✨ Deeplica Message Text:</label>
                                            <input type="color" id="deepplicaTextColor" value="#ffffff" onchange="updateCustomColors()" style="width: 100%; height: 30px; border: 1px solid var(--input-border); border-radius: 4px;">
                                        </div>
                                    </div>

                                    <!-- Chat Background -->
                                    <div style="margin-top: 8px;">
                                        <label style="font-size: 0.8rem; color: var(--input-text); display: block; margin-bottom: 2px;">🎨 Chat Area Background:</label>
                                        <input type="color" id="chatBackgroundColor" value="#ffffff" onchange="updateCustomColors()" style="width: 100%; height: 30px; border: 1px solid var(--input-border); border-radius: 4px;">
                                        <small style="font-size: 0.7rem; color: var(--input-text); opacity: 0.7;">Background for messages area and input field</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Live Preview - Right Side -->
                        <div class="color-preview-side">
                            <h4>💬 Live Preview</h4>
                            <div class="preview-chat-container">
                                <div class="preview-messages" id="previewMessages">
                                    <div class="preview-message user-preview" id="userPreview">
                                        <div class="preview-text">This is your message example</div>
                                    </div>
                                    <div class="preview-message deeplica-preview" id="deepplicaPreview">
                                        <div class="preview-text">This is Deeplica's response example</div>
                                    </div>
                                </div>
                                <div class="preview-input-container">
                                    <input type="text" class="preview-input" placeholder="Type your message..." readonly>
                                    <button class="preview-send-btn">➤</button>
                                </div>
                            </div>
                        </div>

                        <!-- Live Preview -->
                        <div class="color-preview">
                            <h4>💬 Live Preview</h4>
                            <div class="preview-chat">
                                <div class="preview-message user-preview" id="userPreview">
                                    <div class="preview-text">This is your message example</div>
                                </div>
                                <div class="preview-message deeplica-preview" id="deepplicaPreview">
                                    <div class="preview-text">This is Deeplica's response example</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="customization-footer">
                <button class="reset-btn" onclick="resetToDefaults()">🔄 Reset to Defaults</button>
                <button class="apply-btn" onclick="saveCustomization()">✅ Save & Apply</button>
            </div>
        </div>
    </div>
</body>
</html>
