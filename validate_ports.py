#!/usr/bin/env python3
"""
🔍 Port Validation Script
Validates that all services are using correct ports from the port manager.
"""

import sys
import os

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.port_manager import get_service_port, get_all_ports, is_constant_port

def validate_ports():
    """Validate all port assignments"""
    print("🔍 Validating DEEPLICA port assignments...")
    print("=" * 60)
    
    all_ports = get_all_ports()
    
    print("\n📋 Current Port Assignments:")
    for service, port in all_ports.items():
        constant = "CONSTANT" if is_constant_port(service.lower()) else "configurable"
        print(f"  {service:20} {port:5} ({constant})")
    
    print("\n✅ Port validation completed!")
    print("\n💡 To change configurable ports:")
    print("   1. Use the admin interface in web chat")
    print("   2. Or edit shared/deeplica_port_settings.json")
    print("   3. Restart all services")

if __name__ == "__main__":
    validate_ports()
