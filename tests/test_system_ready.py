#!/usr/bin/env python3
"""
Test script to verify the system is fully ready and create a test phone call
"""

import asyncio
import httpx
import json
import time
from shared.port_manager import get_service_port

async def check_system_status():
    """Check if all services are ready"""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"http://localhost:{get_service_port('backend')}/system/status", timeout=10.0)
            if response.status_code == 200:
                data = response.json()
                return data.get("system_ready", False), data
            return False, {"error": "Failed to get status"}
    except Exception as e:
        return False, {"error": str(e)}

async def create_test_mission():
    """Create a test phone call mission"""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "http://localhost:{get_service_port("backend")}/api/v1/missions",
                json={"user_input": "Call +972547000430 and ask how are you, then end the call"},
                timeout=30.0
            )
            if response.status_code == 200:
                return True, response.json()
            return False, {"error": f"HTTP {response.status_code}: {response.text}"}
    except Exception as e:
        return False, {"error": str(e)}

async def main():
    # [SYSTEM:main] TEST | 🔍 Testing Deeplica v0 System Readiness
    # [SYSTEM:main] SYSTEM | =" * 5
    
    # Wait for system to be ready
    # [SYSTEM:main] SUCCESS | ⏳ Waiting for system to be fully ready...
    max_wait = 300  # 5 minutes
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        ready, status = await check_system_status()
        
        if ready:
            # [SYSTEM:main] SUCCESS | ✅ System is fully ready!
            # [SYSTEM:main] SYSTEM | 📊 System Status: {json.dumps(status, indent=}")
            break
        else:
            # [SYSTEM:main] SUCCESS | ⏳ System not ready yet... ({status.get('message', 'Unknown status})")
            await asyncio.sleep(10)
    else:
        # [SYSTEM:main] ERROR | ❌ System failed to become ready within timeout
        return False
    
    # Create test mission
    print("\n📞 Creating test phone call mission...")
    success, result = await create_test_mission()
    
    if success:
        # [SYSTEM:main] SUCCESS | ✅ Test mission created successfully!
        # [SYSTEM:main] SYSTEM | 📋 Mission Details: {json.dumps(result, indent=}")
        print("\n🎉 SYSTEM IS FULLY OPERATIONAL!")
        # [SYSTEM:main] SYSTEM | 📞 You should receive a phone call asking 'How are you?'
        return True
    else:
        # [SYSTEM:main] ERROR | ❌ Failed to create test mission: {result}
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
