<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Input Test - Deeplica</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-container {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .input-area {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .message-input {
            flex: 1;
            padding: 10px;
            border: 1px solid #444;
            border-radius: 5px;
            background: #333;
            color: #fff;
            resize: vertical;
            min-height: 40px;
        }
        .send-button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .send-button:hover {
            background: #0056b3;
        }
        .send-button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .log {
            background: #000;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.connected { background: #28a745; }
        .status.disconnected { background: #dc3545; }
        .status.connecting { background: #ffc107; color: #000; }
    </style>
</head>
<body>
    <h1>🧪 Deeplica Input Test</h1>
    
    <div class="test-container">
        <h3>📝 Input Test</h3>
        <div class="input-area">
            <textarea 
                id="messageInput" 
                class="message-input" 
                placeholder="Type your message and press Enter or click Send..." 
                rows="1"
            ></textarea>
            <button id="sendButton" class="send-button">Send</button>
        </div>
        
        <div id="status" class="status disconnected">❌ Not Connected</div>
        
        <h4>📋 Test Log:</h4>
        <div id="log" class="log"></div>
    </div>

    <script>
        console.log('🧪 Input Test Page Loaded');
        
        let messageInput, sendButton, logDiv, statusDiv;
        let isConnected = false;
        let ws = null;
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            console.log(logMessage);
            
            if (logDiv) {
                logDiv.innerHTML += logMessage + '\n';
                logDiv.scrollTop = logDiv.scrollHeight;
            }
        }
        
        function updateStatus(status, message) {
            if (statusDiv) {
                statusDiv.className = `status ${status}`;
                statusDiv.textContent = message;
            }
        }
        
        function sendMessage() {
            log('🚀 sendMessage() called');
            const message = messageInput.value.trim();
            log(`📝 Message content: "${message}"`);
            
            if (!message) {
                log('❌ Empty message, not sending');
                return;
            }
            
            log('📤 Sending message: ' + message);
            
            // Clear input
            messageInput.value = '';
            
            // Test WebSocket if connected
            if (isConnected && ws) {
                try {
                    const data = { type: 'message', content: message };
                    ws.send(JSON.stringify(data));
                    log('✅ Message sent via WebSocket');
                } catch (error) {
                    log('❌ WebSocket send error: ' + error);
                }
            } else {
                log('⚠️ WebSocket not connected - message would be sent if connected');
            }
        }
        
        function connectWebSocket() {
            log('🔗 Attempting WebSocket connection...');
            updateStatus('connecting', '🟡 Connecting...');
            
            try {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/ws?session_id=test_session`;
                log('🌐 WebSocket URL: ' + wsUrl);
                
                ws = new WebSocket(wsUrl);
                
                ws.onopen = function() {
                    log('✅ WebSocket connected');
                    isConnected = true;
                    updateStatus('connected', '✅ Connected');
                };
                
                ws.onmessage = function(event) {
                    log('📨 Received: ' + event.data);
                };
                
                ws.onclose = function(event) {
                    log(`🔴 WebSocket closed: ${event.code} ${event.reason}`);
                    isConnected = false;
                    updateStatus('disconnected', '❌ Disconnected');
                };
                
                ws.onerror = function(error) {
                    log('❌ WebSocket error: ' + error);
                    updateStatus('disconnected', '❌ Connection Error');
                };
                
            } catch (error) {
                log('❌ Error creating WebSocket: ' + error);
                updateStatus('disconnected', '❌ Failed to Connect');
            }
        }
        
        function initializeTest() {
            log('🚀 Initializing input test...');
            
            // Get DOM elements
            messageInput = document.getElementById('messageInput');
            sendButton = document.getElementById('sendButton');
            logDiv = document.getElementById('log');
            statusDiv = document.getElementById('status');
            
            // Verify elements
            if (!messageInput || !sendButton || !logDiv || !statusDiv) {
                console.error('❌ Required DOM elements not found');
                return;
            }
            
            log('✅ DOM elements found');
            
            // Attach event handlers
            sendButton.addEventListener('click', function(e) {
                log('📤 Send button clicked');
                e.preventDefault();
                sendMessage();
            });
            
            messageInput.addEventListener('keydown', function(e) {
                log(`⌨️ Key pressed: ${e.key} (Shift: ${e.shiftKey})`);
                if (e.key === 'Enter' && !e.shiftKey) {
                    log('📤 Enter key pressed - sending message');
                    e.preventDefault();
                    sendMessage();
                }
            });
            
            log('✅ Event handlers attached');
            
            // Try to connect WebSocket
            connectWebSocket();
            
            messageInput.focus();
            log('🎯 Input focused and ready');
        }
        
        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeTest);
        } else {
            initializeTest();
        }
        
        log('✅ Test script loaded');
    </script>
</body>
</html>
