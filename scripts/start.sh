#!/bin/bash
# =============================================================================
# DEEPLICA SERVICE SCRIPT - ALL PORTS MANAGED DYNAMICALLY
# =============================================================================
# ALL ports are assigned by shared/port_manager.py - NO HARDCODED PORTS
# External services adapt to assigned ports through configuration
# =============================================================================
# =============================================================================
# DEEPLICA SERVICE STARTUP SCRIPT
# =============================================================================
# NOTE: All ports are managed by shared/port_manager.py
# - Backend API: 8888 (CONSTANT - never changes)
# - Other services: configurable via port manager
# =============================================================================
# Deeplica v0 Startup Script
# Comprehensive setup and startup for development environment

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if port is available
port_available() {
    ! nc -z localhost $1 2>/dev/null
}

# Print banner
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    🤖 Deeplica v0 Setup                     ║"
echo "║                  AI Mission Assistant                        ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo

# Check prerequisites
print_status "Checking prerequisites..."

# Check Python
if ! command_exists python3; then
    print_error "Python 3 is required but not installed"
    exit 1
fi

PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
print_success "Python $PYTHON_VERSION found"

# Check pip
if ! command_exists pip3; then
    print_error "pip3 is required but not installed"
    exit 1
fi



echo

# Setup mode selection
echo "🚀 Setup Options:"
echo "1. Local Development (Requires external MongoDB)"
echo "2. Backend Only"
echo "3. CLI Only"
echo

read -p "Choose setup mode (1-3): " SETUP_MODE

case $SETUP_MODE in
    1)
        print_status "Setting up for local development..."

        # Check if MongoDB is running
        if ! port_available 27017; then
            print_success "MongoDB detected on port 27017"
        else
            print_warning "MongoDB not detected on port 27017"
            print_warning "Please start MongoDB or use MongoDB Atlas"
        fi
        
        # Setup backend
        print_status "Setting up backend..."
        cd backend
        
        if [ ! -f ".env" ]; then
            cp .env.example .env
            print_warning "Created .env file - please configure your API keys"
        fi
        
        print_status "Installing backend dependencies..."
        pip3 install -r requirements.txt
        
        # Setup CLI
        print_status "Setting up CLI..."
        cd ../cli
        print_status "Installing CLI dependencies..."
        pip3 install -r requirements.txt
        
        cd ..
        
        print_success "🎉 Local setup complete!"
        echo
        echo "🚀 To start the backend:"
        echo "  cd backend && python -m app.main"
        echo
        echo "🖥️  To start the CLI:"
        echo "  cd cli && python terminal_ui.py"
        ;;

    2)
        print_status "Setting up backend only..."
        cd backend
        
        if [ ! -f ".env" ]; then
            cp .env.example .env
            print_warning "Created .env file - please configure your API keys"
        fi
        
        print_status "Installing dependencies..."
        pip3 install -r requirements.txt
        
        print_success "🎉 Backend setup complete!"
        echo
        echo "🚀 To start the backend:"
        echo "  python -m app.main"
        ;;

    3)
        print_status "Setting up CLI only..."
        cd cli
        
        print_status "Installing dependencies..."
        pip3 install -r requirements.txt
        
        print_success "🎉 CLI setup complete!"
        echo
        echo "🖥️  To start the CLI:"
        echo "  python terminal_ui.py"
        echo
        echo "⚠️  Make sure the backend is running on http://localhost:8000"
        ;;
        
    *)
        print_error "Invalid option selected"
        exit 1
        ;;
esac

echo
print_success "Setup completed successfully!"
echo
echo "📚 Next steps:"
echo "  • Configure your API keys in backend/.env"
echo "  • Check the README.md for detailed usage instructions"
echo "  • Run the CLI to start creating missions!"
echo
echo "🆘 Need help?"
echo "  • Check logs: backend/logs/deeplica_v0.log"
echo "  • API documentation: http://localhost:8000/docs"
echo "  • Health check: curl http://localhost:8000/health"
