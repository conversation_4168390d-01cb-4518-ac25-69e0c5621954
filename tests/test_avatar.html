<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deeplica Avatar Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
        }

        .avatar-container {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            border-radius: 50%;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }

        .avatar-video {
            height: 15vh; /* 15% of viewport height */
            width: auto;
            aspect-ratio: 1;
            object-fit: cover;
            border-radius: 50%;
            display: block;
        }

        .avatar-container:hover {
            transform: translateX(-50%) scale(1.05);
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
        }

        .avatar-container.disconnected {
            opacity: 0.3;
            filter: grayscale(100%);
        }

        .test-info {
            margin-top: 150px;
            text-align: center;
            color: white;
            background: rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .controls {
            margin-top: 20px;
        }

        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }

        button:hover {
            background: #45a049;
        }

        button.disconnect {
            background: #f44336;
        }

        button.disconnect:hover {
            background: #da190b;
        }

        @media (max-width: 768px) {
            .avatar-video {
                height: 12vh;
            }
            
            .avatar-container {
                top: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Deeplica Avatar Video -->
    <div class="avatar-container" id="avatarContainer">
        <video id="deepplicaAvatar" class="avatar-video" autoplay muted loop>
            <source src="/avatar" type="video/mp4">  <!-- Port managed by web server -->
            Your browser does not support the video tag.
        </video>
    </div>

    <div class="test-info">
        <h1>🎬 Deeplica Avatar Test</h1>
        <p>Testing the Deeplica avatar video integration</p>
        <p><strong>Video Height:</strong> 15% of screen height</p>
        <p><strong>Position:</strong> Top center, fixed</p>
        <p><strong>Features:</strong> Auto-play, loop, hover effects</p>
        
        <div class="controls">
            <button onclick="playVideo()">▶️ Play</button>
            <button onclick="pauseVideo()">⏸️ Pause</button>
            <button onclick="toggleConnection()" id="connectionBtn">🔴 Simulate Disconnect</button>
        </div>
        
        <div id="status" style="margin-top: 20px; font-weight: bold;">
            Status: Connected & Playing
        </div>
    </div>

    <script>
        const avatar = document.getElementById('deepplicaAvatar');
        const avatarContainer = document.getElementById('avatarContainer');
        const connectionBtn = document.getElementById('connectionBtn');
        const status = document.getElementById('status');
        let isConnected = true;

        function playVideo() {
            avatar.play().then(() => {
                status.textContent = 'Status: Playing';
            }).catch(e => {
                status.textContent = 'Status: Play failed - ' + e.message;
            });
        }

        function pauseVideo() {
            avatar.pause();
            status.textContent = 'Status: Paused';
        }

        function toggleConnection() {
            isConnected = !isConnected;
            
            if (isConnected) {
                avatarContainer.classList.remove('disconnected');
                avatar.play().catch(e => console.log('Play failed:', e));
                connectionBtn.textContent = '🔴 Simulate Disconnect';
                connectionBtn.className = '';
                status.textContent = 'Status: Connected & Playing';
            } else {
                avatarContainer.classList.add('disconnected');
                avatar.pause();
                connectionBtn.textContent = '🟢 Simulate Connect';
                connectionBtn.className = 'disconnect';
                status.textContent = 'Status: Disconnected & Paused';
            }
        }

        // Auto-start the video when page loads
        avatar.addEventListener('loadeddata', () => {
            playVideo();
        });

        // Handle video errors
        avatar.addEventListener('error', (e) => {
            status.textContent = 'Status: Video Error - ' + e.message;
        });
    </script>
</body>
</html>
