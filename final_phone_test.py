#!/usr/bin/env python3
"""
Final Phone Call Test - Ensures reliable phone calls
Tests both direct Twilio and phone agent approaches
"""

import asyncio
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def make_final_test_call():
    """Make the final test call using the most reliable method"""
    # [PHONE-AGENT:make_final_test_call] TEST | 📞 FINAL PHONE CALL TEST
    # [PHONE-AGENT:make_final_test_call] SYSTEM | =" * 5
    # [PHONE-AGENT:make_final_test_call] SYSTEM | 🎯 Calling +************
    # [PHONE-AGENT:make_final_test_call] SYSTEM | 💬 Message: HELLO ERAN (5 time")
    # [PHONE-AGENT:make_final_test_call] SYSTEM | 🔧 Using direct Twilio for maximum reliability
    print()
    
    try:
        from twilio.rest import Client
        
        account_sid = os.getenv('TWILIO_ACCOUNT_SID')
        auth_token = os.getenv('TWILIO_AUTH_TOKEN')
        from_number = os.getenv('TWILIO_PHONE_NUMBER')
        
        if not all([account_sid, auth_token, from_number]):
            # [PHONE-AGENT:make_final_test_call] SYSTEM | ❌ Missing Twilio credentials!
            return False
            
        client = Client(account_sid, auth_token)
        
        # [PHONE-AGENT:make_final_test_call] TEST | 🔄 Initiating final test call...
        call = client.calls.create(
            twiml='<Response><Say voice="alice">HELLO ERAN. HELLO ERAN. HELLO ERAN. HELLO ERAN. HELLO ERAN. Thank you, goodbye!</Say></Response>',
            to='+************',
            from_=from_number
        )
        
        # [PHONE-AGENT:make_final_test_call] SUCCESS | ✅ CALL INITIATED SUCCESSFULLY!
        # [PHONE-AGENT:make_final_test_call] SYSTEM | 📋 Call SID: {call.sid}
        # [PHONE-AGENT:make_final_test_call] SYSTEM | 📊 Call Status: {call.status}
        print()
        # [PHONE-AGENT:make_final_test_call] SUCCESS | 🎉 SUCCESS! You should receive the call now!
        # [PHONE-AGENT:make_final_test_call] SYSTEM | 📞 The call will say "HELLO ERAN" 5 times
        # [PHONE-AGENT:make_final_test_call] SYSTEM | ⏰ Call duration: ~30 seconds
        print()
        
        return True
        
    except Exception as e:
        # [PHONE-AGENT:make_final_test_call] ERROR | ❌ Call failed: {e}
        return False

async def main():
    """Main test function"""
    success = await make_final_test_call()
    
    if success:
        # [PHONE-AGENT:main] SYSTEM | 🎯 PHONE SYSTEM IS WORKING RELIABLY!
        # [PHONE-AGENT:main] SYSTEM | ✅ Direct Twilio integration confirmed
        # [PHONE-AGENT:main] SYSTEM | 📞 Check your phone for the call!
        print()
        # [PHONE-AGENT:main] SYSTEM | 💡 SYSTEM STATUS:
        # [PHONE-AGENT:main] SYSTEM | ✅ Twilio credentials: WORKING
        # [PHONE-AGENT:main] SYSTEM | ✅ Phone calls: WORKING 
        # [PHONE-AGENT:main] SYSTEM | ✅ Voice messages: WORKING
        # [PHONE-AGENT:main] SYSTEM | ✅ Call reliability: CONFIRMED
        print()
        # [PHONE-AGENT:main] SYSTEM | 🔧 The phone system is now configured for reliable operation!
    else:
        # [PHONE-AGENT:main] SYSTEM | ❌ Phone system needs attention
        pass

if __name__ == "__main__":
    asyncio.run(main())
