#!/usr/bin/env python3
"""
Comprehensive script to find and fix ALL remaining syntax errors automatically.
This script will systematically fix common syntax error patterns.
"""

import os
import re
import ast
import glob
from typing import List, <PERSON><PERSON>

def fix_common_syntax_errors(content: str) -> str:
    """Fix common syntax error patterns"""
    
    # Fix unterminated string literals with missing quotes
    content = re.sub(r'f"([^"]*)":\s*\{([^}]*)\}"\)', r'f"\1: {\2}")', content)
    
    # Fix mismatched parentheses in f-strings
    content = re.sub(r'\{([^}]*)\}\}\)', r'{\1})', content)
    
    # Fix unterminated strings that end with ") instead of ")
    content = re.sub(r'([^"]*)":\s*\{([^}]*)\}"\)', r'\1: {\2}")', content)
    
    # Fix specific patterns found in the codebase
    patterns = [
        # Fix unterminated strings ending with "): {e}")
        (r'([^"]*)":\s*\{e\}"\)', r'\1: {e}")'),
        
        # Fix mismatched braces in f-strings
        (r'\{([^}]*)\}\}\)', r'{\1})'),
        
        # Fix format_exc))} patterns
        (r'format_exc\)\}\}', r'format_exc()}'),
        
        # Fix incomplete words at end of strings
        (r'"([^"]*)\s+([a-z]+)":\s*\{', r'"\1 \2": {'),
        
        # Fix missing closing quotes before colons
        (r'([^"]*)":\s*\{([^}]*)\}"\)', r'\1: {\2}")'),
    ]
    
    for pattern, replacement in patterns:
        content = re.sub(pattern, replacement, content)
    
    return content

def fix_file_comprehensive(file_path: str) -> bool:
    """Comprehensively fix syntax errors in a file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        # Apply fixes
        fixed_content = fix_common_syntax_errors(original_content)
        
        # Try to parse to check if fixed
        try:
            ast.parse(fixed_content)
            syntax_valid = True
        except SyntaxError:
            syntax_valid = False
        
        # Write back if changed and valid
        if fixed_content != original_content:
            if syntax_valid:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(fixed_content)
                print(f"✅ [FIX-SYNTAX:fix_file_comprehensive] SUCCESS | Fixed and validated: {file_path}")
                return True
            else:
                print(f"⚠️ [FIX-SYNTAX:fix_file_comprehensive] WARNING | Changes made but syntax still invalid: {file_path}")
                return False
        else:
            if syntax_valid:
                print(f"✅ [FIX-SYNTAX:fix_file_comprehensive] SUCCESS | Already valid: {file_path}")
            else:
                print(f"❌ [FIX-SYNTAX:fix_file_comprehensive] ERROR | No changes made, syntax invalid: {file_path}")
            return syntax_valid
            
    except Exception as e:
        print(f"❌ [FIX-SYNTAX:fix_file_comprehensive] ERROR | Error processing {file_path}: {type(e).__name__}: {e}")
        return False

def main():
    """Main function to fix all remaining syntax errors"""
    print("🔧 [FIX-SYNTAX:main] SYSTEM | Starting comprehensive syntax error fix...")
    print("=" * 80)
    
    # Find all Python files
    python_files = []
    for pattern in ["**/*.py"]:
        python_files.extend(glob.glob(pattern, recursive=True))
    
    # Filter out __pycache__ and .pyc files
    python_files = [f for f in python_files if '__pycache__' not in f and f.endswith('.py')]
    
    print(f"📁 [FIX-SYNTAX:main] SYSTEM | Found {len(python_files)} Python files to check")
    
    fixed_count = 0
    error_count = 0
    
    for file_path in sorted(python_files):
        if fix_file_comprehensive(file_path):
            fixed_count += 1
        else:
            # Check if it has syntax errors
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                ast.parse(content)
            except SyntaxError:
                error_count += 1
    
    print("=" * 80)
    print(f"🎉 [FIX-SYNTAX:main] SUCCESS | Comprehensive syntax fix complete!")
    print(f"📊 [FIX-SYNTAX:main] SYSTEM | Files processed: {len(python_files)}")
    print(f"📊 [FIX-SYNTAX:main] SYSTEM | Files fixed: {fixed_count}")
    print(f"📊 [FIX-SYNTAX:main] SYSTEM | Files with remaining errors: {error_count}")

if __name__ == "__main__":
    main()
