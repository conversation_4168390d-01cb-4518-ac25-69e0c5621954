"""
Mission Orchestrator - Core mission execution logic.
Replaces the AgentDispatcher with microservices architecture.
"""

import asyncio
import httpx
import os
import sys
from typing import Dict, Any, Optional, List
from shared.unified_logging import get_dispatcher_logger

# Initialize unified logger
logger = get_dispatcher_logger()

# Add project root to path for shared models
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared_models import (
    TaskRequest, TaskCompletionCallback, MissionExecutionResult,
    TaskCompletionStatus, MissionStatus, TaskStatus, MissionContext, TaskSuggestion, Task
)
from shared.port_manager import get_service_port, get_localhost
# Import local modules - handle both relative and absolute imports
try:
    from .agent_registry import AgentRegistry
    from .database import BackendDatabaseClient
    from .parameter_resolver import ParameterResolver
except ImportError:
    # Fallback to absolute imports when run as script
    from agent_registry import AgentRegistry
    from database import BackendDatabaseClient
    from parameter_resolver import ParameterResolver


class MissionOrchestrator:
    """
    Central orchestrator for mission execution.
    Coordinates with agent microservices via HTTP calls and callbacks.
    """
    
    def __init__(self, db_service: BackendDatabaseClient, agent_registry: AgentRegistry):
        self.db_service = db_service
        self.agent_registry = agent_registry
        self.execution_lock = asyncio.Lock()
        self.http_client = httpx.AsyncClient(timeout=30.0)

        # Initialize parameter resolver
        self.parameter_resolver = ParameterResolver(db_service)

        # Get callback URL for agents
        self.callback_url = self._get_callback_url()

        logger.info("🎯 Mission Orchestrator initialized", module="DISPATCHER", routine="MissionOrchestrator.__init__()")
        logger.debug("📊 Configuration: db_service={type(db_service).__name__}, agent_registry={type(agent_registry).__name__}", module="DISPATCHER", routine="MissionOrchestrator.__init__()")
        logger.debug("🔗 Callback URL: {self.callback_url}", module="DISPATCHER", routine="MissionOrchestrator.__init__()")
    
    def _get_callback_url(self) -> str:
        """Get the callback URL for agent services"""
        dispatcher_url = os.getenv("DISPATCHER_URL", f"http://{get_localhost()}:{get_service_port('dispatcher')}")
        return f"{dispatcher_url}/task-completed"
    
    async def start_mission_execution(self, mission_id: str) -> MissionExecutionResult:
        """Start executing a mission"""
        logger.info("🚀 Starting mission execution: {mission_id}", module="DISPATCHER", routine="start_mission_execution")

        async with self.execution_lock:
            try:
                # Get mission from database
                logger.info("🔍 DEBUG: Getting mission from database: {mission_id}", module="DISPATCHER", routine="start_mission_execution")
                mission = await self.db_service.get_mission(mission_id)
                if not mission:
                    raise ValueError(f"Mission {mission_id} not found")
                logger.info("✅ DEBUG: Mission found: {mission.title}", module="DISPATCHER", routine="start_mission_execution")

                # Mark mission as started
                logger.info("🔍 DEBUG: Marking mission as started", module="DISPATCHER", routine="start_mission_execution")
                mission.mark_started()
                await self.db_service.update_mission(mission)
                logger.info("✅ DEBUG: Mission marked as started", module="DISPATCHER", routine="start_mission_execution")

                # Check if mission needs planning (no tasks exist yet)
                logger.info("🔍 DEBUG: Checking for existing tasks", module="DISPATCHER", routine="start_mission_execution")
                existing_tasks = await self.db_service.get_mission_tasks(mission_id)
                logger.info("✅ DEBUG: Found {len(existing_tasks) if existing_tasks else 0} existing tasks", module="DISPATCHER", routine="start_mission_execution")

                if not existing_tasks:
                    logger.info(f"🧠 Mission needs planning, creating planning task: {mission_id}", module="DISPATCHER", routine="start_mission_execution")
                    await self._create_planning_task(mission)
                    logger.info("✅ DEBUG: Planning task created", module="DISPATCHER", routine="start_mission_execution")

                # Execute first available tasks
                logger.info("🔍 DEBUG: Executing next tasks", module="DISPATCHER", routine="start_mission_execution")
                result = await self._execute_next_tasks(mission_id)
                logger.info("✅ DEBUG: Next tasks executed", module="DISPATCHER", routine="start_mission_execution")

                logger.info(f"✅ Mission execution started: {mission_id}", module="DISPATCHER", routine="start_mission_execution")
                return result

            except Exception as e:
                logger.error("❌ Failed to start mission execution: {e}", module="DISPATCHER", routine="start_mission_execution")
                import traceback
                logger.error("❌ Full traceback: {traceback.format_exc()}", module="DISPATCHER", routine="start_mission_execution")
                # Mark mission as failed
                try:
                    mission = await self.db_service.get_mission(mission_id)
                    if mission:
                        mission.mark_failed(str(e))
                        await self.db_service.update_mission(mission)
                except Exception as db_error:
                    logger.error("❌ Failed to update mission status: {db_error}", module="DISPATCHER", routine="start_mission_execution")
                raise
    
    async def continue_mission_execution(
        self, 
        mission_id: str, 
        user_response: Optional[str] = None
    ) -> MissionExecutionResult:
        """Continue mission execution with optional user response"""
        logger.info("▶️ Continuing mission execution: {mission_id}", module="DISPATCHER", routine="continue_mission_execution")
        
        async with self.execution_lock:
            try:
                # Get mission from database
                mission = await self.db_service.get_mission(mission_id)
                if not mission:
                    raise ValueError(f"Mission {mission_id} not found")
                
                # Process user response if provided
                if user_response and mission.current_task_id:
                    await self._process_user_response(mission, user_response)
                
                # Execute next available tasks
                result = await self._execute_next_tasks(mission_id)
                
                logger.info(f"✅ Mission execution continued: {mission_id}", module="DISPATCHER", routine="continue_mission_execution")
                return result

            except Exception as e:
                logger.error(f"❌ Failed to continue mission execution: {e}", module="DISPATCHER", routine="continue_mission_execution")
                raise
    
    async def handle_task_completion(self, callback: TaskCompletionCallback) -> None:
        """Handle task completion callback from agent"""
        logger.info(f"📥 Received task completion: {callback.task_id}", module="DISPATCHER", routine="handle_task_completion")

        try:
            # Update task in database
            task = await self.db_service.get_task(callback.task_id)
            if not task:
                logger.error(f"❌ Task not found: {callback.task_id}", module="DISPATCHER", routine="handle_task_completion")
                return

            logger.info(f"📥 Handling task completion: {callback.task_id}", module="DISPATCHER", routine="handle_task_completion")

            # Handle task completion (unified for all agents) - but don't execute next tasks yet
            await self._handle_task_completion_unified(callback, task)

            # Handle any suggested tasks from the agent BEFORE continuing execution
            if hasattr(callback, 'suggested_tasks') and callback.suggested_tasks:
                await self._handle_suggested_tasks(callback.suggested_tasks, callback.mission_id)

            # Continue execution if task completed successfully (after handling suggested tasks)
            if callback.status == TaskCompletionStatus.COMPLETED:
                await self._execute_next_tasks(callback.mission_id)

            logger.info(f"✅ Task completion handled: {callback.task_id}", module="DISPATCHER", routine="handle_task_completion")

        except Exception as e:
            logger.error(f"❌ Failed to handle task completion: {e}", module="DISPATCHER", routine="handle_task_completion")
            raise

    async def _handle_task_completion_unified(self, callback: TaskCompletionCallback, task) -> None:
        """Handle task completion for any agent type"""
        logger.info(f"📋 Handling task completion: {callback.task_id} ({task.task_type})", module="DISPATCHER", routine="_handle_task_completion_unified")

        # Update task based on callback status
        if callback.status == TaskCompletionStatus.COMPLETED:
            task.mark_completed(callback.result)

            # Handle special mission metadata updates for planning tasks
            if task.task_type == "planning":
                await self._update_mission_metadata_from_planning(callback)

        elif callback.status == TaskCompletionStatus.FAILED:
            task.mark_failed(callback.error_message or "Agent reported failure")
        elif callback.status == TaskCompletionStatus.REQUIRES_USER_INPUT:
            # Task is waiting for user input - don't mark as complete yet
            task.result = callback.result
            # Keep status as IN_PROGRESS

        await self.db_service.update_task(task)

        # Update mission state
        mission = await self.db_service.get_mission(callback.mission_id)
        if mission:
            if callback.status == TaskCompletionStatus.COMPLETED:
                mission.update_task_completion(callback.task_id, success=True)
            elif callback.status == TaskCompletionStatus.FAILED:
                mission.update_task_completion(callback.task_id, success=False)
            elif callback.status == TaskCompletionStatus.REQUIRES_USER_INPUT:
                # Set current task for user input handling
                mission.current_task_id = callback.task_id

            await self.db_service.update_mission(mission)

        # Note: _execute_next_tasks is now called in handle_task_completion after suggested tasks

    async def _update_mission_metadata_from_planning(self, callback: TaskCompletionCallback) -> None:
        """Update mission metadata from planning task results"""
        result = callback.result
        mission_metadata = result.get("mission_metadata", {})

        if not mission_metadata:
            return

        # Update mission with enhanced metadata
        mission = await self.db_service.get_mission(callback.mission_id)
        if mission:
            # Update mission metadata
            if "title" in mission_metadata:
                mission.title = mission_metadata["title"]
            if "description" in mission_metadata:
                mission.description = mission_metadata["description"]
            if "priority" in mission_metadata:
                mission.priority = mission_metadata["priority"]

            await self.db_service.update_mission(mission)
            logger.info("✅ Mission metadata updated: {callback.mission_id}", module="DISPATCHER", routine="_update_mission_metadata_from_planning")

    async def _handle_suggested_tasks(self, suggested_tasks: List[Dict[str, Any]], mission_id: str) -> None:
        """Handle task suggestions from any agent"""
        logger.info("💡 Processing {len(suggested_tasks)} suggested tasks for mission: {mission_id}", module="DISPATCHER", routine="_handle_suggested_tasks")

        from shared_models import Task, TaskStatus

        for task_suggestion in suggested_tasks:
            try:
                # Determine the correct agent for this task type if not specified
                assigned_agent = task_suggestion.assigned_agent
                if not assigned_agent:
                    assigned_agent = self.agent_registry.get_agent_for_task(task_suggestion.task_type)
                    if not assigned_agent:
                        logger.warning("⚠️ No agent found for task type {task_suggestion.task_type}, defaulting to dialogue", module="DISPATCHER", routine="_handle_suggested_tasks")
                        assigned_agent = "dialogue"

                new_task = Task(
                    task_id=task_suggestion.task_id,
                    mission_id=mission_id,
                    task_type=task_suggestion.task_type,
                    description=task_suggestion.description,
                    prompt=task_suggestion.prompt,
                    parent_tasks=task_suggestion.parent_tasks,
                    child_tasks=task_suggestion.child_tasks,
                    assigned_agent=assigned_agent,
                    status=TaskStatus.PENDING,
                    context=task_suggestion.context,
                    has_unresolved_placeholders=getattr(task_suggestion, 'has_placeholders', False)
                )

                await self.db_service.create_task(new_task)
                logger.info("✅ Task created from suggestion: {new_task.task_id}", module="DISPATCHER", routine="_handle_suggested_tasks")

            except Exception as e:
                logger.error(f"❌ Failed to create suggested task: {e}", module="DISPATCHER", routine="_handle_suggested_tasks")
                # Continue with other tasks rather than failing completely
                continue



    async def _execute_next_tasks(self, mission_id: str) -> MissionExecutionResult:
        """Execute all available tasks for a mission"""
        executed_tasks = []
        responses = []

        while True:
            # Get next executable tasks from database
            executable_tasks = await self.db_service.get_executable_tasks(mission_id)

            if not executable_tasks:
                # No more tasks to execute
                break

            # For v0: Execute tasks sequentially (one at a time)
            task = executable_tasks[0]

            try:
                # Send task to appropriate agent
                await self._send_task_to_agent(task)
                executed_tasks.append(task.task_id)

                # Task is now in progress - agent will callback when done
                break  # Wait for callback before continuing

            except Exception as e:
                logger.error(f"❌ Failed to send task to agent: {task.task_id}: {e}", module="DISPATCHER", routine="_execute_next_tasks")
                # Mark task as failed and continue
                task.mark_failed(str(e))
                await self.db_service.update_task(task)

                mission = await self.db_service.get_mission(mission_id)
                if mission:
                    mission.update_task_completion(task.task_id, success=False)
                    await self.db_service.update_mission(mission)
                break

        # Get current mission state and progress
        mission = await self.db_service.get_mission(mission_id)
        progress = await self.db_service.get_mission_progress(mission_id)

        # Check if mission is complete
        is_complete = await self.db_service.is_mission_complete(mission_id)

        # Check if mission has failed
        is_failed = await self.db_service.is_mission_failed(mission_id)

        # If mission is complete but not marked as such, mark it as completed
        if is_complete and mission and mission.status == MissionStatus.IN_PROGRESS:
            logger.info(f"🎉 Mission completed successfully, updating status: {mission_id}", module="DISPATCHER", routine="_execute_next_tasks")

            # Create final result from all completed tasks
            all_tasks = await self.db_service.get_mission_tasks(mission_id)
            completed_task_results = []
            for task in all_tasks:
                if task.status == TaskStatus.DONE and task.result:
                    completed_task_results.append({
                        "task_id": task.task_id,
                        "task_type": task.task_type,
                        "description": task.description,
                        "result": task.result
                    })

            final_result = {
                "mission_completed": True,
                "total_tasks": len(all_tasks),
                "completed_tasks": len([t for t in all_tasks if t.status == TaskStatus.DONE]),
                "failed_tasks": len([t for t in all_tasks if t.status == TaskStatus.FAILED]),
                "task_results": completed_task_results
            }

            mission.mark_completed(final_result)
            await self.db_service.update_mission(mission)

        # If mission has failed but not marked as such, mark it as failed
        elif is_failed and mission and mission.status == MissionStatus.IN_PROGRESS:
            logger.error("❌ Mission failed, updating status: {mission_id}", module="DISPATCHER", routine="_execute_next_tasks")

            # Create failure summary
            all_tasks = await self.db_service.get_mission_tasks(mission_id)
            failed_tasks = [t for t in all_tasks if t.status == TaskStatus.FAILED]

            failed_task_details = []
            for task in failed_tasks:
                failed_task_details.append({
                    "task_id": task.task_id,
                    "task_type": task.task_type,
                    "description": task.description,
                    "error": task.result.get("error", "Unknown error") if task.result else "Unknown error"
                })

            error_message = f"Mission failed: {len(failed_tasks)} out of {len(all_tasks)} tasks failed"

            mission.mark_failed(error_message)
            mission.final_result = {
                "mission_failed": True,
                "total_tasks": len(all_tasks),
                "completed_tasks": len([t for t in all_tasks if t.status == TaskStatus.DONE]),
                "failed_tasks": len(failed_tasks),
                "failed_task_details": failed_task_details
            }
            await self.db_service.update_mission(mission)

        # Check if waiting for user input
        requires_user_input = (mission and mission.current_task_id is not None)
        waiting_message = None

        if requires_user_input:
            # Get the current task to see if it has a message for the user
            current_task = await self.db_service.get_task(mission.current_task_id)
            if current_task and current_task.result:
                waiting_message = current_task.result.get("message")

        return MissionExecutionResult(
            mission_id=mission_id,
            status=mission.status if mission else "unknown",
            executed_tasks=executed_tasks,
            responses=responses,
            progress=progress,
            is_complete=is_complete,
            requires_user_input=requires_user_input,
            waiting_message=waiting_message
        )

    async def _send_task_to_agent(self, task) -> None:
        """Send a task to the appropriate agent service"""
        logger.info("📤 Sending task to agent: {task.task_id}", module="DISPATCHER", routine="_send_task_to_agent")

        # Determine which agent should handle this task
        agent_type = self.agent_registry.get_agent_for_task(task.task_type)
        logger.debug("🔍 Task type '{task.task_type}' mapped to agent type '{agent_type}'", module="DISPATCHER", routine="_send_task_to_agent")
        if not agent_type:
            raise ValueError(f"No agent available for task type: {task.task_type}")

        # Get agent service URL
        execute_url = self.agent_registry.get_execute_url(agent_type)
        logger.debug(f"🔍 Agent '{agent_type}' execute URL: {execute_url}", module="DISPATCHER", routine="_send_task_to_agent")
        if not execute_url:
            raise ValueError(f"Agent service not available: {agent_type}")

        # Get mission context for all agents
        mission = await self.db_service.get_mission(task.mission_id)
        if not mission:
            raise ValueError(f"Mission {task.mission_id} not found")

        # Create unified mission context with conversation history
        mission_context = await self._build_enhanced_mission_context(mission)

        # Prepare unified task data
        task_data = {
            "description": task.description,
            "prompt": task.prompt,
            "context": task.context,
            "user_input": task.context.get("user_input", "")
        }

        # For phone call tasks, merge phone-specific fields into task_data
        logger.debug("🔍 Checking task type for phone field extraction: '{task.task_type}' (type: {type(task.task_type)})", module="DISPATCHER", routine="_send_task_to_agent")
        task_type_str = str(task.task_type).lower()
        logger.debug("🔍 Task type string: '{task_type_str}'", module="DISPATCHER", routine="_send_task_to_agent")
        if task_type_str == "phone_call" or task_type_str == "phone" or "phone" in task_type_str:
            logger.debug("📞 Processing phone call task, extracting phone fields from context: {list(task.context.keys())}", module="DISPATCHER", routine="_send_task_to_agent")
            phone_fields = ["phone_number", "question", "contact_name", "language", "max_duration_minutes"]
            for field in phone_fields:
                if field in task.context:
                    task_data[field] = task.context[field]  # Phone-specific field should override the generic context
                    logger.debug("📞 Extracted phone field '{field}': {task.context[field]}", module="DISPATCHER", routine="_send_task_to_agent")
            if "context" in task.context:
                task_data["context"] = task.context["context"]
            logger.debug("📞 Final task_data for phone agent: {list(task_data.keys())}", module="DISPATCHER", routine="_send_task_to_agent")

        # Resolve parameter placeholders before sending to agent
        if self.parameter_resolver.has_placeholders(task_data):
            logger.info("🔍 Resolving parameters for task: {task.task_id}", module="DISPATCHER", routine="_send_task_to_agent")
            try:
                # Resolve placeholders
                resolved_task_data = await self.parameter_resolver.resolve_task_parameters(
                    task_data, task.mission_id, mission_context
                )
                task_data = resolved_task_data

                # Update task to mark placeholders as resolved (with safety check)
                if hasattr(task, 'has_unresolved_placeholders'):
                    task.has_unresolved_placeholders = False
                    await self.db_service.update_task(task)

                logger.info("✅ Parameters resolved for task: {task.task_id}", module="DISPATCHER", routine="_send_task_to_agent")

            except Exception as e:
                logger.error("❌ Failed to resolve parameters for task {task.task_id}: {e}", module="DISPATCHER", routine="_send_task_to_agent")
                # Mark task as failed due to parameter resolution error
                task.mark_failed(f"Parameter resolution failed: {e}")
                await self.db_service.update_task(task)
                raise

        # Prepare task request with unified structure
        task_request = TaskRequest(
            task_id=task.task_id,
            mission_id=task.mission_id,
            task_type=task.task_type,
            task_data=task_data,
            mission_context=mission_context,
            callback_url=self.callback_url,
            context={}
        )

        # Mark task as in progress
        task.mark_started()
        await self.db_service.update_task(task)

        # Send HTTP request to agent
        try:
            response = await self.http_client.post(
                execute_url,
                json=task_request.model_dump(),
                timeout=30.0
            )
            response.raise_for_status()

            logger.info(f"✅ Task sent to {agent_type} agent: {task.task_id}", module="DISPATCHER", routine="_send_task_to_agent")

        except httpx.HTTPError as e:
            logger.error(f"❌ HTTP error sending task to agent: {e}", module="DISPATCHER", routine="_send_task_to_agent")
            raise
        except Exception as e:
            logger.error(f"❌ Unexpected error sending task to agent: {e}", module="DISPATCHER", routine="_send_task_to_agent")
            raise

    async def _build_enhanced_mission_context(self, mission) -> MissionContext:
        """Build mission context with conversation history from previous tasks"""

        # Get all completed tasks for this mission to build conversation history
        all_tasks = await self.db_service.get_mission_tasks(mission.mission_id)
        conversation_history = []

        # Extract conversation history from dialogue tasks that have results
        # Include both completed tasks and in-progress tasks that have generated responses
        for task in all_tasks:
            if (task.task_type in ["dialogue", "user_interaction", "conversation"] and
                task.result and
                task.status in [TaskStatus.DONE, TaskStatus.IN_PROGRESS]):

                # Add the dialogue exchange to history
                dialogue_entry = {
                    "task_id": task.task_id,
                    "task_type": task.task_type,
                    "description": task.description,
                    "result": task.result,
                    "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                    "status": task.status
                }
                conversation_history.append(dialogue_entry)

        # Sort by completion time to maintain chronological order
        # Use task creation time as fallback for in-progress tasks
        def sort_key(x):
            completed_at = x.get("completed_at")
            if completed_at:
                return completed_at
            # For in-progress tasks, use a timestamp that puts them at the end
            return "9999-12-31T23:59:59"

        conversation_history.sort(key=sort_key)

        # Get mission progress
        progress = await self.db_service.get_mission_progress(mission.mission_id)

        # Add conversation history to progress data
        enhanced_progress = {
            **progress,
            "conversation_history": conversation_history,
            "total_dialogue_tasks": len(conversation_history)
        }

        # Create enhanced mission context
        return MissionContext(
            mission_id=mission.mission_id,
            user_input=mission.user_input,
            title=getattr(mission, 'title', ''),
            description=getattr(mission, 'description', ''),
            priority=getattr(mission, 'priority', 'normal'),
            status=mission.status,
            created_at=mission.created_at.isoformat() if mission.created_at else "",
            started_at=mission.started_at.isoformat() if mission.started_at else None,
            progress=enhanced_progress
        )

    async def _process_user_response(self, mission, user_response: str) -> None:
        """Process user response for the current task"""
        if not mission.current_task_id:
            logger.warning("No current task to process user response", module="DISPATCHER", routine="_process_user_response")
            return

        # Get current task from database
        current_task = await self.db_service.get_task(mission.current_task_id)
        if not current_task:
            logger.warning("Current task {mission.current_task_id} not found", module="DISPATCHER", routine="_process_user_response")
            return

        logger.info(f"💬 Processing user response for task: {mission.current_task_id}", module="DISPATCHER", routine="_process_user_response")

        # Send continuation task to agent with user response
        agent_type = self.agent_registry.get_agent_for_task(current_task.task_type)
        if not agent_type:
            raise ValueError(f"No agent available for task type: {current_task.task_type}")

        execute_url = self.agent_registry.get_execute_url(agent_type)
        if not execute_url:
            raise ValueError(f"Agent service not available: {agent_type}")

        # Get mission context for continuation
        mission = await self.db_service.get_mission(current_task.mission_id)
        if not mission:
            raise ValueError(f"Mission {current_task.mission_id} not found")

        # Create unified mission context with conversation history
        mission_context = await self._build_enhanced_mission_context(mission)

        # Prepare continuation request with unified structure
        task_request = TaskRequest(
            task_id=current_task.task_id,
            mission_id=current_task.mission_id,
            task_type=current_task.task_type,
            task_data={
                "description": current_task.description,
                "prompt": current_task.prompt,
                "context": current_task.context,
                "user_response": user_response,
                "continuation": True
            },
            mission_context=mission_context,
            callback_url=self.callback_url,
            context={"user_response": user_response}
        )

        try:
            response = await self.http_client.post(
                execute_url,
                json=task_request.model_dump(),
                timeout=30.0
            )
            response.raise_for_status()

            # Clear current task ID - agent will callback when done
            mission.current_task_id = None
            await self.db_service.update_mission(mission)

            logger.info("✅ User response sent to agent: {current_task.task_id}", module="DISPATCHER", routine="_process_user_response")

        except Exception as e:
            logger.error("❌ Failed to send user response to agent: {e}", module="DISPATCHER", routine="_process_user_response")
            raise

    async def _create_planning_task(self, mission) -> None:
        """Create a planning task for the mission"""
        from shared_models import Task, TaskType

        planning_task = Task(
            task_id=f"planning_{mission.mission_id}",
            mission_id=mission.mission_id,
            task_type=TaskType.PLANNING,
            description="Plan mission and create task graph",
            prompt="Analyze user input and create a structured mission plan with executable tasks",
            status=TaskStatus.PENDING,
            parent_tasks=[],
            child_tasks=[],
            assigned_agent="planner",
            context={"user_input": mission.user_input}
        )

        # Store planning task in database
        await self.db_service.create_task(planning_task)
        logger.info("✅ Planning task created: {planning_task.task_id}", module="DISPATCHER", routine="_create_planning_task")

    async def get_mission_status(self, mission_id: str) -> Optional[Dict[str, Any]]:
        """Get current status of a mission"""
        mission = await self.db_service.get_mission(mission_id)
        if not mission:
            return None

        # Get progress from database
        progress = await self.db_service.get_mission_progress(mission_id)

        return {
            "mission_id": mission.mission_id,
            "status": mission.status,
            "progress": progress,
            "current_task": mission.current_task_id,
            "requires_user_input": mission.current_task_id is not None,
            "execution_log": mission.execution_log[-5:]  # Last 5 log entries
        }

    async def cancel_mission(self, mission_id: str) -> bool:
        """Cancel a mission"""
        logger.info("🛑 Cancelling mission: {mission_id}", module="DISPATCHER", routine="cancel_mission")

        async with self.execution_lock:
            try:
                # Get mission from database
                mission = await self.db_service.get_mission(mission_id)
                if not mission:
                    logger.warning("🔍 Mission not found for cancellation: {mission_id}", module="DISPATCHER", routine="cancel_mission")
                    return False

                # Check if mission can be cancelled
                if mission.status in [MissionStatus.COMPLETED, MissionStatus.FAILED, MissionStatus.CANCELLED]:
                    logger.warning("🚫 Mission {mission_id} is already in final state: {mission.status}", module="DISPATCHER", routine="cancel_mission")
                    return False

                # Mark mission as cancelled
                mission.mark_cancelled()
                await self.db_service.update_mission(mission)

                # Cancel any running tasks
                if mission.current_task_id:
                    current_task = await self.db_service.get_task(mission.current_task_id)
                    if current_task and current_task.status == TaskStatus.IN_PROGRESS:
                        current_task.mark_failed("Mission cancelled by user")
                        await self.db_service.update_task(current_task)

                logger.info("✅ Mission cancelled successfully: {mission_id}", module="DISPATCHER", routine="cancel_mission")
                return True

            except Exception as e:
                logger.error("❌ Failed to cancel mission: {e}", module="DISPATCHER", routine="cancel_mission")
                return False

    async def execute_task(self, task: "Task") -> None:
        """
        Execute a single task by routing it to the appropriate agent.
        This is called by the task polling system.
        """
        logger.info(f"🎯 Executing task: {task.task_id} (type: {task.task_type})", module="DISPATCHER", routine="execute_task()")
        logger.debug(f"📊 Task details: id={task.task_id}, type={task.task_type}, status={task.status}, agent={task.assigned_agent}", module="DISPATCHER", routine="execute_task()")
        logger.debug(f"📋 Task description: {task.description}", module="DISPATCHER", routine="execute_task()")

        try:
            # Get mission context
            logger.debug("🔍 Getting mission context for mission: {task.mission_id}", module="DISPATCHER", routine="execute_task()")
            mission = await self.db_service.get_mission(task.mission_id)
            if not mission:
                logger.error("❌ Mission {task.mission_id} not found for task {task.task_id}", module="DISPATCHER", routine="execute_task()")
                raise ValueError(f"Mission {task.mission_id} not found for task {task.task_id}")

            logger.debug("✅ Mission found: id={mission.mission_id}, status={mission.status}, user_input='{mission.user_input}'", module="DISPATCHER", routine="execute_task()")

            # Create mission context
            logger.debug("📋 Creating mission context...", module="DISPATCHER", routine="execute_task()")
            mission_context = MissionContext(
                mission_id=mission.mission_id,
                user_input=mission.user_input,
                title=mission.title,
                description=mission.description,
                priority=str(mission.priority),
                status=str(mission.status),
                created_at=mission.created_at.isoformat(),
                started_at=mission.started_at.isoformat() if mission.started_at else None,
                progress={}
            )
            logger.debug("✅ Mission context created: mission_id={mission_context.mission_id}", module="DISPATCHER", routine="execute_task()")

            # Route task to appropriate agent based on task type
            logger.debug("🎯 Determining agent type for task type: {task.task_type}", module="DISPATCHER", routine="execute_task()")
            agent_type = self._get_agent_type_for_task(task)
            logger.debug("🤖 Agent type determined: {agent_type}", module="DISPATCHER", routine="execute_task()")

            execute_url = self.agent_registry.get_execute_url(agent_type)
            logger.debug("🔗 Agent execute URL: {execute_url}", module="DISPATCHER", routine="execute_task()")

            if not execute_url:
                logger.error("❌ No agent available for type: {agent_type}", module="DISPATCHER", routine="execute_task()")
                raise ValueError(f"No agent available for type: {agent_type}")

            # Prepare task data from task fields
            logger.debug("📦 Preparing task data...", module="DISPATCHER", routine="execute_task()")
            task_data = {
                "description": task.description,
                "prompt": task.prompt,
                "context": task.context
            }
            logger.debug("📊 Task data prepared: description_length={len(task.description) if task.description else 0}, prompt_length={len(task.prompt) if task.prompt else 0}, context_keys={list(task.context.keys()) if task.context else []}", module="DISPATCHER", routine="execute_task()")

            # Create task request
            task_request = TaskRequest(
                task_id=task.task_id,
                mission_id=task.mission_id,
                task_type=str(task.task_type),
                task_data=task_data,
                mission_context=mission_context,
                callback_url=self.callback_url,
                context={}
            )

            # Send task to agent using the correct execute endpoint
            logger.info(f"📤 Sending task {task.task_id} to {agent_type} at {execute_url}", module="DISPATCHER", routine="execute_task")

            response = await self.http_client.post(
                execute_url,
                json=task_request.model_dump(),
                timeout=30.0
            )
            response.raise_for_status()

            logger.info(f"✅ Task {task.task_id} sent to agent successfully", module="DISPATCHER", routine="execute_task")

        except Exception as e:
            logger.error(f"❌ Failed to execute task {task.task_id}: {e}", module="DISPATCHER", routine="execute_task")
            # Mark task as failed
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            await self.db_service.update_task(task)
            raise

    def _get_agent_type_for_task(self, task: "Task") -> str:
        """Determine which agent should handle this task type"""
        task_type = str(task.task_type)

        # Map task types to agent types
        task_to_agent_map = {
            "planning": "planner",
            "dialogue": "dialogue",
            "phone_call": "phone",
            "phone": "phone",
            "call": "phone"
        }

        return task_to_agent_map.get(task_type.lower(), "planner")  # Default to planner

    async def __aenter__(self):
        """Async context manager entry"""
        return self

    async def __aexit__(self, _exc_type, _exc_val, _exc_tb):
        """Async context manager exit"""
        await self.http_client.aclose()
