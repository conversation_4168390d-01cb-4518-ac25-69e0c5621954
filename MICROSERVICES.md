# Microservices Architecture

This document describes the new microservices architecture for the Deeplica v0 prototype.

## 🏗️ Architecture Overview

The system is now split into separate microservices that communicate via HTTP:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Backend API   │    │   Dispatcher    │    │  Agent Services │
│                 │    │                 │    │                 │
│ • REST API      │───▶│ • Orchestration │───▶│ • Dialogue      │
│ • Mission CRUD  │    │ • Task Routing  │    │ • Planner       │
│ • User Interface│    │ • State Mgmt    │    │ • (Future...)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                     Shared Database (MongoDB)                   │
│  • missions collection  • tasks collection  • indexes          │
└─────────────────────────────────────────────────────────────────┘
```

## 📦 Services

### 1. **Backend API** (`backend/`)
- **Purpose**: REST API gateway and mission management
- **Port**: 8888 (external)
- **Responsibilities**:
  - Handle user requests
  - Mission CRUD operations
  - Communicate with dispatcher
  - Database management

### 2. **Dispatcher** (`dispatcher/`)
- **Purpose**: Mission orchestration and agent coordination
- **Port**: 8888 (internal)
- **Responsibilities**:
  - Route tasks to appropriate agents
  - Manage mission execution flow
  - Handle agent callbacks
  - Track mission progress

### 3. **Dialogue Agent** (`agents/dialogue/`)
- **Purpose**: User interaction and conversation handling
- **Port**: 8888 (internal)
- **Responsibilities**:
  - Process dialogue tasks
  - Handle user input/output
  - Generate conversational responses

### 4. **Planner Agent** (`agents/planner/`)
- **Purpose**: Mission planning and task generation
- **Port**: 8888 (internal)
- **Responsibilities**:
  - Create mission plans
  - Generate task graphs
  - Store tasks in database

## 🔄 Communication Flow

### Mission Creation
1. **User** → `POST /missions` → **Backend API**
2. **Backend API** → **Planner Agent** (direct call for now)
3. **Planner Agent** → Creates tasks → **Database**
4. **Backend API** → `POST /missions/{id}/start` → **Dispatcher**
5. **Dispatcher** → Routes first task → **Agent Service**
6. **Agent Service** → Executes task → Callbacks **Dispatcher**
7. **Dispatcher** → Continues with next task...

### User Response
1. **User** → `POST /missions/{id}/continue` → **Backend API**
2. **Backend API** → `POST /missions/{id}/continue` → **Dispatcher**
3. **Dispatcher** → Sends user response → **Agent Service**
4. **Agent Service** → Processes response → Callbacks **Dispatcher**
5. **Dispatcher** → Continues mission execution...

## 🚀 Local Development

### Prerequisites
- Docker and Docker Compose
- `.env` file with required variables

### Quick Start
```bash
# 1. Create .env file
cp .env.example .env
# Edit .env with your GEMINI_API_KEY

# 2. Deploy locally
./deploy/local.sh

# 3. Test the deployment
./test-microservices.sh
```

### Environment Variables
```bash
# Required
GEMINI_API_KEY=your_gemini_api_key

# Optional (defaults provided)
MONGODB_CONNECTION_STRING=mongodb://mongo:27017
MONGODB_DATABASE=prototype
ENVIRONMENT=local
DEBUG=true
```

### Service URLs (Local)
- **API Gateway**: http://localhost:8888
- **Dispatcher**: http://dispatcher:8888 (internal)
- **Dialogue Agent**: http://dialogue-agent:8888 (internal)
- **Planner Agent**: http://planner-agent:8888 (internal)
- **MongoDB**: mongodb://localhost:27017

## 🔧 Development Commands

```bash
# Deploy locally
./deploy/local.sh

# Deploy to GCP
./deploy/gcp.sh --project YOUR_PROJECT_ID

# Clean up deployments
./deploy/cleanup.sh --local
./deploy/cleanup.sh --gcp --project YOUR_PROJECT_ID

# View logs (local)
docker-compose logs -f dispatcher
docker-compose logs -f dialogue-agent
docker-compose logs -f planner-agent

# Access database (local)
docker exec -it prototype-mongo mongosh prototype
```

See `deploy/README.md` for comprehensive deployment documentation.

## 🌐 GCP Deployment

For production deployment on Google Cloud Platform:

### Cloud Run Services
Each service will be deployed as a separate Cloud Run service:
- `dispatcher-service`
- `dialogue-agent-service`
- `planner-agent-service`
- `backend-api-service`

### Environment Variables (GCP)
```bash
ENVIRONMENT=production
DISPATCHER_URL=https://dispatcher-service-xyz.run.app
DIALOGUE_AGENT_URL=https://dialogue-agent-xyz.run.app
PLANNER_AGENT_URL=https://planner-agent-xyz.run.app
MONGODB_CONNECTION_STRING=mongodb+srv://...
```

## 🔍 Monitoring & Debugging

### Health Checks
```bash
# Check all services
curl http://localhost:8888/health
curl http://localhost:8888/system/status

# Check individual services (internal)
docker exec prototype-dispatcher curl http://localhost:8888/health
docker exec prototype-dialogue-agent curl http://localhost:8888/health
docker exec prototype-planner-agent curl http://localhost:8888/health
```

### Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f dispatcher

# Follow new logs only
docker-compose logs -f --tail=0 dialogue-agent
```

### Database Access
```bash
# Connect to MongoDB
docker exec -it prototype-mongo mongosh prototype

# View collections
db.missions.find().limit(5)
db.tasks.find().limit(5)

# Check indexes
db.missions.getIndexes()
db.tasks.getIndexes()
```

## 🧪 Testing

### API Testing
```bash
# Create mission
curl -X POST http://localhost:8888/missions \
  -H "Content-Type: application/json" \
  -d '{"user_input": "Help me plan a vacation"}'

# Continue mission
curl -X POST http://localhost:8888/missions/{mission_id}/continue \
  -H "Content-Type: application/json" \
  -d '{"user_response": "I want to go to Japan"}'

# Get status
curl http://localhost:8888/missions/{mission_id}
```

### Service Communication Testing
```bash
# Test dispatcher directly
curl -X POST http://localhost:8888/missions/{mission_id}/start  # Backend API port

# Test agent callback (internal)
curl -X POST http://dispatcher:8888/task-completed \  # Backend API port
  -H "Content-Type: application/json" \
  -d '{"task_id": "test", "mission_id": "test", "status": "completed", "result": {}}'
```

## 🔮 Future Enhancements

### Additional Agents
- **Guardian Agent**: Safety and content filtering
- **Email Agent**: Email communication
- **Phone Agent**: Voice/phone interactions
- **Research Agent**: Web research and data gathering

### Infrastructure
- **Service Discovery**: Consul or similar
- **Load Balancing**: Multiple agent instances
- **Message Queue**: Redis/RabbitMQ for async communication
- **Monitoring**: Prometheus + Grafana
- **Tracing**: Jaeger for distributed tracing

### Features
- **Agent Health Monitoring**: Automatic failover
- **Task Retry Logic**: Exponential backoff
- **Rate Limiting**: Per-agent request limits
- **Authentication**: Service-to-service auth
- **Caching**: Redis for frequently accessed data
