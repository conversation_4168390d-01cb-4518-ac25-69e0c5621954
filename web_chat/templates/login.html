<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DEEPLICA - Web Chat v3.1</title>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON>ni', sans-serif;
            background: #0a0a0a;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: auto; /* Allow scrolling if needed */
            position: relative;
            padding: 10px; /* Add padding for small screens */
        }



        /* Animated background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 107, 53, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(0, 255, 127, 0.05) 0%, transparent 50%);
            animation: backgroundPulse 8s ease-in-out infinite alternate;
            z-index: -2;
        }

        /* Grid overlay */
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(rgba(0, 212, 255, 0.03) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 212, 255, 0.03) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: gridMove 20s linear infinite;
            z-index: -1;
        }

        @keyframes backgroundPulse {
            0% { opacity: 0.3; }
            100% { opacity: 0.6; }
        }

        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        .login-container {
            background: rgba(10, 10, 10, 0.95);
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 20px;
            box-shadow:
                0 0 50px rgba(0, 212, 255, 0.2),
                inset 0 0 50px rgba(0, 212, 255, 0.05);
            padding: 28px; /* Reduced from 40px */
            width: 90%;
            max-width: 500px; /* Increased from 350px for wider content */
            min-width: 320px; /* Ensure minimum width */
            text-align: center;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
            font-size: 0.7em; /* 70% of original font size */
            margin: auto;
            min-height: fit-content;
            transform: scale(0.95); /* Less aggressive scaling */
        }

        /* Website link at very bottom */
        .website-link {
            position: fixed;
            bottom: 5px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            background: rgba(0, 0, 0, 0.3);
            padding: 6px 12px; /* Reduced padding */
            border-radius: 15px;
            border: 1px solid rgba(0, 212, 255, 0.3);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .website-link:hover {
            background: rgba(0, 0, 0, 0.5);
            border-color: rgba(0, 212, 255, 0.6);
            box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
            transform: translateX(-50%) translateY(-2px);
        }

        .website-link a {
            color: #00d4ff;
            text-decoration: none;
            font-size: 8px; /* Reduced from 12px (70% of original) */
            font-weight: 500;
            text-shadow: 0 0 5px rgba(0, 212, 255, 0.5);
        }

        .website-link a:hover {
            color: #ffffff;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg,
                transparent 0%,
                #00d4ff 20%,
                #ff6b35 50%,
                #00ff7f 80%,
                transparent 100%);
            animation: borderGlow 3s ease-in-out infinite alternate;
        }

        @keyframes borderGlow {
            0% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .video-container {
            margin-bottom: 30px;
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            border: 1px solid rgba(0, 212, 255, 0.3);
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.2);
        }

        .deeplica-video {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 15px;
        }

        /* Robot Animation Fallback */
        .robot-animation-fallback {
            background: linear-gradient(135deg, #0a0a0a, #1a1a2e, #16213e);
            height: 200px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-radius: 15px;
            position: relative;
            overflow: hidden;
        }

        .robot-animation-fallback::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 30% 70%, rgba(0, 212, 255, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(255, 107, 53, 0.2) 0%, transparent 50%);
            animation: backgroundShift 4s ease-in-out infinite alternate;
        }

        @keyframes backgroundShift {
            0% { opacity: 0.3; }
            100% { opacity: 0.7; }
        }

        .robot-container {
            position: relative;
            z-index: 2;
            animation: robotFloat 3s ease-in-out infinite;
        }

        @keyframes robotFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .robot-head {
            width: 60px;
            height: 60px;
            background: linear-gradient(145deg, #34495e, #2c3e50);
            border-radius: 15px;
            position: relative;
            margin: 0 auto 10px;
            box-shadow:
                0 0 20px rgba(0, 212, 255, 0.3),
                inset 0 0 20px rgba(0, 212, 255, 0.1);
        }

        .robot-eyes {
            display: flex;
            justify-content: space-between;
            padding: 15px 12px 0;
        }

        .eye {
            width: 8px;
            height: 8px;
            background: #00d4ff;
            border-radius: 50%;
            box-shadow: 0 0 10px #00d4ff;
            animation: eyeBlink 2s ease-in-out infinite;
        }

        @keyframes eyeBlink {
            0%, 90%, 100% { opacity: 1; }
            95% { opacity: 0.3; }
        }

        .robot-mouth {
            width: 20px;
            height: 3px;
            background: #ff6b35;
            border-radius: 2px;
            margin: 8px auto;
            box-shadow: 0 0 8px #ff6b35;
            animation: mouthGlow 1.5s ease-in-out infinite alternate;
        }

        @keyframes mouthGlow {
            0% { box-shadow: 0 0 8px #ff6b35; }
            100% { box-shadow: 0 0 15px #ff6b35; }
        }

        .robot-body {
            width: 40px;
            height: 30px;
            background: linear-gradient(145deg, #2c3e50, #34495e);
            border-radius: 8px;
            margin: 0 auto;
            position: relative;
            box-shadow:
                0 0 15px rgba(0, 212, 255, 0.2),
                inset 0 0 15px rgba(0, 212, 255, 0.05);
        }

        .robot-chest {
            width: 6px;
            height: 6px;
            background: #00ff7f;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 0 8px #00ff7f;
            animation: heartbeat 1s ease-in-out infinite;
        }

        @keyframes heartbeat {
            0%, 100% { transform: translate(-50%, -50%) scale(1); }
            50% { transform: translate(-50%, -50%) scale(1.2); }
        }

        .flower {
            position: absolute;
            right: -10px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5rem;
            animation: flowerSway 2s ease-in-out infinite;
        }

        @keyframes flowerSway {
            0%, 100% { transform: translateY(-50%) rotate(-5deg); }
            50% { transform: translateY(-50%) rotate(5deg); }
        }

        .robot-text {
            color: #00d4ff;
            font-family: 'Orbitron', monospace;
            font-size: 0.9rem;
            font-weight: 600;
            margin-top: 15px;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
            letter-spacing: 1px;
            position: relative;
            z-index: 2;
            animation: textGlow 2s ease-in-out infinite alternate;
        }

        @keyframes textGlow {
            0% { text-shadow: 0 0 10px rgba(0, 212, 255, 0.5); }
            100% { text-shadow: 0 0 20px rgba(0, 212, 255, 0.8); }
        }

        .logo {
            font-family: 'Orbitron', monospace;
            font-size: 3rem;
            font-weight: 900;
            background: linear-gradient(45deg, #00d4ff, #ff6b35, #00ff7f);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 4s ease-in-out infinite;
            margin-bottom: 10px;
            text-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .title {
            font-family: 'Orbitron', monospace;
            font-size: 2.2rem;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 8px;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.6);
            letter-spacing: 2px;
        }

        .subtitle {
            color: #a0a0a0;
            margin-bottom: 30px;
            font-size: 1.1rem;
            font-weight: 400;
            letter-spacing: 1px;
        }

        .version-info {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 25px;
            font-family: 'Orbitron', monospace;
        }

        .version-title {
            color: #00d4ff;
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 8px;
            letter-spacing: 1px;
        }

        .version-details {
            color: #ffffff;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .contact-info {
            background: rgba(255, 107, 53, 0.1);
            border: 1px solid rgba(255, 107, 53, 0.3);
            border-radius: 10px;
            padding: 12px;
            margin-bottom: 25px;
            font-size: 0.85rem;
            color: #ff6b35;
            font-weight: 500;
        }

        .contact-info .phone {
            color: #ffffff;
            font-family: 'Orbitron', monospace;
            font-weight: 600;
        }

        .login-prompt {
            background: rgba(0, 255, 127, 0.1);
            border: 1px solid rgba(0, 255, 127, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 25px;
            text-align: center;
        }

        .login-prompt-text {
            color: #00ff7f;
            font-family: 'Orbitron', monospace;
            font-size: 1.1rem;
            font-weight: 600;
            letter-spacing: 1px;
            text-transform: uppercase;
        }

        .form-group {
            margin-bottom: 25px;
            text-align: left;
            position: relative;
        }

        .form-label {
            display: block;
            margin-bottom: 12px;
            font-weight: 600;
            color: #00d4ff;
            font-size: 2rem; /* Doubled from 1rem */
            font-family: 'Orbitron', monospace;
            letter-spacing: 2px;
            text-transform: uppercase;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }

        .form-input {
            width: 100%;
            padding: 12px 16px; /* Slightly reduced for better fit */
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 12px;
            font-size: 1.4rem; /* Reduced from 2.2rem for better fit */
            font-family: 'Rajdhani', sans-serif;
            font-weight: 500;
            transition: all 0.3s ease;
            background: rgba(0, 0, 0, 0.5);
            color: #ffffff;
            backdrop-filter: blur(5px);
            box-sizing: border-box;
            margin-bottom: 16px; /* Reduced margin */
            min-width: 0; /* Allow shrinking */
            overflow: hidden; /* Prevent text overflow */
            text-overflow: ellipsis; /* Add ellipsis for long text */
        }

        .form-input::placeholder {
            color: #666666;
            font-style: italic;
        }

        .form-input:focus {
            outline: none;
            border-color: #00d4ff;
            background: rgba(0, 0, 0, 0.7);
            box-shadow:
                0 0 20px rgba(0, 212, 255, 0.3),
                inset 0 0 20px rgba(0, 212, 255, 0.1);
            transform: translateY(-2px);
        }

        /* Password input container */
        .password-input-container {
            position: relative;
            display: flex;
            align-items: center;
        }

        .password-input {
            padding-right: 55px; /* Make room for the eye icon */
        }

        /* Password toggle button */
        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .password-toggle:hover {
            background: rgba(0, 212, 255, 0.1);
            transform: translateY(-50%) scale(1.1);
        }

        .password-toggle:active {
            transform: translateY(-50%) scale(0.95);
        }

        .eye-icon {
            font-size: 1.2rem;
            color: #00d4ff;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .password-toggle:hover .eye-icon {
            color: #ffffff;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.8);
        }

        /* Caps lock indicator */
        .caps-lock-indicator {
            display: inline-block;
            margin-left: 10px;
            padding: 4px 8px;
            background: linear-gradient(45deg, #ff6b35, #ff4757);
            color: #ffffff;
            font-size: 0.7rem;
            font-weight: bold;
            border-radius: 12px;
            animation: capsLockPulse 1.5s ease-in-out infinite;
            box-shadow: 0 0 15px rgba(255, 107, 53, 0.5);
            text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
        }

        @keyframes capsLockPulse {
            0%, 100% {
                opacity: 0.8;
                transform: scale(1);
                box-shadow: 0 0 15px rgba(255, 107, 53, 0.5);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
                box-shadow: 0 0 25px rgba(255, 107, 53, 0.8);
            }
        }

        .login-button {
            width: 100%;
            padding: 18px;
            background: linear-gradient(45deg, #00d4ff, #ff6b35);
            background-size: 200% 200%;
            color: #000000;
            border: none;
            border-radius: 12px;
            font-size: 1.2rem;
            font-weight: 700;
            font-family: 'Orbitron', monospace;
            letter-spacing: 2px;
            text-transform: uppercase;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.4);
        }

        .login-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .login-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 0 40px rgba(0, 212, 255, 0.6);
            animation: buttonPulse 1s ease-in-out infinite alternate;
        }

        .login-button:hover::before {
            left: 100%;
        }

        .login-button:active {
            transform: translateY(-1px);
        }

        .login-button:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        @keyframes buttonPulse {
            0% { background-position: 0% 50%; }
            100% { background-position: 100% 50%; }
        }

        .error-message {
            background: rgba(255, 107, 53, 0.1);
            color: #ff6b35;
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            font-size: 1rem;
            font-weight: 500;
            border: 2px solid rgba(255, 107, 53, 0.3);
            box-shadow: 0 0 20px rgba(255, 107, 53, 0.2);
            font-family: 'Rajdhani', sans-serif;
            letter-spacing: 0.5px;
        }

        .footer {
            margin-top: 25px;
            color: #666666;
            font-size: 0.85rem;
            font-family: 'Orbitron', monospace;
            letter-spacing: 1px;
        }

        .footer-link {
            color: #00d4ff;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-link:hover {
            color: #ff6b35;
            text-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
        }

        /* Enhanced responsive design for better scaling */
        @media (max-height: 700px) {
            .login-container {
                transform: scale(0.85);
                margin: 10px auto;
            }

            .deeplica-brand {
                transform: scale(0.6);
            }
        }

        @media (max-height: 500px) {
            .login-container {
                transform: scale(0.75);
                margin: 5px auto;
            }

            .deeplica-brand {
                transform: scale(0.5);
            }
        }

        /* Responsive design */
        @media (max-width: 600px) {
            .login-container {
                margin: 15px auto;
                padding: 21px 18px; /* 70% of 30px 25px */
                max-width: 95%;
                min-width: 300px; /* Ensure minimum width */
                transform: scale(0.95); /* Less aggressive scaling */
            }

            .logo {
                font-size: 1.75rem; /* 70% of 2.5rem */
            }

            .deeplica-brand {
                transform: scale(0.55);
            }

            .title {
                font-size: 1.26rem; /* 70% of 1.8rem */
            }

            .deeplica-video {
                height: 150px;
            }
        }

        @media (max-width: 400px) {
            .login-container {
                padding: 18px 14px; /* 70% of 25px 20px */
                transform: scale(0.9); /* Less aggressive scaling */
                min-width: 280px; /* Ensure minimum width */
            }

            .logo {
                font-size: 1.4rem; /* 70% of 2rem */
            }

            .title {
                font-size: 1.05rem; /* 70% of 1.5rem */
            }

            .deeplica-brand {
                transform: scale(0.5);
            }

            .form-input {
                padding: 8px 11px; /* 70% of 12px 15px */
                font-size: 0.7rem; /* 70% of 1rem */
            }

            .password-input {
                padding-right: 45px; /* Smaller padding for mobile */
            }

            .password-toggle {
                right: 10px; /* Closer to edge on mobile */
            }

            .eye-icon {
                font-size: 1rem; /* Slightly smaller on mobile */
            }

            .caps-lock-indicator {
                font-size: 0.6rem;
                padding: 3px 6px;
                margin-left: 5px;
            }

            .login-button {
                padding: 11px; /* 70% of 15px */
                font-size: 0.7rem; /* 70% of 1rem */
            }
        }
    </style>
</head>
<body>
    <div class="login-container">

        <!-- Deeplica Video -->
        <div class="video-container">
            <video class="deeplica-video" autoplay muted loop>
                <source src="/media/Deeplica Avatar.mp4" type="video/mp4">
                <!-- High-tech robot animation fallback -->
                <div class="robot-animation-fallback">
                    <div class="robot-container">
                        <div class="robot-head">
                            <div class="robot-eyes">
                                <div class="eye left-eye"></div>
                                <div class="eye right-eye"></div>
                            </div>
                            <div class="robot-mouth"></div>
                        </div>
                        <div class="robot-body">
                            <div class="robot-chest"></div>
                        </div>
                        <div class="flower">🌸</div>
                    </div>
                    <div class="robot-text">DEEPLICA AI ASSISTANT</div>
                </div>
            </video>
        </div>

        <div class="logo">DEEPLICA AI</div>
        <h1 class="title">WEB CHAT v3.1</h1>
        <p class="subtitle">Advanced AI Personal Assistant Service</p>

        <!-- Login Prompt -->
        <div class="login-prompt">
            <div class="login-prompt-text">Please log in:</div>
        </div>

        {% if error %}
        <div class="error-message">
            ⚠️ {{ error }}
        </div>
        {% endif %}

        <form method="post" action="/login">
            <div class="form-group">
                <label for="username" class="form-label">Username</label>
                <input
                    type="text"
                    id="username"
                    name="username"
                    class="form-input"
                    required
                    autocomplete="username"
                    placeholder="Enter your access credentials"
                >
            </div>

            <div class="form-group">
                <label for="password" class="form-label">
                    Password
                    <span id="capsLockIndicator" class="caps-lock-indicator" style="display: none;">
                        🔒 CAPS LOCK ON
                    </span>
                </label>
                <div class="password-input-container">
                    <input
                        type="password"
                        id="password"
                        name="password"
                        class="form-input password-input"
                        required
                        autocomplete="current-password"
                        placeholder="Enter your secure password"
                    >
                    <button type="button" class="password-toggle" id="passwordToggle" aria-label="Toggle password visibility">
                        <span class="eye-icon" id="eyeIcon">👁️</span>
                    </button>
                </div>
            </div>

            <button type="submit" class="login-button">
                <span class="button-text">Access System</span>
            </button>
        </form>

        <div class="footer">
            © 2025 DEEPLICA - <a href="tel:+972547000430" class="footer-link">Emergency Contact</a>
        </div>
    </div>

    <!-- Website Link -->
    <div class="website-link">
        <a href="https://deeplica.ai/" target="_blank">Visit Deeplica.AI</a>
    </div>

    <script>
        // Prevent multiple DeepChat tabs
        function preventMultipleTabs() {
            // Set a unique identifier for this DeepChat session
            const deepchatId = 'deeplica_deepchat_main';

            // Check if another DeepChat tab is already open
            if (localStorage.getItem(deepchatId)) {
                // Try to focus existing tab
                try {
                    // Send message to other tabs to focus
                    localStorage.setItem(deepchatId + '_focus', Date.now());

                    // Close this tab after a short delay if another exists
                    setTimeout(() => {
                        if (localStorage.getItem(deepchatId) !== sessionStorage.getItem('deepchat_session_id')) {
                            window.close();
                        }
                    }, 1000);
                } catch (e) {
                    // If we can't close, at least warn
                    console.log('DeepChat: Another tab may already be open');
                }
            }

            // Register this tab
            const sessionId = Date.now() + '_' + Math.random();
            sessionStorage.setItem('deepchat_session_id', sessionId);
            localStorage.setItem(deepchatId, sessionId);

            // Listen for focus requests from other tabs
            window.addEventListener('storage', (e) => {
                if (e.key === deepchatId + '_focus') {
                    window.focus();
                }
            });

            // Clean up when tab closes
            window.addEventListener('beforeunload', () => {
                if (localStorage.getItem(deepchatId) === sessionStorage.getItem('deepchat_session_id')) {
                    localStorage.removeItem(deepchatId);
                }
            });
        }

        // Auto-focus username field after page load and load version info
        document.addEventListener('DOMContentLoaded', function() {
            // Prevent multiple tabs first
            preventMultipleTabs();

            setTimeout(() => {
                document.getElementById('username').focus();
            }, 500);

            // Load dynamic version information
            loadVersionInfo();
        });

        // Load version information from admin settings (simplified)
        function loadVersionInfo() {
            try {
                // Load from localStorage (same as admin panel)
                const savedConfig = localStorage.getItem('deeplica_version_config');
                if (savedConfig) {
                    const versionData = JSON.parse(savedConfig);

                    // Update page title
                    const systemName = versionData.systemName || 'DEEPLICA';
                    const systemVersion = versionData.systemVersion || 'v3.1';
                    document.title = `${systemName} - Web Chat ${systemVersion}`;

                    // Update main title
                    const titleElement = document.querySelector('.title');
                    if (titleElement) {
                        titleElement.textContent = `WEB CHAT ${systemVersion}`;
                    }
                }
            } catch (error) {
                console.error('Error loading version information:', error);
                // Keep default values if loading fails
            }
        }

        // Enhanced form submission with loading animation
        document.querySelector('form').addEventListener('submit', function(e) {
            const button = document.querySelector('.login-button');
            const buttonText = button.querySelector('.button-text');

            buttonText.textContent = 'ACCESSING...';
            button.disabled = true;
            button.style.background = 'linear-gradient(45deg, #666, #999)';

            // Add loading animation
            let dots = 0;
            const loadingInterval = setInterval(() => {
                dots = (dots + 1) % 4;
                buttonText.textContent = 'ACCESSING' + '.'.repeat(dots);
            }, 500);

            // Store interval to clear it if needed
            button.loadingInterval = loadingInterval;
        });

        // Password visibility toggle functionality
        const passwordInput = document.getElementById('password');
        const passwordToggle = document.getElementById('passwordToggle');
        const eyeIcon = document.getElementById('eyeIcon');

        if (passwordToggle && passwordInput && eyeIcon) {
            passwordToggle.addEventListener('click', function() {
                const isPassword = passwordInput.type === 'password';

                // Toggle input type
                passwordInput.type = isPassword ? 'text' : 'password';

                // Toggle eye icon
                eyeIcon.textContent = isPassword ? '🙈' : '👁️';

                // Add visual feedback
                passwordToggle.style.transform = 'translateY(-50%) scale(0.9)';
                setTimeout(() => {
                    passwordToggle.style.transform = 'translateY(-50%) scale(1)';
                }, 150);

                // Keep focus on password input
                passwordInput.focus();
            });
        }

        // Caps Lock detection
        const capsLockIndicator = document.getElementById('capsLockIndicator');

        function checkCapsLock(event) {
            if (event.getModifierState && event.getModifierState('CapsLock')) {
                capsLockIndicator.style.display = 'inline-block';
            } else {
                capsLockIndicator.style.display = 'none';
            }
        }

        // Check caps lock on password field events
        if (passwordInput && capsLockIndicator) {
            passwordInput.addEventListener('keydown', checkCapsLock);
            passwordInput.addEventListener('keyup', checkCapsLock);
            passwordInput.addEventListener('focus', checkCapsLock);
        }

        // Add input field animations
        document.querySelectorAll('.form-input').forEach(input => {
            input.addEventListener('focus', function() {
                // Handle password container differently
                const container = this.closest('.password-input-container') || this.parentElement;
                container.style.transform = 'scale(1.02)';
                container.style.transition = 'transform 0.3s ease';
            });

            input.addEventListener('blur', function() {
                const container = this.closest('.password-input-container') || this.parentElement;
                container.style.transform = 'scale(1)';
            });
        });

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Alt + L to focus login
            if (e.altKey && e.key === 'l') {
                e.preventDefault();
                document.getElementById('username').focus();
            }

            // Escape to clear form
            if (e.key === 'Escape') {
                document.getElementById('username').value = '';
                document.getElementById('password').value = '';
                document.getElementById('username').focus();
            }
        });

        // Add subtle parallax effect to video
        document.addEventListener('mousemove', function(e) {
            const video = document.querySelector('.deeplica-video');
            if (video) {
                const x = (e.clientX / window.innerWidth) * 10 - 5;
                const y = (e.clientY / window.innerHeight) * 10 - 5;
                video.style.transform = `translate(${x}px, ${y}px) scale(1.05)`;
            }
        });

        // Add typing sound effect simulation (visual feedback)
        document.querySelectorAll('.form-input').forEach(input => {
            input.addEventListener('input', function() {
                this.style.boxShadow = '0 0 30px rgba(0, 212, 255, 0.5), inset 0 0 30px rgba(0, 212, 255, 0.2)';
                setTimeout(() => {
                    this.style.boxShadow = '0 0 20px rgba(0, 212, 255, 0.3), inset 0 0 20px rgba(0, 212, 255, 0.1)';
                }, 150);
            });
        });

        // Console easter egg
        console.log(`
        ██████╗ ███████╗███████╗██████╗ ██╗     ██╗ ██████╗ █████╗
        ██╔══██╗██╔════╝██╔════╝██╔══██╗██║     ██║██╔════╝██╔══██╗
        ██║  ██║█████╗  █████╗  ██████╔╝██║     ██║██║     ███████║
        ██║  ██║██╔══╝  ██╔══╝  ██╔═══╝ ██║     ██║██║     ██╔══██║
        ██████╔╝███████╗███████╗██║     ███████╗██║╚██████╗██║  ██║
        ╚═════╝ ╚══════╝╚══════╝╚═╝     ╚══════╝╚═╝ ╚═════╝╚═╝  ╚═╝

        🤖 DEEPLICA Web Chat v3.1 - AI Mission Orchestration Platform
        📞 Support: 054-7000430
        🔧 System Status: OPERATIONAL
        `);
    </script>
</body>
</html>
