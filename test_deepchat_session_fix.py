#!/usr/bin/env python3
"""
🧪 DEEPLICA DEEPCHAT SESSION MANAGEMENT FIX TEST
Tests that the session management issues have been resolved:
1. Users can authenticate and access chat
2. Multiple sessions per user are allowed
3. No automatic window opening
4. Admin access works properly
"""

import requests
import json
import time
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

from shared.port_manager import get_service_port

def test_login_functionality():
    """Test basic login functionality"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔐 Testing login functionality on port {web_chat_port}...")
        
        # Test login page loads
        response = requests.get(f"http://localhost:{web_chat_port}/login", timeout=5)
        if response.status_code == 200:
            print(f"✅ Login page loads successfully")
            
            # Check if login form is present
            if 'username' in response.text and 'password' in response.text:
                print(f"✅ Login form elements found")
            else:
                print(f"❌ Login form elements missing")
                return False
        else:
            print(f"❌ Login page failed to load: {response.status_code}")
            return False
        
        # Test login with admin credentials
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        session = requests.Session()
        response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=False,
            timeout=5
        )
        
        if response.status_code in [302, 303, 307]:
            print(f"✅ Login successful - redirect received (status: {response.status_code})")

            # Check if redirected to chat
            location = response.headers.get('location', '')
            if '/chat' in location:
                print(f"✅ Redirected to chat page as expected")
            else:
                print(f"⚠️ Redirected to: {location}")

            return True
        else:
            print(f"❌ Login failed: {response.status_code}")
            print(f"📋 Response: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing login: {e}")
        return False

def test_chat_access():
    """Test chat page access after login"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"💬 Testing chat access on port {web_chat_port}...")
        
        # Login first
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        session = requests.Session()
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            timeout=5
        )
        
        if login_response.status_code not in [200, 302, 303, 307]:
            print(f"❌ Login failed for chat test")
            return False
        
        # Access chat page
        chat_response = session.get(f"http://localhost:{web_chat_port}/chat", timeout=5)
        
        if chat_response.status_code == 200:
            print(f"✅ Chat page accessible after login")
            
            # Check for chat elements
            content = chat_response.text
            if 'sendButton' in content and 'messageInput' in content:
                print(f"✅ Chat interface elements found")
            else:
                print(f"❌ Chat interface elements missing")
                return False
                
            # Check for WebSocket connection code
            if 'connectWebSocket' in content:
                print(f"✅ WebSocket connection code found")
            else:
                print(f"❌ WebSocket connection code missing")
                return False
                
            return True
        else:
            print(f"❌ Chat page access failed: {chat_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing chat access: {e}")
        return False

def test_admin_access():
    """Test admin page access for admin users"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔧 Testing admin access on port {web_chat_port}...")
        
        # Login as admin
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        session = requests.Session()
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            timeout=5
        )
        
        if login_response.status_code not in [200, 302, 303, 307]:
            print(f"❌ Admin login failed")
            return False
        
        # Access admin page
        admin_response = session.get(f"http://localhost:{web_chat_port}/admin", timeout=5)
        
        if admin_response.status_code == 200:
            print(f"✅ Admin page accessible for admin user")
            
            # Check for admin elements
            content = admin_response.text
            if 'admin' in content.lower() and ('user' in content.lower() or 'service' in content.lower()):
                print(f"✅ Admin interface elements found")
            else:
                print(f"❌ Admin interface elements missing")
                return False
                
            return True
        else:
            print(f"❌ Admin page access failed: {admin_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing admin access: {e}")
        return False

def test_multiple_sessions():
    """Test that multiple sessions are now allowed"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔄 Testing multiple sessions on port {web_chat_port}...")
        
        # Create two separate sessions
        session1 = requests.Session()
        session2 = requests.Session()
        
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        # Login with first session
        response1 = session1.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            timeout=5
        )
        
        if response1.status_code not in [200, 302, 303, 307]:
            print(f"❌ First session login failed")
            return False

        # Login with second session
        response2 = session2.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            timeout=5
        )

        if response2.status_code not in [200, 302, 303, 307]:
            print(f"❌ Second session login failed")
            return False
        
        # Test that both sessions can access chat
        chat1 = session1.get(f"http://localhost:{web_chat_port}/chat", timeout=5)
        chat2 = session2.get(f"http://localhost:{web_chat_port}/chat", timeout=5)
        
        if chat1.status_code == 200 and chat2.status_code == 200:
            print(f"✅ Multiple sessions can access chat simultaneously")
            return True
        else:
            print(f"❌ Multiple session access failed: {chat1.status_code}, {chat2.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing multiple sessions: {e}")
        return False

def test_session_persistence():
    """Test that sessions persist across requests"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"💾 Testing session persistence on port {web_chat_port}...")
        
        # Login and get session
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        session = requests.Session()
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            timeout=5
        )
        
        if login_response.status_code not in [200, 302, 303, 307]:
            print(f"❌ Login failed for persistence test")
            return False
        
        # Wait a moment
        time.sleep(1)
        
        # Test multiple requests with same session
        for i in range(3):
            response = session.get(f"http://localhost:{web_chat_port}/chat", timeout=5)
            if response.status_code != 200:
                print(f"❌ Session persistence failed on request {i+1}: {response.status_code}")
                return False
        
        print(f"✅ Session persists across multiple requests")
        return True
        
    except Exception as e:
        print(f"❌ Error testing session persistence: {e}")
        return False

def main():
    """Run all DeepChat session management tests"""
    print("🧪 DEEPLICA DEEPCHAT SESSION MANAGEMENT FIX TEST")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 5
    
    # Test 1: Login Functionality
    print("\n🧪 TEST 1: Login Functionality")
    if test_login_functionality():
        tests_passed += 1
    
    # Test 2: Chat Access
    print("\n🧪 TEST 2: Chat Access After Login")
    if test_chat_access():
        tests_passed += 1
    
    # Test 3: Admin Access
    print("\n🧪 TEST 3: Admin Access for Admin Users")
    if test_admin_access():
        tests_passed += 1
    
    # Test 4: Multiple Sessions
    print("\n🧪 TEST 4: Multiple Sessions Allowed")
    if test_multiple_sessions():
        tests_passed += 1
    
    # Test 5: Session Persistence
    print("\n🧪 TEST 5: Session Persistence")
    if test_session_persistence():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 60)
    print(f"🧪 TEST RESULTS: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✅ ALL TESTS PASSED - DeepChat session management is fixed!")
        print("🎉 Users can now:")
        print("   - Login successfully without issues")
        print("   - Access chat interface properly")
        print("   - Use admin features (for admin users)")
        print("   - Open multiple tabs/windows")
        print("   - Maintain persistent sessions")
    else:
        print("❌ SOME TESTS FAILED - Session management needs more fixes")
        
        if tests_passed >= 3:
            print("🔧 RECOMMENDATION: Basic functionality works, minor issues remain")
        elif tests_passed >= 1:
            print("🔧 RECOMMENDATION: Login works but session management has issues")
        else:
            print("🔧 RECOMMENDATION: Major session management problems - check service logs")
    
    print(f"\n🌐 Manual test: Open http://localhost:8007 in browser")
    print(f"🔐 Login with: admin / admin123")
    print(f"💡 Try opening multiple tabs to test session management")

if __name__ == '__main__':
    main()
