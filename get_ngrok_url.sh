#!/bin/bash
# =============================================================================
# DEEPLICA SERVICE SCRIPT - ALL PORTS MANAGED DYNAMICALLY
# =============================================================================
# ALL ports are assigned by shared/port_manager.py - NO HARDCODED PORTS
# External services adapt to assigned ports through configuration
# =============================================================================
# =============================================================================
# DEEPLICA SERVICE STARTUP SCRIPT
# =============================================================================
# NOTE: All ports are managed by shared/port_manager.py
# - Backend API: 8888 (CONSTANT - never changes)
# - Other services: configurable via port manager
# =============================================================================

# =============================================================================
# Get Ngrok Public URL Script
# =============================================================================

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 Getting ngrok public URL...${NC}"

# Check if ngrok is running
if ! curl -s http://localhost:${get_service_port("ngrok-api")}/api/tunnels >/dev/null 2>&1; then
    echo -e "${RED}❌ Ngrok is not running or not accessible on port 4040${NC}"
    echo -e "${YELLOW}💡 Start ngrok first with: ./start_ngrok.sh${NC}"
    exit 1
fi

# Get the public URL
PUBLIC_URL=$(curl -s http://localhost:${get_service_port("ngrok-api")}/api/tunnels | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    tunnels = data.get('tunnels', [])
    if tunnels:
        print(tunnels[0]['public_url'])
    else:
        print('No tunnels found')
except:
    print('Error parsing ngrok response')
")

if [[ "$PUBLIC_URL" == *"ngrok"* ]]; then
    echo -e "${GREEN}✅ Ngrok public URL: ${PUBLIC_URL}${NC}"
    echo ""
    echo -e "${BLUE}📋 Update your .env file with:${NC}"
    echo -e "${YELLOW}TWILIO_WEBHOOK_URL=${PUBLIC_URL}${NC}"
    echo ""
    echo -e "${BLUE}🔗 Ngrok dashboard: http://localhost:${get_service_port("ngrok-api")}${NC}"
else
    echo -e "${RED}❌ Could not get ngrok URL: $PUBLIC_URL${NC}"
    echo -e "${YELLOW}💡 Check if ngrok is running: ./start_ngrok.sh${NC}"
fi
