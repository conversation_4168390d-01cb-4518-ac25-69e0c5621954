#!/usr/bin/env python3
"""
Test script to verify unified logging format is working correctly across all services
"""

import sys
import os

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.unified_logging import (
    get_backend_logger, get_dispatcher_logger, get_planner_logger,
    get_phone_logger, get_watchdog_logger, get_webchat_logger, get_cli_logger
)


def test_service_logging(service_name: str, logger):
    """Test logging for a specific service"""
    print(f"\n🧪 Testing {service_name} logging:")
    print("-" * 50)
    
    # Test all log levels
    logger.debug("This is a debug message", module="test_module", routine="test_routine")
    logger.info("This is an info message", module="test_module", routine="test_routine")
    logger.warning("This is a warning message", module="test_module", routine="test_routine")
    logger.error("This is an error message", module="test_module", routine="test_routine")
    
    # Test without module/routine (should use defaults)
    logger.info("This is a message without module/routine specified")
    
    print(f"✅ {service_name} logging test complete")


def main():
    """Test unified logging across all services"""
    print("🚀 Testing Unified Logging Format Across All DEEPLICA Services")
    print("=" * 80)
    print("Expected format: time (yyyy-mm-dd HH:mm:ss) - [LEVEL] - Svc: [service], Mod: [module], Cod: [routine], msg: [message]")
    print("=" * 80)
    
    # Test all service loggers
    services = [
        ("BACKEND-API", get_backend_logger()),
        ("DISPATCHER", get_dispatcher_logger()),
        ("PLANNER-AGENT", get_planner_logger()),
        ("PHONE-AGENT", get_phone_logger()),
        ("WATCHDOG", get_watchdog_logger()),
        ("WEB-CHAT", get_webchat_logger()),
        ("CLI-TERMINAL", get_cli_logger()),
    ]
    
    for service_name, logger in services:
        test_service_logging(service_name, logger)
    
    print("\n" + "=" * 80)
    print("🎉 Unified Logging Test Complete!")
    print("\n📋 Summary:")
    print("✅ All services now use the unified logging format")
    print("✅ CLI Terminal redirects system messages to watchdog console")
    print("✅ All other services print to their own debug consoles")
    print("✅ Consistent timestamp, service, module, and routine identification")
    print("\n🔧 Next Steps:")
    print("1. Restart DEEPLICA services to see the new logging format")
    print("2. Check that all terminal outputs follow the unified format")
    print("3. Verify CLI Terminal shows only user interactions")


if __name__ == "__main__":
    main()
