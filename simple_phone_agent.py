#!/usr/bin/env python3
"""
Simple Phone Agent Service
A working phone agent that can handle phone call tasks from the Dispatcher.
"""

import os
import sys
import json
import asyncio
import uvicorn
from datetime import datetime
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add project root to path
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, PROJECT_ROOT)

app = FastAPI(title="Simple Phone Agent", description="Handles phone call tasks", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class TaskRequest(BaseModel):
    task_id: str
    task_type: str
    description: str
    phone_number: str = ""
    message: str = ""
    mission_id: str = ""
    user_id: str = ""

class TaskResponse(BaseModel):
    task_id: str
    status: str
    message: str
    result: dict = {}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "Simple Phone Agent",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }

@app.post("/execute", response_model=TaskResponse)
async def execute_task(request: TaskRequest):
    """Execute a phone call task"""
    try:
        print(f"📞 Phone Agent: Received task {request.task_id}")
        print(f"   Type: {request.task_type}")
        print(f"   Description: {request.description}")
        print(f"   Phone: {request.phone_number}")
        print(f"   Message: {request.message}")
        
        if request.task_type != "phone_call":
            raise HTTPException(status_code=400, detail=f"Unsupported task type: {request.task_type}")
        
        # Extract phone number and message from description if not provided
        phone_number = request.phone_number
        message = request.message
        
        if not phone_number and "call" in request.description.lower():
            # Try to extract phone number from description
            import re
            phone_match = re.search(r'\+?\d{10,15}', request.description)
            if phone_match:
                phone_number = phone_match.group()
        
        if not message:
            message = "Hello from DEEPLICA! This is a test call."
        
        if not phone_number:
            return TaskResponse(
                task_id=request.task_id,
                status="failed",
                message="No phone number found in task",
                result={"error": "Missing phone number"}
            )
        
        # Make the actual phone call using Twilio
        call_result = await make_twilio_call(phone_number, message)
        
        if call_result["success"]:
            print(f"✅ Phone call successful: {call_result['call_sid']}")
            return TaskResponse(
                task_id=request.task_id,
                status="completed",
                message=f"Phone call completed successfully to {phone_number}",
                result={
                    "phone_number": phone_number,
                    "call_sid": call_result["call_sid"],
                    "call_status": call_result["status"],
                    "message": message,
                    "timestamp": datetime.now().isoformat()
                }
            )
        else:
            print(f"❌ Phone call failed: {call_result['error']}")
            return TaskResponse(
                task_id=request.task_id,
                status="failed",
                message=f"Phone call failed: {call_result['error']}",
                result={
                    "phone_number": phone_number,
                    "error": call_result["error"],
                    "message": message,
                    "timestamp": datetime.now().isoformat()
                }
            )
        
    except Exception as e:
        print(f"❌ Phone Agent error: {e}")
        return TaskResponse(
            task_id=request.task_id,
            status="failed",
            message=f"Phone agent error: {str(e)}",
            result={"error": str(e)}
        )

async def make_twilio_call(phone_number: str, message: str):
    """Make a phone call using Twilio"""
    try:
        # Check if Twilio credentials are available
        twilio_sid = os.getenv("TWILIO_ACCOUNT_SID")
        twilio_token = os.getenv("TWILIO_AUTH_TOKEN")
        twilio_phone = os.getenv("TWILIO_PHONE_NUMBER")
        
        if not all([twilio_sid, twilio_token, twilio_phone]):
            return {
                "success": False,
                "error": "Twilio credentials not configured"
            }
        
        from twilio.rest import Client
        client = Client(twilio_sid, twilio_token)
        
        # Create TwiML for the call
        twiml = f'<Response><Say>{message}</Say></Response>'
        
        # Make the call
        call = client.calls.create(
            twiml=twiml,
            to=phone_number,
            from_=twilio_phone
        )
        
        return {
            "success": True,
            "call_sid": call.sid,
            "status": call.status
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

if __name__ == "__main__":
    print("🚀 Starting Simple Phone Agent...")
    print("📞 Ready to handle phone call tasks")
    
    # Run on port 8004 (Phone Agent port)
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8004,
        log_level="info"
    )
