#!/usr/bin/env python3
"""
Test script to verify the improved logging format works
This script simulates the logging output without actually starting services
"""

import logging
import sys
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from shared.port_manager import get_service_port

# Set up logging to match the microservices
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_backend_logging():
    """Test Backend API logging format"""
    # [SYSTEM:test_backend_logging] TEST | 🔍 Testing Backend API Logging Format:
    # [SYSTEM:test_backend_logging] SYSTEM | =" * 5
    
    # Simulate backend startup messages
    logger.info("[BACKEND-API] lifespan() - 🚀 Starting Deeplica v0 Backend")
    logger.info("[BACKEND-API] initialize_backend_services() - ⏳ Initializing database connection...")
    logger.info("[BACKEND-API] initialize_backend_services() - ✅ Database connected successfully")
    logger.info("[BACKEND-API] initialize_backend_services() - ✅ Backend API is READY and accepting requests!")
    logger.info("[BACKEND-API] readiness_check() - Backend ready for connections")
    
    print()

def test_dispatcher_logging():
    """Test Dispatcher logging format"""
    # [SYSTEM:test_dispatcher_logging] TEST | 🔍 Testing Dispatcher Logging Format:
    # [SYSTEM:test_dispatcher_logging] SYSTEM | =" * 5
    
    # Simulate dispatcher startup messages
    logger.info("[DISPATCHER] lifespan() - 🚀 Starting Dispatcher Service")
    logger.info("[DISPATCHER] cleanup_port(get_service_port("dispatcher")) - 🔪 Killing process 12345 on port 8001")
    logger.info(f"[DISPATCHER] wait_for_backend_and_initialize() - ⏳ Waiting for Backend API at http://localhost:{get_service_port('backend')}")
    logger.info("[DISPATCHER] wait_for_backend_and_initialize() - ✅ Backend API is FULLY READY and initialized! (attempt 1)")
    logger.info("[DISPATCHER] task_polling_loop() - 📋 Found 3 pending tasks")
    logger.info("[DISPATCHER] task_polling_loop() - 🚀 Processing task: abc123 (type: dialogue)")
    
    print()

def test_dialogue_logging():
    """Test Dialogue Agent logging format"""
    # [SYSTEM:test_dialogue_logging] TEST | 🔍 Testing Dialogue Agent Logging Format:
    # [SYSTEM:test_dialogue_logging] SYSTEM | =" * 5
    
    # Simulate dialogue agent messages
    logger.info("[DIALOGUE-AGENT] lifespan() - 🚀 Starting Dialogue Agent Service")
    logger.info(f"[DIALOGUE-AGENT] wait_for_backend_and_initialize() - ⏳ Waiting for Backend API at http://localhost:{get_service_port('backend')}")
    logger.info("[DIALOGUE-AGENT] wait_for_backend_and_initialize() - ✅ Backend API is healthy and ready - proceeding with Dialogue Agent initialization!")
    logger.info("[DIALOGUE-AGENT] execute_task() - 📥 Received task: def456 (type: dialogue)")
    
    print()

def test_planner_logging():
    """Test Planner Agent logging format"""
    # [SYSTEM:test_planner_logging] TEST | 🔍 Testing Planner Agent Logging Format:
    # [SYSTEM:test_planner_logging] SYSTEM | =" * 5
    
    # Simulate planner agent messages
    logger.info("[PLANNER-AGENT] lifespan() - 🚀 Starting Planner Agent Service")
    logger.info(f"[PLANNER-AGENT] wait_for_backend_and_initialize() - ⏳ Waiting for Backend API at http://localhost:{get_service_port('backend')}")
    logger.info("[PLANNER-AGENT] execute_task() - 📥 Received task: ghi789 (type: planning)")
    
    print()

def test_phone_logging():
    """Test Phone Agent logging format"""
    # [SYSTEM:test_phone_logging] TEST | 🔍 Testing Phone Agent Logging Format:
    # [SYSTEM:test_phone_logging] SYSTEM | =" * 5
    
    # Simulate phone agent messages
    logger.info("[PHONE-AGENT] lifespan() - 🚀 Starting Phone Agent Service")
    logger.info("[PHONE-AGENT] cleanup_port(get_service_port("phone")) - 🔪 Killing process 67890 on port 8004")
    logger.info(f"[PHONE-AGENT] wait_for_backend_and_initialize() - ⏳ Waiting for Backend API at http://localhost:{get_service_port('backend')}")
    logger.info("[PHONE-AGENT] execute_task() - 📥 Received phone call task: jkl012")
    
    print()

def main():
    # [TEST-LOGGING:main] SYSTEM | Testing improved logging format
    # [TEST-LOGGING:main] SYSTEM | All logging messages now include service identification
    # [TEST-LOGGING:main] SYSTEM | Function names for traceability
    # [TEST-LOGGING:main] SYSTEM | Relevant data (task IDs, ports, etc.)
    
    test_backend_logging()
    test_dispatcher_logging()
    test_dialogue_logging()
    test_planner_logging()
    test_phone_logging()

    # [TEST-LOGGING:main] SYSTEM | Logging format improvements complete
    # [TEST-LOGGING:main] SYSTEM | Benefits: Easy service identification, function traceability, relevant data, consistent format

if __name__ == "__main__":
    main()
