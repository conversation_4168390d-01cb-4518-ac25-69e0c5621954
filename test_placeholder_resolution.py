#!/usr/bin/env python3
"""
Test script to verify placeholder resolution functionality
"""

import asyncio
import sys
import os
from unittest.mock import AsyncMock

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from dispatcher.app.parameter_resolver import <PERSON><PERSON><PERSON><PERSON>olver
from shared_models import Task, TaskStatus


async def test_phone_call_placeholder_resolution():
    """Test the specific case of phone call result placeholder resolution"""
    
    print("🧪 Testing phone call placeholder resolution...")
    
    # Mock database service
    mock_db_service = AsyncMock()
    
    # Create resolver
    resolver = ParameterResolver(mock_db_service)
    
    # Mock first task result (phone call that asks for a number)
    first_task = Task(
        task_id="mission_123_call_ask_number",
        mission_id="mission_123",
        task_type="phone_call",
        description="Call and ask for a number",
        prompt="Call and ask for a number",
        status=TaskStatus.DONE,
        result={
            "call_status": "completed",
            "duration_seconds": 45,
            "conversation_transcript": [
                {"timestamp": "2025-01-01T10:00:00", "speaker": "agent", "text": "Hello, can you give me a number?"},
                {"timestamp": "2025-01-01T10:00:15", "speaker": "human", "text": "Sure, the number is 42"}
            ],
            "extracted_answer": "42",
            "question_answered": True,
            "twilio_call_sid": "CA123456789",
            "disconnect_reason": "completed-via-hangup",
            "error_message": None
        }
    )
    
    # Configure mock to return the first task
    mock_db_service.get_task.return_value = first_task
    
    # Test data for second task that should use the number from first task
    second_task_data = {
        "phone_number": "+972547000430",
        "contact_name": "Eran",
        "context": "Tell the number from previous call",
        "question": "The number is {{task:mission_123_call_ask_number:extracted_answer}}",
        "language": "en",
        "max_duration_minutes": 5
    }
    
    print(f"📋 Original task data: {second_task_data}")
    
    # Check if placeholders are detected
    has_placeholders = resolver.has_placeholders(second_task_data)
    print(f"🔍 Has placeholders: {has_placeholders}")
    
    if has_placeholders:
        # Find placeholders
        placeholders = resolver._find_all_placeholders(second_task_data)
        print(f"📍 Found placeholders: {len(placeholders)}")
        for i, placeholder in enumerate(placeholders):
            print(f"  {i+1}. {placeholder}")
    
    # Resolve parameters
    try:
        resolved_data = await resolver.resolve_task_parameters(second_task_data, "mission_123")
        print(f"✅ Resolved task data: {resolved_data}")
        
        # Verify the placeholder was resolved correctly
        expected_question = "The number is 42"
        if resolved_data["question"] == expected_question:
            print("✅ SUCCESS: Placeholder resolved correctly!")
            return True
        else:
            print(f"❌ FAILED: Expected '{expected_question}', got '{resolved_data['question']}'")
            return False
            
    except Exception as e:
        print(f"❌ ERROR during resolution: {e}")
        return False


async def main():
    """Run the test"""
    print("🚀 Starting placeholder resolution test...")
    
    success = await test_phone_call_placeholder_resolution()
    
    if success:
        print("\n🎉 All tests passed!")
        return 0
    else:
        print("\n💥 Tests failed!")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
