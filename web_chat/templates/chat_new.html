<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DEEPLICA AI WEB CHAT v3.1</title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <!-- 🔧 FORCE REFRESH: Cache busting timestamp -->
    <meta name="cache-bust" content="{{ timestamp if timestamp else '1704067200' }}">
    <script>
        console.log('🌐 DEEPLICA WEB CHAT v3.1: Page loaded at ' + {{ (timestamp if timestamp else 'Date.now()') | tojson | safe }});
        console.log('🤖 Advanced AI Personal Assistant - JavaScript initializing...');
        // 🔧 DEBUG: Test if JavaScript is executing
        try {
            console.log('✅ JavaScript is executing successfully');
        } catch (e) {
            console.error('❌ JavaScript error:', e);
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --chat-font-size: 2.8rem;
            --input-font-size: 2.8rem;
            --input-min-height: 80px;
            --input-max-height: 180px;
            --send-button-size: 80px;
            --send-button-font: 1.8rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Rajdhani', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            height: 100vh;
            overflow: hidden;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            position: relative;
        }



        /* Animated background particles */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 107, 53, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(0, 255, 127, 0.05) 0%, transparent 50%);
            animation: backgroundPulse 8s ease-in-out infinite alternate;
            z-index: -2;
        }

        @keyframes backgroundPulse {
            0% { opacity: 0.3; }
            100% { opacity: 0.7; }
        }

        .chat-container {
            display: flex;
            flex-direction: column;
            height: 85vh; /* Exactly 85% of viewport height */
            max-width: 2400px; /* Extended by 2x from 1200px */
            width: 90%; /* Use 90% of available width for better centering */
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            box-sizing: border-box;
        }

        .chat-header {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            padding: 25px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            border-bottom: 2px solid rgba(0, 212, 255, 0.3);
            min-height: 140px;
        }

        /* Deeplica Video in Header - Maximized Size */
        .header-video-container {
            position: relative;
            width: 120px;
            height: 120px;
            border-radius: 50%;
            overflow: hidden;
            border: 3px solid rgba(0, 212, 255, 0.6);
            box-shadow: 0 0 25px rgba(0, 212, 255, 0.4);
            margin-right: 20px;
            transition: all 0.3s ease;
        }

        .header-video-container:hover {
            transform: scale(1.05);
            box-shadow: 0 0 35px rgba(0, 212, 255, 0.6);
        }

        .header-deeplica-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #0a0a0a, #1a1a2e, #16213e);
        }

        /* Header video disconnected state */
        .header-video-container.disconnected .header-deeplica-video {
            opacity: 0.3;
            filter: grayscale(100%);
        }

        .header-video-container.disconnected {
            border-color: rgba(220, 53, 69, 0.5);
            box-shadow: 0 0 15px rgba(220, 53, 69, 0.3);
        }

        /* Video fallback animation */
        .header-robot-fallback {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #0a0a0a, #1a1a2e, #16213e);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            animation: robotPulse 2s ease-in-out infinite;
        }

        @keyframes robotPulse {
            0%, 100% { transform: scale(1); opacity: 0.8; }
            50% { transform: scale(1.1); opacity: 1; }
        }

        /* Chat Date/Time Display */
        .chat-datetime-display {
            margin-top: 8px;
            display: flex;
            gap: 10px;
            align-items: center;
            font-family: 'Orbitron', monospace;
        }

        .chat-date-part {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            color: white;
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 0.7rem;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(0, 212, 255, 0.3);
        }

        .chat-time-part {
            background: linear-gradient(45deg, #ff6b35, #ff4757);
            color: white;
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 0.7rem;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
            animation: chatTimePulse 2s ease-in-out infinite;
        }

        @keyframes chatTimePulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }



        /* Responsive design for mobile */
        @media (max-width: 768px) {
            .header-controls {
                gap: 10px;
            }

            .control-icon-btn {
                width: 60px;
                height: 60px;
                font-size: 1.8rem;
            }

            .control-icon-btn.font-small {
                font-size: 1.2rem;
            }

            .control-icon-btn.font-large {
                font-size: 2.2rem;
            }
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .admin-button {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            color: white;
            text-decoration: none;
            font-size: 18px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .admin-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        /* Header Control Icons */
        .header-controls {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .control-icon-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            color: white;
            text-decoration: none;
            font-size: 2.4rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }

        .control-icon-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
            border-color: rgba(255, 255, 255, 0.4);
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        }

        .control-icon-btn.font-small {
            font-size: 1.6rem;
            font-weight: 700;
        }

        .control-icon-btn.font-large {
            font-size: 2.8rem;
            font-weight: 900;
        }

        .control-icon-btn.back-btn:hover {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            box-shadow: 0 6px 20px rgba(46, 204, 113, 0.4);
        }

        .control-icon-btn.logout-btn {
            color: white;
            text-shadow: 0 0 3px rgba(255, 255, 255, 0.8);
        }

        .control-icon-btn.logout-btn:hover {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
            color: white;
            text-shadow: 0 0 5px rgba(255, 255, 255, 1);
        }







        .avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .header-info h1 {
            font-size: 2.4rem;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 0 15px rgba(0, 212, 255, 0.7);
        }

        .header-info p {
            font-size: 1.2rem;
            opacity: 0.95;
            font-weight: 500;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .connection-status {
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .connection-status.connecting {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            color: white;
            border: 1px solid rgba(243, 156, 18, 0.5);
            box-shadow: 0 2px 8px rgba(243, 156, 18, 0.3);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        .connection-status.connected {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
            border: 1px solid rgba(46, 204, 113, 0.5);
            box-shadow: 0 2px 8px rgba(46, 204, 113, 0.3);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        .connection-status.disconnected {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            border: 1px solid rgba(231, 76, 60, 0.5);
            box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        .messages-area {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
            background-image: 
                radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0);
            background-size: 20px 20px;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            animation: messageSlideIn 0.3s ease-out;
        }

        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-bubble {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            position: relative;
            word-wrap: break-word;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            font-size: var(--chat-font-size);
            line-height: 1.6;
        }

        .message.user .message-bubble {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.deeplica .message-bubble {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
            border-bottom-left-radius: 4px;
        }

        .message-time {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 4px;
            text-align: right;
        }

        .typing-indicator {
            display: none;
            padding: 15px 20px;
            font-style: italic;
            color: #666;
            background: rgba(108, 117, 125, 0.1);
            border-radius: 10px;
            margin: 10px 20px;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        .input-area {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 15px;
            align-items: flex-end;
        }

        .message-input {
            flex: 1;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            padding: 12px 20px;
            font-size: var(--input-font-size);
            resize: none;
            outline: none;
            transition: all 0.3s ease;
            font-family: inherit;
            max-height: var(--input-max-height);
            min-height: var(--input-min-height);
        }

        .message-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .send-button {
            width: var(--send-button-size);
            height: var(--send-button-size);
            border: none;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
            font-size: var(--send-button-font);
        }

        .send-button:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .send-button:active {
            transform: scale(0.95);
        }

        .send-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* Scrollbar styling */
        .messages-area::-webkit-scrollbar {
            width: 6px;
        }

        .messages-area::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .messages-area::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .messages-area::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .chat-container {
                width: 95%; /* Use more width on mobile */
                max-width: none; /* Remove max-width constraint on mobile */
            }

            .chat-header {
                padding: 20px 15px;
                min-height: 120px;
            }

            .header-video-container {
                width: 100px;
                height: 100px;
                margin-right: 15px;
            }

            .header-info h1 {
                font-size: 2.0rem;
            }

            .header-info p {
                font-size: 1.0rem;
            }

            .connection-status {
                font-size: 0.6rem;
                padding: 3px 8px;
            }

            .avatar {
                width: 40px;
                height: 40px;
                font-size: 20px;
            }

            .message-bubble {
                max-width: 85%;
            }

            .input-area {
                padding: 15px;
            }
        }


    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <div class="header-left">
                <!-- Deeplica Video Avatar -->
                <div class="header-video-container">
                    <video class="header-deeplica-video" autoplay muted loop playsinline preload="auto">
                        <source src="/media/Deeplica Avatar.mp4" type="video/mp4">
                        <source src="/avatar" type="video/mp4">
                        <!-- Fallback animation -->
                        <div class="header-robot-fallback">🤖</div>
                    </video>
                </div>
                <div class="header-info">
                    <h1 style="color: #00d4ff; font-family: 'Orbitron', monospace; text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);">DEEPLICA AI</h1>
                    <p style="color: #ffffff; opacity: 0.9;">WEB CHAT v3.1 • Advanced AI Personal Assistant</p>
                    <div class="chat-datetime-display" id="chatDateTime">
                        <span class="connection-status connecting" id="connectionStatus">Connecting...</span>
                        <span class="chat-date-part" id="chatDate"></span>
                        <span class="chat-time-part" id="chatTime"></span>
                    </div>
                </div>
            </div>


            <div class="header-right">
                <div class="header-controls">
                    <button class="control-icon-btn font-small" onclick="decreaseFontSize()" title="Decrease font size">
                        A
                    </button>
                    <button class="control-icon-btn font-large" onclick="increaseFontSize()" title="Increase font size">
                        A
                    </button>
                    <button class="control-icon-btn back-btn" onclick="goBack()" title="Go Back">
                        ←
                    </button>
                    {% if user.role.value == "admin" %}
                    <button class="control-icon-btn" onclick="navigateToAdmin()" title="Admin Panel" id="adminButton">
                        ⚙️
                    </button>
                    {% endif %}
                    <button class="control-icon-btn logout-btn" onclick="logoutUser()" title="Log Out">
                        🔓
                    </button>
                </div>
            </div>
        </div>

        <div id="messagesArea" class="messages-area">
            <div class="message deeplica">
                <div class="message-bubble">
                    <div>👋 Hello {{ user.username }}! I'm Deeplica, your AI mission orchestrator. How can I help you today?</div>
                    <div class="message-time">Just now</div>
                </div>
            </div>
        </div>

        <div id="typingIndicator" class="typing-indicator">
            🤖 Deeplica is typing...
        </div>

        <div class="input-area">
            <textarea
                id="messageInput"
                class="message-input"
                placeholder="Type your message..."
                rows="1"
            ></textarea>
            <button id="sendButton" class="send-button">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                </svg>
            </button>
            <!-- 🔧 JAVASCRIPT TEST BUTTON -->
            <button onclick="alert('✅ JavaScript is working!'); console.log('✅ Test button clicked');"
                    style="margin-left: 10px; padding: 8px 12px; background: #00ff00; color: #000; border: none; border-radius: 4px; font-weight: bold;">
                TEST JS
            </button>
            <!-- 🔧 DEBUG: Simple test button -->
            <button onclick="alert('JavaScript works!'); console.log('✅ Test button clicked');" style="margin-left: 10px; padding: 8px; background: #00ff00; color: #000; border: none; border-radius: 4px;">
                Test JS
            </button>
        </div>
    </div>

    <script>
        console.log('🚀 DEEPLICA CHAT - NEW TEMPLATE LOADED');

        // 🔧 IMMEDIATE JAVASCRIPT TEST
        try {
            console.log('✅ JavaScript execution test: SUCCESS');
            // Test basic DOM manipulation
            document.title = 'DEEPLICA WEB CHAT v3.1 • Advanced AI Personal Assistant';
            console.log('✅ DOM manipulation test: SUCCESS');
        } catch (error) {
            console.error('❌ JavaScript execution test: FAILED', error);
            alert('JavaScript Error: ' + error.message);
        }

        // 🔧 FIXED: Safe template variables to prevent JavaScript syntax errors
        const sessionId = "{{ session_id | replace('"', '\\"') | replace('\\', '\\\\') }}";
        const username = "{{ user.username | replace('"', '\\"') | replace('\\', '\\\\') }}";

        // 🔐 SECURITY TOKENS - Safe for JavaScript
        const SECURITY_TOKENS = {
            access_token: "{{ (security_tokens.access_token if security_tokens else '') | replace('"', '\\"') | replace('\\', '\\\\') }}",
            page_token: "{{ (security_tokens.page_token if security_tokens else '') | replace('"', '\\"') | replace('\\', '\\\\') }}",
            html_token: "{{ (html_token if html_token else '') | replace('"', '\\"') | replace('\\', '\\\\') }}",
            secure_url: "{{ (secure_url if secure_url else '') | replace('"', '\\"') | replace('\\', '\\\\') }}"
        };

        // 🔐 TAB-SPECIFIC TOKEN DATA - Safe for JavaScript
        const TAB_TOKEN_DATA = {
            tab_token: "{{ (tab_token if tab_token else '') | replace('"', '\\"') | replace('\\', '\\\\') }}",
            browser_fingerprint: "{{ (browser_fingerprint if browser_fingerprint else '') | replace('"', '\\"') | replace('\\', '\\\\') }}",
            url_tab_token: new URLSearchParams(window.location.search).get('tab_token') || ''
        };

        console.log('📋 Session ID:', sessionId);
        console.log('👤 Username:', username);
        console.log('🔐 Security tokens loaded');
        console.log('🔒 Tab token verification enabled');

        // 🔧 FIXED: Safe security token verification
        if (typeof SECURITY_TOKENS === 'undefined' || !SECURITY_TOKENS || !SECURITY_TOKENS.access_token || !SECURITY_TOKENS.page_token) {
            console.warn('🔒 SECURITY: Missing authentication tokens - chat will work in limited mode');
            // Don't redirect immediately - let chat work in basic mode
            // window.location.href = '/unauthorized';
        }

        // 🔒 TAB-SPECIFIC TOKEN VERIFICATION - Prevents URL copying between tabs
        function verifyTabToken() {
            console.log('🔒 Verifying tab-specific token...');

            // Check if this is setup mode
            const urlParams = new URLSearchParams(window.location.search);
            const setupMode = urlParams.get('setup') === '1';

            if (setupMode) {
                console.log('🔧 Setup mode detected - generating proper tab token...');
                return setupTabToken();
            }

            // 🔧 FIXED: Safe check for tab token
            if (typeof TAB_TOKEN_DATA === 'undefined' || !TAB_TOKEN_DATA || !TAB_TOKEN_DATA.url_tab_token) {
                console.warn('🔒 No tab token in URL - allowing basic access');
                // Don't redirect - let chat work in basic mode
                // window.location.href = '/chat';
                return true; // Allow access without tab token
            }

            // CRITICAL: Verify tab ID from token matches current tab
            try {
                // 🔧 FIXED: Safe token decoding
                if (!TAB_TOKEN_DATA || !TAB_TOKEN_DATA.url_tab_token) {
                    console.log('🔒 No tab token available for verification - allowing access');
                    return true;
                }

                // Decode the token to extract the tab ID
                const tokenData = atob(TAB_TOKEN_DATA.url_tab_token);
                const tokenParts = tokenData.split(':');

                if (tokenParts.length >= 3) {
                    const tokenTabId = tokenParts[2]; // Tab ID is the 3rd part

                    // Verify current tab ID matches token's tab ID
                    if (!verifyCurrentTabId(tokenTabId)) {
                        alert('🔒 SECURITY: This URL was generated for a different browser tab and cannot be used here.');
                        window.location.href = '/login?error=tab_id_mismatch';
                        return false;
                    }
                } else {
                    console.error('🔒 Invalid token format');
                    window.location.href = '/login?error=invalid_token_format';
                    return false;
                }
            } catch (error) {
                console.error('🔒 Error verifying tab ID from token:', error);
                window.location.href = '/login?error=token_verification_error';
                return false;
            }

            // Additional check: verify stored token matches URL token
            const storedToken = sessionStorage.getItem('deeplica_tab_token');
            if (storedToken && TAB_TOKEN_DATA && TAB_TOKEN_DATA.url_tab_token && TAB_TOKEN_DATA.url_tab_token !== storedToken) {
                console.error('🔒 Stored token mismatch - URL was copied from different session');
                alert('🔒 SECURITY: This URL was generated for a different session and cannot be used here.');
                window.location.href = '/login?error=session_mismatch';
                return false;
            }

            // Verify token format
            if (TAB_TOKEN_DATA && TAB_TOKEN_DATA.url_tab_token && TAB_TOKEN_DATA.url_tab_token.length < 20) {
                console.warn('🔒 Invalid tab token format');
                window.location.href = '/login?error=invalid_token';
                return false;
            }

            console.log('✅ Tab-specific token verification passed');
            console.log('🔒 Tab ID:', currentTabId);
            console.log('🔒 Token verified for this specific browser tab');

            return true;
        }

        // 🔧 Setup tab token - generate proper tab-specific token
        async function setupTabToken() {
            try {
                console.log('🔧 Setting up tab-specific token...');

                // Generate unique tab identifier (will use existing if available)
                const tabId = generateTabId();
                console.log('🔒 Using tab ID:', tabId);

                // Store tab ID in sessionStorage (already done in generateTabId)
                sessionStorage.setItem('deeplica_tab_id', tabId);

                // Request proper tab token from server
                const response = await fetch('/api/generate-tab-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        tab_id: tabId
                    })
                });

                if (!response.ok) {
                    throw new Error(`Server error: ${response.status}`);
                }

                const result = await response.json();

                if (!result.success) {
                    throw new Error(result.error || 'Failed to generate tab token');
                }

                console.log('✅ Tab token generated successfully');

                // Store tab token for verification
                sessionStorage.setItem('deeplica_tab_token', result.tab_token);

                // Redirect to chat with proper token (remove setup parameter)
                window.location.href = result.secure_url;

                return true;

            } catch (error) {
                console.error('❌ Tab setup error:', error);
                alert('Error setting up secure tab. Redirecting to login.');
                window.location.href = '/login?error=tab_setup_failed';
                return false;
            }
        }

        // Generate unique tab identifier that's truly unique per browser tab
        function generateTabId() {
            // Check if this tab already has an ID
            let existingTabId = sessionStorage.getItem('deeplica_unique_tab_id');
            if (existingTabId) {
                console.log('🔒 Using existing tab ID:', existingTabId);
                return existingTabId;
            }

            // Generate new unique tab ID using multiple entropy sources
            const timestamp = Date.now();
            const random = Math.random().toString(36).substring(2, 15);
            const performance_now = performance.now().toString().replace('.', '');
            const screen_info = `${screen.width}x${screen.height}x${screen.colorDepth}`;
            const crypto_random = crypto.getRandomValues(new Uint32Array(1))[0].toString(36);

            // Create truly unique tab ID
            const tabId = `tab_${timestamp}_${random}_${performance_now}_${crypto_random}_${btoa(screen_info).substring(0, 8)}`;

            // Store in sessionStorage (unique per tab)
            sessionStorage.setItem('deeplica_unique_tab_id', tabId);

            console.log('🔒 Generated new unique tab ID:', tabId);
            return tabId;
        }

        // Verify that current tab matches the token's tab ID
        function verifyCurrentTabId(expectedTabId) {
            const currentTabId = sessionStorage.getItem('deeplica_unique_tab_id');

            if (!currentTabId) {
                console.error('🔒 SECURITY: No tab ID in current tab - URL was copied from another tab');
                return false;
            }

            if (currentTabId !== expectedTabId) {
                console.error('🔒 SECURITY: Tab ID mismatch');
                console.error('🔒 Current tab ID:', currentTabId);
                console.error('🔒 Expected tab ID:', expectedTabId);
                return false;
            }

            console.log('✅ Tab ID verification passed:', currentTabId);
            return true;
        }

        // Simplified token validation - less aggressive
        function handleTokenError(errorType) {
            console.warn('🔒 Token validation issue:', errorType);

            // Don't show alerts or open new tabs - just redirect quietly
            setTimeout(() => {
                window.location.href = '/login?error=' + errorType;
            }, 1000); // Small delay to avoid jarring redirects
        }

        // Tab token verification will be done during proper initialization
        // Removed early verification that was blocking chat functionality
        
        // WebSocket connection
        let ws = null;
        let isConnected = false;
        let reconnectAttempts = 0;
        const maxReconnectAttempts = 5;
        
        // DOM elements
        let messagesArea, messageInput, sendButton, typingIndicator, connectionStatus;
        
        // Initialize when DOM is ready
        function initializeChat() {
            console.log('🚀 Initializing Deeplica Chat...');

            // Get DOM elements
            messagesArea = document.getElementById('messagesArea');
            messageInput = document.getElementById('messageInput');
            sendButton = document.getElementById('sendButton');
            typingIndicator = document.getElementById('typingIndicator');
            connectionStatus = document.getElementById('connectionStatus');

            console.log('🔍 DOM elements check:');
            console.log('  messagesArea:', !!messagesArea, messagesArea?.id);
            console.log('  messageInput:', !!messageInput, messageInput?.id);
            console.log('  sendButton:', !!sendButton, sendButton?.id);
            console.log('  typingIndicator:', !!typingIndicator, typingIndicator?.id);
            console.log('  connectionStatus:', !!connectionStatus, connectionStatus?.id);

            // Verify elements
            if (!messagesArea || !messageInput || !sendButton || !connectionStatus) {
                console.error('❌ Required DOM elements not found');
                console.error('🔍 Missing elements:', {
                    messagesArea: !messagesArea,
                    messageInput: !messageInput,
                    sendButton: !sendButton,
                    connectionStatus: !connectionStatus
                });

                // List all available elements for debugging
                console.error('🔍 Available elements:',
                    Array.from(document.querySelectorAll('[id]')).map(el => el.id));
                return;
            }

            console.log('✅ DOM elements found, attaching event handlers...');

            // 🔧 FIX: Attach send button click handler
            sendButton.addEventListener('click', function(e) {
                console.log('📤 Send button clicked');
                e.preventDefault();
                sendMessage();
            });

            // 🔧 FIX: Attach keyboard event handler for Enter key
            messageInput.addEventListener('keydown', function(e) {
                console.log('⌨️ Key pressed:', e.key, 'Shift:', e.shiftKey);
                if (e.key === 'Enter' && !e.shiftKey) {
                    console.log('📤 Enter key pressed - sending message');
                    e.preventDefault();
                    sendMessage();
                }
            });

            // Auto-resize textarea
            messageInput.addEventListener('input', autoResizeTextarea);

            console.log('✅ Event handlers attached, connecting WebSocket...');

            // ✅ Event handlers attached successfully
            console.log('✅ Send button ready:', sendButton ? 'Found' : 'Missing');
            console.log('✅ Message input ready:', messageInput ? 'Found' : 'Missing');

            connectWebSocket();

            messageInput.focus();
        }
        
        function connectWebSocket() {
            console.log('🔗 Connecting to WebSocket...');
            
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws?session_id=${sessionId}`;
            
            console.log('🌐 WebSocket URL:', wsUrl);
            updateConnectionStatus('connecting');
            
            try {
                ws = new WebSocket(wsUrl);
                
                ws.onopen = function() {
                    console.log('✅ WebSocket connected');
                    isConnected = true;
                    reconnectAttempts = 0;
                    updateConnectionStatus('connected');
                };
                
                ws.onmessage = function(event) {
                    console.log('📨 Received:', event.data);
                    try {
                        const data = JSON.parse(event.data);
                        handleMessage(data);
                    } catch (e) {
                        console.error('❌ Error parsing message:', e);
                    }
                };
                
                ws.onclose = function(event) {
                    console.log('🔴 WebSocket closed:', event.code, event.reason);
                    isConnected = false;
                    updateConnectionStatus('disconnected');

                    // Check if session expired (code 1008)
                    if (event.code === 1008 && event.reason && event.reason.includes('Session expired')) {
                        console.log('🔄 Session expired - refreshing page...');
                        alert('Your session has expired. The page will refresh to log you in again.');
                        window.location.reload();
                        return;
                    }

                    // Auto-reconnect for other errors
                    if (reconnectAttempts < maxReconnectAttempts) {
                        setTimeout(() => {
                            reconnectAttempts++;
                            console.log(`🔄 Reconnecting... (${reconnectAttempts}/${maxReconnectAttempts})`);
                            connectWebSocket();
                        }, 2000 * reconnectAttempts);
                    } else {
                        console.log('❌ Max reconnection attempts reached. Please refresh the page.');
                        updateConnectionStatus('failed');
                    }
                };
                
                ws.onerror = function(error) {
                    console.error('❌ WebSocket error:', error);
                    updateConnectionStatus('disconnected');
                };
                
            } catch (error) {
                console.error('❌ Error creating WebSocket:', error);
                updateConnectionStatus('disconnected');
            }
        }
        
        function updateConnectionStatus(status) {
            console.log('🔗 Updating connection status to:', status);

            if (!connectionStatus) {
                console.error('❌ Connection status element not found!');
                // Try to find it again
                connectionStatus = document.getElementById('connectionStatus');
                if (!connectionStatus) {
                    console.error('❌ Still cannot find connectionStatus element');
                    return;
                }
            }

            // Update connection status display
            connectionStatus.className = `connection-status ${status}`;
            console.log('✅ Connection status class updated to:', connectionStatus.className);

            // Control header video based on connection status
            const headerVideoContainer = document.querySelector('.header-video-container');
            const headerVideo = document.querySelector('.header-deeplica-video');

            switch(status) {
                case 'connected':
                    connectionStatus.textContent = 'Connected';
                    sendButton.disabled = false;
                    // Play header video when connected
                    if (headerVideo) {
                        headerVideo.play().catch(e => console.log('Header video autoplay prevented:', e));
                        headerVideoContainer?.classList.remove('disconnected');
                    }
                    break;
                case 'disconnected':
                    connectionStatus.textContent = 'Disconnected';
                    sendButton.disabled = true;
                    // Pause header video when disconnected
                    if (headerVideo) {
                        headerVideo.pause();
                        headerVideoContainer?.classList.add('disconnected');
                    }
                    break;
                case 'connecting':
                    connectionStatus.textContent = 'Connecting...';
                    sendButton.disabled = true;
                    // Keep header video playing while connecting
                    if (headerVideo) {
                        headerVideo.play().catch(e => console.log('Header video autoplay prevented:', e));
                        headerVideoContainer?.classList.remove('disconnected');
                    }
                    break;
                case 'failed':
                    connectionStatus.textContent = 'Failed';
                    sendButton.disabled = true;
                    // Pause header video when failed
                    if (headerVideo) {
                        headerVideo.pause();
                        headerVideoContainer?.classList.add('disconnected');
                    }
                    break;
            }
        }
        
        function handleMessage(data) {
            if (data.type === 'message' && data.sender === 'deeplica') {
                addMessage('deeplica', data.content);
                hideTypingIndicator();
            } else if (data.type === 'typing') {
                showTypingIndicator();
            }
        }
        
        function addMessage(sender, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            const time = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            
            messageDiv.innerHTML = `
                <div class="message-bubble">
                    <div>${escapeHtml(content)}</div>
                    <div class="message-time">${time}</div>
                </div>
            `;
            
            messagesArea.appendChild(messageDiv);
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }
        
        function sendMessage() {
            console.log('🚀 sendMessage() called');
            const message = messageInput.value.trim();
            console.log('📝 Message content:', message);
            console.log('🔗 Connection status:', isConnected);

            if (!message) {
                console.log('❌ Empty message, not sending');
                return;
            }

            if (!isConnected) {
                console.log('❌ Not connected, cannot send message');
                return;
            }

            console.log('📤 Sending message:', message);
            addMessage('user', message);

            const data = {
                type: 'message',
                content: message
            };

            try {
                ws.send(JSON.stringify(data));
                messageInput.value = '';
                autoResizeTextarea();
                showTypingIndicator();
                console.log('✅ Message sent successfully');
            } catch (error) {
                console.error('❌ Error sending message:', error);
            }
        }
        
        // 🔧 REMOVED: handleKeyDown function - now using event listener in initializeChat()
        
        function autoResizeTextarea() {
            messageInput.style.height = 'auto';
            messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
        }
        
        function showTypingIndicator() {
            if (typingIndicator) {
                typingIndicator.style.display = 'block';
                messagesArea.scrollTop = messagesArea.scrollHeight;
            }
        }
        
        function hideTypingIndicator() {
            if (typingIndicator) {
                typingIndicator.style.display = 'none';
            }
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Font size control functions
        let currentFontLevel = 4; // Start at level 4 (2.8rem)
        const fontLevels = [
            { chat: '0.8rem', input: '0.8rem', inputMin: '32px', inputMax: '80px', buttonSize: '40px', buttonFont: '0.8rem' },  // Level 0
            { chat: '1.0rem', input: '1.0rem', inputMin: '36px', inputMax: '90px', buttonSize: '44px', buttonFont: '1.0rem' },  // Level 1
            { chat: '1.4rem', input: '1.4rem', inputMin: '44px', inputMax: '110px', buttonSize: '52px', buttonFont: '1.2rem' }, // Level 2
            { chat: '1.8rem', input: '1.8rem', inputMin: '56px', inputMax: '140px', buttonSize: '64px', buttonFont: '1.4rem' }, // Level 3
            { chat: '2.8rem', input: '2.8rem', inputMin: '80px', inputMax: '180px', buttonSize: '80px', buttonFont: '1.8rem' }, // Level 4 (current)
            { chat: '3.6rem', input: '3.6rem', inputMin: '100px', inputMax: '220px', buttonSize: '96px', buttonFont: '2.2rem' }, // Level 5
            { chat: '4.4rem', input: '4.4rem', inputMin: '120px', inputMax: '260px', buttonSize: '112px', buttonFont: '2.6rem' } // Level 6
        ];

        function updateFontSize() {
            const level = fontLevels[currentFontLevel];
            const root = document.documentElement;

            root.style.setProperty('--chat-font-size', level.chat);
            root.style.setProperty('--input-font-size', level.input);
            root.style.setProperty('--input-min-height', level.inputMin);
            root.style.setProperty('--input-max-height', level.inputMax);
            root.style.setProperty('--send-button-size', level.buttonSize);
            root.style.setProperty('--send-button-font', level.buttonFont);

            // Save preference to localStorage
            localStorage.setItem('deepchat-font-level', currentFontLevel);
        }

        function increaseFontSize() {
            if (currentFontLevel < fontLevels.length - 1) {
                currentFontLevel++;
                updateFontSize();
            }
        }

        function decreaseFontSize() {
            if (currentFontLevel > 0) {
                currentFontLevel--;
                updateFontSize();
            }
        }

        // Load saved font preference on page load
        function loadFontPreference() {
            const saved = localStorage.getItem('deepchat-font-level');
            if (saved !== null) {
                currentFontLevel = parseInt(saved);
                updateFontSize();
            }
        }
        
        // Chat Date/Time Display Functions
        function initializeChatDateTime() {
            console.log('🕒 Initializing chat date/time display...');

            // Wait a bit for DOM to be fully ready
            setTimeout(() => {
                // Check if elements exist
                const dateElement = document.getElementById('chatDate');
                const timeElement = document.getElementById('chatTime');

                console.log('🔍 Date element found:', !!dateElement);
                console.log('🔍 Time element found:', !!timeElement);

                if (!dateElement || !timeElement) {
                    console.error('❌ Date/time elements not found!');
                    console.error('🔍 Available elements with "chat" in ID:',
                        Array.from(document.querySelectorAll('[id*="chat"]')).map(el => el.id));
                    return;
                }

                console.log('✅ Date/time elements found, starting updates...');
                updateChatDateTime();

                // Update every second
                const intervalId = setInterval(updateChatDateTime, 1000);
                console.log('✅ Date/time interval started:', intervalId);
            }, 100);
        }

        function updateChatDateTime() {
            const now = new Date();
            const dateOptions = {
                weekday: 'short',
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            };
            const timeOptions = {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            };

            const dateElement = document.getElementById('chatDate');
            const timeElement = document.getElementById('chatTime');

            if (dateElement && timeElement) {
                const dateText = now.toLocaleDateString('en-US', dateOptions);
                const timeText = now.toLocaleTimeString('en-US', timeOptions);

                dateElement.textContent = dateText;
                timeElement.textContent = timeText;

                // Debug log (only occasionally to avoid spam)
                if (now.getSeconds() % 10 === 0) {
                    console.log('🕒 Date/time updated:', dateText, timeText);
                }
            } else {
                console.error('❌ Date/time elements not found during update');
            }
        }

        // Chat Navigation Functions
        function goBack() {
            // Smart back navigation
            if (window.history.length > 1) {
                window.history.back();
            } else {
                // Fallback to login page
                window.location.href = '/login';
            }
        }

        // 🔧 FIXED: Simplified Admin Navigation with fallback
        function navigateToAdmin() {
            try {
                console.log('⚙️ Navigating to admin panel...');

                // Method 1: Try with current URL tokens
                const urlParams = new URLSearchParams(window.location.search);
                const accessToken = urlParams.get('access_token');
                const pageToken = urlParams.get('page_token');

                if (accessToken && pageToken) {
                    console.log('🔐 Using URL tokens for admin access');
                    const adminUrl = `/admin?access_token=${accessToken}&page_token=${pageToken}`;
                    console.log('🔧 Redirecting to admin with tokens:', adminUrl);
                    window.location.href = adminUrl;
                    return;
                }

                // Method 2: Try with embedded security tokens
                if (typeof SECURITY_TOKENS !== 'undefined' && SECURITY_TOKENS && SECURITY_TOKENS.access_token) {
                    console.log('🔐 Using embedded security tokens for admin access');
                    const adminUrl = `/admin?access_token=${SECURITY_TOKENS.access_token}&page_token=${SECURITY_TOKENS.page_token}`;
                    console.log('🔧 Redirecting to admin with embedded tokens');
                    window.location.href = adminUrl;
                    return;
                }

                // Method 3: Simple admin access (fallback)
                console.log('🔧 Using simple admin access (fallback)');
                window.location.href = '/admin';

            } catch (error) {
                console.error('❌ Error navigating to admin:', error);
                // Fallback: try simple admin access
                console.log('🔧 Fallback: trying simple admin access');
                try {
                    window.location.href = '/admin';
                } catch (fallbackError) {
                    console.error('❌ Fallback admin access failed:', fallbackError);
                    alert('Unable to access admin panel. Please try logging in again.');
                    window.location.href = '/login';
                }
            }
        }

        function logoutUser() {
            // Show confirmation dialog
            if (confirm('Are you sure you want to log out? You will need to log in again.')) {
                // Send logout request to server
                fetch('/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'same-origin'
                })
                .then(response => {
                    // Redirect to login page regardless of response
                    window.location.href = '/login';
                })
                .catch(error => {
                    console.error('Logout error:', error);
                    // Still redirect to login page
                    window.location.href = '/login';
                });
            }
        }

        // 🔐 CYBER-SECURE SINGLE SESSION ENFORCEMENT SYSTEM
        class DeepChatSessionManager {
            constructor() {
                this.sessionKey = 'deeplica_chat_session';
                this.tabKey = 'deeplica_tab_registry';
                this.heartbeatKey = 'deeplica_heartbeat';
                this.windowId = this.generateSecureWindowId();
                this.browserToken = this.generateSecureBrowserToken();
                this.tabToken = this.generateUniqueTabToken();
                this.heartbeatInterval = null;
                this.validationInterval = null;
                this.broadcastChannel = null;
                this.sessionRegistered = false;
                this.isClosing = false;

                // Add tab token to URL for unique identification
                this.updateUrlWithTabToken();

                // Initialize BroadcastChannel for cross-tab communication
                if ('BroadcastChannel' in window) {
                    this.broadcastChannel = new BroadcastChannel('deeplica_session');
                    this.broadcastChannel.onmessage = (event) => this.handleBroadcastMessage(event);
                }

                // Register beforeunload to clean up session
                window.addEventListener('beforeunload', () => this.cleanup());
                window.addEventListener('unload', () => this.cleanup());

                console.log('🔐 Secure session manager initialized', {
                    windowId: this.windowId,
                    browserToken: this.browserToken.substring(0, 8) + '...'
                });
            }

            generateSecureWindowId() {
                const timestamp = Date.now();
                const random = crypto.getRandomValues(new Uint32Array(2));
                return `deeplica_${timestamp}_${random[0]}_${random[1]}`;
            }

            generateSecureBrowserToken() {
                // Generate crypto-secure browser token
                const array = new Uint8Array(32);
                crypto.getRandomValues(array);
                return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
            }

            generateUniqueTabToken() {
                // Generate unique tab token with timestamp and crypto-secure random data
                const timestamp = Date.now();
                const array = new Uint8Array(16);
                crypto.getRandomValues(array);
                const randomHex = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
                return `tab_${timestamp}_${randomHex}`;
            }

            updateUrlWithTabToken() {
                // Add tab token to URL for unique tab identification
                try {
                    const url = new URL(window.location);
                    url.searchParams.set('tab_token', this.tabToken);

                    // Update URL without triggering page reload
                    window.history.replaceState({}, '', url.toString());

                    console.log('🔐 Tab token added to URL:', this.tabToken.substring(0, 20) + '...');
                } catch (error) {
                    console.warn('Failed to update URL with tab token:', error);
                }
            }

            getTabTokenFromUrl() {
                // Extract tab token from URL
                try {
                    const url = new URL(window.location);
                    return url.searchParams.get('tab_token');
                } catch (error) {
                    console.warn('Failed to extract tab token from URL:', error);
                    return null;
                }
            }

            async registerBrowserSession() {
                try {
                    const response = await fetch('/api/session/register-browser', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            session_id: sessionId,
                            browser_token: this.browserToken,
                            tab_token: this.tabToken,
                            window_id: this.windowId,
                            browser_signature: this.createBrowserSignature(),
                            user_agent: navigator.userAgent,
                            screen_resolution: `${screen.width}x${screen.height}`,
                            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                            language: navigator.language,
                            timestamp: Date.now()
                        })
                    });

                    const result = await response.json();

                    if (result.status === 'rejected') {
                        console.warn('🚫 Browser session rejected:', result.message);
                        this.handleSessionRejection(result);
                        return false;
                    } else if (result.action === 'force_login') {
                        console.warn('🚨 SECURITY: Forced re-login required');
                        this.forceReLogin(result.reason);
                        return false;
                    } else if (result.status === 'accepted') {
                        console.log('✅ Browser session registered successfully with enhanced security');
                        this.sessionRegistered = true;
                        this.startSessionValidation();
                        return true;
                    } else if (result.action === 'force_login') {
                        console.warn('🚨 SECURITY: Forced re-login required');
                        this.forceReLogin(result.reason);
                        return false;
                    }

                } catch (error) {
                    console.error('❌ Failed to register browser session:', error);
                    return false;
                }
            }

            createBrowserSignature() {
                // Create comprehensive browser/tab signature for unique identification
                const signature = {
                    user_agent: navigator.userAgent,
                    screen_resolution: `${screen.width}x${screen.height}`,
                    color_depth: screen.colorDepth,
                    pixel_ratio: window.devicePixelRatio,
                    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                    language: navigator.language,
                    languages: navigator.languages,
                    platform: navigator.platform,
                    cookie_enabled: navigator.cookieEnabled,
                    do_not_track: navigator.doNotTrack,
                    hardware_concurrency: navigator.hardwareConcurrency,
                    max_touch_points: navigator.maxTouchPoints,
                    viewport: `${window.innerWidth}x${window.innerHeight}`,
                    available_screen: `${screen.availWidth}x${screen.availHeight}`,
                    tab_token: this.tabToken,
                    window_id: this.windowId,
                    timestamp: Date.now()
                };

                // Create hash of signature for compact representation
                const signatureString = JSON.stringify(signature);
                return btoa(signatureString).substring(0, 64); // Base64 encoded, truncated
            }

            async validateBrowserSession() {
                if (this.isClosing) return true;

                try {
                    const response = await fetch('/api/session/validate-browser', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            session_id: sessionId,
                            browser_token: this.browserToken,
                            tab_token: this.tabToken,
                            window_id: this.windowId
                        })
                    });

                    const result = await response.json();

                    if (result.status === 'invalid') {
                        console.warn('🚫 Browser session invalidated:', result.message);
                        this.handleSessionInvalidation(result);
                        return false;
                    }

                    return true;

                } catch (error) {
                    console.error('❌ Failed to validate browser session:', error);
                    return false;
                }
            }

            handleSessionRejection(result) {
                // Try to focus existing session first
                if (this.broadcastChannel) {
                    this.broadcastChannel.postMessage({
                        type: 'focus_request',
                        session_id: sessionId,
                        timestamp: Date.now()
                    });

                    // Wait a moment for existing tab to respond
                    setTimeout(() => {
                        this.closeCurrentTab('Another session is already active');
                    }, 1000);
                } else {
                    this.closeCurrentTab('Another session is already active');
                }
            }

            handleSessionInvalidation(result) {
                if (result.action === 'close_tab') {
                    this.closeCurrentTab('Session is no longer active');
                } else if (result.action === 'redirect_login') {
                    window.location.href = '/login';
                } else if (result.action === 'force_login') {
                    this.forceReLogin(result.reason);
                }
            }

            forceReLogin(reason) {
                // Show security violation message and force re-login
                this.isClosing = true;

                const overlay = document.createElement('div');
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.9);
                    z-index: 10000;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                `;

                overlay.innerHTML = `
                    <div style="
                        background: linear-gradient(135deg, #1a1a2e, #16213e);
                        border: 2px solid #ff4757;
                        border-radius: 15px;
                        padding: 40px;
                        max-width: 600px;
                        text-align: center;
                        box-shadow: 0 20px 60px rgba(255, 71, 87, 0.3);
                    ">
                        <div style="color: #ff4757; font-size: 28px; margin-bottom: 20px;">
                            🚨 SECURITY ALERT
                        </div>
                        <div style="color: white; margin-bottom: 25px; line-height: 1.6; font-size: 16px;">
                            <strong>For security purposes, all sessions have been revoked.</strong><br><br>
                            Reason: ${reason || 'Multiple session attempt detected'}<br><br>
                            You will be redirected to the login page to re-authenticate.
                        </div>
                        <button onclick="window.location.href='/login'" style="
                            background: linear-gradient(45deg, #ff4757, #ff3742);
                            color: white; border: none; padding: 15px 30px;
                            border-radius: 8px; cursor: pointer; font-weight: bold;
                            font-size: 16px; transition: all 0.3s ease;
                        " onmouseover="this.style.transform='scale(1.05)'"
                           onmouseout="this.style.transform='scale(1)'">
                            🔐 Re-Login Now
                        </button>
                    </div>
                `;

                document.body.appendChild(overlay);

                // Auto-redirect after 10 seconds
                setTimeout(() => {
                    window.location.href = '/login';
                }, 10000);
            }

            closeCurrentTab(reason) {
                this.isClosing = true;

                // Show user-friendly message
                const overlay = document.createElement('div');
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.9);
                    color: white;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    z-index: 10000;
                    font-family: Arial, sans-serif;
                `;

                overlay.innerHTML = `
                    <div style="text-align: center; padding: 40px; background: rgba(255, 255, 255, 0.1); border-radius: 15px; border: 1px solid rgba(0, 212, 255, 0.3);">
                        <div style="font-size: 4rem; margin-bottom: 20px;">🔒</div>
                        <h2 style="color: #00d4ff; margin-bottom: 15px;">Session Closed</h2>
                        <p style="margin-bottom: 20px; font-size: 1.1rem;">${reason}</p>
                        <p style="color: #888; font-size: 0.9rem;">This tab will close automatically...</p>
                    </div>
                `;

                document.body.appendChild(overlay);

                // Cleanup and close
                this.cleanup();

                setTimeout(() => {
                    window.close();
                    // If window.close() doesn't work (some browsers), redirect
                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 1000);
                }, 2000);
            }

            startSessionValidation() {
                // Validate session every 15 seconds
                this.validationInterval = setInterval(async () => {
                    await this.validateBrowserSession();
                }, 15000);
            }

            cleanup() {
                if (this.isClosing) return;
                this.isClosing = true;

                if (this.validationInterval) {
                    clearInterval(this.validationInterval);
                }

                if (this.heartbeatInterval) {
                    clearInterval(this.heartbeatInterval);
                }

                // Unregister browser session
                if (this.sessionRegistered) {
                    navigator.sendBeacon('/api/session/unregister-browser', JSON.stringify({
                        session_id: sessionId,
                        browser_token: this.browserToken
                    }));
                }
            }

            async enforceSessionSingleton() {
                try {
                    // Check for existing active sessions
                    const existingSessions = this.getActiveSessions();

                    if (existingSessions.length > 0) {
                        // Try to focus existing tab first
                        if (await this.tryFocusExistingTab()) {
                            this.showProfessionalMessage('DeepChat is already open. Switching to existing session.');
                            window.close();
                            return false;
                        }

                        // If focus failed, show professional takeover dialog
                        const userChoice = await this.showSessionTakeoverDialog();
                        if (!userChoice) {
                            window.close();
                            return false;
                        }

                        // User chose to take over - invalidate other sessions
                        this.invalidateOtherSessions();
                    }

                    // Register this session
                    this.registerSession();
                    this.startHeartbeat();
                    this.startConflictMonitoring();
                    this.setupCleanupHandlers();

                    return true;
                } catch (error) {
                    console.error('Session enforcement error:', error);
                    return true; // Allow access on error to prevent lockout
                }
            }

            getActiveSessions() {
                try {
                    const sessions = JSON.parse(localStorage.getItem(this.tabKey) || '[]');
                    const now = Date.now();

                    // Filter out expired sessions (older than 30 seconds)
                    return sessions.filter(session => (now - session.lastHeartbeat) < 30000);
                } catch {
                    return [];
                }
            }

            async tryFocusExistingTab() {
                if (!this.broadcastChannel) return false;

                return new Promise((resolve) => {
                    let responseReceived = false;
                    let focusAttempts = 0;
                    const maxAttempts = 3;

                    // Listen for focus response
                    const handleResponse = (event) => {
                        if (event.data.type === 'focus_response' && !responseReceived) {
                            responseReceived = true;
                            this.broadcastChannel.removeEventListener('message', handleResponse);

                            if (event.data.success) {
                                // Add visual confirmation
                                this.showProfessionalMessage('Switching to existing DeepChat session...');
                                setTimeout(() => resolve(true), 1000);
                            } else {
                                resolve(false);
                            }
                        }
                    };

                    const attemptFocus = () => {
                        focusAttempts++;

                        // Request existing tab to focus
                        this.broadcastChannel.postMessage({
                            type: 'focus_request',
                            requesterId: this.windowId,
                            attempt: focusAttempts
                        });

                        // Retry if no response and attempts remaining
                        if (focusAttempts < maxAttempts) {
                            setTimeout(attemptFocus, 800);
                        }
                    };

                    this.broadcastChannel.addEventListener('message', handleResponse);

                    // Start focus attempts
                    attemptFocus();

                    // Final timeout after all attempts
                    setTimeout(() => {
                        if (!responseReceived) {
                            responseReceived = true;
                            this.broadcastChannel.removeEventListener('message', handleResponse);
                            resolve(false);
                        }
                    }, 3000);
                });
            }

            async showSessionTakeoverDialog() {
                return new Promise((resolve) => {
                    const modal = this.createProfessionalModal();
                    document.body.appendChild(modal);

                    modal.querySelector('.session-takeover-btn').onclick = () => {
                        document.body.removeChild(modal);
                        resolve(true);
                    };

                    modal.querySelector('.session-close-btn').onclick = () => {
                        document.body.removeChild(modal);
                        resolve(false);
                    };
                });
            }

            createProfessionalModal() {
                const modal = document.createElement('div');
                modal.style.cssText = `
                    position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                    background: rgba(0, 0, 0, 0.8); z-index: 10000;
                    display: flex; align-items: center; justify-content: center;
                    font-family: 'Orbitron', monospace;
                `;

                modal.innerHTML = `
                    <div style="
                        background: linear-gradient(135deg, #1a1a2e, #16213e);
                        border: 2px solid #00d4ff;
                        border-radius: 15px;
                        padding: 30px;
                        max-width: 500px;
                        text-align: center;
                        box-shadow: 0 20px 60px rgba(0, 212, 255, 0.3);
                    ">
                        <div style="color: #00d4ff; font-size: 24px; margin-bottom: 20px;">
                            🔒 DEEPLICA Security Notice
                        </div>
                        <div style="color: white; margin-bottom: 25px; line-height: 1.6;">
                            DeepChat is already active in another browser tab/window.<br><br>
                            For security and data integrity, only one session is permitted per user.<br><br>
                            <strong>Choose your action:</strong>
                        </div>
                        <div style="display: flex; gap: 15px; justify-content: center;">
                            <button class="session-takeover-btn" style="
                                background: linear-gradient(45deg, #00d4ff, #0066ff);
                                color: white; border: none; padding: 12px 24px;
                                border-radius: 8px; cursor: pointer; font-weight: bold;
                                transition: all 0.3s ease;
                            ">
                                🔄 Take Over Session
                            </button>
                            <button class="session-close-btn" style="
                                background: linear-gradient(45deg, #e74c3c, #c0392b);
                                color: white; border: none; padding: 12px 24px;
                                border-radius: 8px; cursor: pointer; font-weight: bold;
                                transition: all 0.3s ease;
                            ">
                                ❌ Close This Window
                            </button>
                        </div>
                    </div>
                `;

                return modal;
            }

            registerSession() {
                const sessions = this.getActiveSessions();
                sessions.push({
                    windowId: this.windowId,
                    timestamp: Date.now(),
                    lastHeartbeat: Date.now(),
                    url: window.location.href
                });

                localStorage.setItem(this.tabKey, JSON.stringify(sessions));
                localStorage.setItem(this.sessionKey, this.windowId);
            }

            startHeartbeat() {
                this.heartbeatInterval = setInterval(() => {
                    this.updateHeartbeat();
                }, 5000);
            }

            updateHeartbeat() {
                try {
                    const sessions = JSON.parse(localStorage.getItem(this.tabKey) || '[]');
                    const sessionIndex = sessions.findIndex(s => s.windowId === this.windowId);

                    if (sessionIndex !== -1) {
                        sessions[sessionIndex].lastHeartbeat = Date.now();
                        localStorage.setItem(this.tabKey, JSON.stringify(sessions));
                    }
                } catch (error) {
                    console.error('Heartbeat update failed:', error);
                }
            }

            startConflictMonitoring() {
                this.conflictCheckInterval = setInterval(() => {
                    const currentSession = localStorage.getItem(this.sessionKey);
                    if (currentSession && currentSession !== this.windowId) {
                        this.handleSessionConflict();
                    }
                }, 3000);
            }

            handleSessionConflict() {
                this.showProfessionalMessage('Session transferred to another window. Closing for security.');
                setTimeout(() => window.close(), 2000);
            }

            handleBroadcastMessage(event) {
                const { type, requesterId, attempt, session_id, windowId } = event.data;

                switch (type) {
                    case 'focus_request':
                        // Enhanced focus request handling with session validation
                        if (session_id === sessionId && this.sessionRegistered && !this.isClosing && requesterId !== this.windowId) {
                            console.log('🎯 Focusing existing session tab');

                            let focusSuccess = false;
                            try {
                                // Method 1: Standard window focus
                                window.focus();

                                // Method 2: Bring to front if available
                                if (window.parent && window.parent !== window) {
                                    window.parent.focus();
                                }

                                // Method 3: Try to make window visible
                                if (document.hidden) {
                                    // Page is hidden, try to make it visible
                                    document.title = '🔔 DeepChat - Click to Switch!';
                                } else {
                                    // Page is visible, focus worked
                                    focusSuccess = true;
                                    document.title = 'DeepChat - DEEPLICA AI Assistant';
                                }

                        // Method 4: Visual indication
                        this.showProfessionalMessage(`Focus request ${attempt || 1} - Bringing window to front...`);

                        // Method 5: Try to scroll to top to trigger visibility
                        window.scrollTo(0, 0);

                        focusSuccess = true;

                    } catch (error) {
                        console.log('Focus attempt failed:', error);
                    }

                    // Send response
                    if (this.broadcastChannel) {
                        this.broadcastChannel.postMessage({
                            type: 'focus_response',
                            success: focusSuccess,
                            responderId: this.windowId,
                            attempt: attempt || 1
                        });
                    }
                }

                if (type === 'session_invalidated' && event.data.newSessionId !== this.windowId) {
                    // Another session is taking over
                    this.showProfessionalMessage('Session moved to another window. Closing...');
                    setTimeout(() => window.close(), 2000);
                }
            }

            invalidateOtherSessions() {
                if (this.broadcastChannel) {
                    this.broadcastChannel.postMessage({
                        type: 'session_invalidated',
                        newSessionId: this.windowId
                    });
                }
            }

            setupCleanupHandlers() {
                window.addEventListener('beforeunload', () => this.cleanup());
                window.addEventListener('unload', () => this.cleanup());

                // Handle page visibility changes
                document.addEventListener('visibilitychange', () => {
                    if (document.visibilityState === 'visible') {
                        this.updateHeartbeat();
                        // Reset title when page becomes visible
                        document.title = 'DeepChat - DEEPLICA AI Assistant';

                        // Check if we're still the active session
                        const currentSession = localStorage.getItem(this.sessionKey);
                        if (currentSession && currentSession !== this.windowId) {
                            this.showProfessionalMessage('Another DeepChat session is active. This window will close.');
                            setTimeout(() => window.close(), 3000);
                        }
                    }
                });

                // Handle window focus events
                window.addEventListener('focus', () => {
                    this.updateHeartbeat();
                    document.title = 'DeepChat - DEEPLICA AI Assistant';
                });

                // Handle storage changes (when another tab modifies session)
                window.addEventListener('storage', (event) => {
                    if (event.key === this.sessionKey && event.newValue && event.newValue !== this.windowId) {
                        this.showProfessionalMessage('Session transferred to another window.');
                        setTimeout(() => window.close(), 2000);
                    }
                });
            }

            cleanup() {
                try {
                    // Remove this session from registry
                    const sessions = JSON.parse(localStorage.getItem(this.tabKey) || '[]');
                    const filteredSessions = sessions.filter(s => s.windowId !== this.windowId);
                    localStorage.setItem(this.tabKey, JSON.stringify(filteredSessions));

                    // Clear session key if this was the active session
                    if (localStorage.getItem(this.sessionKey) === this.windowId) {
                        localStorage.removeItem(this.sessionKey);
                    }

                    // Clear intervals
                    if (this.heartbeatInterval) clearInterval(this.heartbeatInterval);
                    if (this.conflictCheckInterval) clearInterval(this.conflictCheckInterval);

                    // Close broadcast channel
                    if (this.broadcastChannel) this.broadcastChannel.close();
                } catch (error) {
                    console.error('Cleanup error:', error);
                }
            }

            showProfessionalMessage(message) {
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed; top: 20px; right: 20px; z-index: 10001;
                    background: linear-gradient(135deg, #1a1a2e, #16213e);
                    color: white; padding: 15px 25px; border-radius: 10px;
                    border: 2px solid #00d4ff; font-family: 'Orbitron', monospace;
                    box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
                    max-width: 300px; font-size: 14px;
                `;
                notification.textContent = message;
                document.body.appendChild(notification);

                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 3000);
            }
        }

        // Initialize session manager
        const sessionManager = new DeepChatSessionManager();

        // Register browser session for single session enforcement
        async function initializeSecureSession() {
            console.log('🔐 Initializing secure single session...');

            const registered = await sessionManager.registerBrowserSession();
            if (!registered) {
                console.warn('🚫 Failed to register browser session');
                return false;
            }

            console.log('✅ Secure session initialized successfully');
            return true;
        }

        // Initialize header video
        function initializeHeaderVideo() {
            const headerVideo = document.querySelector('.header-deeplica-video');
            const headerVideoContainer = document.querySelector('.header-video-container');

            if (headerVideo) {
                console.log('🎬 Initializing header video...');

                // Add event listeners for video
                headerVideo.addEventListener('loadstart', function() {
                    console.log('🎬 Header video loading started');
                });

                headerVideo.addEventListener('canplay', function() {
                    console.log('🎬 Header video can play');
                    headerVideo.play().catch(e => {
                        console.log('🎬 Header video autoplay prevented:', e);
                        // Try to play on user interaction
                        document.addEventListener('click', function playOnClick() {
                            headerVideo.play().catch(console.log);
                            document.removeEventListener('click', playOnClick);
                        }, { once: true });
                    });
                });

                headerVideo.addEventListener('error', function(e) {
                    console.error('🎬 Header video error:', e);
                    // Show fallback
                    if (headerVideoContainer) {
                        headerVideoContainer.innerHTML = '<div class="header-robot-fallback">🤖</div>';
                    }
                });

                headerVideo.addEventListener('loadeddata', function() {
                    console.log('🎬 Header video loaded successfully');
                });

                // Force load the video
                headerVideo.load();
            } else {
                console.warn('🎬 Header video element not found');
            }
        }

        // Prevent multiple DeepChat tabs
        function preventMultipleTabs() {
            const deepchatId = 'deeplica_deepchat_main';

            // Check if another DeepChat tab is already open
            if (localStorage.getItem(deepchatId)) {
                // Try to focus existing tab
                localStorage.setItem(deepchatId + '_focus', Date.now());

                // Close this tab after a short delay if another exists
                setTimeout(() => {
                    if (localStorage.getItem(deepchatId) !== sessionStorage.getItem('deepchat_session_id')) {
                        window.close();
                    }
                }, 1000);
            }

            // Register this tab (use server-provided session ID)
            const tabSessionId = Date.now() + '_' + Math.random();
            sessionStorage.setItem('deepchat_session_id', tabSessionId);
            localStorage.setItem(deepchatId, tabSessionId);

            // Listen for focus requests from other tabs
            window.addEventListener('storage', (e) => {
                if (e.key === deepchatId + '_focus') {
                    window.focus();
                }
            });

            // Clean up when tab closes
            window.addEventListener('beforeunload', () => {
                if (localStorage.getItem(deepchatId) === sessionStorage.getItem('deepchat_session_id')) {
                    localStorage.removeItem(deepchatId);
                }
            });
        }

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', async function() {
                console.log('🚀 Chat page DOM loaded - initializing...');

                // Initialize chat functionality FIRST (essential for user interaction)
                initializeHeaderVideo();
                initializeChat();
                initializeChatDateTime();
                loadFontPreference();

                console.log('✅ Chat functionality initialized');

                // Initialize admin button functionality
                setTimeout(() => {
                    const adminButton = document.getElementById('adminButton');
                    if (adminButton) {
                        console.log('⚙️ Admin panel access ready');
                    }
                }, 1000);

                // Initialize security features AFTER chat is working
                try {
                    console.log('🔒 Initializing security features...');

                    // Tab verification (non-blocking)
                    if (!verifyTabToken()) {
                        console.warn('🔒 Tab token verification failed - but chat remains functional');
                        // Don't block chat functionality, just log the issue
                    } else {
                        console.log('✅ Tab token verified');
                    }

                    // Session enforcement (non-blocking)
                    if (!(await initializeSecureSession())) {
                        console.warn('🔒 Session enforcement failed - but chat remains functional');
                        // Don't block chat functionality
                    } else {
                        console.log('✅ Secure session initialized');
                    }
                } catch (error) {
                    console.error('🔒 Security initialization error (non-blocking):', error);
                    // Don't let security errors break chat functionality
                }
            });
        } else {
            (async () => {
                console.log('🚀 Chat page already loaded - initializing...');

                // Initialize chat functionality FIRST (essential for user interaction)
                initializeHeaderVideo();
                initializeChat();
                initializeChatDateTime();
                loadFontPreference();

                console.log('✅ Chat functionality initialized');

                // Initialize security features AFTER chat is working (non-blocking)
                try {
                    console.log('🔒 Initializing security features...');

                    // Tab verification (non-blocking)
                    if (!verifyTabToken()) {
                        console.warn('🔒 Tab token verification failed - but chat remains functional');
                    } else {
                        console.log('✅ Tab token verified');
                    }

                    // Session enforcement (non-blocking)
                    if (!(await initializeSecureSession())) {
                        console.warn('🔒 Session enforcement failed - but chat remains functional');
                    } else {
                        console.log('✅ Secure session initialized');
                    }
                } catch (error) {
                    console.error('🔒 Security initialization error (non-blocking):', error);
                    // Don't let security errors break chat functionality
                }
            })();
        }
        
        console.log('✅ Deeplica Chat script loaded successfully');
    </script>
</body>
</html>
