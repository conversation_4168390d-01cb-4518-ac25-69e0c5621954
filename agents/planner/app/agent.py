"""
Planner Agent - Handles mission planning and task generation.
Adapted from backend PlannerAgent for microservice architecture.
"""

import httpx
from typing import Dict, Any, Optional, List
from shared.unified_logging import get_planner_logger

# Initialize unified logger
logger = get_planner_logger()

from .models import TaskRequest, TaskResponse
from shared_models import TaskSuggestion
from .llm_service import BackendLLMClient


class PlannerAgent:
    """
    Agent responsible for mission planning and task generation.

    Creates comprehensive mission plans and generates task graphs
    for mission execution.
    """

    def __init__(self, llm_service: BackendLLMClient):
        self.llm_service = llm_service
        self.agent_name = "planner_agent"
        self.http_client = httpx.AsyncClient(timeout=30.0)
    
    async def execute_task_async(self, request: TaskRequest) -> None:
        """
        Execute a planning task asynchronously and callback to dispatcher.
        This is the main entry point for the microservice.
        """
        logger.info("🧠 Executing planning task: {request.task_id}", module="PLANNER-AGENT", routine="execute_task_async")
        
        try:
            # Execute the planning task
            result = await self._execute_planning_task(request)
            
            # Send success callback to dispatcher
            await self._send_callback(request, "completed", result)
            
            logger.info("✅ Planning task {request.task_id} completed successfully", module="PLANNER-AGENT", routine="execute_task_async")
            
        except Exception as e:
            logger.error("❌ Failed to execute planning task {request.task_id}: {e}", module="PLANNER-AGENT", routine="execute_task_async")
            
            # Send failure callback
            await self._send_callback(
                request, 
                "failed", 
                {}, 
                error_message=str(e)
            )
    
    async def _execute_planning_task(self, request: TaskRequest) -> Dict[str, Any]:
        """Execute the actual planning logic"""

        # Use unified mission context
        mission_context = request.mission_context
        user_input = mission_context.user_input

        # Debug logging to see what we're getting
        logger.debug("🔍 Task data: {request.task_data}", module="PLANNER-AGENT", routine="_execute_planning_task")
        logger.debug("🔍 Mission context: {mission_context}", module="PLANNER-AGENT", routine="_execute_planning_task")
        logger.debug("🔍 User input from mission context: '{user_input}'", module="PLANNER-AGENT", routine="_execute_planning_task")
        logger.info("🧠 Planning mission for input: {user_input[:100] if user_input else 'EMPTY'}...", module="main", routine="unknown")

        try:

            # Ensure we have valid user input
            if not user_input or user_input.strip() == "":
                raise ValueError(f"No user input found for mission {request.mission_id}. Task data: {request.task_data}")

            # Generate mission metadata
            mission_metadata = await self._generate_mission_metadata(user_input)

            # Generate tasks for the mission (without storing them)
            tasks = await self._generate_tasks(
                user_input,
                mission_metadata,
                request.mission_id
            )

            logger.info("✅ Mission plan created with {len(tasks)} tasks", module="PLANNER-AGENT", routine="_execute_planning_task")

            return {
                "planning_result": "success",
                "mission_metadata": mission_metadata,
                "tasks_created": len(tasks),
                "suggested_tasks": tasks,  # Return TaskSuggestion objects
                "task_ids": [task.task_id for task in tasks]
            }

        except Exception as e:
            logger.error("❌ Failed to create mission plan: {e}", module="PLANNER-AGENT", routine="_execute_planning_task")
            raise
    
    async def _generate_mission_metadata(self, user_input: str) -> Dict[str, Any]:
        """Generate mission title, description, and priority"""

        system_prompt = """You are a mission planning assistant. Your job is to analyze user requests and generate appropriate metadata.

Generate a JSON response with:
- title: A concise, descriptive title (max 60 chars)
- description: A detailed description of what needs to be accomplished
- priority: One of "low", "normal", "high", "urgent"

Consider the complexity, time sensitivity, and importance when setting priority."""

        user_prompt = f"""Analyze this user request and generate mission metadata:

User Request: "{user_input}"

Respond with JSON containing title, description, and priority."""
        
        response = await self.llm_service.generate_response(
            prompt=user_prompt,
            system_prompt=system_prompt,
            temperature=0.3,
            response_format="json"
        )
        
        return response["content"]

    async def _generate_tasks(
        self,
        user_input: str,
        mission_metadata: Dict[str, Any],
        mission_id: str
    ) -> List:
        """Generate tasks without storing them in the database"""

        # Generate task data using LLM
        task_data = await self._generate_task_data(user_input, mission_metadata, mission_id)

        # Validate LLM response structure
        if not isinstance(task_data, dict) or "tasks" not in task_data:
            logger.error("❌ Invalid LLM response structure: {task_data}", module="PLANNER-AGENT", routine="_generate_tasks")
            raise ValueError(f"LLM response missing 'tasks' key. Got: {type(task_data)} with keys: {list(task_data.keys()) if isinstance(task_data, dict) else 'not a dict'}")

        # Convert to TaskSuggestion objects for unified interface
        tasks = []
        for task_dict in task_data["tasks"]:
            try:
                # Make task ID unique by prefixing with mission ID
                unique_task_id = f"{mission_id}_{task_dict['task_id']}"

                # Replace MISSION_ID placeholders in the task data
                task_dict = self._replace_mission_id_placeholders(task_dict, mission_id)

                # Prepare context with task-specific data
                context = {}

                # For phone call tasks, include phone-specific fields in context
                if task_dict["task_type"] == "phone_call":
                    phone_fields = ["phone_number", "question", "conversation_script", "language", "contact_name", "max_duration_minutes"]
                    for field in phone_fields:
                        if field in task_dict:
                            context[field] = task_dict[field]

                    # Handle context field specially to avoid naming conflict
                    if "context" in task_dict:
                        context["context"] = task_dict["context"]

                    # Set default contact name if not provided
                    if "contact_name" not in context:
                        context["contact_name"] = "Unknown"

                # Replace MISSION_ID placeholders with actual mission ID
                context = self._resolve_mission_id_placeholders(context, mission_id)

                # Check if task contains parameter placeholders
                has_placeholders = self._has_placeholders(context)

                # Create TaskSuggestion object
                task_suggestion = TaskSuggestion(
                    task_id=unique_task_id,
                    task_type=task_dict["task_type"],
                    description=task_dict["description"],
                    prompt=task_dict["prompt"],
                    parent_tasks=[f"{mission_id}_{parent}" for parent in task_dict.get("parent_tasks", [])],
                    child_tasks=[f"{mission_id}_{child}" for child in task_dict.get("child_tasks", [])],
                    assigned_agent=None,  # Let dispatcher determine agent based on task type
                    context=context,
                    has_placeholders=has_placeholders
                )

                tasks.append(task_suggestion)
                logger.info("✅ Task suggestion prepared: {unique_task_id} (type: {task_dict['task_type']})", module="main", routine="unknown")

            except Exception as e:
                logger.error("❌ Failed to prepare task suggestion {task_dict.get('task_id', 'unknown')}: {e}", module="PLANNER-AGENT", routine="_generate_tasks")
                raise

        # Validate task structure
        self._validate_task_data(tasks)

        return tasks

    async def _generate_task_data(self, user_input: str, mission_metadata: Dict[str, Any], mission_id: str) -> Dict[str, Any]:
        """Generate task data using LLM"""

        system_prompt = """You are an expert task decomposition agent. Break down user requests into executable tasks.

CRITICAL RULES - NEVER VIOLATE THESE:
1. NEVER ask for user input, preferences, or clarifications
2. NEVER include "requires_user_input": true in any response
3. ALWAYS create concrete, executable tasks immediately
4. Extract phone numbers and names directly from user input
5. Create phone_call tasks for any phone-related requests
6. Use parameter placeholders for information flow between tasks

TASK CREATION RULES:
1. Create EFFICIENT task flows - no back-and-forth questioning
2. Extract all information directly from the user's request
3. Each task should be immediately executable
4. Create logical dependencies between tasks using parent_tasks/child_tasks

TASK STRUCTURE:
1. Create a task graph as a JSON object with a "tasks" array
2. Each task must have: task_id, task_type, description, prompt, parent_tasks, child_tasks
3. Task types: "dialogue", "information_gathering", "phone_call", "decision_making", "external_interaction", "execution"
4. For v0: Focus on "dialogue", "information_gathering", and "phone_call" tasks
5. Create logical dependencies between tasks using parent_tasks/child_tasks

PHONE CALL TASKS:
- Use "phone_call" task type when you need to contact someone by phone to get specific information OR deliver a specific message
- Phone tasks require: phone_number, question OR conversation_script, context, language (optional, defaults to English)
- For "say X" requests: Use conversation_script field with the exact text to be spoken
- For questions: Use question field to ask for information
- Phone calls are useful for: restaurant reservations, appointment scheduling, business inquiries, service requests, delivering messages
- Always provide clear context and specific questions for phone calls
- IMPORTANT: Extract actual phone numbers from user input when provided (e.g., "+972542133614", "call 555-1234")
- If no phone number in user input, DO NOT create phone call tasks - ask user for phone number instead
- NEVER use "user_will_provide" as a phone number - this will cause task failures
- For repetitive messages (like "say EEL-EEL 5 times"), use conversation_script with the exact repetition

Example task structure:
{
  "tasks": [
    {
      "task_id": "task_1",
      "task_type": "information_gathering",
      "description": "Gather user preferences",
      "prompt": "Collect information about user preferences for...",
      "parent_tasks": [],
      "child_tasks": ["task_2"]
    },
    {
      "task_id": "task_2",
      "task_type": "phone_call",
      "description": "Call restaurant to check availability",
      "prompt": "Call the restaurant to ask about table availability for tonight",
      "phone_number": "+1234567890",
      "question": "Do you have a table available for 4 people tonight at 7 PM?",
      "context": "User wants to book dinner for 4 people",
      "parent_tasks": ["task_1"],
      "child_tasks": ["task_3"]
    },
    {
      "task_id": "task_3",
      "task_type": "dialogue",
      "description": "Report results to user",
      "prompt": "Inform the user about the restaurant availability and next steps",
      "parent_tasks": ["task_2"],
      "child_tasks": []
    }
  ]
}"""

        # Few-shot examples for better task generation
        examples = self._get_few_shot_examples()

        user_prompt = f"""Break down this user request into a CONSOLIDATED task graph:

User Request: "{user_input}"
Mission Description: "{mission_metadata['description']}"

{examples}

CRITICAL INSTRUCTIONS:
- DO NOT ask for user preferences or clarifications - create concrete executable tasks immediately
- Extract phone numbers directly from the user input (like "+972547000430")
- Extract contact names directly from the user input (like "Eran")
- Create phone_call tasks with specific phone numbers and questions
- Use parameter placeholders for information that flows between tasks
- NEVER respond with "requires_user_input": true - always create executable tasks

PHONE CALL TASK REQUIREMENTS:
- phone_number: Use exact number from user input or "user_will_provide" if not specified
- question: Create specific question based on user's request
- context: Explain what information is needed and why
- contact_name: Use name from user input if provided

PARAMETER PLACEHOLDERS:
- Use {{task:MISSION_ID_taskname:result.extracted_answer}} to reference results from previous tasks
- Replace MISSION_ID with the actual mission ID: {mission_id}

Now create a concrete task graph with executable phone_call tasks. Do not ask for clarifications."""

        response = await self.llm_service.generate_response(
            prompt=user_prompt,
            system_prompt=system_prompt,
            temperature=0.4,
            response_format="json"
        )

        return response["content"]

    def _get_few_shot_examples(self) -> str:
        """Get few-shot examples for task planning"""
        return """
EXAMPLE 1 - Restaurant Booking (WITH PHONE CALL):
User Request: "Help me book a table for 8 people tonight"

{
  "tasks": [
    {
      "task_id": "gather_preferences_and_search",
      "task_type": "dialogue",
      "description": "Gather essential dining preferences and provide restaurant options",
      "prompt": "Help the user plan their dinner for 8 people tonight. Ask about their key preferences: cuisine type, budget range, preferred time, and location area. Then research and present 3-4 suitable restaurant options with contact information for booking.",
      "parent_tasks": [],
      "child_tasks": ["call_restaurant"]
    },
    {
      "task_id": "call_restaurant",
      "task_type": "phone_call",
      "description": "Call chosen restaurant to make reservation",
      "prompt": "Call the restaurant to make a reservation for 8 people tonight",
      "phone_number": "user_will_provide",
      "question": "I'd like to make a reservation for 8 people tonight. What times do you have available?",
      "context": "User needs dinner reservation for 8 people tonight. Get available times and confirm booking details.",
      "parent_tasks": ["gather_preferences_and_search"],
      "child_tasks": ["confirm_booking"]
    },
    {
      "task_id": "confirm_booking",
      "task_type": "dialogue",
      "description": "Confirm booking details with user",
      "prompt": "Share the restaurant's response about availability and help finalize the reservation details with the user.",
      "parent_tasks": ["call_restaurant"],
      "child_tasks": []
    }
  ]
}

EXAMPLE 2 - Cable Bill Negotiation (CONSOLIDATED APPROACH):
User Request: "Help me negotiate my cable bill"

{
  "tasks": [
    {
      "task_id": "analyze_and_strategize",
      "task_type": "dialogue",
      "description": "Gather account info and provide negotiation strategy",
      "prompt": "Help the user negotiate their cable bill. Ask for their current provider, bill amount, services, and customer tenure. Then research competitor rates and provide a comprehensive negotiation strategy with talking points. Work with whatever information they provide and make reasonable assumptions to deliver actionable advice.",
      "parent_tasks": [],
      "child_tasks": []
    }
  ]
}

EXAMPLE 3 - Doctor Appointment (PHONE CALL EXAMPLE):
User Request: "Help me schedule a doctor appointment for next week"

{
  "tasks": [
    {
      "task_id": "gather_appointment_details",
      "task_type": "dialogue",
      "description": "Gather appointment requirements and doctor information",
      "prompt": "Help the user schedule a doctor appointment. Ask for: doctor's name/clinic, preferred days/times next week, reason for visit (if comfortable sharing), and any scheduling preferences. Get the clinic's phone number.",
      "parent_tasks": [],
      "child_tasks": ["call_clinic"]
    },
    {
      "task_id": "call_clinic",
      "task_type": "phone_call",
      "description": "Call clinic to schedule appointment",
      "prompt": "Call the medical clinic to schedule an appointment for next week",
      "phone_number": "user_will_provide",
      "question": "I'd like to schedule an appointment for next week. What availability do you have?",
      "context": "Patient needs appointment next week. Get available slots and scheduling requirements.",
      "parent_tasks": ["gather_appointment_details"],
      "child_tasks": ["confirm_appointment"]
    },
    {
      "task_id": "confirm_appointment",
      "task_type": "dialogue",
      "description": "Confirm appointment details with user",
      "prompt": "Share the clinic's available appointment times and help the user confirm their preferred slot.",
      "parent_tasks": ["call_clinic"],
      "child_tasks": []
    }
  ]
}

EXAMPLE 4 - Direct Phone Call (WITH PHONE NUMBER):
User Request: "call +972542133614 and ask Oren what is his last name"

{
  "tasks": [
    {
      "task_id": "make_phone_call",
      "task_type": "phone_call",
      "description": "Call Oren to ask for his last name",
      "prompt": "Call Oren at the provided number to ask for his last name",
      "phone_number": "+972542133614",
      "question": "Hi Oren, what is your last name?",
      "context": "User wants to know Oren's last name. Make a direct phone call to ask this question.",
      "contact_name": "Oren",
      "parent_tasks": [],
      "child_tasks": ["report_result"]
    },
    {
      "task_id": "report_result",
      "task_type": "dialogue",
      "description": "Report the phone call result to user",
      "prompt": "Share Oren's response about his last name with the user.",
      "parent_tasks": ["make_phone_call"],
      "child_tasks": []
    }
  ]
}

EXAMPLE 4 - Phone Call with Script (SAY SPECIFIC TEXT):
User Request: "call +972547000430 and say EEL-EEL 5 times"

{
  "tasks": [
    {
      "task_id": "call_and_say_eel",
      "task_type": "phone_call",
      "description": "Call and say EEL-EEL 5 times",
      "prompt": "Call the number and say 'EEL-EEL' exactly 5 times",
      "phone_number": "+972547000430",
      "conversation_script": "EEL-EEL. EEL-EEL. EEL-EEL. EEL-EEL. EEL-EEL.",
      "context": "User requested to call and say 'EEL-EEL' exactly 5 times.",
      "contact_name": "Unknown",
      "parent_tasks": [],
      "child_tasks": ["report_completion"]
    },
    {
      "task_id": "report_completion",
      "task_type": "dialogue",
      "description": "Report that the call was completed",
      "prompt": "Inform the user that the phone call was made and EEL-EEL was said 5 times.",
      "parent_tasks": ["call_and_say_eel"],
      "child_tasks": []
    }
  ]
}

EXAMPLE 4 - Dinner Planning (BALANCED APPROACH):
User Request: "plan my dinner"

{
  "tasks": [
    {
      "task_id": "gather_preferences_and_plan",
      "task_type": "dialogue",
      "description": "Gather key preferences and create dinner plan",
      "prompt": "Help the user plan their dinner tonight. Ask about their essential preferences: any dietary restrictions, preferred cuisine type, available cooking time, and major likes/dislikes. Then recommend a specific recipe with ingredients list and cooking instructions. Focus on key information that affects the recipe choice, and use reasonable defaults for minor details.",
      "parent_tasks": [],
      "child_tasks": []
    }
  ]
}

EXAMPLE 5 - Chain Phone Calls (WITH PARAMETER PLACEHOLDERS):
User Request: "Call Eran to get Oren's number and then call Oren to tell him about the meeting tomorrow"

{
  "tasks": [
    {
      "task_id": "call_eran",
      "task_type": "phone_call",
      "description": "Call Eran to get Oren's phone number",
      "prompt": "Call Eran to ask for Oren's phone number",
      "phone_number": "user_will_provide",
      "question": "Hi Eran, can you give me Oren's phone number? I need to call him about something.",
      "context": "Need to get Oren's contact information from Eran for a follow-up call.",
      "contact_name": "Eran",
      "parent_tasks": [],
      "child_tasks": ["call_oren"]
    },
    {
      "task_id": "call_oren",
      "task_type": "phone_call",
      "description": "Call Oren to tell him about the meeting tomorrow",
      "prompt": "Call Oren to inform him about the meeting tomorrow",
      "phone_number": "{{task:MISSION_ID_call_eran:result.extracted_answer}}",
      "question": "Hi Oren, I wanted to let you know about the meeting tomorrow. Are you available?",
      "context": "Inform Oren about tomorrow's meeting and confirm his availability.",
      "contact_name": "Oren",
      "parent_tasks": ["call_eran"],
      "child_tasks": []
    }
  ]
}

IMPORTANT: Replace MISSION_ID with the actual mission ID in placeholders. For example:
- "{{task:MISSION_ID_call_eran:result.extracted_answer}}" becomes "{{task:2c2cbbb6-7b56-44f8-9637-b08d700be1d2_call_eran:result.extracted_answer}}"
- This ensures each placeholder references the correct task within the mission

EXAMPLE 6 - Ask for Number and Tell Number (EXACT PATTERN MATCH):
User Request: "call +972547000430 and ask for a number, then call +972547000430 to tell that number"

{
  "tasks": [
    {
      "task_id": "call_ask_number",
      "task_type": "phone_call",
      "description": "Call to ask for a number",
      "prompt": "Call the provided number to ask for a number",
      "phone_number": "+972547000430",
      "question": "Hi, can you give me a number?",
      "context": "Need to get a number from the contact for use in a follow-up call.",
      "contact_name": "Contact",
      "parent_tasks": [],
      "child_tasks": ["call_tell_number"]
    },
    {
      "task_id": "call_tell_number",
      "task_type": "phone_call",
      "description": "Call to tell the number received",
      "prompt": "Call to inform about the number received from previous call",
      "phone_number": "+972547000430",
      "question": "The number is {{task:MISSION_ID_call_ask_number:extracted_answer}}",
      "context": "Tell the contact the number that was received from the previous call.",
      "contact_name": "Contact",
      "parent_tasks": ["call_ask_number"],
      "child_tasks": []
    }
  ]
}

EXAMPLE 7 - Three-Step Phone Chain (EXACT PATTERN MATCH):
User Request: "call me to +972547000430 and ask for Eran's number. then call Eran and tell him 'Please go home'. then call me back to confirm it was done."

{
  "tasks": [
    {
      "task_id": "call_for_eran_number",
      "task_type": "phone_call",
      "description": "Call to get Eran's phone number",
      "prompt": "Call the provided number to ask for Eran's phone number",
      "phone_number": "+972547000430",
      "question": "Hi, can you give me Eran's phone number? I need to contact him.",
      "context": "Need to get Eran's contact information for a follow-up call.",
      "contact_name": "Contact",
      "parent_tasks": [],
      "child_tasks": ["call_eran"]
    },
    {
      "task_id": "call_eran",
      "task_type": "phone_call",
      "description": "Call Eran with the message",
      "prompt": "Call Eran to deliver the message",
      "phone_number": "{{task:MISSION_ID_call_for_eran_number:extracted_answer}}",
      "question": "Hi Eran, please go home.",
      "context": "Deliver the specific message 'Please go home' to Eran.",
      "contact_name": "Eran",
      "parent_tasks": ["call_for_eran_number"],
      "child_tasks": ["call_back_confirm"]
    },
    {
      "task_id": "call_back_confirm",
      "task_type": "phone_call",
      "description": "Call back to confirm completion",
      "prompt": "Call back to confirm the message was delivered",
      "phone_number": "+972547000430",
      "question": "Hi, I've completed the task. I called Eran and delivered the message 'Please go home'.",
      "context": "Confirm with the original caller that the message was successfully delivered to Eran.",
      "contact_name": "Contact",
      "parent_tasks": ["call_eran"],
      "child_tasks": []
    }
  ]
}

KEY PRINCIPLES:
1. For simple requests: Create ONE comprehensive task that handles everything
2. For complex requests: if possible aggregate similar logical phases, not as many small steps
3. Each task should gather information AND provide solutions
4. Make reasonable assumptions for minor details
5. Use phone_call tasks when you need to contact businesses, services, or people by phone
6. Phone calls are ideal for: reservations, appointments, inquiries, bookings, service requests
7. Use parameter placeholders when a task needs information from a previous task:
   - Format: {{task:FULL_TASK_ID:result.path.to.value}}
   - Example: {{task:MISSION_ID_call_eran:result.extracted_answer}} to use the answer from call_eran task
   - The FULL_TASK_ID includes the mission prefix (e.g., 2c2cbbb6-7b56-44f8-9637-b08d700be1d2_call_eran)
   - Common paths: result.extracted_answer, result.phone_number, result.appointment_time
8. Always set proper parent_tasks dependencies when using placeholders
"""

    def _resolve_mission_id_placeholders(self, data: Any, mission_id: str) -> Any:
        """Replace MISSION_ID placeholders with actual mission ID"""
        import re
        import json

        if isinstance(data, dict):
            return {key: self._resolve_mission_id_placeholders(value, mission_id) for key, value in data.items()}
        elif isinstance(data, list):
            return [self._resolve_mission_id_placeholders(item, mission_id) for item in data]
        elif isinstance(data, str):
            # Replace MISSION_ID_ with actual mission ID
            return data.replace("MISSION_ID_", f"{mission_id}_")
        else:
            return data

    def _has_placeholders(self, data: Any) -> bool:
        """Check if data contains parameter placeholders"""
        import re
        import json

        # Convert to string for pattern matching
        data_str = json.dumps(data) if not isinstance(data, str) else data

        # Pattern for placeholder detection: {{task:task_id:path}}
        placeholder_pattern = r'\{\{task:[^:]+:[^}]+\}\}'

        return bool(re.search(placeholder_pattern, data_str))

    def _replace_mission_id_placeholders(self, task_dict: Dict[str, Any], mission_id: str) -> Dict[str, Any]:
        """Replace MISSION_ID placeholders with actual mission ID"""
        import json

        # Convert to JSON string for easy replacement
        task_json = json.dumps(task_dict)

        # Replace all occurrences of MISSION_ID with actual mission ID
        task_json = task_json.replace("MISSION_ID", mission_id)

        # Convert back to dictionary
        return json.loads(task_json)

    def _validate_task_data(self, tasks: List[Dict[str, Any]]) -> None:
        """Validate task data for consistency"""
        task_ids = set(task.task_id for task in tasks)

        for task in tasks:
            # Check parent task references
            for parent_id in task.parent_tasks:
                if parent_id not in task_ids:
                    raise ValueError(f"Task {task.task_id} references non-existent parent {parent_id}")

            # Check child task references
            for child_id in task.child_tasks:
                if child_id not in task_ids:
                    raise ValueError(f"Task {task.task_id} references non-existent child {child_id}")

        # Check for cycles (basic check)
        if self._has_cycles_in_task_suggestions(tasks):
            raise ValueError("Task graph contains cycles")

        logger.debug("✅ Task validation passed", module="PLANNER-AGENT", routine="_validate_task_data")

    def _has_cycles_in_task_suggestions(self, tasks: List[Dict[str, Any]]) -> bool:
        """Basic cycle detection in task suggestions list"""
        visited = set()
        rec_stack = set()

        # Create task lookup dict
        task_dict = {task.task_id: task for task in tasks}

        def dfs(task_id: str) -> bool:
            if task_id in rec_stack:
                return True
            if task_id in visited:
                return False

            visited.add(task_id)
            rec_stack.add(task_id)

            task = task_dict.get(task_id)
            if task:
                for child_id in task.child_tasks:
                    if dfs(child_id):
                        return True

            rec_stack.remove(task_id)
            return False

        for task in tasks:
            if task.task_id not in visited:
                if dfs(task.task_id):
                    return True

        return False

    async def _send_callback(
        self,
        request: TaskRequest,
        status: str,
        result: Dict[str, Any],
        error_message: Optional[str] = None
    ) -> None:
        """Send completion callback to dispatcher"""

        # Extract suggested tasks from result if present
        suggested_tasks = result.get("suggested_tasks", [])

        callback_data = TaskResponse(
            task_id=request.task_id,
            mission_id=request.mission_id,
            status=status,
            result=result,
            error_message=error_message,
            suggested_tasks=suggested_tasks if suggested_tasks else None
        )

        try:
            response = await self.http_client.post(
                request.callback_url,
                json=callback_data.model_dump(),
                timeout=10.0
            )
            response.raise_for_status()

            logger.info("✅ Callback sent to dispatcher: {request.task_id}", module="PLANNER-AGENT", routine="_send_callback")

        except Exception as e:
            logger.error("❌ Failed to send callback to dispatcher: {e}", module="PLANNER-AGENT", routine="_send_callback")
            # Don't raise - we don't want to fail the task because of callback issues
