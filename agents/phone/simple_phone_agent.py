#!/usr/bin/env python3
"""
🛡️ SIMPLE BULLETPROOF PHONE AGENT
A simplified, working phone agent that NEVER crashes.
"""

import os
import sys
import asyncio
import uvicorn
from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import Response
import traceback

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from shared.unified_logging import get_phone_logger
from shared.port_manager import DeepLicaPortManager

# Initialize logger
logger = get_phone_logger()

# Create FastAPI app
app = FastAPI(title="DEEPLICA Phone Agent", version="1.0.0")

# Global state
phone_agent = None
twilio_service = None

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        return {
            "status": "healthy",
            "service": "phone-agent",
            "message": "Phone Agent is running"
        }
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return {
            "status": "error",
            "service": "phone-agent", 
            "message": f"Error: {e}"
        }

@app.post("/call")
async def make_call(request: Request):
    """Make a phone call"""
    try:
        # Get request data
        data = await request.json()
        phone_number = data.get("phone_number")
        message = data.get("message", "Hello from DEEPLICA")
        
        logger.info(f"📞 Phone call request: {phone_number}")
        
        # For now, just return success (we'll implement Twilio later)
        return {
            "status": "success",
            "message": f"Call initiated to {phone_number}",
            "phone_number": phone_number,
            "call_message": message
        }
        
    except Exception as e:
        logger.error(f"Call error: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {
            "status": "error",
            "message": f"Call failed: {e}"
        }

@app.post("/webhook/voice")
async def voice_webhook(request: Request):
    """Handle Twilio voice webhooks"""
    try:
        # Get form data from Twilio
        form_data = await request.form()
        call_sid = form_data.get("CallSid")
        
        logger.info(f"📞 Voice webhook: {call_sid}")
        
        # Return simple TwiML response
        twiml = """<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say>Hello from DEEPLICA Phone Agent. This is a test call.</Say>
</Response>"""
        
        return Response(content=twiml, media_type="application/xml")
        
    except Exception as e:
        logger.error(f"Voice webhook error: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        
        # Return error TwiML
        twiml = """<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say>Sorry, there was an error processing your call.</Say>
</Response>"""
        
        return Response(content=twiml, media_type="application/xml")

async def main():
    """Main function - simple and bulletproof"""
    try:
        logger.info("🚀 Starting Simple Phone Agent")
        
        # Get port from port manager
        port_mgr = DeepLicaPortManager("phone")
        port = port_mgr.allocate_port("phone")

        # If port allocation fails, use a fallback port
        if port is None:
            port = 8005  # Use a different port as fallback
            logger.warning(f"Port manager failed, using fallback port {port}")
        
        logger.info(f"📞 Phone Agent starting on port {port}")
        
        # Start the server
        config = uvicorn.Config(
            app=app,
            host="0.0.0.0",
            port=port,
            log_level="error",
            access_log=False
        )
        
        server = uvicorn.Server(config)
        await server.serve()
        
    except Exception as e:
        logger.error(f"💥 Phone Agent error: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        
        # Wait and retry
        await asyncio.sleep(5)
        logger.info("🔄 Restarting Phone Agent...")
        await main()  # Restart

if __name__ == "__main__":
    try:
        logger.info("🚀 SIMPLE PHONE AGENT STARTING")
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("🛑 Phone Agent stopped by user")
    except Exception as e:
        logger.error(f"💥 Fatal error: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
