{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "🚀 FULL DEEPLICA SYSTEM",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/start_deeplica_manual.py",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        },
        {
            "name": "🔍 Show Processes",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/show_processes.py",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        },
        {
            "name": "🔧 Fix Dependencies",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/fix_dependencies.py",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        },
        {
            "name": "🧹 Clean Duplicates (MANUAL ONLY)",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/cleanup_duplicates.py",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        },
        {
            "name": "🚀 START DEEPLICA (Manual Guide - RECOMMENDED)",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/start_deeplica_manual.py",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        },
        {
            "name": "🔧 START DEEPLICA (Guided Orchestrator)",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/startup_orchestrator.py",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "justMyCode": false,
            "stopOnEntry": false,
            "showReturnValue": false
        },
        {
            "name": "🌐 START DeepChat Browser",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/open_deepchat.py",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        },
        {
            "name": "🛑 STOP DEEPLICA",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/cleanup_residuals.py",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        },
        {
            "name": "🛡️ CLEANUP: Kill All DEEPLICA Processes (Auto)",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/cleanup_deeplica_processes.py",
            "args": ["--yes", "--force"],
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "justMyCode": false
        },
        {
            "name": "🛡️ CLEANUP: Interactive Process Cleanup",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/cleanup_deeplica_processes.py",
            "args": [],
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "justMyCode": false
        },
        {
            "name": "────────── DEEPLICA SERVICES ──────────",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/dummy.py"
        },
        {
            "name": "🐕 Watchdog Service",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/watchdog/main.py",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "SERVICE_NAME": "WATCHDOG"
            },
            "presentation": {
                "group": "DEEPLICA-SERVICES",
                "order": 1
            }
        },
        {
            "name": "🌐 Backend API Service",
            "type": "debugpy",
            "request": "launch",
            "module": "backend.app.main",
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "PORT": "8888",
                "SERVICE_NAME": "BACKEND-API"
            },
            "presentation": {
                "group": "DEEPLICA-SERVICES",
                "order": 2
            }
            // Backend API port managed by port_manager.py - ALL ports are configurable
        },
        {
            "name": "🎯 Dispatcher Service",
            "type": "debugpy",
            "request": "launch",
            "module": "dispatcher.app.main",
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "WAIT_FOR_BACKEND": "true",
                "SERVICE_NAME": "DISPATCHER"
            },
            "presentation": {
                "group": "DEEPLICA-SERVICES",
                "order": 3
            }
        },
        {
            "name": "💬 Dialogue Agent Service",
            "type": "debugpy",
            "request": "launch",
            "module": "agents.dialogue.app.main",
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "WAIT_FOR_BACKEND": "true",
                "SERVICE_NAME": "DIALOGUE-AGENT"
            },
            "presentation": {
                "group": "DEEPLICA-SERVICES",
                "order": 4
            }
        },
        {
            "name": "🧠 Planner Agent Service",
            "type": "debugpy",
            "request": "launch",
            "module": "agents.planner.app.main",
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "WAIT_FOR_BACKEND": "true",
                "SERVICE_NAME": "PLANNER-AGENT"
            },
            "presentation": {
                "group": "DEEPLICA-SERVICES",
                "order": 5
            }
        },
        {
            "name": "📞 Phone Agent Service",
            "type": "debugpy",
            "request": "launch",
            "module": "agents.phone.app.main",
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "WAIT_FOR_BACKEND": "true",
                "SERVICE_NAME": "PHONE-AGENT"
            },
            "presentation": {
                "group": "DEEPLICA-SERVICES",
                "order": 6
            }
        },
        {
            "name": "🖥️ CLI Terminal Service",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/cli/main.py",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "SERVICE_NAME": "CLI-TERMINAL",
                "WAIT_FOR_BACKEND": "true"
            },
            "presentation": {
                "group": "DEEPLICA-SERVICES",
                "order": 7
            }
        },
        {
            "name": "🌐 Web Chat Service",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/web_chat/main.py",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "SERVICE_NAME": "WEB-CHAT",
                "WAIT_FOR_BACKEND": "false"
            },
            "presentation": {
                "group": "DEEPLICA-SERVICES",
                "order": 8
            }
        },
        {
            "name": "────────── EXTERNAL SERVICES ──────────",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/dummy.py"
        },
        {
            "name": "🌐 Ngrok Tunnel (Backend API)",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/start_ngrok.py",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "NGROK_PORT": "8080"
            }
            // Ngrok tunnel port managed by port_manager.py - ALL ports are configurable
        },
        {
            "name": "🌐 Ngrok Tunnel (Phone Webhooks)",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/start_ngrok.py",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "NGROK_PORT": "8004"
            }
            // Ngrok for Phone Agent - port managed by port_manager.py
        },
        {
            "name": "🔗 Webhook Server (Twilio)",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/webhook_server.py",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        },
        {
            "name": "────────── TEST OPTIONS ──────────",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/dummy.py"
        },
        {
            "name": "🧪 Test Python Environment",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/test_python_path.py",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        },
        {
            "name": "🗄️ Test MongoDB Connection",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/test_mongodb.py",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        },
        {
            "name": "🔍 Check System Shells",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/check_shells.py",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        }
    ],
    "compounds": [
        {
            "name": "🚀 FULL DEEPLICA SYSTEM (Optimized Order)",
            "configurations": [
                "🐕 Watchdog Service",
                "🌐 Backend API Service",
                "🎯 Dispatcher Service",
                "💬 Dialogue Agent Service",
                "🧠 Planner Agent Service",
                "📞 Phone Agent Service",
                "🌐 Web Chat Service",
                "🖥️ CLI Terminal Service"
            ],
            "presentation": {
                "group": "DEEPLICA",
                "order": 1
            }
        },
        {
            "name": "🔧 CORE SERVICES ONLY",
            "configurations": [
                "🐕 Watchdog Service",
                "🌐 Backend API Service"
            ]
        },
        {
            "name": "🤖 AI AGENTS ONLY",
            "configurations": [
                "🎯 Dispatcher Service",
                "💬 Dialogue Agent Service",
                "🧠 Planner Agent Service",
                "📞 Phone Agent Service"
            ]
        }
    ]
}
