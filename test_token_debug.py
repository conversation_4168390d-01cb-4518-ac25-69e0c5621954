#!/usr/bin/env python3
"""
🔒 TAB TOKEN DEBUG TEST BED

This creates a simplified test environment to debug the token generation and verification:
1. Simple login page at /test_login
2. Token generation using browser tab properties + username
3. Test page that shows all token debugging information
4. JavaScript that generates tokens the same way as server
5. Detailed debugging output for correlation testing
"""

from fastapi import FastAPI, Request, Form, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
import hashlib
import hmac
import base64
import time
import json
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

from shared.port_manager import get_service_port

app = FastAPI(title="Tab Token Debug Test Bed")
templates = Jinja2Templates(directory="web_chat/templates")

# Secret key for token generation
TOKEN_SECRET = "deeplica-debug-secret-2025"

def get_browser_fingerprint_simple(request: Request) -> str:
    """Simple browser fingerprint for debugging"""
    user_agent = request.headers.get("user-agent", "unknown")
    accept_language = request.headers.get("accept-language", "unknown")
    
    # Create simple fingerprint
    fingerprint_data = f"{user_agent}:{accept_language}"
    fingerprint = hashlib.sha256(fingerprint_data.encode()).hexdigest()[:16]
    
    return fingerprint

def generate_debug_token(username: str, browser_fingerprint: str, timestamp: int = None) -> dict:
    """Generate token with full debugging information"""
    if timestamp is None:
        timestamp = int(time.time())
    
    # Create token data
    token_data = f"{username}:{browser_fingerprint}:{timestamp}"
    
    # Generate HMAC signature
    signature = hmac.new(
        TOKEN_SECRET.encode(),
        token_data.encode(),
        hashlib.sha256
    ).hexdigest()
    
    # Combine data and signature
    full_token_data = f"{token_data}:{signature}"
    
    # Base64 encode for URL safety
    token_base64 = base64.urlsafe_b64encode(full_token_data.encode()).decode()
    
    return {
        "username": username,
        "browser_fingerprint": browser_fingerprint,
        "timestamp": timestamp,
        "token_data": token_data,
        "signature": signature,
        "full_token_data": full_token_data,
        "token_base64": token_base64,
        "secret_used": TOKEN_SECRET
    }

def verify_debug_token(token_base64: str, expected_username: str, expected_fingerprint: str) -> dict:
    """Verify token with full debugging information"""
    try:
        # Decode the token
        full_token_data = base64.urlsafe_b64decode(token_base64.encode()).decode()
        
        # Split into components
        parts = full_token_data.split(':')
        if len(parts) != 4:
            return {
                "valid": False,
                "error": f"Invalid token format - expected 4 parts, got {len(parts)}",
                "parts": parts
            }
        
        token_username, token_fingerprint, token_timestamp, token_signature = parts
        
        # Recreate expected signature
        expected_data = f"{token_username}:{token_fingerprint}:{token_timestamp}"
        expected_signature = hmac.new(
            TOKEN_SECRET.encode(),
            expected_data.encode(),
            hashlib.sha256
        ).hexdigest()
        
        # Check signature
        signature_valid = hmac.compare_digest(token_signature, expected_signature)
        
        # Check data matches
        username_match = token_username == expected_username
        fingerprint_match = token_fingerprint == expected_fingerprint
        
        # Check token age
        token_age = time.time() - float(token_timestamp)
        token_expired = token_age > 3600  # 1 hour for testing
        
        return {
            "valid": signature_valid and username_match and fingerprint_match and not token_expired,
            "token_base64": token_base64,
            "full_token_data": full_token_data,
            "parts": parts,
            "token_username": token_username,
            "token_fingerprint": token_fingerprint,
            "token_timestamp": token_timestamp,
            "token_signature": token_signature,
            "expected_username": expected_username,
            "expected_fingerprint": expected_fingerprint,
            "expected_data": expected_data,
            "expected_signature": expected_signature,
            "signature_valid": signature_valid,
            "username_match": username_match,
            "fingerprint_match": fingerprint_match,
            "token_age": token_age,
            "token_expired": token_expired,
            "secret_used": TOKEN_SECRET
        }
        
    except Exception as e:
        return {
            "valid": False,
            "error": f"Exception during verification: {str(e)}",
            "token_base64": token_base64
        }

@app.get("/test_login", response_class=HTMLResponse)
async def test_login_page(request: Request):
    """Simple login page for testing"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>🔒 Tab Token Debug - Login</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #1a1a1a; color: #00ff00; }
            .container { max-width: 500px; margin: 0 auto; padding: 20px; border: 1px solid #00ff00; }
            input, button { padding: 10px; margin: 10px 0; width: 100%; font-size: 16px; }
            button { background: #00ff00; color: #000; border: none; cursor: pointer; }
            .debug { background: #333; padding: 10px; margin: 10px 0; font-family: monospace; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🔒 Tab Token Debug - Login</h1>
            <p>This is a test bed for debugging tab token generation and verification.</p>
            
            <form method="post" action="/test_login">
                <input type="text" name="username" placeholder="Username (try: admin)" required>
                <input type="password" name="password" placeholder="Password (try: admin123)" required>
                <button type="submit">🔐 Login and Generate Token</button>
            </form>
            
            <div class="debug">
                <h3>🔍 Browser Information (for debugging):</h3>
                <p><strong>User-Agent:</strong> <span id="userAgent"></span></p>
                <p><strong>Accept-Language:</strong> <span id="acceptLanguage"></span></p>
                <p><strong>Screen Resolution:</strong> <span id="screenRes"></span></p>
                <p><strong>Timezone:</strong> <span id="timezone"></span></p>
            </div>
        </div>
        
        <script>
            // Display browser information
            document.getElementById('userAgent').textContent = navigator.userAgent;
            document.getElementById('acceptLanguage').textContent = navigator.language;
            document.getElementById('screenRes').textContent = screen.width + 'x' + screen.height;
            document.getElementById('timezone').textContent = Intl.DateTimeFormat().resolvedOptions().timeZone;
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@app.post("/test_login")
async def test_login_submit(request: Request, username: str = Form(...), password: str = Form(...)):
    """Process login and redirect to test page with token"""
    
    # Simple authentication (for testing)
    if username != "admin" or password != "admin123":
        raise HTTPException(status_code=401, detail="Invalid credentials")
    
    # Get browser fingerprint
    browser_fingerprint = get_browser_fingerprint_simple(request)
    
    # Generate token
    token_info = generate_debug_token(username, browser_fingerprint)
    
    # Redirect to test page with token
    test_url = f"/test_page?token={token_info['token_base64']}&username={username}"
    return RedirectResponse(url=test_url, status_code=302)

@app.get("/test_page", response_class=HTMLResponse)
@app.post("/test_page", response_class=HTMLResponse)
async def test_page(request: Request, token: str = None, username: str = None):
    """Test page that shows all token debugging information"""

    # Handle both GET and POST parameters
    if request.method == "POST":
        # For POST, get from query parameters (redirect case)
        token = request.query_params.get("token")
        username = request.query_params.get("username")

    if not token or not username:
        raise HTTPException(status_code=400, detail="Missing token or username")

    # Get current browser fingerprint
    current_fingerprint = get_browser_fingerprint_simple(request)
    
    # Verify the token
    verification_result = verify_debug_token(token, username, current_fingerprint)
    
    # Generate a new token for comparison
    new_token_info = generate_debug_token(username, current_fingerprint)
    
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>🔒 Tab Token Debug - Test Page</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: #00ff00; }}
            .container {{ max-width: 1200px; margin: 0 auto; }}
            .section {{ border: 1px solid #00ff00; margin: 20px 0; padding: 15px; }}
            .success {{ border-color: #00ff00; background: #003300; }}
            .error {{ border-color: #ff0000; background: #330000; color: #ff6666; }}
            .debug {{ background: #333; padding: 10px; margin: 10px 0; font-family: monospace; font-size: 12px; }}
            .token {{ word-break: break-all; background: #444; padding: 5px; }}
            table {{ width: 100%; border-collapse: collapse; }}
            th, td {{ border: 1px solid #666; padding: 8px; text-align: left; }}
            th {{ background: #333; }}
            .match {{ color: #00ff00; }}
            .mismatch {{ color: #ff6666; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🔒 Tab Token Debug - Test Page</h1>
            
            <div class="section {'success' if verification_result['valid'] else 'error'}">
                <h2>🎯 Token Verification Result: {'✅ VALID' if verification_result['valid'] else '❌ INVALID'}</h2>
                {f'<p><strong>Error:</strong> {verification_result.get("error", "")}</p>' if not verification_result['valid'] and 'error' in verification_result else ''}
            </div>
            
            <div class="section">
                <h2>🔗 URL Information</h2>
                <div class="debug">
                    <p><strong>Full URL:</strong> {request.url}</p>
                    <p><strong>Token Parameter:</strong> <span class="token">{token}</span></p>
                    <p><strong>Username Parameter:</strong> {username}</p>
                </div>
            </div>
            
            <div class="section">
                <h2>🔍 Browser Fingerprint</h2>
                <div class="debug">
                    <p><strong>Current Fingerprint:</strong> {current_fingerprint}</p>
                    <p><strong>User-Agent:</strong> {request.headers.get('user-agent', 'unknown')}</p>
                    <p><strong>Accept-Language:</strong> {request.headers.get('accept-language', 'unknown')}</p>
                </div>
            </div>
            
            <div class="section">
                <h2>📊 Token Verification Details</h2>
                <table>
                    <tr><th>Property</th><th>Expected</th><th>Actual</th><th>Match</th></tr>
                    <tr>
                        <td>Username</td>
                        <td>{verification_result.get('expected_username', 'N/A')}</td>
                        <td>{verification_result.get('token_username', 'N/A')}</td>
                        <td class="{'match' if verification_result.get('username_match') else 'mismatch'}">
                            {'✅' if verification_result.get('username_match') else '❌'}
                        </td>
                    </tr>
                    <tr>
                        <td>Browser Fingerprint</td>
                        <td>{verification_result.get('expected_fingerprint', 'N/A')}</td>
                        <td>{verification_result.get('token_fingerprint', 'N/A')}</td>
                        <td class="{'match' if verification_result.get('fingerprint_match') else 'mismatch'}">
                            {'✅' if verification_result.get('fingerprint_match') else '❌'}
                        </td>
                    </tr>
                    <tr>
                        <td>Signature</td>
                        <td>{verification_result.get('expected_signature', 'N/A')[:20]}...</td>
                        <td>{verification_result.get('token_signature', 'N/A')[:20]}...</td>
                        <td class="{'match' if verification_result.get('signature_valid') else 'mismatch'}">
                            {'✅' if verification_result.get('signature_valid') else '❌'}
                        </td>
                    </tr>
                    <tr>
                        <td>Token Age</td>
                        <td>&lt; 3600 seconds</td>
                        <td>{verification_result.get('token_age', 0):.2f} seconds</td>
                        <td class="{'match' if not verification_result.get('token_expired') else 'mismatch'}">
                            {'✅' if not verification_result.get('token_expired') else '❌'}
                        </td>
                    </tr>
                </table>
            </div>
            
            <div class="section">
                <h2>🔧 Token Generation Details</h2>
                <div class="debug">
                    <p><strong>Secret Used:</strong> {verification_result.get('secret_used', 'N/A')}</p>
                    <p><strong>Token Data:</strong> {verification_result.get('expected_data', 'N/A')}</p>
                    <p><strong>Full Token Data:</strong> {verification_result.get('full_token_data', 'N/A')}</p>
                    <p><strong>Token Parts:</strong> {verification_result.get('parts', [])}</p>
                </div>
            </div>
            
            <div class="section">
                <h2>🆕 Fresh Token Generation (for comparison)</h2>
                <div class="debug">
                    <p><strong>New Token:</strong> <span class="token">{new_token_info['token_base64']}</span></p>
                    <p><strong>New Token Data:</strong> {new_token_info['token_data']}</p>
                    <p><strong>New Signature:</strong> {new_token_info['signature']}</p>
                </div>
            </div>
            
            <div class="section">
                <h2>🧪 JavaScript Token Generation Test</h2>
                <div class="debug">
                    <p><strong>JS Generated Token:</strong> <span id="jsToken">Generating...</span></p>
                    <p><strong>JS Token Data:</strong> <span id="jsTokenData">Generating...</span></p>
                    <p><strong>JS Signature:</strong> <span id="jsSignature">Generating...</span></p>
                    <p><strong>JS vs Server Match:</strong> <span id="jsMatch">Checking...</span></p>
                </div>
            </div>
            
            <div class="section">
                <h2>🔄 Actions</h2>
                <p><a href="/test_login" style="color: #00ff00;">🔙 Back to Login</a></p>
                <p><a href="javascript:location.reload()" style="color: #00ff00;">🔄 Refresh Page</a></p>
                <p><a href="javascript:copyUrlToNewTab()" style="color: #00ff00;">📋 Test URL Copy (should fail)</a></p>
            </div>
        </div>
        
        <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
        <script>
            // JavaScript token generation (matching server logic)
            const TOKEN_SECRET = "{TOKEN_SECRET}";
            const username = "{username}";
            const currentFingerprint = "{current_fingerprint}";
            const timestamp = Math.floor(Date.now() / 1000);
            
            // Generate token data
            const tokenData = `${{username}}:${{currentFingerprint}}:${{timestamp}}`;
            
            // Generate HMAC signature
            const signature = CryptoJS.HmacSHA256(tokenData, TOKEN_SECRET).toString();
            
            // Create full token
            const fullTokenData = `${{tokenData}}:${{signature}}`;
            const jsTokenBase64 = btoa(fullTokenData);
            
            // Display results
            document.getElementById('jsToken').textContent = jsTokenBase64;
            document.getElementById('jsTokenData').textContent = tokenData;
            document.getElementById('jsSignature').textContent = signature;
            
            // Compare with server token
            const serverToken = "{new_token_info['token_base64']}";
            const tokensMatch = jsTokenBase64 === serverToken;
            document.getElementById('jsMatch').innerHTML = tokensMatch ? 
                '<span style="color: #00ff00;">✅ MATCH</span>' : 
                '<span style="color: #ff6666;">❌ MISMATCH</span>';
            
            function copyUrlToNewTab() {{
                const currentUrl = window.location.href;
                window.open(currentUrl, '_blank');
                alert('🔒 URL copied to new tab. The new tab should show token verification failure.');
            }}
            
            // Log all debugging information
            console.log('🔒 Tab Token Debug Information:');
            console.log('Username:', username);
            console.log('Current Fingerprint:', currentFingerprint);
            console.log('Timestamp:', timestamp);
            console.log('Token Data:', tokenData);
            console.log('JS Signature:', signature);
            console.log('JS Token:', jsTokenBase64);
            console.log('Server Token:', serverToken);
            console.log('Tokens Match:', tokensMatch);
        </script>
    </body>
    </html>
    """
    
    return HTMLResponse(content=html_content)

if __name__ == "__main__":
    import uvicorn
    
    port = 8999  # Use a different port for testing
    print(f"🔒 Starting Tab Token Debug Test Bed on port {port}")
    print(f"🌐 Open: http://localhost:{port}/test_login")
    print(f"🔐 Login with: admin / admin123")
    
    uvicorn.run(app, host="0.0.0.0", port=port)
