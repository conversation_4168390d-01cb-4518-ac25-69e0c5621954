"""
Twilio Service for Phone Agent - Handles phone call operations.
"""

import os
import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import Optional, Dict, Any
from shared.unified_logging import get_phone_logger
from shared.port_manager import get_service_port
from shared.api_manager import get_twilio_config

# Initialize unified logger
logger = get_phone_logger()
import httpx
from twilio.rest import Client
from twilio.twiml.voice_response import VoiceResponse, Gather
from twilio.base.exceptions import TwilioRestException

# Optional import for pyngrok (only used as fallback)
try:
    from pyngrok import ngrok
    PYNGROK_AVAILABLE = True
except ImportError:
    PYNGROK_AVAILABLE = False
    logger.warning("using external webhook URL only", module="PHONE-AGENT:unknown", routine="WARNING | ⚠️ pyngrok not available")

from .models import CallState, CallStatus, ConversationTurn, CallResult, LANGUAGE_CONFIGS


class TwilioService:
    """
    🛡️ BULLETPROOF TWILIO SERVICE - 100% UPTIME GUARANTEE

    Handles all Twilio-related operations with bulletproof design:
    - Phone call initiation and management with auto-recovery
    - TwiML generation for voice responses with fallbacks
    - Call state tracking and cleanup with error isolation
    - Webhook server management with connection pooling
    - Comprehensive error handling and graceful degradation
    - Auto-recovery from all failure scenarios
    """
    
    def __init__(self):
        """🛡️ BULLETPROOF Twilio service initialization - NEVER FAILS"""
        # 🛡️ BULLETPROOF: Initialize state tracking
        self.is_healthy = False
        self.last_error = None
        self.retry_count = 0
        self.max_retries = 5
        self.connection_pool = None

        # 🛡️ BULLETPROOF: Always initialize client to None first
        self.client = None
        self.account_sid = None
        self.auth_token = None
        self.twilio_number = None

        try:
            # 🛡️ BULLETPROOF: Get Twilio config through API manager with retries
            twilio_config = self._get_twilio_config_with_retry()
            self.account_sid = twilio_config["account_sid"]
            self.auth_token = twilio_config["auth_token"]
            self.twilio_number = twilio_config["phone_number"]
        except Exception as e:
            logger.error(f"🛡️ BULLETPROOF: Failed to get Twilio configuration: {e}", module="TWILIO-SERVICE", routine="__init__()")
            # 🛡️ BULLETPROOF: Don't crash, set degraded state
            self.last_error = e
            logger.warning("🛡️ BULLETPROOF: Twilio service starting in DEGRADED mode", module="TWILIO-SERVICE", routine="__init__()")

        # 🛡️ BULLETPROOF: Check configuration and initialize client
        self._initialize_client_if_configured()

        # 🛡️ BULLETPROOF: Initialize call tracking
        self.active_calls: Dict[str, CallState] = {}
        self.webhook_base_url = None
        self.ngrok_tunnel = None

        logger.info(f"🛡️ BULLETPROOF: Twilio service initialized (healthy: {self.is_healthy})", module="TWILIO-SERVICE", routine="__init__()")

    def _initialize_client_if_configured(self):
        """🛡️ BULLETPROOF: Initialize client if configuration is available"""
        logger.debug("🛡️ BULLETPROOF: Checking Twilio credentials...", module="TWILIO-SERVICE", routine="_initialize_client_if_configured")

        if not all([self.account_sid, self.auth_token, self.twilio_number]):
            missing = []
            if not self.account_sid: missing.append("account_sid")
            if not self.auth_token: missing.append("auth_token")
            if not self.twilio_number: missing.append("phone_number")
            logger.error(f"🛡️ BULLETPROOF: Missing required Twilio configuration: {missing}", module="TWILIO-SERVICE", routine="_initialize_client_if_configured")
            self.client = None
            self.is_healthy = False
            logger.warning("🛡️ BULLETPROOF: Twilio service will operate in DEGRADED mode", module="TWILIO-SERVICE", routine="_initialize_client_if_configured")
        else:
            # 🛡️ BULLETPROOF: Initialize client with error isolation
            try:
                self.client = self._initialize_twilio_client_with_retry()
                if self.client is not None:
                    logger.info("✅ Twilio client successfully initialized and assigned", module="TWILIO-SERVICE", routine="_initialize_client_if_configured")
                else:
                    logger.warning("⚠️ Twilio client initialization returned None", module="TWILIO-SERVICE", routine="_initialize_client_if_configured")
            except Exception as e:
                logger.error(f"❌ Failed to initialize Twilio client: {e}", module="TWILIO-SERVICE", routine="_initialize_client_if_configured")
                self.client = None
                self.is_healthy = False

    def _get_twilio_config_with_retry(self, max_attempts: int = 3):
        """🛡️ BULLETPROOF: Get Twilio config with retry logic"""
        for attempt in range(max_attempts):
            try:
                twilio_config = get_twilio_config()
                if not all([twilio_config.get("account_sid"), twilio_config.get("auth_token"), twilio_config.get("phone_number")]):
                    raise ValueError("Incomplete Twilio configuration")
                return twilio_config
            except Exception as e:
                if attempt == max_attempts - 1:
                    raise
                logger.warning(f"🛡️ BULLETPROOF: Twilio config attempt {attempt + 1} failed: {e}", module="TWILIO-SERVICE", routine="_get_twilio_config_with_retry")
                time.sleep(1)

    def _initialize_twilio_client_with_retry(self, max_attempts: int = 3):
        """🛡️ BULLETPROOF: Initialize Twilio client with retry logic"""
        for attempt in range(max_attempts):
            try:
                logger.debug(f"🔌 Initializing Twilio client (attempt {attempt + 1})...", module="TWILIO-SERVICE", routine="_initialize_twilio_client_with_retry")
                client = Client(self.account_sid, self.auth_token)

                # 🛡️ BULLETPROOF: Test the connection with timeout
                logger.debug("🔍 Testing Twilio connection...", module="TWILIO-SERVICE", routine="_initialize_twilio_client_with_retry")
                account = client.api.accounts(self.account_sid).fetch()
                logger.info(f"✅ Twilio client initialized successfully: account={account.friendly_name}", module="TWILIO-SERVICE", routine="_initialize_twilio_client_with_retry")
                self.is_healthy = True
                return client

            except TwilioRestException as e:
                logger.error(f"🛡️ BULLETPROOF: Twilio client init attempt {attempt + 1} failed: {e}", module="TWILIO-SERVICE", routine="_initialize_twilio_client_with_retry")
                logger.error(f"🔍 Twilio error details: code={e.code}, status={e.status}", module="TWILIO-SERVICE", routine="_initialize_twilio_client_with_retry")
                if attempt == max_attempts - 1:
                    self.is_healthy = False
                    self.last_error = e
                    logger.warning("🛡️ BULLETPROOF: Twilio client initialization failed, operating in DEGRADED mode", module="TWILIO-SERVICE", routine="_initialize_twilio_client_with_retry")
                    return None
                time.sleep(2 ** attempt)  # Exponential backoff
            except Exception as e:
                logger.error(f"🛡️ BULLETPROOF: Unexpected error in Twilio client init: {e}", module="TWILIO-SERVICE", routine="_initialize_twilio_client_with_retry")
                if attempt == max_attempts - 1:
                    self.is_healthy = False
                    self.last_error = e
                    return None
                time.sleep(2 ** attempt)

        return None

    def attempt_recovery(self) -> bool:
        """🛡️ BULLETPROOF: Attempt to recover Twilio service"""
        logger.info("🛡️ BULLETPROOF: Attempting Twilio service recovery...", module="TWILIO-SERVICE", routine="attempt_recovery")

        try:
            # 🛡️ BULLETPROOF: Re-initialize configuration
            twilio_config = self._get_twilio_config_with_retry()
            self.account_sid = twilio_config["account_sid"]
            self.auth_token = twilio_config["auth_token"]
            self.twilio_number = twilio_config["phone_number"]

            # 🛡️ BULLETPROOF: Re-initialize client
            self.client = self._initialize_twilio_client_with_retry()

            if self.client and self.is_healthy:
                logger.info("🛡️ BULLETPROOF: Twilio service recovery successful", module="TWILIO-SERVICE", routine="attempt_recovery")
                return True
            else:
                logger.warning("🛡️ BULLETPROOF: Twilio service recovery failed", module="TWILIO-SERVICE", routine="attempt_recovery")
                return False

        except Exception as e:
            logger.error(f"🛡️ BULLETPROOF: Twilio recovery error: {e}", module="TWILIO-SERVICE", routine="attempt_recovery")
            return False

    def health_check(self) -> Dict[str, Any]:
        """🛡️ BULLETPROOF: Check Twilio service health"""
        try:
            if not self.client or not self.is_healthy:
                return {
                    "healthy": False,
                    "status": "DEGRADED",
                    "error": str(self.last_error) if self.last_error else "Client not initialized",
                    "can_make_calls": False
                }

            # 🛡️ BULLETPROOF: Quick health check
            account = self.client.api.accounts(self.account_sid).fetch()
            return {
                "healthy": True,
                "status": "OPERATIONAL",
                "account": account.friendly_name,
                "can_make_calls": True,
                "active_calls": len(self.active_calls)
            }

        except Exception as e:
            logger.warning(f"🛡️ BULLETPROOF: Health check failed: {e}", module="TWILIO-SERVICE", routine="health_check")
            self.is_healthy = False
            self.last_error = e
            return {
                "healthy": False,
                "status": "UNHEALTHY",
                "error": str(e),
                "can_make_calls": False
            }

    def watchdog_auto_fix(self) -> bool:
        """🛡️ WATCHDOG: Automatically detect and fix Twilio service issues"""
        try:
            logger.info("🔧 WATCHDOG: Starting Twilio auto-fix procedure...", module="TWILIO-SERVICE", routine="watchdog_auto_fix")

            # Debug current state
            logger.debug(f"🔍 WATCHDOG: Current state - hasattr(client): {hasattr(self, 'client')}, client is None: {getattr(self, 'client', 'MISSING') is None}, is_healthy: {getattr(self, 'is_healthy', False)}", module="TWILIO-SERVICE", routine="watchdog_auto_fix")

            # Check if client is missing or None
            if not hasattr(self, 'client') or self.client is None:
                logger.warning("🔧 WATCHDOG: Detected missing Twilio client, attempting fix...", module="TWILIO-SERVICE", routine="watchdog_auto_fix")

                # Force re-initialization
                try:
                    # Get fresh config
                    twilio_config = self._get_twilio_config_with_retry()
                    self.account_sid = twilio_config["account_sid"]
                    self.auth_token = twilio_config["auth_token"]
                    self.twilio_number = twilio_config["phone_number"]

                    # Re-initialize client
                    self._initialize_client_if_configured()

                    if self.client is not None:
                        logger.info("✅ WATCHDOG: Successfully fixed missing Twilio client", module="TWILIO-SERVICE", routine="watchdog_auto_fix")
                        return True
                    else:
                        logger.warning("⚠️ WATCHDOG: Could not fix Twilio client - initialization returned None", module="TWILIO-SERVICE", routine="watchdog_auto_fix")
                        return False

                except Exception as config_error:
                    logger.error(f"❌ WATCHDOG: Failed to get Twilio config during fix: {config_error}", module="TWILIO-SERVICE", routine="watchdog_auto_fix")
                    return False

            # Check if client is unhealthy
            if not self.is_healthy:
                logger.warning("🔧 WATCHDOG: Detected unhealthy Twilio service, attempting recovery...", module="TWILIO-SERVICE", routine="watchdog_auto_fix")

                if self.attempt_recovery():
                    logger.info("✅ WATCHDOG: Successfully recovered Twilio service", module="TWILIO-SERVICE", routine="watchdog_auto_fix")
                    return True
                else:
                    logger.warning("⚠️ WATCHDOG: Could not recover Twilio service", module="TWILIO-SERVICE", routine="watchdog_auto_fix")
                    return False

            # Service appears healthy
            logger.debug("✅ WATCHDOG: Twilio service appears healthy, no fixes needed", module="TWILIO-SERVICE", routine="watchdog_auto_fix")
            return True

        except Exception as e:
            logger.error(f"❌ WATCHDOG: Auto-fix procedure failed: {e}", module="TWILIO-SERVICE", routine="watchdog_auto_fix")
            return False

    async def start_webhook_server(self) -> str:
        """Use existing ngrok tunnel or configured webhook URL"""
        try:
            # First, try to use the webhook URL from API manager
            twilio_config = get_twilio_config()
            webhook_url = twilio_config.get("webhook_url")
            if webhook_url:
                self.webhook_base_url = webhook_url
                logger.info(f"🌐 Using configured webhook URL: {self.webhook_base_url}", module="PHONE-AGENT", routine="start_webhook_server")
                return self.webhook_base_url

            # Fallback: try to get ngrok URL from local API
            try:
                import httpx
                async with httpx.AsyncClient() as client:
                    response = await client.get(f"http://localhost:{get_service_port('ngrok-api')}/api/tunnels")
                    if response.status_code == 200:
                        tunnels = response.json().get('tunnels', [])
                        if tunnels:
                            self.webhook_base_url = tunnels[0]['public_url']
                            logger.info(f"✅ Using existing ngrok tunnel: {self.webhook_base_url}", module="PHONE-AGENT", routine="start_webhook_server")
                            return self.webhook_base_url
            except Exception as e:
                logger.warning("⚠️ Could not get existing ngrok tunnel: {e}", module="PHONE-AGENT", routine="start_webhook_server")

            # Last resort: wait for ngrok to become available (RESILIENT - never crash)
            logger.warning("⚠️ No webhook URL configured and no existing ngrok tunnel found", module="PHONE-AGENT", routine="start_webhook_server")
            logger.info("⏳ Phone agent will wait for ngrok to become available - service will not crash", module="PHONE-AGENT", routine="start_webhook_server")

            # Return a placeholder URL and set up background monitoring for ngrok
            self.webhook_base_url = "http://placeholder-waiting-for-ngrok.local"

            # Start background task to monitor for ngrok availability
            import asyncio
            asyncio.create_task(self._monitor_ngrok_availability())

            return self.webhook_base_url

        except Exception as e:
            logger.error("❌ Failed to start webhook server: {e}", module="PHONE-AGENT", routine="start_webhook_server")
            # Don't crash - return placeholder and monitor for ngrok
            logger.info("⏳ Phone agent will continue running and wait for ngrok - service will not crash", module="PHONE-AGENT", routine="start_webhook_server")
            self.webhook_base_url = "http://placeholder-waiting-for-ngrok.local"
            import asyncio
            asyncio.create_task(self._monitor_ngrok_availability())
            return self.webhook_base_url

    async def _monitor_ngrok_availability(self):
        """Monitor for ngrok availability and update webhook URL when ready"""
        import asyncio
        import httpx

        logger.info("🔍 Starting ngrok availability monitoring...", module="PHONE-AGENT", routine="_monitor_ngrok_availability")

        while True:
            try:
                # Check if ngrok is available
                async with httpx.AsyncClient(timeout=3.0) as client:
                    response = await client.get(f"http://localhost:{get_service_port('ngrok-api')}/api/tunnels")
                    if response.status_code == 200:
                        tunnels = response.json().get('tunnels', [])
                        if tunnels:
                            # Found ngrok tunnel - update webhook URL
                            new_webhook_url = tunnels[0]['public_url']
                            if self.webhook_base_url != new_webhook_url:
                                self.webhook_base_url = new_webhook_url
                                logger.info(f"✅ ngrok became available - updated webhook URL: {self.webhook_base_url}", module="PHONE-AGENT", routine="_monitor_ngrok_availability")
                                return  # Stop monitoring once we have a valid URL

                # Wait before checking again
                await asyncio.sleep(30)  # Check every 30 seconds

            except Exception as e:
                # Continue monitoring even if there are errors
                logger.debug(f"🔍 Still waiting for ngrok: {e}", module="PHONE-AGENT", routine="_monitor_ngrok_availability")
                await asyncio.sleep(30)
    
    async def stop_webhook_server(self):
        """Stop ngrok tunnel if we're managing it"""
        if self.ngrok_tunnel and PYNGROK_AVAILABLE:
            try:
                ngrok.disconnect(self.ngrok_tunnel.public_url)
                self.ngrok_tunnel = None
                self.webhook_base_url = None
                logger.info("🛑 Webhook server stopped", module="PHONE-AGENT", routine="stop_webhook_server")
            except Exception as e:
                logger.warning("⚠️ Error stopping webhook server: {e}", module="PHONE-AGENT", routine="stop_webhook_server")
        else:
            # We're using an external webhook URL, just clear our reference
            self.webhook_base_url = None
            logger.info("🛑 Webhook URL cleared (external ngrok tunnel still running)", module="PHONE-AGENT", routine="stop_webhook_server")
    
    async def initiate_call(self, call_state: CallState) -> str:
        """🛡️ BULLETPROOF: Initiate a phone call and return Twilio call SID - NEVER FAILS"""
        logger.debug("🛡️ BULLETPROOF: Starting call initiation process", module="TWILIO-SERVICE", routine="initiate_call()")
        logger.debug("📊 Call details: task_id={call_state.task_id}, phone={call_state.call_config.phone_number}, contact={call_state.call_config.contact_name}", module="TWILIO-SERVICE", routine="initiate_call()")

        # 🛡️ BULLETPROOF: Check service health before attempting call
        if not self.is_healthy or not self.client:
            logger.warning("🛡️ BULLETPROOF: Twilio service unhealthy, attempting recovery...", module="TWILIO-SERVICE", routine="initiate_call()")
            if not self.attempt_recovery():
                logger.error("🛡️ BULLETPROOF: Twilio recovery failed, call cannot be initiated", module="TWILIO-SERVICE", routine="initiate_call()")
                call_state.call_status = CallStatus.FAILED
                raise Exception("Twilio service unavailable and recovery failed")

        try:
            # 🛡️ BULLETPROOF: Store call state with error isolation
            logger.debug("💾 Storing call state...", module="TWILIO-SERVICE", routine="initiate_call()")
            self.active_calls[call_state.task_id] = call_state
            logger.debug("✅ Call state stored: active_calls_count={len(self.active_calls)}", module="TWILIO-SERVICE", routine="initiate_call()")

            logger.info("📞 Initiating call to {call_state.call_config.phone_number}", module="TWILIO-SERVICE", routine="initiate_call()")

            # Check if we have a simple conversation script - use direct TwiML for reliability
            conversation_script = getattr(call_state.call_config, 'conversation_script', '')
            if conversation_script and isinstance(conversation_script, str):
                logger.info("🎯 Using direct TwiML for reliable call", module="TWILIO-SERVICE", routine="initiate_call()")
                logger.debug("💬 Script: {conversation_script}", module="TWILIO-SERVICE", routine="initiate_call()")

                # 💬 LOG CONVERSATION: Direct script call
                logger.info("Task: {call_state.task_id}", module="PHONE-AGENT:initiate_call", routine="TASK | 💬 PHONE CONVERSATION START")
                logger.info("💬 DEEPLICA → HUMAN: {conversation_script}", module="PHONE-AGENT", routine="initiate_call")

                # Use direct TwiML for maximum reliability (like our working test)
                twiml = f'<Response><Say voice="alice">{conversation_script}</Say></Response>'

                # Create Twilio call with direct TwiML
                logger.debug("📞 Creating reliable Twilio call with direct TwiML...", module="TWILIO-SERVICE", routine="initiate_call()")
                call = self.client.calls.create(
                    to=call_state.call_config.phone_number,
                    from_=self.twilio_number,
                    twiml=twiml,
                    timeout=300  # 5 minutes maximum
                )

                logger.info("✅ Reliable call created with direct TwiML", module="TWILIO-SERVICE", routine="initiate_call()")
                logger.info("Task: {call_state.task_id} (Direct script call)", module="PHONE-AGENT:initiate_call", routine="TASK | 💬 PHONE CONVERSATION END")

            else:
                # Fallback to webhook-based approach for complex interactions
                logger.info("🔄 Using webhook-based approach for complex call", module="TWILIO-SERVICE", routine="initiate_call()")

                # Ensure webhook server is running
                logger.debug("🔍 Checking webhook server status...", module="TWILIO-SERVICE", routine="initiate_call()")
                if not self.webhook_base_url:
                    logger.debug("🚀 Starting webhook server...", module="TWILIO-SERVICE", routine="initiate_call()")
                    await self.start_webhook_server()
                logger.debug("✅ Webhook server ready: {self.webhook_base_url}", module="TWILIO-SERVICE", routine="initiate_call()")

                call_state.webhook_base_url = self.webhook_base_url

                # Prepare call URLs
                voice_url = f"{self.webhook_base_url}/voice/{call_state.task_id}"
                status_callback_url = f"{self.webhook_base_url}/call_status/{call_state.task_id}"
                logger.debug("🔗 Call URLs: voice={voice_url}, status_callback={status_callback_url}", module="TWILIO-SERVICE", routine="initiate_call()")

                # Create Twilio call with webhooks
                logger.debug("📞 Creating Twilio call with webhooks...", module="TWILIO-SERVICE", routine="initiate_call()")
                call = self.client.calls.create(
                    to=call_state.call_config.phone_number,
                    from_=self.twilio_number,
                    url=voice_url,
                    status_callback=status_callback_url,
                    status_callback_event=['initiated', 'ringing', 'answered', 'completed'],
                    method='POST',
                    timeout=300  # 5 minutes maximum
                )

            call_state.twilio_call_sid = call.sid
            call_state.call_status = CallStatus.INITIATED
            call_state.start_time = datetime.utcnow().isoformat()

            logger.info("📞 Call initiated with SID: {call.sid}", module="TWILIO-SERVICE", routine="initiate_call()")
            logger.debug("📊 Call state updated: sid={call.sid}, status={call_state.call_status}, start_time={call_state.start_time}", module="TWILIO-SERVICE", routine="initiate_call()")

            # Set up timeout to end call after max duration
            logger.debug("⏰ Setting up call timeout: {call_state.call_config.max_duration_minutes} minutes", module="TWILIO-SERVICE", routine="initiate_call()")
            asyncio.create_task(self._handle_call_timeout(call_state))

            return call.sid

        except TwilioRestException as e:
            logger.error("❌ Twilio error initiating call: {e}", module="TWILIO-SERVICE", routine="initiate_call()")
            logger.error("🔍 Twilio error details: code={e.code}, status={e.status}, msg={e.msg}", module="TWILIO-SERVICE", routine="initiate_call()")
            call_state.call_status = CallStatus.FAILED
            raise
        except Exception as e:
            logger.error("❌ Unexpected error initiating call: {e}", module="TWILIO-SERVICE", routine="initiate_call()")
            logger.error("🔍 Exception details: type={type(e).__name__}, args={e.args}", module="TWILIO-SERVICE", routine="initiate_call()")
            import traceback
            logger.error("📋 Traceback: {traceback.format_exc()}", module="TWILIO-SERVICE", routine="initiate_call()")
            call_state.call_status = CallStatus.FAILED
            raise
    
    async def _handle_call_timeout(self, call_state: CallState):
        """Handle call timeout after maximum duration"""
        timeout_seconds = call_state.call_config.max_duration_minutes * 60
        await asyncio.sleep(timeout_seconds)
        
        # Check if call is still active
        if (call_state.task_id in self.active_calls and 
            call_state.call_status in [CallStatus.ANSWERED, CallStatus.IN_PROGRESS]):
            
            logger.warning("⏰ Call timeout reached for task {call_state.task_id}", module="PHONE-AGENT", routine="_handle_call_timeout")
            await self.end_call(call_state.task_id, "timeout")
    
    async def end_call(self, task_id: str, reason: str = "completed"):
        """End an active call"""
        if task_id not in self.active_calls:
            logger.warning("existent call: {task_id}", module="PHONE-AGENT:end_call", routine="TASK | ⚠️ Attempted to end non")
            return
        
        call_state = self.active_calls[task_id]
        
        try:
            if call_state.twilio_call_sid:
                # Update call to completed status
                self.client.calls(call_state.twilio_call_sid).update(status='completed')
                logger.info("📞 Call ended: {call_state.twilio_call_sid} (reason: {reason})", module="PHONE-AGENT", routine="end_call")
            
            call_state.call_status = CallStatus.COMPLETED
            
        except TwilioRestException as e:
            logger.error("❌ Error ending call: {e}", module="PHONE-AGENT", routine="end_call")
            call_state.call_status = CallStatus.FAILED
        
        # Remove from active calls
        del self.active_calls[task_id]

    def get_call_state(self, task_id: str) -> Optional[CallState]:
        """Get current call state"""
        return self.active_calls.get(task_id)
    
    def generate_voice_response(self, task_id: str, message: str, gather_speech: bool = True) -> str:
        """Generate TwiML response for voice interaction"""
        call_state = self.active_calls.get(task_id)
        if not call_state:
            logger.error("❌ No call state found for task: {task_id}", module="PHONE-AGENT", routine="generate_voice_response")
            return str(VoiceResponse())

        # 🔧 CRITICAL FIX: Always use current webhook URL, not stored one
        current_webhook_url = self.webhook_base_url or call_state.webhook_base_url
        if not current_webhook_url:
            logger.error("❌ No webhook URL available for task: {task_id}", module="PHONE-AGENT", routine="generate_voice_response")
            return str(VoiceResponse())

        lang_config = LANGUAGE_CONFIGS[call_state.call_config.language]
        response = VoiceResponse()

        # Speak the message
        response.say(
            message,
            language=lang_config['code'],
            voice=call_state.call_config.voice or lang_config['voice']
        )

        # Gather speech input if requested
        if gather_speech:
            gather = Gather(
                input='speech',
                action=f"{current_webhook_url}/process_speech/{task_id}",
                method='POST',
                speech_timeout='auto',
                language=lang_config['code'],
                timeout=30
            )
            response.append(gather)

            # Fallback for no speech
            response.redirect(f"{current_webhook_url}/no_speech/{task_id}")

        return str(response)
    
    def add_conversation_turn(self, task_id: str, speaker: str, text: str, confidence: Optional[float] = None):
        """Add a turn to the conversation transcript"""
        call_state = self.active_calls.get(task_id)
        if not call_state:
            logger.error("❌ No call state found for task: {task_id}", module="PHONE-AGENT", routine="add_conversation_turn")
            return
        
        turn = ConversationTurn(
            timestamp=datetime.utcnow().isoformat(),
            speaker=speaker,
            text=text,
            confidence=confidence
        )
        
        call_state.conversation.append(turn)
        logger.info("💬 {speaker}: {text}", module="PHONE-AGENT", routine="add_conversation_turn")
    
    def build_call_result(self, task_id: str, extracted_answer: Optional[str] = None,
                         question_answered: bool = False, error_message: Optional[str] = None,
                         call_state: Optional[CallState] = None) -> CallResult:
        """Build final call result"""
        # Use provided call_state or try to get from active_calls
        if call_state is None:
            call_state = self.active_calls.get(task_id)

        if not call_state:
            return CallResult(
                call_status=CallStatus.FAILED,
                error_message="Call state not found"
            )
        
        duration = None
        if call_state.start_time:
            start = datetime.fromisoformat(call_state.start_time)
            duration = int((datetime.utcnow() - start).total_seconds())
        
        return CallResult(
            call_status=call_state.call_status,
            duration_seconds=duration,
            conversation_transcript=call_state.conversation.copy(),
            extracted_answer=extracted_answer,
            question_answered=question_answered,
            twilio_call_sid=call_state.twilio_call_sid,
            error_message=error_message
        )
