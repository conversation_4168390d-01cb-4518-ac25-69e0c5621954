#!/usr/bin/env python3
"""
🚀 DEEPLICA STARTUP ORCHESTRATOR
Starts ALL DEEPLICA services including external services in correct order
Makes DEEPLICA fully ready and communicative via DeepChat
"""

import asyncio
import httpx
import time
import sys
import subprocess
import os
import webbrowser
from datetime import datetime
from shared.port_manager import get_service_port, cleanup_all_ports, ensure_service_port_free

class DeepplicaStartupOrchestrator:
    """Orchestrates the startup of ALL Deeplica services including external services"""

    def __init__(self):
        self.orchestrator_name = "STARTUP-ORCHESTRATOR"
        self.workspace_folder = os.path.dirname(os.path.abspath(__file__))

        # Get correct ports from port manager - ALL PORTS ARE CONFIGURABLE
        self.watchdog_port = get_service_port("watchdog")
        self.backend_port = get_service_port("backend")
        self.web_chat_port = get_service_port("web-chat")
        self.dispatcher_port = get_service_port("dispatcher")
        self.dialogue_port = get_service_port("dialogue")
        self.planner_port = get_service_port("planner")
        self.phone_port = get_service_port("phone")
        self.ngrok_tunnel_port = get_service_port("ngrok-tunnel")
        self.webhook_port = get_service_port("webhook-server")
        self.twilio_port = get_service_port("twilio-echo-bot")

        self.watchdog_url = f"http://localhost:{self.watchdog_port}"
        self.backend_url = f"http://localhost:{self.backend_port}"
        self.web_chat_url = f"http://localhost:{self.web_chat_port}"

        # PROPER startup sequence - WATCHDOG FIRST, then Backend, then others
        self.startup_sequence = [
            {
                "name": "WATCHDOG",
                "description": "🐕 Watchdog (Registry & Monitor)",
                "port": self.watchdog_port,
                "health_url": f"{self.watchdog_url}/health",
                "startup_delay": 5,
                "required": True,
                "start_command": ["python3", "watchdog/main.py"],
                "cwd": self.workspace_folder,
                "env": {"SERVICE_NAME": "WATCHDOG", "PYTHONPATH": self.workspace_folder}
            },
            {
                "name": "BACKEND-API",
                "description": "🌐 Backend API",
                "port": self.backend_port,
                "health_url": f"{self.backend_url}/health",
                "startup_delay": 10,
                "required": True,
                "start_command": ["python3", "-m", "app.main"],
                "cwd": os.path.join(self.workspace_folder, "backend"),
                "env": {"PORT": str(self.backend_port), "SERVICE_NAME": "BACKEND-API", "PYTHONPATH": self.workspace_folder}
            },
            {
                "name": "DISPATCHER",
                "description": "🎯 Dispatcher",
                "port": get_service_port("DISPATCHER"),
                "health_url": f"http://localhost:{get_service_port('DISPATCHER')}/health",
                "startup_delay": 5,
                "required": True,
                "start_command": ["python3", "-m", "app.main"],
                "cwd": os.path.join(self.workspace_folder, "dispatcher"),
                "env": {"WAIT_FOR_BACKEND": "true", "SERVICE_NAME": "DISPATCHER", "PYTHONPATH": self.workspace_folder}
            },
            {
                "name": "DIALOGUE-AGENT",
                "description": "💬 Dialogue Agent",
                "port": get_service_port("DIALOGUE-AGENT"),
                "health_url": f"http://localhost:{get_service_port('DIALOGUE-AGENT')}/health",
                "startup_delay": 3,
                "required": False,
                "start_command": ["python3", "-m", "app.main"],
                "cwd": os.path.join(self.workspace_folder, "agents/dialogue"),
                "env": {"WAIT_FOR_BACKEND": "true", "SERVICE_NAME": "DIALOGUE-AGENT", "PYTHONPATH": self.workspace_folder}
            },
            {
                "name": "PLANNER-AGENT",
                "description": "🧠 Planner Agent",
                "port": get_service_port("PLANNER-AGENT"),
                "health_url": f"http://localhost:{get_service_port('PLANNER-AGENT')}/health",
                "startup_delay": 3,
                "required": False,
                "start_command": ["python3", "-m", "app.main"],
                "cwd": os.path.join(self.workspace_folder, "agents/planner"),
                "env": {"WAIT_FOR_BACKEND": "true", "SERVICE_NAME": "PLANNER-AGENT", "PYTHONPATH": self.workspace_folder}
            },
            {
                "name": "PHONE-AGENT",
                "description": "📞 Phone Agent",
                "port": get_service_port("PHONE-AGENT"),
                "health_url": f"http://localhost:{get_service_port('PHONE-AGENT')}/health",
                "startup_delay": 3,
                "required": False,
                "start_command": ["python3", "-m", "app.main"],
                "cwd": os.path.join(self.workspace_folder, "agents/phone"),
                "env": {"WAIT_FOR_BACKEND": "true", "SERVICE_NAME": "PHONE-AGENT", "PYTHONPATH": self.workspace_folder}
            },
            {
                "name": "WEB-CHAT",
                "description": "🌐 Web Chat Interface",
                "port": self.web_chat_port,
                "health_url": f"{self.web_chat_url}/health",
                "startup_delay": 5,
                "required": True,
                "start_command": ["python3", "-m", "uvicorn", "web_chat.main:app", "--host", "0.0.0.0", "--port", str(self.web_chat_port), "--reload"],
                "cwd": self.workspace_folder,
                "env": {"PYTHONPATH": self.workspace_folder, "SERVICE_NAME": "WEB-CHAT"}
            },
            {
                "name": "NGROK-TUNNEL",
                "description": "🌐 Ngrok Tunnel (Default)",
                "port": None,
                "health_url": None,
                "startup_delay": 5,
                "required": False,
                "start_command": ["python3", "start_ngrok.py"],
                "cwd": self.workspace_folder,
                "env": {"NGROK_PORT": str(self.ngrok_tunnel_port), "PYTHONPATH": self.workspace_folder}
            },
            {
                "name": "NGROK-PHONE",
                "description": "🌐 Ngrok Tunnel (Phone)",
                "port": None,
                "health_url": None,
                "startup_delay": 3,
                "required": False,
                "start_command": ["python3", "start_ngrok.py"],
                "cwd": self.workspace_folder,
                "env": {"NGROK_PORT": str(self.phone_port), "PYTHONPATH": self.workspace_folder}
            }
        ]

        # Track running processes
        self.running_processes = {}

    async def cleanup_ports(self):
        """Clean up all DEEPLICA ports before starting"""
        print(f"[{self.orchestrator_name}] 🧹 Cleaning up all DEEPLICA ports...")
        try:
            cleanup_results = cleanup_all_ports(force=True)
            successful = sum(1 for success in cleanup_results.values() if success)
            total = len(cleanup_results)
            print(f"[{self.orchestrator_name}] ✅ Port cleanup: {successful}/{total} ports cleaned")

            # Wait a moment for ports to be fully released
            await asyncio.sleep(2)
            return True
        except Exception as e:
            print(f"[{self.orchestrator_name}] ⚠️ Port cleanup error: {e}")
            return False

    async def start_service_process(self, service_config: dict) -> bool:
        """Guide user to start service in its own VS Code debug console"""
        service_name = service_config["name"]

        # Map service names to VS Code launch configuration names
        launch_config_map = {
            "WATCHDOG": "🐕 Watchdog Service",
            "BACKEND-API": "🌐 Backend API Service",
            "DISPATCHER": "🎯 Dispatcher Service",
            "DIALOGUE-AGENT": "💬 Dialogue Agent Service",
            "PLANNER-AGENT": "🧠 Planner Agent Service",
            "PHONE-AGENT": "📞 Phone Agent Service",
            "WEB-CHAT": "🌐 Web Chat Service",
            "NGROK-BACKEND": "🌐 Ngrok Tunnel (Backend API)",
            "NGROK-PHONE": "🌐 Ngrok Tunnel (Phone Webhooks)"
        }

        launch_config_name = launch_config_map.get(service_name)
        if not launch_config_name:
            print(f"[{self.orchestrator_name}] ⏭️ No launch configuration for {service_name}")
            return True

        try:
            # Ensure port is free first
            port = service_config.get("port")
            if port:
                print(f"[{self.orchestrator_name}] 🔍 Ensuring port {port} is free for {service_name}...")
                ensure_service_port_free(service_name, force=True)

            print(f"\n" + "="*80)
            print(f"🚀 PLEASE START: {service_name}")
            print(f"📋 VS Code Launch Config: '{launch_config_name}'")
            print(f"🎯 Go to VS Code Debug Panel and click '{launch_config_name}'")
            print(f"⏳ This will start {service_name} in its own Python Debug Console")
            print(f"✅ Each service MUST run as a separate microservice!")
            print("="*80)

            # Wait for user to start the service
            print(f"[{self.orchestrator_name}] ⏳ Waiting for you to start {service_name}...")
            print(f"[{self.orchestrator_name}] 🎯 Click '{launch_config_name}' in VS Code Debug Panel")

            # Give user time to start the service
            await asyncio.sleep(5)

            return True

        except Exception as e:
            print(f"[{self.orchestrator_name}] ❌ Failed to prepare {service_name}: {e}")
            return False

    async def wait_for_service(self, service_config: dict, max_wait: int = 60) -> bool:
        """Wait for a service to become healthy"""
        service_name = service_config["name"]
        health_url = service_config.get("health_url")
        
        if not health_url:
            print(f"[{self.orchestrator_name}] ⏭️ Skipping health check for {service_name} (no health endpoint)")
            return True
            
        print(f"[{self.orchestrator_name}] ⏳ Waiting for {service_name} to become ready...")
        
        start_time = time.time()
        while time.time() - start_time < max_wait:
            try:
                async with httpx.AsyncClient(timeout=5.0) as client:
                    response = await client.get(health_url)
                    if response.status_code == 200:
                        print(f"[{self.orchestrator_name}] ✅ {service_name} is ready!")
                        return True
            except Exception:
                pass
                
            await asyncio.sleep(1)
        
        print(f"[{self.orchestrator_name}] ⚠️ {service_name} did not become ready within {max_wait}s")
        return False

    async def verify_startup_success(self) -> bool:
        """Verify that all critical services started successfully"""
        print(f"[{self.orchestrator_name}] 🔍 Verifying startup success...")
        
        success_count = 0
        total_services = len(self.startup_sequence)
        
        for service_config in self.startup_sequence:
            service_name = service_config["name"]
            health_url = service_config.get("health_url")
            required = service_config.get("required", False)
            
            if health_url:
                try:
                    async with httpx.AsyncClient(timeout=3.0) as client:
                        response = await client.get(health_url)
                        if response.status_code == 200:
                            print(f"[{self.orchestrator_name}] ✅ {service_name}: RUNNING")
                            success_count += 1
                        else:
                            status = "❌ CRITICAL" if required else "⚠️ OPTIONAL"
                            print(f"[{self.orchestrator_name}] {status} {service_name}: NOT RESPONDING")
                except Exception:
                    status = "❌ CRITICAL" if required else "⚠️ OPTIONAL"
                    print(f"[{self.orchestrator_name}] {status} {service_name}: UNREACHABLE")
            else:
                print(f"[{self.orchestrator_name}] ℹ️ {service_name}: NO HEALTH CHECK")
                success_count += 1
        
        success_rate = (success_count / total_services) * 100
        print(f"[{self.orchestrator_name}] 📊 Startup Success Rate: {success_rate:.1f}% ({success_count}/{total_services})")
        
        return success_rate >= 70  # At least 70% success rate

    async def orchestrate_startup(self) -> bool:
        """Orchestrate the complete startup sequence"""
        print("🚀 DEEPLICA STARTUP ORCHESTRATOR")
        print("=" * 60)
        print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("🎯 Starting ALL Deeplica services including external services")
        print("🧹 Cleaning ports first, then starting in proper dependency order")
        print("🌐 Will open DeepChat browser when ready")
        print("=" * 60)

        try:
            # Phase 1: Clean up all ports first
            print(f"\n[{self.orchestrator_name}] 🧹 Phase 1: Port Cleanup")
            await self.cleanup_ports()

            # Service name mapping for user guidance
            launch_config_map = {
                "WATCHDOG": "🐕 Watchdog Service",
                "BACKEND-API": "🌐 Backend API Service",
                "DISPATCHER": "🎯 Dispatcher Service",
                "DIALOGUE-AGENT": "💬 Dialogue Agent Service",
                "PLANNER-AGENT": "🧠 Planner Agent Service",
                "PHONE-AGENT": "📞 Phone Agent Service",
                "WEB-CHAT": "🌐 Web Chat Service",
                "NGROK-BACKEND": "🌐 Ngrok Tunnel (Backend API)",
                "NGROK-PHONE": "🌐 Ngrok Tunnel (Phone Webhooks)"
            }

            # Phase 2: Start services in proper order
            print(f"\n[{self.orchestrator_name}] 🚀 Phase 2: Manual Service Startup")
            print(f"[{self.orchestrator_name}] 🎯 You will be guided to start each service manually in VS Code")

            # Start services in sequence
            for i, service_config in enumerate(self.startup_sequence, 1):
                service_name = service_config["name"]
                description = service_config["description"]
                startup_delay = service_config.get("startup_delay", 2)
                required = service_config.get("required", False)

                print(f"\n[{self.orchestrator_name}] 🚀 Step {i}/{len(self.startup_sequence)}: {description}")

                # Guide user to start the service process
                start_success = await self.start_service_process(service_config)
                # Note: start_success just means guidance was provided, not that service actually started

                # Wait for the service to become healthy (with extended timeout for manual startup)
                print(f"[{self.orchestrator_name}] ⏳ Waiting for {service_name} to become healthy...")
                print(f"[{self.orchestrator_name}] 🎯 Please start '{launch_config_map.get(service_name, service_name)}' in VS Code Debug Panel now!")

                success = await self.wait_for_service(service_config, max_wait=120)  # Extended timeout for manual startup

                if success:
                    print(f"[{self.orchestrator_name}] ✅ {service_name} is healthy and ready!")
                    # Give service time to fully initialize
                    if startup_delay > 0:
                        print(f"[{self.orchestrator_name}] ⏳ Allowing {startup_delay}s for {service_name} initialization...")
                        await asyncio.sleep(startup_delay)
                else:
                    if required:
                        print(f"[{self.orchestrator_name}] ❌ CRITICAL: {service_name} failed to become healthy")
                        print(f"[{self.orchestrator_name}] 💡 Make sure you started '{launch_config_map.get(service_name, service_name)}' in VS Code Debug Panel")
                        print(f"[{self.orchestrator_name}] 🛑 Startup sequence aborted")
                        return False
                    else:
                        print(f"[{self.orchestrator_name}] ⚠️ OPTIONAL: {service_name} not healthy (continuing anyway)")
            
            # Phase 3: Final verification
            print(f"\n[{self.orchestrator_name}] 🔍 Phase 3: Final System Verification")
            overall_success = await self.verify_startup_success()

            if overall_success:
                print(f"\n🎉 DEEPLICA STARTUP COMPLETED SUCCESSFULLY")
                print(f"✅ All critical services are running")
                print(f"🌐 Web Chat available at: {self.web_chat_url}")
                print(f"🔗 System is fully ready for user interaction")

                # Open DeepChat browser
                await self.open_deepchat_browser()

                print(f"\n🎯 DEEPLICA is now FULLY OPERATIONAL and COMMUNICATIVE!")
                print(f"📱 Users can interact via DeepChat web interface")
                print(f"📞 Phone services are ready for calls")
                print(f"🤖 All AI agents are online and ready")
                return True
            else:
                print(f"\n⚠️ DEEPLICA STARTUP COMPLETED WITH WARNINGS")
                print(f"🔧 Some optional services may not be running")
                print(f"🔗 Core system should still be functional")

                # Still try to open DeepChat browser
                await self.open_deepchat_browser()
                return True
                
        except Exception as e:
            print(f"\n❌ DEEPLICA STARTUP FAILED")
            print(f"🚨 Error: {e}")
            print(f"🚨 Error type: {type(e).__name__}")
            import traceback
            print(f"🚨 Full traceback:")
            traceback.print_exc()
            return False

    async def open_deepchat_browser(self):
        """Open DeepChat in browser"""
        try:
            print(f"\n[{self.orchestrator_name}] 🌐 Opening DeepChat in browser...")

            # Wait a moment for web chat to be fully ready
            await asyncio.sleep(2)

            # Try to verify web chat is responding
            try:
                async with httpx.AsyncClient(timeout=5.0) as client:
                    response = await client.get(self.web_chat_url)
                    if response.status_code == 200:
                        print(f"[{self.orchestrator_name}] ✅ Web Chat is responding")
                    else:
                        print(f"[{self.orchestrator_name}] ⚠️ Web Chat returned status {response.status_code}")
            except Exception as e:
                print(f"[{self.orchestrator_name}] ⚠️ Could not verify Web Chat: {e}")

            # Open browser
            webbrowser.open(self.web_chat_url)
            print(f"[{self.orchestrator_name}] 🎯 DeepChat opened at {self.web_chat_url}")

        except Exception as e:
            print(f"[{self.orchestrator_name}] ❌ Failed to open DeepChat: {e}")

    def cleanup_processes(self):
        """Clean up running processes"""
        print(f"\n[{self.orchestrator_name}] 🧹 Cleaning up processes...")
        for service_name, process in self.running_processes.items():
            try:
                if process.poll() is None:  # Process is still running
                    print(f"[{self.orchestrator_name}] 🛑 Terminating {service_name} (PID: {process.pid})")
                    process.terminate()
                    process.wait(timeout=5)
            except Exception as e:
                print(f"[{self.orchestrator_name}] ⚠️ Error terminating {service_name}: {e}")

async def main():
    """Main function"""
    try:
        print("🔍 Initializing DEEPLICA Startup Orchestrator...")
        orchestrator = DeepplicaStartupOrchestrator()
        print("✅ Orchestrator initialized successfully")

        print("🚀 Starting orchestration process...")
        success = await orchestrator.orchestrate_startup()

        if success:
            print("\n🎯 Startup orchestration completed successfully")
            return 0
        else:
            print("\n💥 Startup orchestration failed - check logs above for details")
            orchestrator.cleanup_processes()
            return 1

    except KeyboardInterrupt:
        print(f"\n🛑 Startup orchestration cancelled by user")
        if 'orchestrator' in locals():
            orchestrator.cleanup_processes()
        return 130
    except Exception as e:
        print(f"\n🚨 Unexpected error during startup: {e}")
        print(f"🚨 Error type: {type(e).__name__}")
        import traceback
        print(f"🚨 Full traceback:")
        traceback.print_exc()
        if 'orchestrator' in locals():
            orchestrator.cleanup_processes()
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    # Check if running in debugger (VS Code)
    import sys
    if hasattr(sys, 'gettrace') and sys.gettrace() is not None:
        print(f"\n🔍 Running in debugger mode - exit code would be: {exit_code}")
        if exit_code != 0:
            print("💡 In production, this would exit with error code")
    # Don't exit in production - let the orchestrator restart itself
    # else:
    #     sys.exit(exit_code)  # REMOVED - orchestrator should never exit itself
