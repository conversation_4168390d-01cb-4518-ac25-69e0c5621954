
# =============================================================================
# ALL PORTS ARE MANAGED DYNAMICALLY BY shared/port_manager.py
# =============================================================================
# NO CONSTANT PORTS - ALL ports including Backend API are configurable
# External services will adapt to assigned ports through configuration
# =============================================================================

# Environment Configuration for Microservices
# Copy this file to .env and fill in your values

# Required: Gemini API Key for LLM services
GEMINI_API_KEY=AIzaSyAJEa_NQQ6xFS3zQYfwg6BRzwS9ibr3Lwg

# Database Configuration (using MongoDB Atlas)
MONGODB_CONNECTION_STRING=mongodb+srv://deeplica-db:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0
MONGODB_DATABASE=deeplica-dev

# Environment Settings
ENVIRONMENT=local
DEBUG=true

# Service URLs for local development (non-Docker)
DISPATCHER_URL=http://localhost:8001  # Port managed by port_manager.py
DIALOGUE_AGENT_URL=http://localhost:8004
PLANNER_AGENT_URL=http://localhost:8002
PHONE_AGENT_URL=http://localhost:8003
BACKEND_URL=http://localhost:8888  # Port managed by port_manager.py  # CONSTANT PORT - never changes

# Server Configuration
HOST=0.0.0.0
# ============================================================================
# PORT CONFIGURATION - CENTRALLY MANAGED
# ============================================================================
# IMPORTANT: All ports are managed by shared/port_manager.py
# 
# CONSTANT PORTS (never change):
#   - Backend API: 8888 (required for external integrations)
#   - Twilio Echo Bot: 8009 (webhook compatibility)
#   - Webhook Server: 8010 (external webhook compatibility)
#   - Ngrok API: 4040 (standard ngrok API port)
#   - Ngrok Tunnel: 8080 (default tunnel port)
#
# CONFIGURABLE PORTS (can be changed via admin interface):
#   - All other service ports are dynamically assigned
#
# To change configurable ports:
#   1. Use the admin interface in the web chat
#   2. Or modify shared/deeplica_port_settings.json
#   3. Restart all services for changes to take effect
# ============================================================================
PORT=8888

TWILIO_ACCOUNT_SID=**********************************
TWILIO_AUTH_TOKEN=b6c945587ec27779617634f3b61c13f4
TWILIO_PHONE_NUMBER=+***********
TWILIO_WEBHOOK_URL=https://fbfb-194-90-91-163.ngrok-free.app