#!/usr/bin/env python3
"""
🚀 LAUNCH ALL DEEPLICA SERVICES IN SEPARATE VS CODE TERMINALS
This script launches each microservice in its own VS Code integrated terminal
"""

import os
import sys
import time
import subprocess
from datetime import datetime

# Add project root to path for shared modules
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.port_manager import get_service_port

class VSCodeTerminalLauncher:
    """Launches each microservice in its own VS Code terminal"""
    
    def __init__(self):
        self.launcher_name = "VSCODE-TERMINAL-LAUNCHER"
        self.services = [
            {
                "name": "🐕 Watchdog",
                "command": ["python3", "watchdog/main.py"],
                "cwd": ".",
                "env": {
                    "PYTHONPATH": os.path.abspath("."),
                    "SERVICE_NAME": "WATCHDOG"
                },
                "wait_time": 3
            },
            {
                "name": "🌐 Backend API",
                "command": ["python3", "-m", "app.main"],
                "cwd": "backend",
                "env": {
                    "PYTHONPATH": os.path.abspath("."),
                    "PORT": str(get_service_port("backend")),
                    "SERVICE_NAME": "BACKEND-API"
                },
                "wait_time": 5
            },
            {
                "name": "🎯 Dispatcher",
                "command": ["python3", "-m", "app.main"],
                "cwd": "dispatcher",
                "env": {
                    "PYTHONPATH": os.path.abspath("."),
                    "PORT": str(get_service_port("dispatcher")),
                    "WAIT_FOR_BACKEND": "true",
                    "SERVICE_NAME": "DISPATCHER"
                },
                "wait_time": 3
            },
            {
                "name": "💬 Dialogue Agent",
                "command": ["python3", "-m", "app.main"],
                "cwd": "agents/dialogue",
                "env": {
                    "PYTHONPATH": os.path.abspath("."),
                    "PORT": str(get_service_port("dialogue")),
                    "WAIT_FOR_BACKEND": "true",
                    "SERVICE_NAME": "DIALOGUE-AGENT"
                },
                "wait_time": 2
            },
            {
                "name": "🧠 Planner Agent",
                "command": ["python3", "-m", "app.main"],
                "cwd": "agents/planner",
                "env": {
                    "PYTHONPATH": os.path.abspath("."),
                    "PORT": str(get_service_port("planner")),
                    "WAIT_FOR_BACKEND": "true",
                    "SERVICE_NAME": "PLANNER-AGENT"
                },
                "wait_time": 2
            },
            {
                "name": "📞 Phone Agent",
                "command": ["python3", "-m", "app.main"],
                "cwd": "agents/phone",
                "env": {
                    "PYTHONPATH": os.path.abspath("."),
                    "PORT": str(get_service_port("phone")),
                    "WAIT_FOR_BACKEND": "true",
                    "SERVICE_NAME": "PHONE-AGENT"
                },
                "wait_time": 2
            },
            {
                "name": "🖥️ CLI Terminal",
                "command": ["python3", "cli/main.py"],
                "cwd": ".",
                "env": {
                    "PYTHONPATH": os.path.abspath("."),
                    "SERVICE_NAME": "CLI-TERMINAL"
                },
                "wait_time": 1
            }
        ]

    def create_vscode_terminal_command(self, service_config):
        """Create a command to open a new VS Code terminal and run the service"""
        name = service_config["name"]
        command = service_config["command"]
        cwd = service_config["cwd"]
        env = service_config["env"]
        
        # Build environment variable string
        env_vars = []
        for key, value in env.items():
            env_vars.append(f'export {key}="{value}"')
        env_string = " && ".join(env_vars)
        
        # Build the full command
        cmd_string = " ".join(command)
        full_command = f"{env_string} && cd {cwd} && {cmd_string}"
        
        return full_command

    def launch_service_in_terminal(self, service_config):
        """Launch a service in a new VS Code terminal"""
        name = service_config["name"]
        
        print(f"[{self.launcher_name}] 🚀 Opening new terminal for {name}...")
        
        try:
            # Create the command for the terminal
            terminal_command = self.create_vscode_terminal_command(service_config)
            
            # Use VS Code CLI to create a new terminal and run the command
            # This requires VS Code CLI to be installed and available
            vscode_cmd = [
                "code",
                "--new-window",
                "--command", "workbench.action.terminal.new",
                "--command", f"workbench.action.terminal.sendSequence",
                "--args", f'{"text": "{terminal_command}\\r"}'
            ]
            
            # Alternative approach: Use osascript on macOS to send commands to VS Code
            if sys.platform == "darwin":
                script = f'''
                tell application "Visual Studio Code"
                    activate
                end tell
                
                tell application "System Events"
                    tell process "Visual Studio Code"
                        keystroke "t" using {{command down, shift down}}
                        delay 1
                        keystroke "{terminal_command}"
                        key code 36
                    end tell
                end tell
                '''
                
                subprocess.run(["osascript", "-e", script], check=False)
                print(f"[{self.launcher_name}] ✅ {name} terminal opened via AppleScript")
                return True
            
            # Fallback: Print instructions for manual terminal creation
            print(f"[{self.launcher_name}] 📋 Manual setup required for {name}:")
            print(f"[{self.launcher_name}]   1. Open new VS Code terminal")
            print(f"[{self.launcher_name}]   2. Run: {terminal_command}")
            return True
            
        except Exception as e:
            print(f"[{self.launcher_name}] ❌ Failed to open terminal for {name}: {e}")
            return False

    def launch_all_services(self):
        """Launch all services in separate VS Code terminals"""
        print("🚀 VS CODE TERMINAL LAUNCHER")
        print("=" * 60)
        print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("🎯 Opening each microservice in its own VS Code terminal")
        print("📋 Each service will have its own dedicated terminal tab")
        print("=" * 60)
        
        success_count = 0
        
        for i, service_config in enumerate(self.services, 1):
            name = service_config["name"]
            wait_time = service_config.get("wait_time", 2)
            
            print(f"\n[{self.launcher_name}] 📋 Step {i}/{len(self.services)}: {name}")
            
            if self.launch_service_in_terminal(service_config):
                success_count += 1
                print(f"[{self.launcher_name}] ⏳ Waiting {wait_time}s before next service...")
                time.sleep(wait_time)
            else:
                print(f"[{self.launcher_name}] ⚠️ Failed to launch {name}")
        
        # Final summary
        success_rate = (success_count / len(self.services)) * 100
        print(f"\n[{self.launcher_name}] 📊 LAUNCH SUMMARY:")
        print(f"[{self.launcher_name}] ✅ Successfully launched: {success_count}/{len(self.services)} services")
        print(f"[{self.launcher_name}] 🎯 Success rate: {success_rate:.1f}%")
        
        if success_count == len(self.services):
            print(f"\n🎉 ALL DEEPLICA SERVICES LAUNCHED!")
            print(f"✅ Each service is running in its own VS Code terminal")
            print(f"🔍 Check the terminal tabs to monitor each service")
            print(f"🛑 Use 'STOP DEEPLICA' to stop all services")
        else:
            print(f"\n⚠️ SOME SERVICES FAILED TO LAUNCH")
            print(f"🔧 Check the output above for details")
        
        return success_count == len(self.services)

def main():
    """Main function"""
    launcher = VSCodeTerminalLauncher()
    
    try:
        success = launcher.launch_all_services()
        
        if success:
            print(f"\n🎯 All services launched successfully!")
            print(f"💡 Each microservice is now running in its own VS Code terminal")
        else:
            print(f"\n⚠️ Some services failed to launch")
            
        # Keep the launcher running briefly to show the summary
        time.sleep(5)
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print(f"\n🛑 Launch cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n🚨 Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
