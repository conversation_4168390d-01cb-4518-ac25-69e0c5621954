#!/usr/bin/env python3
"""
Test the fixed backend API that starts immediately
"""

import subprocess
import time
import requests
import sys
import os
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from shared.port_manager import get_service_port

def cleanup_ports():
    """Clean up existing processes"""
    # [BACKEND-API:cleanup_ports] CLEANUP | 🧹 Cleaning up...
    try:
        subprocess.run(["pkill", "-f", "python3.*app.main"], capture_output=True)
        subprocess.run(["lsof", f"-ti:{get_service_port('backend')}"], capture_output=True, text=True)
        time.sleep(2)
    except:
        pass

def test_backend():
    """Test the backend startup"""
    # [BACKEND-API:test_backend] TEST | 🚀 Testing Backend API (Core First Architectur")
    # [BACKEND-API:test_backend] SYSTEM | =" * 5
    
    # Clean up first
    cleanup_ports()
    
    # Start backend
    # [BACKEND-API:test_backend] STARTUP | 🔄 Starting backend...
    env = os.environ.copy()
    env["SKIP_NGROK"] = "true"  # Skip ngrok for testing
    
    process = subprocess.Popen(
        [sys.executable, "-m", "app.main"],
        cwd="/Users/<USER>/Documents/prototype/backend",
        env=env,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        text=True
    )
    
    # Wait a moment for startup
    time.sleep(5)
    
    # Test if backend is responding
    try:
        response = requests.get(f"http://localhost:{get_service_port('backend')}/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            # [BACKEND-API:test_backend] SYSTEM | ✅ Backend is responding: {data.get('status', 'unknown}")
            # [BACKEND-API:test_backend] SUCCESS | 📊 Database ready: {data.get('database_ready', Fals}")
            
            # Test readiness endpoint
            ready_response = requests.get(f"http://localhost:{get_service_port('backend')}/ready", timeout=10)
            if ready_response.status_code == 200:
                # [BACKEND-API:test_backend] SUCCESS | ✅ Backend is READY immediately (as it should be)
                pass
            else:
                # [BACKEND-API:test_backend] SUCCESS | ⚠️ Backend readiness: {ready_response.status_code}
                pass
                
            # Test system status
            try:
                status_response = requests.get(f"http://localhost:{get_service_port('backend')}/system/status", timeout=10)
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    # [BACKEND-API:test_backend] SYSTEM | 📋 System status: {status_data.get('status', 'unknown}")
                    # [BACKEND-API:test_backend] SUCCESS | 🔧 Backend ready: {status_data.get('backend_ready', Fals}")
                else:
                    # [BACKEND-API:test_backend] SYSTEM | ⚠️ System status: {status_response.status_code}
                    pass
            except Exception as e:
                # [BACKEND-API:test_backend] ERROR | ⚠️ System status error: {e}
                
                pass
        else:
            # [BACKEND-API:test_backend] SYSTEM | ❌ Backend not responding: {response.status_code}
            
            pass
    except Exception as e:
        # [BACKEND-API:test_backend] ERROR | ❌ Backend connection failed: {e}
    
    # Show some output from the process
        pass
    print("\n📋 Backend output:")
    try:
        stdout, _ = process.communicate(timeout=5)
        # [BACKEND-API:test_backend] SYSTEM | stdout[-500:]  # Last 500 chars
    except subprocess.TimeoutExpired:
        # [BACKEND-API:test_backend] SYSTEM | Backend is still running...
        process.terminate()
        try:
            stdout, _ = process.communicate(timeout=5)
            # [BACKEND-API:test_backend] SYSTEM | stdout[-500:]  # Last 500 chars
        except:
            process.kill()

if __name__ == "__main__":
    test_backend()
