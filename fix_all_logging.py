#!/usr/bin/env python3
"""
Comprehensive script to fix ALL unidentified logging messages across the entire Deeplica codebase.
This script will systematically find and fix every logger call that doesn't follow the required format:
[SERVICE:routine] CATEGORY | MESSAGE

Required format: [SERVICE:routine] CATEGORY | MESSAGE | key=value
"""

import os
import re
import glob
from typing import List, Tuple, Dict

def get_service_name_from_path(file_path: str) -> str:
    """Extract service name from file path"""
    if "backend" in file_path:
        return "BACKEND-API"
    elif "dispatcher" in file_path:
        return "DISPATCHER"
    elif "dialogue" in file_path:
        return "DIALOGUE-AGENT"
    elif "planner" in file_path:
        return "PLANNER-AGENT"
    elif "phone" in file_path:
        return "PHONE-AGENT"
    elif "cli" in file_path:
        return "CLI-TERMINAL"
    elif "watchdog" in file_path:
        return "WATCHDOG"
    elif "orchestrator" in file_path:
        return "ORCHESTRATOR"
    elif "stop_deeplica" in file_path:
        return "STOP-DEEPLICA"
    else:
        return "SYSTEM"

def extract_function_name(file_content: str, line_number: int) -> str:
    """Extract the function name where the logging call is located"""
    lines = file_content.split('\n')
    
    # Look backwards from the current line to find the function definition
    for i in range(line_number - 1, -1, -1):
        line = lines[i].strip()
        
        # Look for function definitions
        if line.startswith('def ') or line.startswith('async def '):
            # Extract function name
            match = re.match(r'(?:async\s+)?def\s+([a-zA-Z_][a-zA-Z0-9_]*)', line)
            if match:
                return match.group(1)
        
        # Look for class methods
        if 'def ' in line and 'self' in line:
            match = re.search(r'def\s+([a-zA-Z_][a-zA-Z0-9_]*)', line)
            if match:
                return match.group(1)
    
    return "unknown"

def categorize_log_message(message: str, level: str) -> str:
    """Categorize the log message based on its content"""
    message_lower = message.lower()
    
    if any(word in message_lower for word in ['database', 'db', 'mongodb', 'connection']):
        return "DATABASE"
    elif any(word in message_lower for word in ['port', 'server', 'startup', 'shutdown']):
        return "SERVER"
    elif any(word in message_lower for word in ['task', 'mission', 'execute', 'processing']):
        return "TASK"
    elif any(word in message_lower for word in ['health', 'status', 'ready']):
        return "HEALTH"
    elif any(word in message_lower for word in ['error', 'failed', 'exception']):
        return "ERROR"
    elif any(word in message_lower for word in ['process', 'pid', 'kill']):
        return "PROCESS"
    elif any(word in message_lower for word in ['network', 'http', 'request']):
        return "NETWORK"
    elif level.upper() == "ERROR":
        return "ERROR"
    elif level.upper() == "WARNING":
        return "WARNING"
    else:
        return "SYSTEM"

def fix_logger_call(match: re.Match, service_name: str, function_name: str) -> str:
    """Fix a single logger call to follow the required format"""
    level = match.group(1)  # info, warning, error, debug
    original_message = match.group(2)
    
    # Remove quotes and f-string prefix
    message = original_message.strip()
    if message.startswith('f"') or message.startswith("f'"):
        message = message[2:-1]
    elif message.startswith('"') or message.startswith("'"):
        message = message[1:-1]
    
    # Categorize the message
    category = categorize_log_message(message, level)
    
    # Create the fixed format
    fixed_message = f'f"[{service_name}:{function_name}] {category} | {message}"'
    
    return f"logger.{level}({fixed_message})"

def process_file(file_path: str) -> bool:
    """Process a single file and fix all unidentified logger calls"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        service_name = get_service_name_from_path(file_path)
        
        # Find all logger calls that don't start with [
        pattern = r'logger\.(info|warning|error|debug)\(([^[].+?)\)'
        
        lines = content.split('\n')
        modified = False
        
        for i, line in enumerate(lines):
            matches = list(re.finditer(pattern, line))
            if matches:
                function_name = extract_function_name(content, i + 1)
                
                for match in reversed(matches):  # Process in reverse to maintain positions
                    # Skip if already properly formatted
                    if '[' in match.group(2) and ']' in match.group(2):
                        continue
                    
                    fixed_call = fix_logger_call(match, service_name, function_name)
                    
                    # Replace in the line
                    start, end = match.span()
                    lines[i] = lines[i][:start] + fixed_call + lines[i][end:]
                    modified = True
        
        if modified:
            # Write the fixed content back
            fixed_content = '\n'.join(lines)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(fixed_content)
            
            # [SYSTEM:process_file] SYSTEM | ✅ Fixed logging in: {file_path}
            return True
        else:
            # [SYSTEM:process_file] SYSTEM | ⏭️  No fixes needed: {file_path}
            return False
            
    except Exception as e:
        # [SYSTEM:process_file] ERROR | ❌ Error processing {file_path}: {e}
        return False

def main():
    """Main function to process all Python files"""
    print("🔧 [FIX-LOGGING:main] SYSTEM | Starting comprehensive logging format fix...")
    # [SYSTEM:main] SYSTEM | =" * 8
    
    # Find all Python files in the project
    python_files = []
    
    # Add specific directories
    directories = [
        "backend/**/*.py",
        "dispatcher/**/*.py", 
        "agents/**/*.py",
        "cli/**/*.py",
        "watchdog/**/*.py",
        "orchestrator/**/*.py",
        "stop_deeplica/**/*.py",
        "*.py"
    ]
    
    for pattern in directories:
        python_files.extend(glob.glob(pattern, recursive=True))
    
    # Remove duplicates and filter out __pycache__ and .pyc files
    python_files = list(set([f for f in python_files if '__pycache__' not in f and f.endswith('.py')]))
    
    print(f"📁 [FIX-LOGGING:main] SYSTEM | Found {len(python_files)} Python files to process")
    
    fixed_count = 0
    total_count = len(python_files)
    
    for file_path in python_files:
        if process_file(file_path):
            fixed_count += 1
    
    # [SYSTEM:main] SYSTEM | =" * 8
    print(f"🎉 [FIX-LOGGING:main] SYSTEM | Logging format fix complete!")
    print(f"📊 [FIX-LOGGING:main] SYSTEM | Files processed: {total_count}")
    print(f"📊 [FIX-LOGGING:main] SYSTEM | Files fixed: {fixed_count}")
    print(f"📊 [FIX-LOGGING:main] SYSTEM | Files unchanged: {total_count - fixed_count}")

if __name__ == "__main__":
    main()
