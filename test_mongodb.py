#!/usr/bin/env python3
"""
🗄️ Test MongoDB Connection
Tests the MongoDB Atlas connection and database operations
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_mongodb_connection():
    """Test MongoDB connection"""
    print("🗄️ Testing MongoDB Connection...")
    print("=" * 50)
    
    try:
        # Check if MongoDB URI is configured
        mongodb_uri = os.getenv("MONGODB_URI")
        if not mongodb_uri:
            print("❌ MONGODB_URI not found in environment variables")
            print("💡 Please set MONGODB_URI in your .env file")
            return False
        
        print(f"🔍 MongoDB URI configured: {mongodb_uri[:20]}...")
        
        # Try to import and test MongoDB
        try:
            from motor.motor_asyncio import AsyncIOMotorClient
            print("✅ Motor MongoDB driver imported successfully")
        except ImportError:
            print("❌ Motor MongoDB driver not installed")
            print("💡 Install with: pip install motor")
            return False
        
        # Test connection
        print("🔗 Testing MongoDB connection...")
        client = AsyncIOMotorClient(mongodb_uri)
        
        # Test database access
        db = client.deeplica
        
        # Try to ping the database
        await client.admin.command('ping')
        print("✅ MongoDB connection successful!")
        
        # Test basic operations
        print("🧪 Testing basic database operations...")
        
        # Test collection access
        test_collection = db.test_connection
        
        # Insert test document
        test_doc = {"test": "connection", "timestamp": "2025-01-01"}
        result = await test_collection.insert_one(test_doc)
        print(f"✅ Test document inserted: {result.inserted_id}")
        
        # Find test document
        found_doc = await test_collection.find_one({"_id": result.inserted_id})
        if found_doc:
            print("✅ Test document retrieved successfully")
        
        # Clean up test document
        await test_collection.delete_one({"_id": result.inserted_id})
        print("✅ Test document cleaned up")
        
        # Close connection
        client.close()
        print("✅ MongoDB connection closed")
        
        print("\n🎉 MongoDB connection test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ MongoDB connection test failed: {e}")
        print(f"🔍 Error type: {type(e).__name__}")
        return False

async def main():
    """Main function"""
    success = await test_mongodb_connection()
    
    if success:
        print("\n✅ MongoDB is ready for DEEPLICA!")
    else:
        print("\n❌ MongoDB connection issues detected")
        print("💡 Check your MONGODB_URI and network connection")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
