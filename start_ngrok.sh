#!/bin/bash
# =============================================================================
# DEEPLICA SERVICE SCRIPT - ALL PORTS MANAGED DYNAMICALLY
# =============================================================================
# ALL ports are assigned by shared/port_manager.py - NO HARDCODED PORTS
# External services adapt to assigned ports through configuration
# =============================================================================
# =============================================================================
# DEEPLICA SERVICE STARTUP SCRIPT
# =============================================================================
# NOTE: All ports are managed by shared/port_manager.py
# - Backend API: 8888 (CONSTANT - never changes)
# - Other services: configurable via port manager
# =============================================================================

# =============================================================================
# Ngrok Tunnel Startup Script for Phone Agent
# =============================================================================

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🌐 Starting ngrok tunnel for Phone Agent...${NC}"

# Check if ngrok is configured
if ! ngrok config check >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  Ngrok is not configured with an auth token.${NC}"
    echo -e "${YELLOW}Please run: ngrok config add-authtoken YOUR_TOKEN_HERE${NC}"
    echo -e "${YELLOW}Get your token from: https://dashboard.ngrok.com/get-started/your-authtoken${NC}"
    exit 1
fi

# Start ngrok tunnel on dynamically assigned port
PHONE_PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('phone'))")
echo -e "${BLUE}🚀 Starting ngrok tunnel on port ${PHONE_PORT}...${NC}"
# Port is dynamically assigned by port_manager.py
ngrok http ${PHONE_PORT} --log=stdout &

# Wait a moment for ngrok to start
sleep 3

# Get the public URL
echo -e "${GREEN}✅ Ngrok tunnel started!${NC}"
echo -e "${BLUE}📋 To get your webhook URL, run:${NC}"
echo -e "${YELLOW}curl -s http://localhost:${get_service_port("ngrok-api")}/api/tunnels | jq -r '.tunnels[0].public_url'${NC}"
echo ""
echo -e "${BLUE}💡 Update your .env file with:${NC}"
echo -e "${YELLOW}TWILIO_WEBHOOK_URL=https://YOUR_NGROK_URL.ngrok.io${NC}"
echo ""
echo -e "${BLUE}🛑 To stop ngrok, press Ctrl+C${NC}"

# Keep the script running
wait
