#!/bin/bash

# Test Microservices Architecture
# This script tests the basic functionality of the microservices setup

echo "🧪 Testing Microservices Architecture"
echo "====================================="

# Check if services are running
echo "📊 Checking service health..."

# Test Backend API
echo -n "Backend API: "
if curl -s http://localhost:8000/health > /dev/null; then
    echo "✅ Healthy"
else
    echo "❌ Not responding"
    exit 1
fi

# Test Dispatcher (internal)
echo -n "Dispatcher: "
if docker exec prototype-dispatcher curl -s http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ Healthy"
else
    echo "❌ Not responding"
fi

# Test Dialogue Agent (internal)
echo -n "Dialogue Agent: "
if docker exec prototype-dialogue-agent curl -s http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ Healthy"
else
    echo "❌ Not responding"
fi

# Test Planner Agent (internal)
echo -n "Planner Agent: "
if docker exec prototype-planner-agent curl -s http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ Healthy"
else
    echo "❌ Not responding"
fi

# Test MongoDB
echo -n "MongoDB: "
if docker exec prototype-mongo mongosh --quiet --eval "db.runCommand('ping')" > /dev/null 2>&1; then
    echo "✅ Connected"
else
    echo "❌ Not connected"
fi

echo ""
echo "🚀 Testing API functionality..."

# Test mission creation
echo "Creating test mission..."
MISSION_RESPONSE=$(curl -s -X POST http://localhost:8000/missions \
  -H "Content-Type: application/json" \
  -d '{"user_input": "Help me test the microservices architecture"}')

if echo "$MISSION_RESPONSE" | grep -q "mission_id"; then
    echo "✅ Mission creation successful"
    MISSION_ID=$(echo "$MISSION_RESPONSE" | grep -o '"mission_id":"[^"]*"' | cut -d'"' -f4)
    echo "Mission ID: $MISSION_ID"
    
    # Wait a moment for processing
    sleep 2
    
    # Test mission status
    echo "Checking mission status..."
    STATUS_RESPONSE=$(curl -s http://localhost:8000/missions/$MISSION_ID)
    
    if echo "$STATUS_RESPONSE" | grep -q "mission_id"; then
        echo "✅ Mission status retrieval successful"
    else
        echo "❌ Mission status retrieval failed"
    fi
    
else
    echo "❌ Mission creation failed"
    echo "Response: $MISSION_RESPONSE"
fi

echo ""
echo "📋 Service logs (last 10 lines each):"
echo "======================================"

echo ""
echo "🎯 Dispatcher logs:"
docker-compose logs --tail=10 dispatcher

echo ""
echo "💬 Dialogue Agent logs:"
docker-compose logs --tail=10 dialogue-agent

echo ""
echo "🧠 Planner Agent logs:"
docker-compose logs --tail=10 planner-agent

echo ""
echo "✅ Microservices test completed!"
echo ""
echo "💡 To continue testing:"
echo "  - Check logs: docker-compose logs -f [service-name]"
echo "  - Test API: curl http://localhost:8000/missions"
echo "  - Access DB: docker exec -it prototype-mongo mongosh prototype"
