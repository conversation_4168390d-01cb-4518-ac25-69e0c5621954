#!/usr/bin/env python3
"""
Final CLI cleanup script to remove ALL remaining debug comments and ensure clean interface.
"""

import re

def clean_cli_final():
    """Final cleanup of CLI terminal UI"""
    
    with open("cli/terminal_ui.py", "r") as f:
        content = f.read()
    
    # Remove ALL debug comments
    content = re.sub(r'\s*# \[CLI-TERMINAL:.*?\n', '\n', content)
    
    # Fix print statements to be clean (remove \n prefixes)
    content = re.sub(r'print\(f?"\\n([^"]*)"', r'print(f"\1"', content)
    content = re.sub(r'print\("\\n([^"]*)"', r'print("\1"', content)
    
    # Clean up extra newlines
    content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
    
    # Write back
    with open("cli/terminal_ui.py", "w") as f:
        f.write(content)
    
    print("✅ CLI final cleanup complete")

if __name__ == "__main__":
    clean_cli_final()
