# 🐕 Deeplica Watchdog Microservice Guide

## 📋 **Overview**

The **Deeplica Watchdog** is a comprehensive system monitoring microservice that runs continuously in the background and provides real-time logging and monitoring of the entire Deeplica ecosystem.

## 🔍 **Monitoring Capabilities**

### **🎯 Core Monitoring:**
- ✅ **Process Monitoring** - All Deeplica microservices and their states
- ✅ **Port Monitoring** - Network port usage (8000-8004)
- ✅ **Service Health** - Health endpoint monitoring and availability
- ✅ **System Resources** - CPU, memory, and disk usage
- ✅ **Performance Tracking** - Resource usage per service

### **🖥️ Extended Monitoring:**
- ✅ **Terminal Activity** - VS Code and terminal process tracking
- ✅ **Network Connections** - Active connections and network activity
- ✅ **Log File Detection** - New log files and error capture
- ✅ **Error Tracking** - Service errors and crash detection
- ✅ **Transaction Logging** - API calls and system transactions

### **🌐 External Service Monitoring:**
- ✅ **Twilio Status** - Configuration and connectivity monitoring
- ✅ **ngrok Tunnel** - Tunnel status and public URL tracking
- ✅ **MongoDB Atlas** - Database connectivity and readiness
- ✅ **Circuit Breaker** - Phone agent circuit breaker state

### **📞 Communication Monitoring:**
- ✅ **Phone Call Content** - Real-time call monitoring and text capture
- ✅ **Error Call Detection** - Alerts when system calls users to report errors
- ✅ **Conversation Logging** - All text communications across services
- ✅ **Error Pattern Detection** - Automatic detection of error messages
- ✅ **Communication Analysis** - Pattern analysis and alert thresholds

### **🔧 Auto-Recovery System:**
- ✅ **Service Auto-Restart** - Automatically restarts crashed microservices
- ✅ **ngrok Auto-Recovery** - Restarts ngrok tunnels when they fail
- ✅ **Health Check Recovery** - Restarts services that become unhealthy
- ✅ **Process Monitoring** - Detects when services stop and restarts them
- ✅ **Recovery Cooldown** - Prevents restart loops with intelligent delays
- ✅ **Stop Service Detection** - Disables auto-recovery when STOP DEEPLICA runs
- ✅ **Recovery Attempt Tracking** - Limits recovery attempts to prevent infinite loops
- ✅ **Smart Recovery Logic** - Progressive delays and attempt limits

### **📊 System Intelligence:**
- ✅ **Change Detection** - Alerts on state changes
- ✅ **Performance Alerts** - High resource usage warnings
- ✅ **Crash Detection** - Service failure identification
- ✅ **Trend Analysis** - Historical performance data
- ✅ **Comprehensive Logging** - Structured JSON logging

## 🎮 **How to Use**

### **🚀 Method 1: VS Code Launch (Recommended)**

1. Open VS Code Debug panel (`Cmd+Shift+D`)
2. Select `🐕 Watchdog (System Monitor)`
3. Click the green play button
4. **Result:** Continuous monitoring in dedicated terminal

### **🚀 Method 2: With All Services**

1. Open VS Code Debug panel (`Cmd+Shift+D`)
2. Select `🚀 ALL SERVICES (Separate VS Code Terminals)`
3. Click the green play button
4. **Result:** All services + Watchdog monitoring

### **🚀 Method 3: Standalone Monitoring**

1. Open VS Code Debug panel (`Cmd+Shift+D`)
2. Select `🐕 Watchdog (System Monitor Only)`
3. Click the green play button
4. **Result:** Only Watchdog for monitoring existing services

### **🚀 Method 4: Command Line**

```bash
python3 watchdog/main.py
```

## 📺 **Real-Time Monitoring Output**

### **🎯 Startup Information:**
```
🐕 Process name set to: DEEPLICA-WATCHDOG
[WATCHDOG:startup    ] WATCHDOG   | Started monitoring system
```

### **📋 Message Format:**
Each log message follows this structured format:
```
[SERVICE:ROUTINE     ] CATEGORY   | MESSAGE | key=value, key=value
```

**Components:**
- **SERVICE** - Which microservice the message is about (BACKEND, PHONE, WATCHDOG, etc.)
- **ROUTINE** - Specific function/routine within the service (startup, health_check, etc.)
- **CATEGORY** - Type of event (PROCESS, HEALTH, PORT, NETWORK, etc.)
- **MESSAGE** - Human-readable description
- **Key details** - Important data points (pid, port, status, etc.)

### **🔄 Process Monitoring:**
```
[BACKEND:startup     ] PROCESS    | Started | pid=12345, status=running, cpu_percent=2.5
[PHONE:status_change ] PROCESS    | Status changed | old_status=running, new_status=sleeping, pid=12346
[DISPATCHER:shutdown ] PROCESS    | Stopped | pid=12347, status=terminated
```

### **🔓 Port Monitoring:**
```
[BACKEND:port_monitor] PORT       | Port opened | port=8000, status=opened
[PHONE:port_monitor  ] PORT       | Port closed | port=8004, status=closed
```

### **🏥 Health Monitoring:**
```
[BACKEND:health_check] HEALTH     | Now healthy | status_code=200, response_time=0.045, healthy=true
[PHONE:health_check  ] HEALTH     | Became unavailable | error=Connection refused
[DISPATCHER:health_check] HEALTH  | Health check timeout | timeout=2s
```

### **🖥️ Terminal Activity:**
```
[VSCODE:process_monitor] TERMINAL | VS Code processes changed from 20 to 23
[VSCODE:terminal_monitor] TERMINAL| Terminal count changed from 2 to 6
```

### **🌐 External Service Monitoring:**
```
[TWILIO:status_monitor] TWILIO    | Status changed to not_configured | status=not_configured
[NGROK:status_monitor] NGROK      | Status changed to stopped | status=stopped
[MONGODB:status_monitor] DATABASE | Status changed to connected | status=connected
```

### **📞 Phone Call Monitoring:**
```
[PHONE:circuit_breaker_monitor] PHONE_CALLS | Circuit breaker state changed to CLOSED
[PHONE:circuit_breaker_alert] PHONE_CALLS | Circuit breaker OPEN - calls blocked
[PHONE:call_monitor] PHONE_CALLS | Active calls detected
```

### **💬 Conversation Monitoring:**
```
[BACKEND:error_detection] CONVERSATION | Error communication detected in BACKEND
[PHONE:error_detection] CONVERSATION | Error communication detected in PHONE
[WATCHDOG:error_analysis] CONVERSATION | High error communication rate detected
```

### **🔧 Auto-Recovery Messages:**
```
[STOP_SERVICE:detection] AUTO_RECOVERY | STOP DEEPLICA detected - disabling auto-recovery
[BACKEND:auto_restart] AUTO_RECOVERY | Attempting to restart backend | attempt=1, max_attempts=3
[BACKEND:restart_success] AUTO_RECOVERY | Started backend recovery process | pid=12345
[NGROK:auto_restart] AUTO_RECOVERY | Attempting to restart ngrok tunnel | attempt=1
[PHONE:recovery_reset] AUTO_RECOVERY | Service phone recovered - resetting attempt counter
[WATCHDOG:recovery_enabler] AUTO_RECOVERY | STOP DEEPLICA no longer running - enabling auto-recovery
```

### **📊 System Monitoring:**
```
[SYSTEM:resource_monitor] SYSTEM  | High CPU usage detected | cpu_percent=85.2
[PHONE:performance_monitor] PERFORMANCE | High memory usage detected | memory_percent=82.1, pid=12348
[SYSTEM:resource_monitor] SYSTEM  | Memory usage normalized | memory_percent=45.3
```

### **📋 System Summary:**
```
[SUMMARY] System status summary | Data: {
    "watchdog_uptime": "0:05:23.456789",
    "active_services": 6,
    "active_ports": 5,
    "total_errors": 2,
    "total_transactions": 45,
    "avg_cpu": 21.8,
    "avg_memory": 57.4
}
```

## 📁 **Log Categories**

### **🎯 WATCHDOG** - Watchdog lifecycle events
- Startup and shutdown
- Configuration changes
- Monitoring status

### **🔄 PROCESS** - Process monitoring
- Service starts and stops
- Status changes
- Process crashes

### **🔓 PORT** - Port monitoring
- Port opens and closes
- Port conflicts
- Network binding

### **🏥 HEALTH** - Service health
- Health check results
- Service availability
- Response times

### **🖥️ TERMINAL** - Terminal activity
- VS Code processes
- Terminal sessions
- Development activity

### **🌐 NETWORK** - Network monitoring
- Connection changes
- Network activity
- API transactions

### **📊 SYSTEM** - System resources
- CPU, memory, disk usage
- Performance alerts
- Resource trends

### **⚠️ PERFORMANCE** - Performance issues
- High resource usage
- Service bottlenecks
- Performance degradation

### **📋 SUMMARY** - Periodic summaries
- System status overview
- Aggregate metrics
- Health reports

## 🔧 **Configuration**

### **📊 Monitoring Intervals:**
- **Core monitoring:** Every 5 seconds
- **Extended monitoring:** Every 10 seconds
- **Log monitoring:** Every 30 seconds
- **System summary:** Every 5 minutes

### **🎯 Monitored Services:**
```python
services = {
    "backend": {"port": 8000, "health_url": "http://localhost:8000/api/v1/health"},
    "dispatcher": {"port": 8001, "health_url": "http://localhost:8001/health"},
    "dialogue": {"port": 8002, "health_url": "http://localhost:8002/health"},
    "planner": {"port": 8003, "health_url": "http://localhost:8003/health"},
    "phone": {"port": 8004, "health_url": "http://localhost:8004/health"},
    "cli": {"port": None, "process_name": "DEEPLICA-CLI-TERMINAL"},
    "orchestrator": {"port": None, "process_name": "DEEPLICA-ORCHESTRATOR"},
    "stop": {"port": None, "process_name": "DEEPLICA-STOP-SERVICE"}
}
```

### **📁 Log Storage:**
- **Console output:** Real-time terminal display
- **File logging:** `logs/watchdog_YYYYMMDD_HHMMSS.log`
- **Structured data:** JSON format for analysis
- **Retention:** Last 1000 events per category

## 🛡️ **Error Detection**

### **🚨 Service Crashes:**
```
[PROCESS] Service backend stopped | Data: {
    "pid": 12345,
    "last_status": "running",
    "crash_time": "2025-07-04T06:20:15.123456"
}
```

### **⚠️ Performance Issues:**
```
[PERFORMANCE] High CPU usage in dispatcher | Data: {
    "service": "dispatcher",
    "cpu_percent": 95.2,
    "pid": 12346
}
```

### **🔌 Connection Problems:**
```
[HEALTH] Service phone became unavailable | Data: {
    "service": "phone",
    "last_healthy": "2025-07-04T06:19:45.123456"
}
```

## 📊 **Performance Metrics**

### **📈 Resource Tracking:**
- **CPU usage:** Per service and system-wide
- **Memory usage:** Process memory consumption
- **Disk usage:** System disk utilization
- **Network activity:** Connection counts and traffic

### **⏱️ Response Times:**
- **Health checks:** Service response times
- **API calls:** Transaction timing
- **System calls:** Process monitoring overhead

### **📋 Statistics:**
- **Uptime tracking:** Service availability
- **Error rates:** Failure frequency
- **Performance trends:** Historical analysis

## 🎯 **Use Cases**

### **🔧 Development:**
- Monitor service startup and shutdown
- Track resource usage during development
- Identify performance bottlenecks
- Debug service interactions

### **🚨 Debugging:**
- Real-time error detection
- Service crash investigation
- Performance issue identification
- System state analysis

### **📊 Monitoring:**
- Production system monitoring
- Health status tracking
- Performance trend analysis
- Capacity planning

### **🛡️ Operations:**
- System health oversight
- Proactive issue detection
- Resource utilization monitoring
- Service dependency tracking

## 🎉 **Result**

The Deeplica Watchdog provides:

- ✅ **Comprehensive monitoring** - All aspects of the Deeplica system
- ✅ **Real-time logging** - Immediate visibility into system state
- ✅ **Error detection** - Proactive issue identification
- ✅ **Performance tracking** - Resource usage and trends
- ✅ **VS Code integration** - Easy launch and monitoring
- ✅ **Structured logging** - JSON format for analysis
- ✅ **Background operation** - Continuous monitoring
- ✅ **Professional insights** - Deep system understanding

**The Watchdog is your eyes and ears into the entire Deeplica ecosystem!** 🐕👁️🚀
