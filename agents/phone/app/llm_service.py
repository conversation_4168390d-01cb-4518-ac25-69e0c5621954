"""
LLM Service Client for Phone Agent - Handles communication with backend LLM service.
"""

import os
import httpx
from typing import Dict, Any, Optional
from shared.unified_logging import get_phone_logger

# Initialize unified logger
logger = get_phone_logger()


class BackendLLMClient:
    """Client for communicating with the backend LLM service"""
    
    def __init__(self):
        """Initialize the LLM client"""
        # Get backend URL from environment
        from shared.port_manager import get_service_port, get_service_host
        self.backend_url = os.getenv('BACKEND_URL', f'http://{get_service_host("backend")}:{get_service_port("backend")}')
        if not self.backend_url.startswith('http'):
            self.backend_url = f"http://{self.backend_url}"

        self.llm_endpoint = f"{self.backend_url}/api/v1/llm/generate"
        self.timeout = 30.0

        logger.info("🧠 LLM client initialized with backend: {self.backend_url}", module="PHONE-AGENT", routine="__init__")
    
    async def generate_response(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        response_format: str = "text"
    ) -> Dict[str, Any]:
        """Generate a response using the backend LLM service"""
        
        request_data = {
            "prompt": prompt,
            "system_prompt": system_prompt,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "response_format": response_format
        }
        
        try:
            # Use bulletproof timeout and connection settings
            timeout = httpx.Timeout(self.timeout, connect=5.0, read=self.timeout, write=10.0, pool=5.0)
            limits = httpx.Limits(max_connections=1, max_keepalive_connections=0)

            async with httpx.AsyncClient(timeout=timeout, limits=limits) as client:
                try:
                    response = await client.post(
                        self.llm_endpoint,
                        json=request_data,
                        headers={"Content-Type": "application/json"}
                    )

                    response.raise_for_status()

                    try:
                        result = response.json()
                        logger.debug("🧠 LLM response received (tokens: {result.get('usage', {}).get('total_tokens', 'unknown')})", module="PHONE-AGENT", routine="generate_response")
                        return result
                    except (ValueError, TypeError) as json_error:
                        logger.error("❌ Invalid JSON response from LLM: {json_error}", module="PHONE-AGENT", routine="generate_response")
                        raise Exception("LLM service returned invalid response format")

                except httpx.ConnectError as e:
                    logger.error("❌ Cannot connect to LLM service: {e}", module="PHONE-AGENT", routine="generate_response")
                    raise Exception("LLM service unavailable (connection failed)")
                except httpx.TimeoutException as e:
                    logger.error("❌ LLM request timed out: {e}", module="PHONE-AGENT", routine="generate_response")
                    raise Exception("LLM service timeout")
                except httpx.ReadTimeout as e:
                    logger.error("❌ LLM read timeout: {e}", module="PHONE-AGENT", routine="generate_response")
                    raise Exception("LLM service processing timeout")
                except httpx.HTTPStatusError as e:
                    logger.error("{e.response.text}", module="PHONE-AGENT:generate_response", routine="HEALTH | ❌ LLM HTTP error: {e.response.status_code}")
                    if e.response.status_code == 503:
                        raise Exception("LLM service temporarily unavailable")
                    elif e.response.status_code == 500:
                        raise Exception("LLM service internal error")
                    else:
                        raise Exception(f"LLM service error: {e.response.status_code}")
                except httpx.RequestError as e:
                    logger.error("❌ LLM request error: {e}", module="PHONE-AGENT", routine="generate_response")
                    raise Exception(f"LLM service request failed: {e}")
                except Exception as e:
                    logger.error("❌ Unexpected LLM HTTP error: {e}", module="PHONE-AGENT", routine="generate_response")
                    raise Exception(f"LLM service unexpected error: {e}")

        except ImportError as e:
            logger.error("❌ HTTP client not available: {e}", module="PHONE-AGENT", routine="generate_response")
            raise Exception("HTTP client not available for LLM service")
        except Exception as e:
            # Re-raise if it's already our custom exception
            if str(e).startswith(("LLM service", "HTTP client")):
                raise
            else:
                logger.error("❌ Unexpected error in LLM request: {e}", module="PHONE-AGENT", routine="generate_response")
                raise Exception(f"LLM service failed: {e}")
    
    async def analyze_conversation(
        self,
        question: str,
        conversation_transcript: str,
        context: str
    ) -> Dict[str, Any]:
        """Analyze conversation to determine if question was answered"""

        system_prompt = """You are an AI assistant analyzing phone conversations to determine if specific tasks were completed successfully.

Your task is to:
1. Analyze the conversation transcript
2. Determine if the task was completed successfully
3. Look specifically for confirmation responses like "OK", "yes", "got it", "understood", etc.
4. For script-based calls (like saying "EEL-EEL" multiple times), check if the script was delivered and acknowledged

Special handling for "EEL-EEL" script calls:
- The task is successful if the human responds with "OK", "yes", "got it", "understood", or similar confirmation
- The task is NOT successful if the human says "no", "what?", "I don't understand", or hangs up
- If no clear response, the task should be retried

Respond with a JSON object containing:
- "question_answered": boolean - true if the task was completed successfully
- "extracted_answer": string - the confirmation response from the human (e.g., "OK", "yes")
- "should_continue": boolean - true if conversation should continue, false if it should end
- "next_response": string - what the agent should say next (if continuing)
- "analysis": string - brief explanation of your decision
- "confirmation_received": boolean - true if human gave clear confirmation (OK, yes, etc.)"""

        user_prompt = f"""ORIGINAL QUESTION: {question}

CONTEXT: {context}

CONVERSATION TRANSCRIPT:
{conversation_transcript}

Analyze this conversation and determine if the original question was adequately answered."""

        return await self.generate_response(
            prompt=user_prompt,
            system_prompt=system_prompt,
            temperature=0.3,
            response_format="json"
        )
    
    async def generate_conversation_response(
        self,
        question: str,
        context: str,
        conversation_so_far: str,
        is_first_turn: bool = False
    ) -> Dict[str, Any]:
        """Generate appropriate response for conversation turn"""
        
        system_prompt = """You are an AI assistant making a phone call to ask a specific question. 

Guidelines:
- Be polite and professional
- Stay focused on the original question
- If the person goes off-topic, gently redirect them
- Keep responses concise and clear
- If you get the answer, thank them and prepare to end the call
- If they can't answer, politely acknowledge and prepare to end the call

Respond with a JSON object containing:
- "response": string - what you should say to the person
- "should_end_call": boolean - true if the call should end after this response"""

        if is_first_turn:
            user_prompt = f"""This is the beginning of a phone call. 

QUESTION TO ASK: {question}
CONTEXT: {context}

Generate an appropriate greeting and ask the question clearly."""
        else:
            user_prompt = f"""ORIGINAL QUESTION: {question}
CONTEXT: {context}

CONVERSATION SO FAR:
{conversation_so_far}

The person just spoke. Generate an appropriate response to continue the conversation or conclude it."""

        return await self.generate_response(
            prompt=user_prompt,
            system_prompt=system_prompt,
            temperature=0.7,
            response_format="json"
        )
