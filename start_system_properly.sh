#!/bin/bash
# =============================================================================
# DEEPLICA SERVICE SCRIPT - ALL PORTS MANAGED DYNAMICALLY
# =============================================================================
# ALL ports are assigned by shared/port_manager.py - NO HARDCODED PORTS
# External services adapt to assigned ports through configuration
# =============================================================================

# =============================================================================
# AI Mission Orchestration System - Proper Sequential Startup
# =============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to wait for service to be healthy
wait_for_service() {
    local service_name=$1
    local url=$2
    local max_attempts=${3:-30}
    local attempt=1
    
    print_status "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" >/dev/null 2>&1; then
            print_success "$service_name is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to start after $((max_attempts * 2)) seconds"
    return 1
}

# Function to start service and wait for it to be ready
start_and_wait() {
    local service_name=$1
    local service_dir=$2
    local health_url=$3
    local env_vars=$4

    print_status "Starting $service_name..."

    # Get absolute path to logs directory
    local logs_dir="$(pwd)/logs"
    local project_root="$(pwd)"

    cd "$service_dir"

    # Export environment variables properly
    if [[ -n "$env_vars" ]]; then
        # Parse and export each environment variable
        IFS=' ' read -ra ENV_ARRAY <<< "$env_vars"
        for env_var in "${ENV_ARRAY[@]}"; do
            export "$env_var"
        done
    fi

    # Start the service with proper environment
    python3 -m app.main > "${logs_dir}/${service_name}.log" 2>&1 &
    local pid=$!
    echo $pid > "${logs_dir}/${service_name}.pid"
    cd - > /dev/null

    # Wait for service to be healthy
    if wait_for_service "$service_name" "$health_url"; then
        print_success "$service_name started successfully (PID: $pid)"
        return 0
    else
        print_error "$service_name failed to start"
        kill $pid 2>/dev/null || true
        return 1
    fi
}

# Print banner
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║           🤖 AI Mission Orchestration System v1             ║"
echo "║                Sequential Startup (Safe)                     ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo

# Clean up any existing processes
print_status "Cleaning up existing processes..."
for port in 8000 8001 8002 8003 8004; do
    pid=$(lsof -ti:$port 2>/dev/null || true)
    if [[ -n "$pid" ]]; then
        print_status "Killing process on port $port (PID: $pid)"
        kill -9 $pid 2>/dev/null || true
        sleep 1
    fi
done

# Create logs directory
mkdir -p logs

# Step 1: Start Backend API (CRITICAL - must be first)
print_status "🚀 Step 1: Starting Backend API..."
print_status "Starting backend..."
logs_dir="$(pwd)/logs"
cd backend
python3 -m app.main > "${logs_dir}/backend.log" 2>&1 &
BACKEND_PID=$!
echo $BACKEND_PID > "${logs_dir}/backend.pid"
cd ..

# Wait longer for backend to be ready (MongoDB Atlas connection can be slow)
print_status "Waiting for backend to be ready (this may take up to 60 seconds for MongoDB Atlas)..."
if wait_for_service "Backend API" "http://localhost:8000/health" 60; then
    print_success "Backend API started successfully (PID: $BACKEND_PID)"
else
    print_error "❌ Backend API failed to start - cannot continue"
    kill $BACKEND_PID 2>/dev/null || true
    exit 1
fi

# Step 2: Start Dispatcher (depends on Backend)
print_status "🚀 Step 2: Starting Dispatcher..."
if ! start_and_wait "dispatcher" "dispatcher" "http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('dispatcher')")/health" "DISPATCHER_PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('dispatcher')")"; then
    print_error "❌ Dispatcher failed to start"
    exit 1
fi

# Step 3: Start Dialogue Agent (depends on Backend)
print_status "🚀 Step 3: Starting Dialogue Agent..."
if ! start_and_wait "dialogue-agent" "agents/dialogue" "http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('dialogue')")/health" "DIALOGUE_AGENT_PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('dialogue')")"; then
    print_error "❌ Dialogue Agent failed to start"
    exit 1
fi

# Step 4: Start Planner Agent (depends on Backend)
print_status "🚀 Step 4: Starting Planner Agent..."
if ! start_and_wait "planner-agent" "agents/planner" "http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('planner')")/health" "PLANNER_AGENT_PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('planner')")"; then
    print_error "❌ Planner Agent failed to start"
    exit 1
fi

# Step 5: Start Phone Agent (optional - depends on Twilio config)
print_status "🚀 Step 5: Starting Phone Agent..."
if start_and_wait "phone-agent" "agents/phone" "http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('phone')")/health" "PHONE_AGENT_PORT=$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('phone')")"; then
    print_success "Phone Agent started successfully"
else
    print_warning "Phone Agent failed to start (check Twilio credentials)"
fi

echo
print_success "🎉 System startup complete!"
echo
echo "📋 Service Status:"
echo "  • Backend API:      http://localhost:8000 ✅"
echo "  • Dispatcher:       http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('dispatcher')") ✅"
echo "  • Dialogue Agent:   http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('dialogue')") ✅"
echo "  • Planner Agent:    http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('planner')") ✅"
echo "  • Phone Agent:      http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('phone')") (check logs if failed)"
echo
echo "🖥️  To start the CLI:"
echo "  cd cli && python3 terminal_ui.py"
echo
echo "🛑 To stop all services:"
echo "  ./stop_system.sh"
