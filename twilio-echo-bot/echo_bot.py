#!/usr/bin/env python3
"""
Twi<PERSON>-<PERSON>t PoC

A Python script that:
1. Takes a phone number as argument
2. Calls the number using Twilio Voice API
3. Transcribes user's speech (STT)
4. Logs the transcript to console
5. Plays back the same text via TTS immediately

Usage:
    python echo_bot.py +**********
"""

import argparse
import os
import sys
import signal
import time

# Add project root to path for shared modules
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.port_manager import get_service_port, get_service_host
import threading
import atexit
from typing import Optional

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # dotenv not installed, skip automatic loading
    pass

from twilio.rest import Client
from twilio.twiml.voice_response import VoiceResponse, Gather
from flask import Flask, request
from pyngrok import ngrok
from twilio.base.exceptions import TwilioRestException

# Language configurations
LANGUAGES = {
    'en': {
        'code': 'en-US',
        'voice': 'Google.en-US-Standard-C',
        'welcome_message': "Hello! This is Deeplica. Please say something and I will echo it back. Hang up when done.",
        'no_speech_message': "I didn't hear anything. Please try again.",
        'echo_prefix': "You said: "
    },
    'he': {
        'code': 'he-IL',
        'voice': 'Google.he-IL-Standard-A',
        'welcome_message': "שלום! כאן דיפליקה. נא לומר משהו ואני אחזור אחריו. יש לנתק בסיום.",
        'no_speech_message': "לא שמעתי כלום. בבקשה לנסות שוב.",
        'echo_prefix': "אמרת: "
    }
}

# Default language (English)
DEFAULT_LANGUAGE = 'en'

# Route paths
PROCESS_SPEECH_ROUTE = '/process_speech'
NO_SPEECH_ROUTE = '/no_speech'


class TwilioEchoBot:
    def __init__(self, account_sid: str, auth_token: str, twilio_number: str, language: str = DEFAULT_LANGUAGE):
        """Initialize the Twilio Echo Bot."""
        try:
            self.client = Client(account_sid, auth_token)
            # Test the connection
            self.client.api.accounts(account_sid).fetch()
            print("✅ Twilio client initialized successfully")
        except TwilioRestException as e:
            self._handle_twilio_exception(e, "client initialization")
            raise
        except Exception as e:
            print(f"❌ Unexpected error initializing Twilio client: {e}")
            raise

        self.twilio_number = twilio_number

        # Set language configuration
        if language not in LANGUAGES:
            print(f"⚠️ Language '{language}' not supported. Using default language '{DEFAULT_LANGUAGE}'")
            language = DEFAULT_LANGUAGE

        self.language = language
        self.lang_config = LANGUAGES[language]
        print(f"🌐 Language set to: {language} ({self.lang_config['code']})")

        self.app = Flask(__name__)
        self.current_call_sid: Optional[str] = None
        self.is_running = False
        self.ngrok_tunnel = None

        # Set up Flask routes
        self.setup_routes()

        # Set up signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

        # Register cleanup function to run on exit
        atexit.register(self.cleanup)

    def _create_gather(self, action_route: str) -> Gather:
        """Create a standardized Gather object for speech input."""
        return Gather(
            input='speech',
            action=action_route,
            method='POST',
            speech_timeout='auto',
            language=self.lang_config['code']
        )

    def _create_response_with_message(self, message: str) -> VoiceResponse:
        """Create a VoiceResponse with a spoken message."""
        response = VoiceResponse()
        response.say(message, language=self.lang_config['code'], voice=self.lang_config['voice'])
        return response

    def _add_speech_gathering(self, response: VoiceResponse, continue_listening: bool = True) -> VoiceResponse:
        """Add speech gathering capability to a response."""
        gather = self._create_gather(PROCESS_SPEECH_ROUTE)
        response.append(gather)

        if continue_listening:
            response.redirect(NO_SPEECH_ROUTE)

        return response

    def _handle_twilio_exception(self, e: TwilioRestException, context: str) -> None:
        """Handle Twilio exceptions with specific error code messages."""
        print(f"❌ Twilio API error in {context}: {e}")

        error_messages = {
            21212: "💡 Invalid 'To' phone number. Make sure it's in E.164 format (+**********)",
            21213: "💡 Invalid 'From' phone number. Make sure you own this Twilio number",
            21214: "💡 'To' number is not a valid mobile number",
            20404: "ℹ️ Call already ended"
        }

        if e.code in error_messages:
            print(error_messages[e.code])
        else:
            print(f"💡 Error code: {e.code}")

    def setup_routes(self):
        """Set up Flask routes for Twilio webhooks."""
        
        @self.app.route('/voice', methods=['POST'])
        def handle_voice():
            """Handle incoming voice calls and initial setup."""
            print("📞 Call connected!")

            response = self._create_response_with_message(self.lang_config['welcome_message'])
            response = self._add_speech_gathering(response)

            return str(response)

        @self.app.route('/process_speech', methods=['POST'])
        def process_speech():
            """Process transcribed speech and echo it back."""
            speech_result = request.form.get('SpeechResult', '')

            if speech_result:
                print(f"🎤 Transcribed: {speech_result}")

                echo_message = f"{self.lang_config['echo_prefix']}{speech_result}"
                response = self._create_response_with_message(echo_message)
                response = self._add_speech_gathering(response)

                return str(response)
            # Note: The else case (no speech) is now handled by /no_speech endpoint

        @self.app.route('/no_speech', methods=['POST'])
        def no_speech():
            """Handle when no speech is detected."""
            print("🔇 No speech detected")

            response = self._create_response_with_message(self.lang_config['no_speech_message'])
            response = self._add_speech_gathering(response)

            return str(response)

        @self.app.route('/call_status', methods=['POST'])
        def call_status():
            """Handle call status updates."""
            call_status = request.form.get('CallStatus')
            print(f"📊 Call status: {call_status}")

            if call_status in ['completed', 'busy', 'no-answer', 'failed', 'canceled']:
                print("📞 Call ended")
                self.current_call_sid = None
                self.stop()

            return '', 200
    
    def start_ngrok(self) -> str:
        """Start ngrok tunnel and return the public URL."""
        try:
            # Check if ngrok auth token is set (optional but recommended)
            ngrok_token = os.getenv('NGROK_AUTHTOKEN')
            if ngrok_token and ngrok_token != 'your_ngrok_token_here':
                ngrok.set_auth_token(ngrok_token)
                print("🔑 Ngrok auth token configured")
            elif ngrok_token == 'your_ngrok_token_here':
                print("⚠️ Placeholder ngrok token detected, skipping auth token")

            # Start ngrok tunnel
            self.ngrok_tunnel = ngrok.connect(6000)
            public_url = self.ngrok_tunnel.public_url
            print(f"🌐 Ngrok tunnel started: {public_url}")
            return public_url
        except Exception as e:
            print(f"❌ Failed to start ngrok: {e}")

            # Check for common SSL certificate issues
            if "CERTIFICATE_VERIFY_FAILED" in str(e):
                print("\n🔧 SSL Certificate Issue Detected!")
                print("💡 Try these solutions:")
                print("   1. Install certificates: /Applications/Python\\ 3.*/Install\\ Certificates.command")
                print("   2. Install ngrok manually: brew install ngrok")
                print("   3. Update certificates: pip install --upgrade certifi")
                print("   4. Or download ngrok from: https://ngrok.com/download")
            else:
                print("💡 Make sure ngrok is installed and accessible")
                print("💡 You can install it with: pip install pyngrok")
            raise
    
    def make_call(self, to_number: str):
        """Make a call to the specified number."""
        try:
            # Start ngrok tunnel
            webhook_url = self.start_ngrok()

            print(f"📞 Calling {to_number}...")

            call = self.client.calls.create(
                to=to_number,
                from_=self.twilio_number,
                url=f"{webhook_url}/voice",
                status_callback=f"{webhook_url}/call_status",
                status_callback_event=['initiated', 'ringing', 'answered', 'completed'],
                method='POST'
            )

            self.current_call_sid = call.sid
            print(f"📞 Call initiated with SID: {call.sid}")

            return call

        except TwilioRestException as e:
            print(f"❌ Twilio API error: {e}")
            if e.code == 21212:
                print("💡 Invalid 'To' phone number. Make sure it's in E.164 format (+**********)")
            elif e.code == 21213:
                print("💡 Invalid 'From' phone number. Make sure you own this Twilio number")
            elif e.code == 21214:
                print("💡 'To' number is not a valid mobile number")
            else:
                print(f"💡 Error code: {e.code}")
            raise
        except Exception as e:
            print(f"❌ Failed to make call: {e}")
            raise
    
    def run_server(self):
        """Run the Flask server."""
        server_port = get_service_port("twilio-echo-bot")
        self.app.run(host=get_service_host("twilio-echo-bot"), port=server_port, debug=False, use_reloader=False)
    
    def start(self, phone_number: str):
        """Start the echo bot."""
        self.is_running = True
        
        try:
            # Start Flask server in a separate thread
            server_thread = threading.Thread(target=self.run_server, daemon=True)
            server_thread.start()
            
            # Give server time to start
            time.sleep(2)
            
            # Make the call
            self.make_call(phone_number)
            
            print("🤖 Echo bot is running. Press Ctrl+C to stop.")
            print("📞 Waiting for call to complete...")
            
            # Keep the main thread alive
            while self.is_running and self.current_call_sid:
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n🛑 Received interrupt signal")
        except Exception as e:
            print(f"❌ Error: {e}")
        finally:
            self.cleanup()
    
    def stop(self):
        """Stop the echo bot."""
        self.is_running = False
    
    def cleanup(self):
        """Clean up resources and hang up any active calls."""
        if not hasattr(self, '_cleanup_called'):
            self._cleanup_called = True
            print("🧹 Cleaning up...")

            if self.current_call_sid:
                try:
                    print(f"📞 Hanging up call {self.current_call_sid}")
                    self.client.calls(self.current_call_sid).update(status='completed')
                    print("✅ Call hung up successfully")
                except TwilioRestException as e:
                    if e.code == 20404:
                        print("ℹ️ Call already ended")
                    else:
                        print(f"⚠️ Twilio error hanging up call: {e}")
                except Exception as e:
                    print(f"⚠️ Failed to hang up call: {e}")

            if self.ngrok_tunnel:
                try:
                    print("🌐 Closing ngrok tunnel")
                    ngrok.disconnect(self.ngrok_tunnel.public_url)
                    print("✅ Ngrok tunnel closed")
                except Exception as e:
                    print(f"⚠️ Failed to close ngrok tunnel: {e}")

            print("✅ Cleanup complete")
    
    def signal_handler(self, signum, frame):
        """Handle system signals for graceful shutdown."""
        _ = frame  # Unused parameter
        print(f"\n🛑 Received signal {signum}")
        self.stop()


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Twilio Echo-Bot PoC with multilingual support')
    parser.add_argument('phone_number', help='Phone number to call (e.g., +**********)')
    parser.add_argument('--language', '-l',
                       choices=list(LANGUAGES.keys()),
                       default=DEFAULT_LANGUAGE,
                       help=f'Language for the bot (default: {DEFAULT_LANGUAGE}). Available: {", ".join(LANGUAGES.keys())}')

    args = parser.parse_args()

    # Get Twilio credentials from environment variables
    account_sid = os.getenv('TWILIO_ACCOUNT_SID')
    auth_token = os.getenv('TWILIO_AUTH_TOKEN')
    twilio_number = os.getenv('TWILIO_PHONE_NUMBER')

    if not all([account_sid, auth_token, twilio_number]):
        print("❌ Missing required environment variables:")
        print("   TWILIO_ACCOUNT_SID")
        print("   TWILIO_AUTH_TOKEN")
        print("   TWILIO_PHONE_NUMBER")
        sys.exit(1)

    # Validate phone number format
    phone_number = args.phone_number
    if not phone_number.startswith('+'):
        print("❌ Phone number must start with '+' (e.g., +**********)")
        sys.exit(1)

    # Create and start the echo bot with specified language
    bot = TwilioEchoBot(account_sid, auth_token, twilio_number, args.language)
    bot.start(phone_number)


if __name__ == '__main__':
    main()
