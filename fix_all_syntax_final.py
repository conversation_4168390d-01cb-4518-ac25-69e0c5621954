#!/usr/bin/env python3
"""
Final comprehensive script to fix ALL remaining syntax errors.
This script will add missing pass statements and fix empty code blocks.
"""

import os
import re
import glob
from typing import List, <PERSON><PERSON>

def fix_empty_blocks(content: str) -> str:
    """Fix empty code blocks by adding pass statements"""
    lines = content.split('\n')
    fixed_lines = []

    i = 0
    while i < len(lines):
        line = lines[i]
        fixed_lines.append(line)

        # Check if this line starts a code block that might be empty
        if (line.strip().endswith(':') and
            any(keyword in line for keyword in ['if ', 'else:', 'elif ', 'except', 'try:', 'for ', 'while ', 'def ', 'class ', 'with '])):

            # Look ahead to see if the next non-empty line is at the same or lower indentation
            j = i + 1
            current_indent = len(line) - len(line.lstrip())

            # Skip empty lines and comments
            while j < len(lines) and (not lines[j].strip() or lines[j].strip().startswith('#')):
                fixed_lines.append(lines[j])
                j += 1

            # Check if we need to add pass
            if j >= len(lines):
                # End of file, add pass
                fixed_lines.append(' ' * (current_indent + 4) + 'pass')
            elif j < len(lines):
                next_line = lines[j]
                next_indent = len(next_line) - len(next_line.lstrip())

                # If next line is at same or lower indentation, we need pass
                if next_indent <= current_indent:
                    fixed_lines.append(' ' * (current_indent + 4) + 'pass')

            i = j - 1  # Will be incremented at end of loop

        i += 1

    return '\n'.join(fixed_lines)

def fix_file_syntax(file_path: str) -> bool:
    """Fix syntax errors in a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Apply fixes
        fixed_content = fix_empty_blocks(content)

        # Write back if changed
        if fixed_content != content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(fixed_content)

            print(f"✅ [FIX-SYNTAX:fix_file_syntax] SUCCESS | Fixed syntax in: {file_path}")
            return True
        else:
            print(f"⏭️ [FIX-SYNTAX:fix_file_syntax] SYSTEM | No changes needed: {file_path}")
            return False

    except Exception as e:
        print(f"❌ [FIX-SYNTAX:fix_file_syntax] ERROR | Error processing {file_path}: {type(e).__name__}: {e}")
        return False

def main():
    """Main function to fix syntax errors in all Python files"""
    print("🔧 [FIX-SYNTAX:main] SYSTEM | Starting final syntax error fix...")
    print("=" * 80)

    # Files with known syntax errors
    error_files = [
        "cli/terminal_ui.py",
        "launch_separate_terminals.py",
        "orchestrator/main.py",
        "start_microservice.py",
        "stop_deeplica/main.py",
        "test_backend_fixed.py",
        "test_backend_startup.py",
        "test_phone_call.py",
        "test_resilience.py"
    ]

    print(f"📁 [FIX-SYNTAX:main] SYSTEM | Fixing {len(error_files)} files with syntax errors")

    fixed_count = 0

    for file_path in error_files:
        if os.path.exists(file_path):
            if fix_file_syntax(file_path):
                fixed_count += 1
        else:
            print(f"⚠️ [FIX-SYNTAX:main] WARNING | File not found: {file_path}")

    print("=" * 80)
    print(f"🎉 [FIX-SYNTAX:main] SUCCESS | Final syntax fix complete!")
    print(f"📊 [FIX-SYNTAX:main] SYSTEM | Files processed: {len(error_files)}")
    print(f"📊 [FIX-SYNTAX:main] SYSTEM | Files fixed: {fixed_count}")

if __name__ == "__main__":
    main()