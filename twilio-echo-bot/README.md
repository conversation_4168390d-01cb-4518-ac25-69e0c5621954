# 📌 Twilio Echo-Bot PoC

A Python-based proof of concept that demonstrates real-time speech transcription and text-to-speech echo functionality using <PERSON><PERSON><PERSON>'s Voice API with multilingual support.

## 🎯 What it does

1. **Makes a call** to a specified phone number using Twilio Voice API
2. **Transcribes speech** in real-time using <PERSON><PERSON><PERSON>'s built-in STT (Speech-to-Text)
3. **Logs transcripts** to the console
4. **Echoes back** the transcribed text using TTS (Text-to-Speech)
5. **Supports multiple languages** (English and Hebrew)
6. **Handles one call at a time** with graceful cleanup

## 🌐 Supported Languages

- **English (en)** - Default language
  - Language Code: `en-US`
  - Voice: `Google.en-US-Standard-C`
- **Hebrew (he)**
  - Language Code: `he-IL`
  - Voice: `Google.he-IL-Standard-A`

## 🚀 Quick Start

### Prerequisites

- Python 3.7+
- Twilio account with:
  - Account SID
  - Auth Token
  - Phone number (purchased from Twilio)
- Internet connection for ngrok tunneling

### Installation

1. **Clone/Download** this folder to your local machine

2. **Install dependencies**:
   ```bash
   cd twilio-echo-bot
   pip install -r requirements.txt
   ```

3. **Set up environment variables**:
   ```bash
   # Copy the example file
   cp .env.example .env
   
   # Edit .env with your Twilio credentials
   nano .env  # or use your preferred editor
   ```

4. **Configure your .env file**:
   ```env
   TWILIO_ACCOUNT_SID=your_account_sid_here
   TWILIO_AUTH_TOKEN=your_auth_token_here
   TWILIO_PHONE_NUMBER=+**********
   NGROK_AUTHTOKEN=your_ngrok_token_here  # Optional but recommended
   ```

### Usage

```bash
# Load environment variables and run (English - default)
source .env  # On Windows: set -a; source .env; set +a
python echo_bot.py +**********

# Run with Hebrew language
python echo_bot.py +********** --language he

# Run with explicit English language
python echo_bot.py +********** --language en
```

**Examples**:
```bash
# English (default)
python echo_bot.py +***********

# Hebrew
python echo_bot.py +*********** -l he

# Show help for language options
python echo_bot.py --help
```

## 🔧 Configuration

### Required Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `TWILIO_ACCOUNT_SID` | Your Twilio Account SID | `ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxx` |
| `TWILIO_AUTH_TOKEN` | Your Twilio Auth Token | `your_auth_token_here` |
| `TWILIO_PHONE_NUMBER` | Your Twilio phone number | `+***********` |

### Optional Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NGROK_AUTHTOKEN` | Ngrok auth token for stable URLs | None |

## 📱 How to Get Twilio Credentials

1. **Sign up** at [Twilio Console](https://console.twilio.com/)
2. **Get your credentials**:
   - Account SID: Found on your Console Dashboard
   - Auth Token: Found on your Console Dashboard (click to reveal)
3. **Buy a phone number**:
   - Go to Phone Numbers → Manage → Buy a number
   - Choose a number with Voice capabilities
   - Note: This will cost money (~$1/month + usage)

## 🌐 Ngrok Setup (Recommended)

Ngrok creates a secure tunnel to your local server so Twilio can send webhooks.

1. **Sign up** at [ngrok.com](https://ngrok.com/)
2. **Get your auth token** from the [dashboard](https://dashboard.ngrok.com/get-started/your-authtoken)
3. **Add it to your .env file**:
   ```env
   NGROK_AUTHTOKEN=your_token_here
   ```

## 🎮 Usage Examples

### Basic Usage (English - Default)
```bash
python echo_bot.py +***********
```

### Hebrew Language
```bash
python echo_bot.py +*********** --language he
```

### With Debug Output
```bash
PYTHONUNBUFFERED=1 python echo_bot.py +*********** -l he
```

### Show Available Languages
```bash
python echo_bot.py --help
```

### Expected Output (English)
```
✅ Twilio client initialized successfully
🌐 Language set to: en (en-US)
🌐 Ngrok tunnel started: https://abc123.ngrok.io
📞 Calling +***********...
📞 Call initiated with SID: CAxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
🤖 Echo bot is running. Press Ctrl+C to stop.
📞 Waiting for call to complete...
📞 Call connected!
🎤 Transcribed: Hello, this is a test
📊 Call status: answered
🎤 Transcribed: Can you hear me clearly
📊 Call status: completed
📞 Call ended
🧹 Cleaning up...
✅ Call hung up successfully
✅ Ngrok tunnel closed
✅ Cleanup complete
```

### Expected Output (Hebrew)
```
✅ Twilio client initialized successfully
🌐 Language set to: he (he-IL)
🌐 Ngrok tunnel started: https://abc123.ngrok.io
📞 Calling +***********...
📞 Call initiated with SID: CAxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
🤖 Echo bot is running. Press Ctrl+C to stop.
📞 Waiting for call to complete...
📞 Call connected!
🎤 Transcribed: שלום עולם
📊 Call status: answered
🎤 Transcribed: איך שלומך
📊 Call status: completed
📞 Call ended
🧹 Cleaning up...
✅ Call hung up successfully
✅ Ngrok tunnel closed
✅ Cleanup complete
```

## 🛠️ Troubleshooting

### Common Issues

**"Missing required environment variables"**
- Make sure you've set all required environment variables
- Use `source .env` to load them in your current shell

**"Failed to initialize Twilio client"**
- Check your Account SID and Auth Token
- Verify they're correctly set in your environment

**"Invalid 'To' phone number"**
- Phone numbers must be in E.164 format: `+**********`
- Include the country code

**"Invalid 'From' phone number"**
- Make sure you own the Twilio phone number
- Verify it's correctly set in `TWILIO_PHONE_NUMBER`

**"Failed to start ngrok"**
- Install ngrok: `pip install pyngrok`
- Set your auth token in the environment variables

### Debug Mode

Run with debug output:
```bash
# English
PYTHONUNBUFFERED=1 python echo_bot.py +***********

# Hebrew
PYTHONUNBUFFERED=1 python echo_bot.py +*********** --language he
```

## 🔒 Security Notes

- Never commit your `.env` file to version control
- Keep your Twilio credentials secure
- The ngrok tunnel is temporary and changes each run
- Consider using Twilio's IP allowlisting for production

## 💰 Cost Considerations

- **Twilio phone number**: ~$1/month
- **Outbound calls**: ~$0.013/minute in the US
- **Speech recognition**: ~$0.02/minute
- **Text-to-speech**: ~$0.04/1000 characters

## 🚫 Limitations

- One call at a time
- Requires internet connection for ngrok
- Speech recognition quality depends on phone connection
- No persistent storage (logs only to console)

## 🔧 Advanced Configuration

### Custom Ngrok Configuration
```python
# In echo_bot.py, modify start_ngrok() method
ngrok.connect(6000, subdomain="my-echo-bot")  # Requires paid ngrok plan
```

### Adding New Languages

To add support for additional languages, modify the `LANGUAGES` dictionary in `echo_bot.py`:

```python
LANGUAGES = {
    'en': {
        'code': 'en-US',
        'voice': 'Google.en-US-Standard-C',
        'welcome_message': "Hello! This is Deeplica. Please say something and I will echo it back. Hang up when done.",
        'no_speech_message': "I didn't hear anything. Please try again.",
        'echo_prefix': "You said: "
    },
    'he': {
        'code': 'he-IL',
        'voice': 'Google.he-IL-Standard-A',
        'welcome_message': "שלום הילהיל! כאן דיפליקה. נא לומר משהו ואני אחזור אחריו. יש לנתק בסיום.",
        'no_speech_message': "לא שמעתי כלום. בבקשה לנסות שוב.",
        'echo_prefix': "אמרת: "
    },
    # Add your new language here
    'es': {
        'code': 'es-ES',
        'voice': 'Google.es-ES-Standard-A',
        'welcome_message': "¡Hola! Soy Deeplica. Di algo y lo repetiré. Cuelga cuando termines.",
        'no_speech_message': "No escuché nada. Inténtalo de nuevo.",
        'echo_prefix': "Dijiste: "
    }
}
```

## 📝 License

This is a proof of concept for educational purposes. Use at your own risk.
