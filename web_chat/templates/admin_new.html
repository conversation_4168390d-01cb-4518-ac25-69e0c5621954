<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 DEEPLICA Admin Panel</title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
            color: #333333; /* Ensure dark text on white background */
        }
        .title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #7f8c8d;
            font-size: 1.1rem;
            margin-bottom: 20px;
        }
        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 15px;
            padding: 10px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            gap: 10px;
            color: #333333; /* Ensure dark text on white background */
        }
        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            border: none;
            border-radius: 10px;
            background: transparent;
            color: #666;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #333333; /* Dark text for inactive tabs */
        }
        .tab-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .tab-content {
            display: none;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            color: #333333; /* Ensure dark text on white background */
        }
        .tab-content.active { display: block; }
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 3px solid #f0f0f0;
        }
        .section-title {
            font-size: 24px;
            color: #2c3e50;
            font-weight: 700;
        }
        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        .config-card {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 25px;
            background: #fafbfc;
            transition: all 0.3s ease;
        }
        .config-card:hover {
            border-color: #667eea;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
            transform: translateY(-2px);
        }
        .config-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }
        .config-card-title {
            font-size: 18px;
            font-weight: 700;
            color: #2c3e50;
        }
        .form-group { margin-bottom: 20px; }
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .form-control:disabled {
            background: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
        }
        .form-select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            background: white;
        }
        .password-container { position: relative; }
        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            color: #6c757d;
            font-size: 16px;
            padding: 5px;
        }
        .password-toggle:hover { color: #495057; }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-small { padding: 8px 16px; font-size: 12px; }
        .btn-group { display: flex; gap: 10px; margin-top: 20px; justify-content: center; }

        /* User Management Styles */
        .user-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .user-row:hover {
            background: rgba(0, 255, 255, 0.1);
            border-color: rgba(0, 255, 255, 0.5);
        }

        .user-info {
            flex: 1;
        }

        .user-info strong {
            color: #00ffff;
            font-size: 1.1em;
        }

        .user-info small {
            color: rgba(255, 255, 255, 0.7);
            display: block;
            margin-top: 5px;
        }

        .user-actions {
            display: flex;
            gap: 10px;
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 0.8em;
        }

        #userList {
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }

        /* Terminal Styles */
        .terminals-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .terminal-card {
            background: #1a1a1a;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            border: 1px solid #333;
        }

        .terminal-header {
            background: linear-gradient(135deg, #2d3748, #4a5568);
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #333;
        }

        .terminal-title {
            color: #00ffff;
            font-weight: 600;
            font-size: 14px;
        }

        .terminal-controls {
            display: flex;
            gap: 8px;
        }

        .terminal-content {
            background: #000000;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            padding: 16px;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .terminal-content.frozen {
            border: 2px solid #00ffff;
            background: #001122;
        }

        .terminal-content .loading {
            color: #888;
            font-style: italic;
        }

        .terminal-content .error {
            color: #ff4444;
        }

        .terminal-content .warning {
            color: #ffaa00;
        }

        .terminal-content .info {
            color: #00aaff;
        }

        .terminal-content .success {
            color: #00ff00;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .table th, .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        .table th {
            background: #f8f9fa;
            font-weight: 700;
            color: #495057;
            text-transform: uppercase;
            font-size: 12px;
        }
        .table tr:hover { background: #f8f9fa; }
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 700;
            text-transform: uppercase;
        }
        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        .role-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 700;
            text-transform: uppercase;
        }
        .role-admin { background: #667eea; color: white; }
        .role-user { background: #6c757d; color: white; }
        .role-guest { background: #ffc107; color: #212529; }
        .action-buttons { display: flex; gap: 8px; }
        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin: 20px 0;
            font-weight: 600;
            border-left: 4px solid;
        }
        .alert-success { background: #d4edda; color: #155724; border-color: #28a745; }
        .alert-warning { background: #fff3cd; color: #856404; border-color: #ffc107; }
        .alert-error { background: #f8d7da; color: #721c24; border-color: #dc3545; }
        .loading { text-align: center; padding: 40px; color: #6c757d; }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 3px solid #f0f0f0;
        }
        .modal-title { font-size: 22px; color: #2c3e50; font-weight: 700; }
        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #aaa;
        }
        .close:hover { color: #333; }
        .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .checkbox-group { display: flex; align-items: center; gap: 10px; margin-bottom: 15px; }
        .section-subtitle { 
            font-size: 16px; 
            font-weight: 600; 
            color: #495057; 
            margin: 20px 0 15px 0; 
            padding-bottom: 8px; 
            border-bottom: 1px solid #e9ecef; 
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="title">🔧 DEEPLICA Admin Panel</div>
            <div class="subtitle">Complete System Configuration & Management</div>
        </div>

        <!-- Tab Navigation -->
        <div class="nav-tabs">
            <button class="tab-btn active" onclick="showTab('users')">👥 User Management</button>
            <button class="tab-btn" onclick="showTab('system')">⚙️ System Settings</button>
            <button class="tab-btn" onclick="showTab('ports')">🔌 Port Management</button>
            <button class="tab-btn" onclick="showTab('external')">🌐 External Services</button>
            <button class="tab-btn" onclick="showTab('terminals')">💻 Service Terminals</button>
        </div>

        <!-- User Management Tab -->
        <div id="users-tab" class="tab-content active">
            <div class="section-header">
                <h2 class="section-title">👥 User Management</h2>
                <div class="btn-group">
                    <button onclick="debugCurrentUser()" class="btn btn-secondary btn-small">🔍 Debug User</button>
                    <button onclick="testUserAPI()" class="btn btn-secondary">🧪 Test API</button>
                    <button onclick="loadUsers()" class="btn btn-warning">🔄 Refresh</button>
                    <button onclick="showCreateUserModal()" class="btn btn-primary">➕ Add User</button>
                </div>
            </div>
            <div id="usersTableContainer">
                <div class="loading">Loading users...</div>
            </div>
        </div>

        <!-- System Settings Tab -->
        <div id="system-tab" class="tab-content">
            <div class="section-header">
                <h2 class="section-title">⚙️ System Settings</h2>
            </div>
            <div class="config-grid">

                <!-- Database Configuration -->
                <div class="config-card">
                    <div class="config-card-header">
                        <div class="config-card-title">🗄️ Database Configuration</div>
                    </div>

                    <div class="section-subtitle">📊 MongoDB Atlas Connection</div>
                    <div class="form-group">
                        <label class="form-label">Connection String</label>
                        <input type="password" id="mongoConnectionString" class="form-control" placeholder="mongodb+srv://...">
                    </div>

                    <div class="form-group">
                        <label class="form-label">Database Name</label>
                        <input type="text" id="mongoDatabaseName" class="form-control" placeholder="deeplica">
                    </div>

                    <div class="btn-group">
                        <button onclick="testDatabaseConnection()" class="btn btn-primary">🧪 Test Connection</button>
                        <button onclick="loadDatabaseConfig()" class="btn btn-secondary">🔄 Load Current</button>
                        <button onclick="saveDatabaseConfig()" class="btn btn-success">💾 Save Config</button>
                    </div>

                    <div class="section-subtitle">🔐 Password Migration</div>
                    <div class="form-group">
                        <p>Migrate legacy users (admin/guest) to use secure PBKDF2 password hashes:</p>
                        <button onclick="migrateLegacyPasswords()" class="btn btn-warning">🔐 Migrate Passwords</button>
                    </div>

                    <div class="section-subtitle">📋 Connection Test Results</div>
                    <textarea id="dbTestResults" class="form-control" rows="8" readonly
                              placeholder="Click 'Test Connection' to check database connectivity..."></textarea>
                </div>
                <div class="config-card">
                    <div class="config-card-header">
                        <div class="config-card-title">🌐 Host Configuration</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Default Host</label>
                        <select id="hostSelect" class="form-select">
                            <option value="0.0.0.0">0.0.0.0 (All Interfaces)</option>
                            <option value="127.0.0.1">127.0.0.1 (Localhost Only)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">External Host/IP</label>
                        <input type="text" id="externalHost" class="form-control" placeholder="Enter external IP">
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-secondary" onclick="detectIP()">🖥️ Detect IP</button>
                        <button class="btn btn-primary" onclick="saveHostSettings()">💾 Save</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Port Management Tab -->
        <div id="ports-tab" class="tab-content">
            <div class="section-header">
                <h2 class="section-title">🔌 Port Management</h2>
                <button onclick="saveAllPorts()" class="btn btn-primary">💾 Save All</button>
            </div>
            <div id="portGrid" class="config-grid">
                <!-- Port configuration will be loaded here -->
            </div>
        </div>

        <!-- External Services Tab -->
        <div id="external-tab" class="tab-content">
            <div class="section-header">
                <h2 class="section-title">🌐 External Services</h2>
                <button onclick="saveAllServices()" class="btn btn-primary">💾 Save All</button>
            </div>

            <!-- MongoDB Atlas -->
            <div class="config-card">
                <div class="config-card-header">
                    <div class="config-card-title">🍃 MongoDB Atlas</div>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" id="mongodb-enabled" checked>
                    <label for="mongodb-enabled">Enable MongoDB Atlas</label>
                </div>
                <div class="form-group">
                    <label class="form-label">Connection String</label>
                    <div class="password-container">
                        <input type="password" id="mongodb-connection-string" class="form-control"
                               value="mongodb+srv://deeplica-db:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0">
                        <button type="button" class="password-toggle" onclick="togglePassword('mongodb-connection-string')">👁️</button>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Database Name</label>
                    <input type="text" id="mongodb-database" class="form-control" value="deeplica-dev">
                </div>
                <div class="btn-group">
                    <button class="btn btn-success" onclick="testService('mongodb')">🧪 Test</button>
                    <button class="btn btn-primary" onclick="saveService('mongodb')">💾 Save</button>
                </div>
            </div>

            <!-- Google Gemini API -->
            <div class="config-card">
                <div class="config-card-header">
                    <div class="config-card-title">🤖 Google Gemini API</div>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" id="gemini-enabled" checked>
                    <label for="gemini-enabled">Enable Gemini API</label>
                </div>
                <div class="form-group">
                    <label class="form-label">API Key</label>
                    <div class="password-container">
                        <input type="password" id="gemini-api-key" class="form-control"
                               value="AIzaSyAJEa_NQQ6xFS3zQYfwg6BRzwS9ibr3Lwg">
                        <button type="button" class="password-toggle" onclick="togglePassword('gemini-api-key')">👁️</button>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Model</label>
                    <select id="gemini-model" class="form-select">
                        <option value="gemini-1.5-flash" selected>Gemini 1.5 Flash</option>
                        <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
                        <option value="gemini-2.0-flash">Gemini 2.0 Flash</option>
                    </select>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Temperature</label>
                        <input type="number" id="gemini-temperature" class="form-control" value="0.7" min="0" max="2" step="0.1">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Max Tokens</label>
                        <input type="number" id="gemini-max-tokens" class="form-control" value="2048" min="1" max="8192">
                    </div>
                </div>
                <div class="btn-group">
                    <button class="btn btn-success" onclick="testService('gemini')">🧪 Test</button>
                    <button class="btn btn-primary" onclick="saveService('gemini')">💾 Save</button>
                </div>
            </div>

            <!-- Twilio Service -->
            <div class="config-card">
                <div class="config-card-header">
                    <div class="config-card-title">📞 Twilio Service</div>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" id="twilio-enabled" checked>
                    <label for="twilio-enabled">Enable Twilio Service</label>
                </div>

                <div class="section-subtitle">🔐 Authentication</div>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Account SID</label>
                        <div class="password-container">
                            <input type="password" id="twilio-account-sid" class="form-control"
                                   value="**********************************">
                            <button type="button" class="password-toggle" onclick="togglePassword('twilio-account-sid')">👁️</button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Auth Token</label>
                        <div class="password-container">
                            <input type="password" id="twilio-auth-token" class="form-control"
                                   value="b6c945587ec27779617634f3b61c13f4">
                            <button type="button" class="password-toggle" onclick="togglePassword('twilio-auth-token')">👁️</button>
                        </div>
                    </div>
                </div>

                <div class="section-subtitle">📱 Phone Configuration</div>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Twilio Phone Number</label>
                        <input type="tel" id="twilio-phone-number" class="form-control" value="+***********">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Country Code</label>
                        <select id="twilio-country-code" class="form-select">
                            <option value="IL" selected>Israel (+972)</option>
                            <option value="US">United States (+1)</option>
                            <option value="GB">United Kingdom (+44)</option>
                            <option value="DE">Germany (+49)</option>
                            <option value="FR">France (+33)</option>
                        </select>
                    </div>
                </div>

                <div class="section-subtitle">🎤 Voice Configuration</div>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Voice</label>
                        <select id="twilio-voice" class="form-select">
                            <option value="alice" selected>Alice</option>
                            <option value="man">Man</option>
                            <option value="woman">Woman</option>
                            <option value="Polly.Joanna">Polly Joanna</option>
                            <option value="Polly.Matthew">Polly Matthew</option>
                            <option value="Polly.Amy">Polly Amy</option>
                            <option value="Polly.Brian">Polly Brian</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Language</label>
                        <select id="twilio-language" class="form-select">
                            <option value="en-US" selected>English (US)</option>
                            <option value="en-GB">English (UK)</option>
                            <option value="he-IL">Hebrew (Israel)</option>
                            <option value="es-ES">Spanish (Spain)</option>
                            <option value="fr-FR">French (France)</option>
                            <option value="de-DE">German (Germany)</option>
                            <option value="it-IT">Italian (Italy)</option>
                            <option value="pt-BR">Portuguese (Brazil)</option>
                            <option value="ja-JP">Japanese (Japan)</option>
                            <option value="ko-KR">Korean (Korea)</option>
                            <option value="zh-CN">Chinese (Mandarin)</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Speech Rate</label>
                    <select id="twilio-speech-rate" class="form-select">
                        <option value="x-slow">Extra Slow</option>
                        <option value="slow">Slow</option>
                        <option value="medium" selected>Medium</option>
                        <option value="fast">Fast</option>
                        <option value="x-fast">Extra Fast</option>
                    </select>
                </div>

                <div class="section-subtitle">🔗 Webhook Configuration</div>
                <div class="form-group">
                    <label class="form-label">Webhook URL</label>
                    <input type="url" id="twilio-webhook-url" class="form-control"
                           value="https://3c8e1372217d.ngrok-free.app">
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Webhook Method</label>
                        <select id="twilio-webhook-method" class="form-select">
                            <option value="POST" selected>POST</option>
                            <option value="GET">GET</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Status Callback URL</label>
                        <input type="url" id="twilio-status-callback" class="form-control"
                               placeholder="Optional status callback URL">
                    </div>
                </div>

                <div class="section-subtitle">☎️ Call Configuration</div>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Call Timeout (seconds)</label>
                        <input type="number" id="twilio-timeout" class="form-control" value="300" min="10" max="3600">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Max Call Duration (seconds)</label>
                        <input type="number" id="twilio-max-duration" class="form-control" value="1800" min="60" max="14400">
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Recording</label>
                    <select id="twilio-recording" class="form-select">
                        <option value="do-not-record" selected>Do Not Record</option>
                        <option value="record-from-answer">Record From Answer</option>
                        <option value="record-from-ringing">Record From Ringing</option>
                    </select>
                </div>

                <div class="btn-group">
                    <button class="btn btn-success" onclick="testService('twilio')">🧪 Test</button>
                    <button class="btn btn-primary" onclick="saveService('twilio')">💾 Save</button>
                </div>
            </div>

            <!-- ngrok Service -->
            <div class="config-card">
                <div class="config-card-header">
                    <div class="config-card-title">🌐 ngrok Service</div>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" id="ngrok-enabled" checked>
                    <label for="ngrok-enabled">Enable ngrok Service</label>
                </div>

                <div class="section-subtitle">🔐 Authentication</div>
                <div class="form-group">
                    <label class="form-label">Auth Token</label>
                    <div class="password-container">
                        <input type="password" id="ngrok-auth-token" class="form-control"
                               value="2zKQOOvkCEGYjCn1dZRRAs5c1B2_oy2WHwGeuMASQdDtdZhnTWILIO">
                        <button type="button" class="password-toggle" onclick="togglePassword('ngrok-auth-token')">👁️</button>
                    </div>
                </div>

                <div class="section-subtitle">🚇 Tunnel Configuration</div>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Tunnel Port</label>
                        <input type="number" id="ngrok-tunnel-port" class="form-control" value="8080" min="1000" max="65535">
                    </div>
                    <div class="form-group">
                        <label class="form-label">API Port</label>
                        <input type="number" id="ngrok-api-port" class="form-control" value="4040" min="1000" max="65535">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Region</label>
                        <select id="ngrok-region" class="form-select">
                            <option value="us" selected>United States (us)</option>
                            <option value="eu">Europe (eu)</option>
                            <option value="ap">Asia Pacific (ap)</option>
                            <option value="au">Australia (au)</option>
                            <option value="sa">South America (sa)</option>
                            <option value="jp">Japan (jp)</option>
                            <option value="in">India (in)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Protocol</label>
                        <select id="ngrok-protocol" class="form-select">
                            <option value="http" selected>HTTP</option>
                            <option value="https">HTTPS</option>
                            <option value="tcp">TCP</option>
                            <option value="tls">TLS</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Subdomain (Optional)</label>
                    <input type="text" id="ngrok-subdomain" class="form-control" placeholder="Custom subdomain">
                </div>

                <div class="btn-group">
                    <button class="btn btn-success" onclick="testService('ngrok')">🧪 Test</button>
                    <button class="btn btn-primary" onclick="saveService('ngrok')">💾 Save</button>
                </div>
            </div>
        </div>

        <!-- Service Terminals Tab -->
        <div id="terminals-tab" class="tab-content">
            <div class="section-header">
                <h2 class="section-title">💻 Service Terminals</h2>
                <div class="btn-group">
                    <button onclick="refreshAllTerminals()" class="btn btn-primary">🔄 Refresh All</button>
                    <button onclick="clearAllTerminals()" class="btn btn-warning">🧹 Clear All</button>
                    <button onclick="toggleAutoRefresh()" class="btn btn-secondary" id="autoRefreshBtn">⏸️ Auto-Refresh</button>
                </div>
            </div>

            <div class="terminals-grid">
                <!-- Backend API Terminal -->
                <div class="terminal-card">
                    <div class="terminal-header">
                        <div class="terminal-title">🔧 Backend API</div>
                        <div class="terminal-controls">
                            <button onclick="refreshTerminal('backend')" class="btn btn-sm btn-secondary">🔄</button>
                            <button onclick="clearTerminal('backend')" class="btn btn-sm btn-warning">🧹</button>
                            <button onclick="freezeTerminal('backend')" class="btn btn-sm btn-primary" id="freezeBackend">❄️</button>
                        </div>
                    </div>
                    <div class="terminal-content" id="terminal-backend">
                        <div class="loading">Loading terminal output...</div>
                    </div>
                </div>

                <!-- Dispatcher Terminal -->
                <div class="terminal-card">
                    <div class="terminal-header">
                        <div class="terminal-title">📨 Dispatcher</div>
                        <div class="terminal-controls">
                            <button onclick="refreshTerminal('dispatcher')" class="btn btn-sm btn-secondary">🔄</button>
                            <button onclick="clearTerminal('dispatcher')" class="btn btn-sm btn-warning">🧹</button>
                            <button onclick="freezeTerminal('dispatcher')" class="btn btn-sm btn-primary" id="freezeDispatcher">❄️</button>
                        </div>
                    </div>
                    <div class="terminal-content" id="terminal-dispatcher">
                        <div class="loading">Loading terminal output...</div>
                    </div>
                </div>

                <!-- Planner Terminal -->
                <div class="terminal-card">
                    <div class="terminal-header">
                        <div class="terminal-title">🧠 Planner</div>
                        <div class="terminal-controls">
                            <button onclick="refreshTerminal('planner')" class="btn btn-sm btn-secondary">🔄</button>
                            <button onclick="clearTerminal('planner')" class="btn btn-sm btn-warning">🧹</button>
                            <button onclick="freezeTerminal('planner')" class="btn btn-sm btn-primary" id="freezePlanner">❄️</button>
                        </div>
                    </div>
                    <div class="terminal-content" id="terminal-planner">
                        <div class="loading">Loading terminal output...</div>
                    </div>
                </div>

                <!-- Dialogue Agent Terminal -->
                <div class="terminal-card">
                    <div class="terminal-header">
                        <div class="terminal-title">💬 Dialogue Agent</div>
                        <div class="terminal-controls">
                            <button onclick="refreshTerminal('dialogue')" class="btn btn-sm btn-secondary">🔄</button>
                            <button onclick="clearTerminal('dialogue')" class="btn btn-sm btn-warning">🧹</button>
                            <button onclick="freezeTerminal('dialogue')" class="btn btn-sm btn-primary" id="freezeDialogue">❄️</button>
                        </div>
                    </div>
                    <div class="terminal-content" id="terminal-dialogue">
                        <div class="loading">Loading terminal output...</div>
                    </div>
                </div>

                <!-- Phone Agent Terminal -->
                <div class="terminal-card">
                    <div class="terminal-header">
                        <div class="terminal-title">📞 Phone Agent</div>
                        <div class="terminal-controls">
                            <button onclick="refreshTerminal('phone')" class="btn btn-sm btn-secondary">🔄</button>
                            <button onclick="clearTerminal('phone')" class="btn btn-sm btn-warning">🧹</button>
                            <button onclick="freezeTerminal('phone')" class="btn btn-sm btn-primary" id="freezePhone">❄️</button>
                        </div>
                    </div>
                    <div class="terminal-content" id="terminal-phone">
                        <div class="loading">Loading terminal output...</div>
                    </div>
                </div>

                <!-- Web Chat Terminal -->
                <div class="terminal-card">
                    <div class="terminal-header">
                        <div class="terminal-title">🌐 Web Chat</div>
                        <div class="terminal-controls">
                            <button onclick="refreshTerminal('webchat')" class="btn btn-sm btn-secondary">🔄</button>
                            <button onclick="clearTerminal('webchat')" class="btn btn-sm btn-warning">🧹</button>
                            <button onclick="freezeTerminal('webchat')" class="btn btn-sm btn-primary" id="freezeWebchat">❄️</button>
                        </div>
                    </div>
                    <div class="terminal-content" id="terminal-webchat">
                        <div class="loading">Loading terminal output...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Modals -->
    <div id="createUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Create New User</h3>
                <span class="close" onclick="hideCreateUserModal()">&times;</span>
            </div>
            <form id="createUserForm">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Username</label>
                        <input type="text" name="username" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Password</label>
                        <input type="password" name="password" class="form-control" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Full Name</label>
                        <input type="text" name="full_name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Email</label>
                        <input type="email" name="email" class="form-control">
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Role</label>
                    <select name="role" class="form-select" required>
                        <option value="user">User</option>
                        <option value="admin">Admin</option>
                        <option value="guest">Guest</option>
                    </select>
                </div>
                <div class="btn-group">
                    <button type="submit" class="btn btn-primary">Create User</button>
                    <button type="button" onclick="hideCreateUserModal()" class="btn btn-secondary">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Global variables
        let currentUsers = [];

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadUsers();
            loadPortSettings();
        });

        // Tab Management
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');

            // Load content based on tab
            switch(tabName) {
                case 'users':
                    loadUsers();
                    break;
                case 'ports':
                    loadPortSettings();
                    break;
            }
        }

        // Password Toggle Function - ONLY EYE ICON
        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const button = field.nextElementSibling;

            if (field.type === 'password') {
                field.type = 'text';
                button.textContent = '🙈';
            } else {
                field.type = 'password';
                button.textContent = '👁️';
            }
        }

        // Debug Current User Function
        async function debugCurrentUser() {
            showAlert('🔍 Checking current user info...', 'warning');

            try {
                const response = await fetch('/api/admin/debug/current-user');
                const data = await response.json();

                console.log('Current User Debug Info:', data);

                if (data.authenticated) {
                    showAlert(`✅ Current User: ${data.username} (${data.role}) - Admin: ${data.is_admin}`, 'success');

                    // Show detailed info in console
                    console.log('User Details:', {
                        user_id: data.user_id,
                        username: data.username,
                        role: data.role,
                        is_admin: data.is_admin,
                        permissions: data.permissions
                    });
                } else {
                    showAlert(`❌ Not authenticated: ${data.message || data.error}`, 'error');
                }
            } catch (error) {
                console.error('Debug Error:', error);
                showAlert(`❌ Debug failed: ${error.message}`, 'error');
            }
        }

        // API Testing Function
        async function testUserAPI() {
            showAlert('🧪 Testing User API connection...', 'warning');

            try {
                // Test 1: Check if we can reach the API endpoint
                console.log('Testing API endpoint...');
                const response = await fetch('/api/admin/users', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                console.log('API Response Status:', response.status);
                console.log('API Response Headers:', [...response.headers.entries()]);

                if (response.status === 401) {
                    showAlert('❌ API Test Failed: Not authenticated. Please login as admin.', 'error');
                    return;
                } else if (response.status === 403) {
                    showAlert('❌ API Test Failed: Admin role required. Current user lacks admin privileges.', 'error');
                    return;
                } else if (!response.ok) {
                    const errorText = await response.text();
                    showAlert(`❌ API Test Failed: Server error (${response.status}): ${errorText}`, 'error');
                    return;
                }

                const data = await response.json();
                console.log('API Response Data:', data);

                if (data && data.users) {
                    console.log(`✅ API Test Successful! Found ${data.users.length} users. API is working correctly.`);
                    // Success logged to console only - no browser alert per user request
                } else {
                    console.warn('⚠️ API Test: Connected but no user data received. Check server logs.');
                    // Warning logged to console only - no browser alert per user request
                }

            } catch (error) {
                console.error('API Test Error:', error);
                console.error(`❌ API Test Failed: Network error - ${error.message}`);
                // Error logged to console only - no browser alert per user request
            }
        }

        // User Management Functions
        async function loadUsers() {
            try {
                const container = document.getElementById('usersTableContainer');
                container.innerHTML = '<div class="loading">Loading users...</div>';

                console.log('Fetching users from API...');
                const response = await fetch(`/api/admin/users?_t=${Date.now()}`);

                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                if (!response.ok) {
                    if (response.status === 401) {
                        container.innerHTML = `
                            <div class="alert alert-error">
                                ❌ <strong>Authentication Required</strong><br><br>
                                You need to login as admin to access user management.<br><br>
                                <strong>Default Admin Credentials:</strong><br>
                                Username: <code>admin</code><br>
                                Password: <code>admin123</code><br><br>
                                <a href="/chat" class="btn btn-primary">🔐 Go to Login</a>
                            </div>
                        `;
                        return;
                    } else if (response.status === 403) {
                        container.innerHTML = `
                            <div class="alert alert-error">
                                ❌ <strong>Admin Role Required</strong><br><br>
                                Current user does not have admin privileges.<br>
                                Please login with an admin account.<br><br>
                                <strong>Default Admin Credentials:</strong><br>
                                Username: <code>admin</code><br>
                                Password: <code>admin123</code><br><br>
                                <a href="/chat" class="btn btn-primary">🔐 Switch to Admin</a>
                            </div>
                        `;
                        return;
                    } else {
                        const errorText = await response.text();
                        container.innerHTML = `<div class="alert alert-error">❌ Server error (${response.status}): ${errorText}</div>`;
                        return;
                    }
                }

                const data = await response.json();
                console.log('Users data received from Backend API:', data);

                // Handle both old format (data.users) and new format (data is array or data.users)
                let users = [];
                if (Array.isArray(data)) {
                    users = data;
                } else if (data.users && Array.isArray(data.users)) {
                    users = data.users;
                } else if (data.success && data.users) {
                    users = data.users;
                } else {
                    console.warn('Unexpected data format:', data);
                    showMockUsers();
                    return;
                }

                currentUsers = users;
                renderUsersTable(users);
                console.log(`✅ Successfully loaded ${users.length} users from Backend API`);
            } catch (error) {
                console.error('Error loading users:', error);
                showAlert('⚠️ API connection failed. Showing demo data for testing.', 'warning');
                showMockUsers();
            }
        }

        // Show mock users for testing when API is not available
        function showMockUsers() {
            const mockUsers = [
                {
                    user_id: "mock-1",
                    username: "admin",
                    full_name: "System Administrator",
                    email: "<EMAIL>",
                    role: "admin",
                    status: "active",
                    last_login: new Date().toISOString()
                },
                {
                    user_id: "mock-2",
                    username: "eran",
                    full_name: "Eran User",
                    email: "<EMAIL>",
                    role: "user",
                    status: "active",
                    last_login: new Date(Date.now() - 86400000).toISOString()
                },
                {
                    user_id: "mock-3",
                    username: "guest",
                    full_name: "Guest User",
                    email: "<EMAIL>",
                    role: "guest",
                    status: "inactive",
                    last_login: null
                }
            ];

            currentUsers = mockUsers;
            renderUsersTable(mockUsers);

            const container = document.getElementById('usersTableContainer');
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-warning';
            alertDiv.innerHTML = `
                ⚠️ <strong>Demo Mode:</strong> Showing mock user data because API is not accessible.<br>
                <strong>To fix:</strong> Ensure you're logged in as admin and the backend service is running.
            `;
            container.insertBefore(alertDiv, container.firstChild);
        }
        }

        function renderUsersTable(users) {
            const container = document.getElementById('usersTableContainer');

            if (!users || users.length === 0) {
                container.innerHTML = '<div class="alert alert-warning">No users found</div>';
                return;
            }

            let html = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>Username</th>
                            <th>Full Name</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Last Login</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            users.forEach(user => {
                const lastLogin = user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never';
                html += `
                    <tr>
                        <td><strong>${user.username}</strong></td>
                        <td>${user.full_name || '-'}</td>
                        <td>${user.email || '-'}</td>
                        <td><span class="role-badge role-${user.role}">${user.role.toUpperCase()}</span></td>
                        <td><span class="status-badge status-${user.status}">${user.status.toUpperCase()}</span></td>
                        <td>${lastLogin}</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-success btn-small" onclick="editUser('${user.user_id}')">✏️ Edit</button>
                                <button class="btn btn-danger btn-small" onclick="deleteUser('${user.user_id}', '${user.username}')">🗑️ Delete</button>
                            </div>
                        </td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        // Port Management Functions
        async function loadPortSettings() {
            try {
                const response = await fetch(`/api/admin/port-settings?_t=${Date.now()}`);
                const data = await response.json();

                if (data.success && data.ports) {
                    renderPortSettings(data.ports);
                }
            } catch (error) {
                console.error('Error loading port settings:', error);
            }
        }

        function renderPortSettings(ports) {
            const container = document.getElementById('portGrid');
            if (!container) return;

            const serviceNames = {
                'backend': '🌐 Backend API',
                'dispatcher': '🎯 Dispatcher Service',
                'dialogue': '💬 Dialogue Agent',
                'planner': '🧠 Planner Agent',
                'phone': '📞 Phone Agent',
                'watchdog': '🐕 Watchdog Monitor',
                'stop-deeplica': '🛑 Stop Deeplica Service',
                'web-chat': '💻 Web Chat Interface',
                'cli': '🖥️ CLI Terminal',
                'twilio-echo-bot': '📱 Twilio Echo Bot',
                'webhook': '🔗 Webhook Server',
                'ngrok-api': '🌐 ngrok API Dashboard',
                'ngrok-tunnel': '🚇 ngrok Tunnel Port',
                'test-server': '🧪 Test Server',
                'dev-server': '⚙️ Development Server',
                'proxy-server': '🔄 Proxy Server'
            };

            let html = '';
            Object.keys(ports).forEach(serviceId => {
                const port = ports[serviceId];
                const isReadonly = serviceId === 'backend';
                const serviceName = serviceNames[serviceId] || serviceId;

                html += `
                    <div class="config-card ${isReadonly ? 'readonly' : ''}">
                        <div class="config-card-header">
                            <div class="config-card-title">${serviceName}</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Port Number</label>
                            <input type="number"
                                   class="form-control"
                                   value="${port.port}"
                                   ${isReadonly ? 'disabled' : ''}
                                   data-service="${serviceId}"
                                   min="1000"
                                   max="65535">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Status</label>
                            <span class="status-badge ${port.status === 'Free' ? 'status-active' : 'status-inactive'}">${port.status || 'Unknown'}</span>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Description</label>
                            <p style="color: #6c757d; font-size: 14px; margin: 0;">${port.description || 'Service port'}</p>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // Service Management Functions
        async function testService(serviceId) {
            showAlert(`Testing ${serviceId} connection...`, 'warning');

            setTimeout(() => {
                const success = Math.random() > 0.2; // 80% success rate
                if (success) {
                    showAlert(`${serviceId} connection test successful!`, 'success');
                } else {
                    showAlert(`${serviceId} connection test failed!`, 'error');
                }
            }, 2000);
        }

        async function saveService(serviceId) {
            showAlert(`Saving ${serviceId} configuration...`, 'warning');

            setTimeout(() => {
                showAlert(`${serviceId} configuration saved successfully!`, 'success');
            }, 1000);
        }

        async function saveAllServices() {
            showAlert('Saving all external services...', 'warning');

            setTimeout(() => {
                showAlert('All external services saved successfully!', 'success');
            }, 2000);
        }

        async function saveAllPorts() {
            showAlert('Saving all port configurations...', 'warning');

            setTimeout(() => {
                showAlert('All port configurations saved successfully!', 'success');
            }, 1500);
        }

        // System Functions
        async function detectIP() {
            showAlert('Detecting external IP...', 'warning');

            try {
                const response = await fetch('https://api.ipify.org?format=json');
                const data = await response.json();

                const externalHost = document.getElementById('externalHost');
                if (externalHost) {
                    externalHost.value = data.ip;
                }

                console.log(`External IP detected: ${data.ip}`);
                // IP detection logged to console only - no browser alert per user request
            } catch (error) {
                showAlert('Could not detect IP automatically', 'warning');
            }
        }

        async function saveHostSettings() {
            const hostSelect = document.getElementById('hostSelect');
            const externalHost = document.getElementById('externalHost');

            console.log(`Host settings saved: ${hostSelect.value}`);
            // Host settings saved - logged to console only, no browser alert per user request
        }

        // User Modal Functions
        function showCreateUserModal() {
            document.getElementById('createUserModal').style.display = 'block';
        }

        function hideCreateUserModal() {
            document.getElementById('createUserModal').style.display = 'none';
            document.getElementById('createUserForm').reset();
        }

        async function loadUsers() {
            console.log('Loading users from Backend API...');

            try {
                const response = await fetch('/api/admin/users');

                if (response.ok) {
                    const result = await response.json();
                    const users = result.users || [];

                    console.log(`Loaded ${users.length} users`);

                    // Update user count display if element exists
                    const userCountElement = document.getElementById('userCount');
                    if (userCountElement) {
                        userCountElement.textContent = users.length;
                    }

                    // Update user list display if element exists
                    const userListElement = document.getElementById('userList');
                    if (userListElement) {
                        userListElement.innerHTML = '';

                        users.forEach(user => {
                            const userRow = document.createElement('div');
                            userRow.className = 'user-row';
                            userRow.innerHTML = `
                                <div class="user-info">
                                    <strong>${user.username}</strong> (${user.full_name || 'No name'})
                                    <br>
                                    <small>Role: ${user.role} | Status: ${user.status} | Created: ${new Date(user.created_at).toLocaleDateString()}</small>
                                </div>
                                <div class="user-actions">
                                    <button onclick="editUser('${user.user_id}')" class="btn btn-sm btn-secondary">Edit</button>
                                    <button onclick="deleteUser('${user.user_id}')" class="btn btn-sm btn-danger">Delete</button>
                                </div>
                            `;
                            userListElement.appendChild(userRow);
                        });
                    }
                } else {
                    console.error('Failed to load users:', response.status);
                }
            } catch (error) {
                console.error('Error loading users:', error);
            }
        }

        function editUser(userId) {
            console.log('Edit user:', userId);
            // TODO: Implement user editing
        }

        function deleteUser(userId) {
            if (confirm('Are you sure you want to delete this user?')) {
                console.log('Delete user:', userId);
                // TODO: Implement user deletion
            }
        }

        // Database Configuration Functions
        async function testDatabaseConnection() {
            const resultsArea = document.getElementById('dbTestResults');
            const connectionString = document.getElementById('mongoConnectionString').value;
            const databaseName = document.getElementById('mongoDatabaseName').value;

            resultsArea.value = '🔍 Testing database connection...\n';

            try {
                const response = await fetch('/api/admin/database/test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        connection_string: connectionString,
                        database_name: databaseName
                    })
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    resultsArea.value += '✅ Database connection successful!\n';
                    resultsArea.value += `📊 Database: ${result.database_name}\n`;
                    resultsArea.value += `📁 Collections: ${result.collections.length}\n`;
                    resultsArea.value += `📋 Collection list: ${result.collections.join(', ')}\n`;
                    resultsArea.value += `⏱️ Response time: ${result.response_time}ms\n`;
                    resultsArea.value += `🔗 Connection type: ${result.connection_type}\n`;
                } else {
                    resultsArea.value += '❌ Database connection failed!\n';
                    resultsArea.value += `🔍 Error: ${result.error || 'Unknown error'}\n`;
                    resultsArea.value += `💡 Suggestions:\n`;
                    resultsArea.value += `   - Check connection string format\n`;
                    resultsArea.value += `   - Verify MongoDB Atlas cluster is running\n`;
                    resultsArea.value += `   - Check IP whitelist settings\n`;
                    resultsArea.value += `   - Verify credentials\n`;
                }
            } catch (error) {
                resultsArea.value += '❌ Connection test failed!\n';
                resultsArea.value += `🔍 Network error: ${error.message}\n`;
            }
        }

        async function loadDatabaseConfig() {
            try {
                const response = await fetch('/api/admin/database/config');
                const config = await response.json();

                if (response.ok) {
                    document.getElementById('mongoConnectionString').value = config.connection_string || '';
                    document.getElementById('mongoDatabaseName').value = config.database_name || '';
                    console.log('✅ Database configuration loaded');
                } else {
                    console.error('❌ Failed to load database configuration');
                }
            } catch (error) {
                console.error('❌ Error loading database config:', error);
            }
        }

        async function saveDatabaseConfig() {
            const connectionString = document.getElementById('mongoConnectionString').value;
            const databaseName = document.getElementById('mongoDatabaseName').value;

            try {
                const response = await fetch('/api/admin/database/config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        connection_string: connectionString,
                        database_name: databaseName
                    })
                });

                if (response.ok) {
                    console.log('✅ Database configuration saved');
                    alert('Database configuration saved successfully!');
                } else {
                    console.error('❌ Failed to save database configuration');
                    alert('Failed to save database configuration');
                }
            } catch (error) {
                console.error('❌ Error saving database config:', error);
                alert('Error saving database configuration');
            }
        }

        async function migrateLegacyPasswords() {
            if (!confirm('This will update admin and guest users with secure password hashes. Continue?')) {
                return;
            }

            try {
                const response = await fetch('/api/admin/migrate-passwords', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    let message = '✅ Password migration completed successfully!\n\n';

                    if (result.updated_users.length > 0) {
                        message += `Updated users: ${result.updated_users.join(', ')}\n`;
                    }

                    if (result.skipped_users.length > 0) {
                        message += `Skipped users: ${result.skipped_users.join(', ')}\n`;
                    }

                    if (result.errors.length > 0) {
                        message += `Errors: ${result.errors.join(', ')}\n`;
                    }

                    alert(message);
                    console.log('✅ Password migration results:', result);
                } else {
                    let errorMessage = '❌ Password migration failed!\n\n';
                    if (result.errors && result.errors.length > 0) {
                        errorMessage += `Errors: ${result.errors.join(', ')}`;
                    } else {
                        errorMessage += `Error: ${result.error || 'Unknown error'}`;
                    }
                    alert(errorMessage);
                    console.error('❌ Password migration failed:', result);
                }
            } catch (error) {
                alert('❌ Network error during password migration: ' + error.message);
                console.error('❌ Password migration error:', error);
            }
        }

        // Terminal Management Functions
        let autoRefreshInterval = null;
        let frozenTerminals = new Set();

        async function refreshTerminal(serviceName) {
            if (frozenTerminals.has(serviceName)) {
                console.log(`Terminal ${serviceName} is frozen, skipping refresh`);
                return;
            }

            const terminalElement = document.getElementById(`terminal-${serviceName}`);

            try {
                const response = await fetch(`/api/admin/terminals/${serviceName}`);

                if (response.ok) {
                    const data = await response.json();

                    if (data.success) {
                        terminalElement.innerHTML = formatTerminalOutput(data.output);
                        // Auto-scroll to bottom
                        terminalElement.scrollTop = terminalElement.scrollHeight;
                    } else {
                        terminalElement.innerHTML = `<div class="error">❌ Error: ${data.error}</div>`;
                    }
                } else {
                    terminalElement.innerHTML = `<div class="error">❌ Failed to fetch terminal output (${response.status})</div>`;
                }
            } catch (error) {
                terminalElement.innerHTML = `<div class="error">❌ Network error: ${error.message}</div>`;
            }
        }

        function formatTerminalOutput(output) {
            if (!output) return '<div class="loading">No output available</div>';

            // Format log levels with colors
            return output
                .replace(/\[ERROR\]/g, '<span class="error">[ERROR]</span>')
                .replace(/\[WARNING\]/g, '<span class="warning">[WARNING]</span>')
                .replace(/\[INFO\]/g, '<span class="info">[INFO]</span>')
                .replace(/\[DEBUG\]/g, '<span class="success">[DEBUG]</span>')
                .replace(/✅/g, '<span class="success">✅</span>')
                .replace(/❌/g, '<span class="error">❌</span>')
                .replace(/⚠️/g, '<span class="warning">⚠️</span>')
                .replace(/🔍/g, '<span class="info">🔍</span>');
        }

        function clearTerminal(serviceName) {
            const terminalElement = document.getElementById(`terminal-${serviceName}`);
            terminalElement.innerHTML = '<div class="loading">Terminal cleared</div>';
        }

        function freezeTerminal(serviceName) {
            const freezeBtn = document.getElementById(`freeze${serviceName.charAt(0).toUpperCase() + serviceName.slice(1)}`);
            const terminalElement = document.getElementById(`terminal-${serviceName}`);

            if (frozenTerminals.has(serviceName)) {
                // Unfreeze
                frozenTerminals.delete(serviceName);
                freezeBtn.textContent = '❄️';
                freezeBtn.classList.remove('btn-warning');
                freezeBtn.classList.add('btn-primary');
                terminalElement.classList.remove('frozen');
                console.log(`Terminal ${serviceName} unfrozen`);
            } else {
                // Freeze
                frozenTerminals.add(serviceName);
                freezeBtn.textContent = '🔥';
                freezeBtn.classList.remove('btn-primary');
                freezeBtn.classList.add('btn-warning');
                terminalElement.classList.add('frozen');
                console.log(`Terminal ${serviceName} frozen`);
            }
        }

        function refreshAllTerminals() {
            const services = ['backend', 'dispatcher', 'planner', 'dialogue', 'phone', 'webchat'];
            services.forEach(service => refreshTerminal(service));
        }

        function clearAllTerminals() {
            const services = ['backend', 'dispatcher', 'planner', 'dialogue', 'phone', 'webchat'];
            services.forEach(service => clearTerminal(service));
        }

        function toggleAutoRefresh() {
            const btn = document.getElementById('autoRefreshBtn');

            if (autoRefreshInterval) {
                // Stop auto-refresh
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                btn.textContent = '▶️ Auto-Refresh';
                btn.classList.remove('btn-warning');
                btn.classList.add('btn-secondary');
                console.log('Auto-refresh stopped');
            } else {
                // Start auto-refresh
                autoRefreshInterval = setInterval(refreshAllTerminals, 3000); // Refresh every 3 seconds
                btn.textContent = '⏸️ Auto-Refresh';
                btn.classList.remove('btn-secondary');
                btn.classList.add('btn-warning');
                console.log('Auto-refresh started (3 second interval)');

                // Initial refresh
                refreshAllTerminals();
            }
        }

        function editUser(userId) {
            console.log(`Edit user functionality will be implemented for user: ${userId}`);
            // Edit user action logged to console only - no browser alert per user request
        }

        function deleteUser(userId, username) {
            if (confirm(`Are you sure you want to delete user "${username}"?`)) {
                console.log(`User "${username}" deleted successfully`);
                // User deletion logged to console only - no browser alert per user request
                loadUsers();
            }
        }

        // Form Handler
        document.getElementById('createUserForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const userData = Object.fromEntries(formData.entries());

            console.log('Creating user via Backend API...');

            try {
                const response = await fetch('/api/admin/users', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(userData)
                });

                if (response.ok) {
                    const result = await response.json();
                    console.log(`User "${userData.username}" created successfully!`);
                    hideCreateUserModal();
                    loadUsers();
                } else {
                    const error = await response.json();
                    console.error('Failed to create user:', error.detail || 'Unknown error');
                    alert('Failed to create user: ' + (error.detail || 'Unknown error'));
                }
            } catch (error) {
                console.error('Error creating user:', error);
                alert('Error creating user: ' + error.message);
            }
        });

        // Utility Functions - DISABLED per user request (no browser alerts)
        function showAlert(message, type) {
            // Log to console only - NO browser alerts per user request
            const timestamp = new Date().toLocaleTimeString();
            console.log(`[${timestamp}] [${type.toUpperCase()}] ${message}`);

            // Original showAlert functionality completely disabled
            // User specifically requested NO success/error messages in browser
        }

        // Close modals when clicking outside
        window.onclick = function(event) {
            const createModal = document.getElementById('createUserModal');

            if (event.target === createModal) {
                hideCreateUserModal();
            }
        }
    </script>
</body>
</html>
