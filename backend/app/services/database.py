"""
Database service for MongoDB operations.
Handles all database interactions for missions and tasks.
"""

import os
import sys
from typing import List, Optional, Dict, Any
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase, AsyncIOMotorCollection
from pymongo.errors import Du<PERSON><PERSON>eyError, ConnectionFailure, NetworkTimeout, ServerSelectionTimeoutError
from shared.unified_logging import get_backend_logger

# Initialize unified logger
logger = get_backend_logger()
from datetime import datetime
from dotenv import load_dotenv
import asyncio
from functools import wraps

# Ensure environment variables are loaded
load_dotenv()

# Add project root to path for shared models
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared_models import Mission, Task, TaskStatus


def retry_on_connection_error(max_retries: int = 3, delay: float = 1.0):
    """Decorator to retry database operations on connection errors"""
    def decorator(func):
        @wraps(func)
        async def wrapper(self, *args, **kwargs):
            last_exception = None

            for attempt in range(max_retries + 1):
                try:
                    return await func(self, *args, **kwargs)
                except (ConnectionFailure, NetworkTimeout, ServerSelectionTimeoutError) as e:
                    last_exception = e
                    if attempt < max_retries:
                        logger.warning(f"Database operation failed (attempt {attempt + 1}/{max_retries + 1}): {e}", module="BACKEND-API", routine="wrapper")
                        logger.info(f"Retrying in {delay} seconds...", module="BACKEND-API", routine="wrapper")
                        await asyncio.sleep(delay)
                        # Try to reconnect
                        try:
                            await self.connect()
                        except Exception as reconnect_error:
                            logger.warning(f"Reconnection attempt failed: {reconnect_error}", module="BACKEND-API", routine="wrapper")
                    else:
                        logger.error(f"Database operation failed after {max_retries + 1} attempts: {e}", module="BACKEND-API", routine="wrapper")
                        raise last_exception
                except Exception as e:
                    # For non-connection errors, don't retry
                    raise e

            # This should never be reached, but just in case
            raise last_exception
        return wrapper
    return decorator


class DatabaseService:
    """
    Async MongoDB service for mission and task management.
    
    Provides CRUD operations and specialized queries for the mission
    orchestration system.
    """
    
    def __init__(self):
        self.client: Optional[AsyncIOMotorClient] = None
        self.database: Optional[AsyncIOMotorDatabase] = None
        self.missions_collection: Optional[AsyncIOMotorCollection] = None
        self.tasks_collection: Optional[AsyncIOMotorCollection] = None
        self.users_collection: Optional[AsyncIOMotorCollection] = None
        self.sessions_collection: Optional[AsyncIOMotorCollection] = None
        self.settings_collection: Optional[AsyncIOMotorCollection] = None
        self.logs_collection: Optional[AsyncIOMotorCollection] = None

        # Get connection string from environment
        self.connection_string = os.getenv("MONGODB_CONNECTION_STRING")
        self.database_name = os.getenv("MONGODB_DATABASE")

        # FORCE REAL DATABASE MODE - NO MOCK DATABASE ALLOWED
        use_real = os.getenv("USE_REAL_DATABASE", "true").lower() == "true"
        use_mock = os.getenv("USE_MOCK_DATABASE", "false").lower() == "true"

        if not use_mock and use_real:
            logger.info("🏭 REAL DATABASE MODE ENFORCED - Using MongoDB Atlas connection", module="BACKEND-API", routine="__init__")
            if not self.connection_string or self.connection_string == "":
                raise ValueError("❌ MONGODB_CONNECTION_STRING is required for real database mode")
            if self.connection_string.startswith("mock://"):
                raise ValueError("❌ Mock connection string detected but real database mode is enforced")
        elif not self.connection_string or self.connection_string == "":
            logger.error("❌ No MongoDB connection string found and real database mode is required", module="BACKEND-API", routine="__init__")
            raise ValueError("MONGODB_CONNECTION_STRING is required")

        # Validate real MongoDB connection string
        if not self.connection_string.startswith("mongodb"):
            logger.error(f"❌ Invalid MongoDB connection string: {self.connection_string}", module="BACKEND-API", routine="__init__")
            raise ValueError("Invalid MongoDB connection string format")
    
    async def connect(self) -> None:
        """Establish connection to MongoDB with retry logic - NEVER CRASH"""
        import asyncio

        logger.info("🔌 Connecting to MongoDB...", module="BACKEND-API", routine="connect")
        logger.info(f"🔗 Connection string: {self.connection_string[:50] if self.connection_string else 'None'}...", module="BACKEND-API", routine="connect")  # Show first 50 chars
        logger.info("💪 Will keep trying INDEFINITELY until MongoDB is ready!", module="BACKEND-API", routine="connect")

        # Check for mock database mode (for testing without MongoDB)
        if self.connection_string.startswith("mock://"):
            logger.info("🎭 MOCK DATABASE MODE - Simulating successful connection", module="BACKEND-API", routine="connect")
            await asyncio.sleep(2)  # Simulate connection delay

            # Create REAL mock database with in-memory storage
            from unittest.mock import AsyncMock, MagicMock

            # In-memory storage for mock database
            self._mock_missions = {}
            self._mock_tasks = {}

            # Mock client
            self.client = MagicMock()

            # Create proper mock collections with real storage
            class MockCollection:
                def __init__(self, storage_dict):
                    self.storage = storage_dict

                async def insert_one(self, document):
                    # Store document by mission_id or task_id
                    doc_id = document.get('mission_id') or document.get('task_id')
                    if doc_id:
                        self.storage[doc_id] = document
                        logger.info("📝 Mock DB: Stored document with ID: {doc_id}", module="BACKEND-API", routine="insert_one")
                    return MagicMock(inserted_id=doc_id)

                async def find_one(self, query):
                    # Find document by query
                    if isinstance(query, dict):
                        for key, value in query.items():
                            for doc_id, document in self.storage.items():
                                if document.get(key) == value:
                                    logger.info("🔍 Mock DB: Found document with {key}={value}", module="BACKEND-API", routine="find_one")
                                    return document
                    logger.info("🔍 Mock DB: Document not found for query: {query}", module="BACKEND-API", routine="find_one")
                    return None

                async def update_one(self, query, update_doc):
                    # Update document
                    if isinstance(query, dict):
                        for key, value in query.items():
                            for doc_id, document in self.storage.items():
                                if document.get(key) == value:
                                    # Update the document
                                    if isinstance(update_doc, dict):
                                        document.update(update_doc)
                                    logger.info("📝 Mock DB: Updated document with {key}={value}", module="BACKEND-API", routine="update_one")
                                    return MagicMock(modified_count=1)
                    return MagicMock(modified_count=0)

                async def replace_one(self, query, replacement_doc):
                    # Replace entire document
                    if isinstance(query, dict):
                        for key, value in query.items():
                            for doc_id, document in self.storage.items():
                                if document.get(key) == value:
                                    self.storage[doc_id] = replacement_doc
                                    logger.info("📝 Mock DB: Replaced document with {key}={value}", module="BACKEND-API", routine="replace_one")
                                    return MagicMock(matched_count=1, modified_count=1)
                    return MagicMock(matched_count=0, modified_count=0)

                def find(self, query=None):
                    # Return all documents or filtered documents
                    if query is None:
                        return list(self.storage.values())
                    # Simple filtering (can be enhanced)
                    results = []
                    if isinstance(query, dict):
                        for doc in self.storage.values():
                            match = True
                            for key, value in query.items():
                                if doc.get(key) != value:
                                    match = False
                                    break
                            if match:
                                results.append(doc)
                    return results

                def __aiter__(self):
                    # Make the find result async iterable for compatibility
                    return self

                async def __anext__(self):
                    # This is a simple implementation - in real use, we'd need proper async iteration
                    raise StopAsyncIteration

            # Create mock collections
            mock_missions = MockCollection(self._mock_missions)
            mock_tasks = MockCollection(self._mock_tasks)

            # Mock database
            mock_db = MagicMock()
            mock_db.missions = mock_missions
            mock_db.tasks = mock_tasks

            # Set the database and collections properly
            self.database = mock_db
            self.missions_collection = mock_missions
            self.tasks_collection = mock_tasks
            self.users_collection = mock_db.users
            self.sessions_collection = mock_db.sessions
            self.settings_collection = mock_db.settings
            self.logs_collection = mock_db.logs

            logger.info("✅ Mock database connection successful with mock collections!", module="BACKEND-API", routine="__anext__")
            return

        # Check if this is a MongoDB Atlas connection
        is_atlas = "mongodb+srv://" in self.connection_string

        attempt = 1
        # BULLETPROOF: Never give up - keep trying indefinitely until MongoDB is ready
        # Database service MUST NEVER CRASH - it's the only service allowed to access MongoDB

        while True:  # Infinite retry loop - never give up
            try:
                if is_atlas:
                    # MongoDB Atlas - SSL configuration handled in connection string
                    self.client = AsyncIOMotorClient(
                        self.connection_string,
                        serverSelectionTimeoutMS=15000,  # 15 second timeout for Atlas
                        connectTimeoutMS=20000,  # 20 second timeout
                        socketTimeoutMS=20000,   # 20 second timeout
                        # SSL options are in the connection string to avoid conflicts
                    )
                else:
                    # Local MongoDB - use TLS settings
                    self.client = AsyncIOMotorClient(
                        self.connection_string,
                        serverSelectionTimeoutMS=5000,  # 5 second timeout
                        connectTimeoutMS=10000,  # 10 second timeout
                        socketTimeoutMS=10000,   # 10 second timeout
                        tls=True,  # Enable TLS/SSL
                        tlsAllowInvalidCertificates=True,  # Allow self-signed certificates for development
                    )

                # Test the connection
                await self.client.admin.command('ping')

                self.database = self.client[self.database_name]
                self.missions_collection = self.database["missions"]
                self.tasks_collection = self.database["tasks"]
                self.users_collection = self.database["users"]
                self.sessions_collection = self.database["sessions"]
                self.settings_collection = self.database["settings"]
                self.logs_collection = self.database["logs"]

                # Create indexes for better performance
                await self._create_indexes()

                logger.info(f"✅ Connected to MongoDB database: {self.database_name} (attempt {attempt})", module="BACKEND-API", routine="connect")
                return  # Success! Exit the method

            except Exception as e:
                logger.debug(f"MongoDB connection attempt {attempt} failed: {e}", module="BACKEND-API", routine="connect")

                # Log progress every 10 attempts (every 20 seconds)
                if attempt % 10 == 0:
                    logger.warning(f"⏳ MongoDB still not responding... (attempt {attempt}) - Database service will NEVER give up!", module="BACKEND-API", routine="connect")
                    logger.warning(f"🔍 Last error: {str(e)[:100]}", module="BACKEND-API", routine="connect")
                    logger.info("🛡️ BULLETPROOF MODE: Database service will keep trying indefinitely until MongoDB is ready", module="BACKEND-API", routine="connect")

                # BULLETPROOF: Never fall back to mock mode - keep trying indefinitely
                # The Database service is the ONLY service allowed to access MongoDB
                # It MUST wait for MongoDB to be ready and NEVER crash

                await asyncio.sleep(2)  # Wait 2 seconds before next attempt
                attempt += 1

        # This should never be reached due to the fallback above
        logger.error("🚨 Unexpected end of connection attempts", module="BACKEND-API", routine="connect")
    
    async def disconnect(self) -> None:
        """Close database connection"""
        if self.client:
            self.client.close()
            logger.info("🔌 Disconnected from MongoDB", module="BACKEND-API", routine="disconnect")
    
    async def _create_indexes(self) -> None:
        """Create database indexes for optimal performance"""
        try:
            # Mission collection indexes
            await self.missions_collection.create_index("mission_id", unique=True)
            await self.missions_collection.create_index("status")
            await self.missions_collection.create_index("created_at")
            await self.missions_collection.create_index([
                ("user_id", 1),
                ("status", 1),
                ("created_at", -1)
            ])

            # User collection indexes
            if hasattr(self, 'users_collection') and self.users_collection:
                await self.users_collection.create_index("user_id", unique=True)
                await self.users_collection.create_index("username", unique=True)
                await self.users_collection.create_index("email", unique=True, sparse=True)
                await self.users_collection.create_index("mobile_phone", unique=True, sparse=True)
                await self.users_collection.create_index("role")
                await self.users_collection.create_index("status")
                await self.users_collection.create_index("created_at")

            # Task collection indexes
            await self.tasks_collection.create_index("task_id", unique=True)
            await self.tasks_collection.create_index("mission_id")
            await self.tasks_collection.create_index("status")
            await self.tasks_collection.create_index("created_at")

            # Compound indexes for task queries
            await self.tasks_collection.create_index([
                ("mission_id", 1),
                ("status", 1)
            ])
            await self.tasks_collection.create_index([
                ("mission_id", 1),
                ("created_at", 1)
            ])

            logger.info("📊 Database indexes created successfully", module="BACKEND-API", routine="_create_indexes")

        except Exception as e:
            logger.warning("⚠️ Failed to create indexes: {e}", module="BACKEND-API", routine="_create_indexes")
    
    # Mission CRUD Operations
    
    @retry_on_connection_error(max_retries=3, delay=2.0)
    async def create_mission(self, mission: Mission) -> str:
        """
        Create a new mission in the database.
        
        Args:
            mission: Mission object to create
            
        Returns:
            The mission_id of the created mission
            
        Raises:
            DuplicateKeyError: If mission_id already exists
        """
        try:
            mission_dict = mission.model_dump()
            
            # Convert datetime objects to ISO strings for MongoDB
            mission_dict = self._serialize_datetime_fields(mission_dict)
            
            result = await self.missions_collection.insert_one(mission_dict)
            
            logger.info("📝 Created mission: {mission.mission_id}", module="BACKEND-API", routine="create_mission")
            return mission.mission_id
            
        except DuplicateKeyError:
            logger.error("❌ Mission {mission.mission_id} already exists", module="BACKEND-API", routine="create_mission")
            raise
        except Exception as e:
            logger.error("❌ Failed to create mission {mission.mission_id}: {e}", module="BACKEND-API", routine="create_mission")
            raise
    
    @retry_on_connection_error(max_retries=3, delay=2.0)
    async def get_mission(self, mission_id: str) -> Optional[Mission]:
        """
        Retrieve a mission by ID.
        
        Args:
            mission_id: Unique mission identifier
            
        Returns:
            Mission object if found, None otherwise
        """
        try:
            mission_dict = await self.missions_collection.find_one(
                {"mission_id": mission_id}
            )
            
            if not mission_dict:
                logger.warning("🔍 Mission not found: {mission_id}", module="BACKEND-API", routine="get_mission")
                return None
            
            # Remove MongoDB's _id field
            mission_dict.pop("_id", None)
            
            # Deserialize datetime fields
            mission_dict = self._deserialize_datetime_fields(mission_dict)
            
            mission = Mission(**mission_dict)
            logger.debug("📖 Retrieved mission: {mission_id}", module="BACKEND-API", routine="get_mission")
            return mission
            
        except Exception as e:
            logger.error("❌ Failed to retrieve mission {mission_id}: {e}", module="BACKEND-API", routine="get_mission")
            raise
    
    async def update_mission(self, mission: Mission) -> bool:
        """
        Update an existing mission.
        
        Args:
            mission: Updated mission object
            
        Returns:
            True if updated successfully, False if mission not found
        """
        try:
            mission_dict = mission.model_dump()
            mission_dict = self._serialize_datetime_fields(mission_dict)
            
            result = await self.missions_collection.replace_one(
                {"mission_id": mission.mission_id},
                mission_dict
            )
            
            if result.matched_count == 0:
                logger.warning("🔍 Mission not found for update: {mission.mission_id}", module="BACKEND-API", routine="update_mission")
                return False
            
            logger.info("📝 Updated mission: {mission.mission_id}", module="BACKEND-API", routine="update_mission")
            return True
            
        except Exception as e:
            logger.error("❌ Failed to update mission {mission.mission_id}: {e}", module="BACKEND-API", routine="update_mission")
            raise
    

    
    async def list_missions(
        self,
        status: Optional[str] = None,
        user_id: Optional[str] = None,
        limit: int = 50,
        skip: int = 0
    ) -> List[Mission]:
        """
        List missions with optional filtering.
        
        Args:
            status: Filter by mission status
            user_id: Filter by user ID
            limit: Maximum number of missions to return
            skip: Number of missions to skip (for pagination)
            
        Returns:
            List of Mission objects
        """
        try:
            # Build query filter
            query_filter = {}
            if status:
                query_filter["status"] = status
            if user_id:
                query_filter["user_id"] = user_id
            
            # Execute query with sorting and pagination
            cursor = self.missions_collection.find(query_filter).sort(
                "created_at", -1  # Most recent first
            ).skip(skip).limit(limit)
            
            missions = []
            async for mission_dict in cursor:
                mission_dict.pop("_id", None)
                mission_dict = self._deserialize_datetime_fields(mission_dict)
                missions.append(Mission(**mission_dict))
            
            logger.debug("📋 Listed {len(missions)} missions", module="BACKEND-API", routine="list_missions")
            return missions
            
        except Exception as e:
            logger.error("❌ Failed to list missions: {e}", module="BACKEND-API", routine="list_missions")
            raise

    # ========================================
    # USER MANAGEMENT METHODS
    # ========================================

    async def create_user(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new user in the database.

        Args:
            user_data: Dictionary containing user information

        Returns:
            Dictionary containing the created user data
        """
        try:
            import uuid
            from datetime import datetime
            import hashlib
            import secrets

            # Generate unique user ID
            user_id = str(uuid.uuid4())

            # Hash password using PBKDF2 with salt (as per user preference)
            password = user_data.get("password", "")
            salt = secrets.token_hex(32)
            password_hash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000)
            password_hash_hex = password_hash.hex()

            # Prepare user document
            user_doc = {
                "user_id": user_id,
                "username": user_data.get("username", "").lower(),  # Case-insensitive storage
                "email": user_data.get("email", ""),
                "mobile_phone": user_data.get("mobile_phone", ""),
                "password_hash": password_hash_hex,
                "password_salt": salt,
                "full_name": user_data.get("full_name", ""),
                "display_name": user_data.get("display_name", user_data.get("full_name", "")),
                "role": user_data.get("role", "user"),
                "status": user_data.get("status", "active"),
                "is_admin": user_data.get("role") == "admin",
                "permissions": user_data.get("permissions", []),
                "preferences": user_data.get("preferences", {}),
                "login_count": 0,
                "last_login": None,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }

            # Check if username already exists (case-insensitive)
            existing_user = await self.users_collection.find_one({
                "username": {"$regex": f"^{user_doc['username']}$", "$options": "i"}
            })

            if existing_user:
                raise ValueError(f"Username '{user_data.get('username')}' already exists")

            # Check if email already exists (if provided)
            if user_doc["email"]:
                existing_email = await self.users_collection.find_one({
                    "email": {"$regex": f"^{user_doc['email']}$", "$options": "i"}
                })
                if existing_email:
                    raise ValueError(f"Email '{user_doc['email']}' already exists")

            # Insert user into database
            await self.users_collection.insert_one(user_doc)

            # Remove sensitive data from response
            user_response = user_doc.copy()
            user_response.pop("password_hash", None)
            user_response.pop("password_salt", None)

            logger.info(f"✅ User created: {user_id} ({user_doc['username']})", module="BACKEND-API", routine="create_user")
            return user_response

        except ValueError:
            raise
        except Exception as e:
            logger.error(f"❌ Failed to create user: {e}", module="BACKEND-API", routine="create_user")
            raise

    async def get_user_by_id(self, user_id: str) -> Optional[Dict[str, Any]]:
        """
        Get user by user ID.

        Args:
            user_id: User identifier

        Returns:
            User dictionary or None if not found
        """
        try:
            user_doc = await self.users_collection.find_one({"user_id": user_id})

            if not user_doc:
                return None

            # Remove sensitive data
            user_doc.pop("_id", None)
            user_doc.pop("password_hash", None)
            user_doc.pop("password_salt", None)

            logger.debug(f"✅ User found by ID: {user_id}", module="BACKEND-API", routine="get_user_by_id")
            return user_doc

        except Exception as e:
            logger.error(f"❌ Failed to get user by ID {user_id}: {e}", module="BACKEND-API", routine="get_user_by_id")
            raise

    async def get_user_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """
        Get user by username (case-insensitive).

        Args:
            username: Username to search for

        Returns:
            User dictionary or None if not found
        """
        try:
            user_doc = await self.users_collection.find_one({
                "username": {"$regex": f"^{username}$", "$options": "i"}
            })

            if not user_doc:
                return None

            # Remove sensitive data
            user_doc.pop("_id", None)
            user_doc.pop("password_hash", None)
            user_doc.pop("password_salt", None)

            logger.debug(f"✅ User found by username: {username}", module="BACKEND-API", routine="get_user_by_username")
            return user_doc

        except Exception as e:
            logger.error(f"❌ Failed to get user by username {username}: {e}", module="BACKEND-API", routine="get_user_by_username")
            raise

    async def list_users(self, limit: int = 100, skip: int = 0, role: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        List all users with optional filtering.

        Args:
            limit: Maximum number of users to return
            skip: Number of users to skip (for pagination)
            role: Filter by user role

        Returns:
            List of user dictionaries
        """
        try:
            # Build query filter
            query_filter = {}
            if role:
                query_filter["role"] = role

            # Execute query with sorting and pagination
            cursor = self.users_collection.find(query_filter).sort(
                "created_at", -1  # Most recent first
            ).skip(skip).limit(limit)

            users = []
            async for user_doc in cursor:
                # Remove sensitive data
                user_doc.pop("_id", None)
                user_doc.pop("password_hash", None)
                user_doc.pop("password_salt", None)
                users.append(user_doc)

            logger.debug(f"📋 Listed {len(users)} users", module="BACKEND-API", routine="list_users")
            return users

        except Exception as e:
            logger.error(f"❌ Failed to list users: {e}", module="BACKEND-API", routine="list_users")
            raise

    async def update_user(self, user_id: str, update_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Update user information.

        Args:
            user_id: User identifier
            update_data: Dictionary containing fields to update

        Returns:
            Updated user dictionary or None if not found
        """
        try:
            from datetime import datetime
            import hashlib
            import secrets

            # Prepare update document
            update_doc = {}

            # Handle password update with PBKDF2
            if "password" in update_data:
                password = update_data["password"]
                salt = secrets.token_hex(32)
                password_hash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000)
                update_doc["password_hash"] = password_hash.hex()
                update_doc["password_salt"] = salt

            # Handle other fields
            allowed_fields = ["username", "email", "mobile_phone", "full_name", "display_name",
                            "role", "status", "permissions", "preferences"]

            for field in allowed_fields:
                if field in update_data:
                    if field == "username":
                        update_doc[field] = update_data[field].lower()  # Case-insensitive storage
                    else:
                        update_doc[field] = update_data[field]

            # Update admin flag based on role
            if "role" in update_data:
                update_doc["is_admin"] = update_data["role"] == "admin"

            # Always update the timestamp
            update_doc["updated_at"] = datetime.now().isoformat()

            # Perform the update
            result = await self.users_collection.update_one(
                {"user_id": user_id},
                {"$set": update_doc}
            )

            if result.matched_count == 0:
                return None

            # Return updated user
            return await self.get_user_by_id(user_id)

        except Exception as e:
            logger.error(f"❌ Failed to update user {user_id}: {e}", module="BACKEND-API", routine="update_user")
            raise

    async def delete_user(self, user_id: str) -> bool:
        """
        Delete a user from the database.

        Args:
            user_id: User identifier

        Returns:
            True if user was deleted, False if not found
        """
        try:
            result = await self.users_collection.delete_one({"user_id": user_id})

            if result.deleted_count > 0:
                logger.info(f"✅ User deleted: {user_id}", module="BACKEND-API", routine="delete_user")
                return True
            else:
                logger.warning(f"⚠️ User not found for deletion: {user_id}", module="BACKEND-API", routine="delete_user")
                return False

        except Exception as e:
            logger.error(f"❌ Failed to delete user {user_id}: {e}", module="BACKEND-API", routine="delete_user")
            raise

    async def authenticate_user(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """
        Authenticate user with username and password.

        Args:
            username: Username (case-insensitive)
            password: Plain text password

        Returns:
            User dictionary if authentication successful, None otherwise
        """
        try:
            import hashlib
            from datetime import datetime

            # Find user by username (case-insensitive)
            user_doc = await self.users_collection.find_one({
                "username": {"$regex": f"^{username}$", "$options": "i"}
            })

            if not user_doc:
                logger.debug(f"❌ User not found: {username}", module="BACKEND-API", routine="authenticate_user")
                return None

            # Verify password using unified PBKDF2 system
            stored_hash = user_doc.get("password_hash", "")

            if not stored_hash:
                logger.warning(f"⚠️ User {username} has no password hash", module="BACKEND-API", routine="authenticate_user")
                return None

            # Use unified password verification system
            from shared_models.password_utils import verify_password, needs_rehash, hash_password

            # Verify password using unified system
            if not verify_password(password, stored_hash):
                logger.debug(f"❌ Invalid password for user: {username}", module="BACKEND-API", routine="authenticate_user")
                return None

            # Check if password hash needs migration to current standard
            if needs_rehash(stored_hash):
                new_hash = hash_password(password)
                await self.users_collection.update_one(
                    {"_id": user_doc["_id"]},
                    {"$set": {"password_hash": new_hash}}
                )
                logger.info(f"🔐 Migrated password hash for user {username} to current PBKDF2 standard", module="BACKEND-API", routine="authenticate_user")

            # Update login count and last login time
            await self.users_collection.update_one(
                {"user_id": user_doc["user_id"]},
                {
                    "$set": {"last_login": datetime.now().isoformat()},
                    "$inc": {"login_count": 1}
                }
            )

            # Remove sensitive data from response
            user_doc.pop("_id", None)
            user_doc.pop("password_hash", None)
            user_doc.pop("password_salt", None)

            logger.info(f"✅ User authenticated: {username}", module="BACKEND-API", routine="authenticate_user")
            return user_doc

        except Exception as e:
            logger.error(f"❌ Failed to authenticate user {username}: {e}", module="BACKEND-API", routine="authenticate_user")
            raise

    # ========================================
    # SETTINGS MANAGEMENT METHODS
    # ========================================

    async def get_setting(self, key: str, default_value: Any = None) -> Any:
        """
        Get a setting value by key.

        Args:
            key: Setting key
            default_value: Default value if setting not found

        Returns:
            Setting value or default
        """
        try:
            setting_doc = await self.settings_collection.find_one({"key": key})

            if setting_doc:
                return setting_doc.get("value", default_value)
            else:
                return default_value

        except Exception as e:
            logger.error(f"❌ Failed to get setting {key}: {e}", module="BACKEND-API", routine="get_setting")
            return default_value

    async def set_setting(self, key: str, value: Any, user_id: Optional[str] = None) -> bool:
        """
        Set a setting value.

        Args:
            key: Setting key
            value: Setting value
            user_id: User who set the setting (optional)

        Returns:
            True if successful
        """
        try:
            from datetime import datetime

            setting_doc = {
                "key": key,
                "value": value,
                "updated_by": user_id,
                "updated_at": datetime.now().isoformat()
            }

            await self.settings_collection.update_one(
                {"key": key},
                {"$set": setting_doc},
                upsert=True
            )

            logger.debug(f"✅ Setting updated: {key}", module="BACKEND-API", routine="set_setting")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to set setting {key}: {e}", module="BACKEND-API", routine="set_setting")
            return False

    async def list_settings(self) -> Dict[str, Any]:
        """
        List all settings.

        Returns:
            Dictionary of all settings
        """
        try:
            settings = {}
            cursor = self.settings_collection.find({})

            async for setting_doc in cursor:
                settings[setting_doc["key"]] = setting_doc.get("value")

            logger.debug(f"📋 Listed {len(settings)} settings", module="BACKEND-API", routine="list_settings")
            return settings

        except Exception as e:
            logger.error(f"❌ Failed to list settings: {e}", module="BACKEND-API", routine="list_settings")
            return {}

    # ========================================
    # LOGS MANAGEMENT METHODS
    # ========================================

    async def add_log_entry(self, level: str, service: str, routine: str, message: str, user_id: Optional[str] = None) -> bool:
        """
        Add a log entry to the database.

        Args:
            level: Log level (INFO, ERROR, WARNING, DEBUG)
            service: Service name
            routine: Routine/function name
            message: Log message
            user_id: User ID if applicable

        Returns:
            True if successful
        """
        try:
            from datetime import datetime

            log_doc = {
                "timestamp": datetime.now().isoformat(),
                "level": level,
                "service": service,
                "routine": routine,
                "message": message,
                "user_id": user_id
            }

            await self.logs_collection.insert_one(log_doc)
            return True

        except Exception as e:
            # Don't log this error to avoid infinite recursion
            print(f"❌ Failed to add log entry: {e}")
            return False

    async def get_logs(self, limit: int = 100, skip: int = 0, level: Optional[str] = None,
                      service: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get log entries with optional filtering.

        Args:
            limit: Maximum number of logs to return
            skip: Number of logs to skip (for pagination)
            level: Filter by log level
            service: Filter by service name

        Returns:
            List of log dictionaries
        """
        try:
            # Build query filter
            query_filter = {}
            if level:
                query_filter["level"] = level
            if service:
                query_filter["service"] = service

            # Execute query with sorting and pagination
            cursor = self.logs_collection.find(query_filter).sort(
                "timestamp", -1  # Most recent first
            ).skip(skip).limit(limit)

            logs = []
            async for log_doc in cursor:
                log_doc.pop("_id", None)
                logs.append(log_doc)

            return logs

        except Exception as e:
            logger.error(f"❌ Failed to get logs: {e}", module="BACKEND-API", routine="get_logs")
            return []

    # Task CRUD Operations

    @retry_on_connection_error(max_retries=3, delay=2.0)
    async def create_task(self, task: Task) -> str:
        """
        Create a new task in the database.

        Args:
            task: Task object to create

        Returns:
            The task_id of the created task

        Raises:
            DuplicateKeyError: If task_id already exists
        """
        try:
            task_dict = task.model_dump()
            task_dict = self._serialize_datetime_fields(task_dict)

            result = await self.tasks_collection.insert_one(task_dict)

            logger.info("📝 Created task: {task.task_id} for mission: {task.mission_id}", module="BACKEND-API", routine="create_task")
            return task.task_id

        except DuplicateKeyError:
            logger.error("❌ Task {task.task_id} already exists", module="BACKEND-API", routine="create_task")
            raise
        except Exception as e:
            logger.error("❌ Failed to create task {task.task_id}: {e}", module="BACKEND-API", routine="create_task")
            raise

    async def get_task(self, task_id: str) -> Optional[Task]:
        """
        Retrieve a task by ID.

        Args:
            task_id: Unique task identifier

        Returns:
            Task object if found, None otherwise
        """
        try:
            task_dict = await self.tasks_collection.find_one({"task_id": task_id})

            if not task_dict:
                logger.warning("🔍 Task not found: {task_id}", module="BACKEND-API", routine="get_task")
                return None

            task_dict.pop("_id", None)
            task_dict = self._deserialize_datetime_fields(task_dict)

            task = Task(**task_dict)
            logger.debug("📖 Retrieved task: {task_id}", module="BACKEND-API", routine="get_task")
            return task

        except Exception as e:
            logger.error("❌ Failed to retrieve task {task_id}: {e}", module="BACKEND-API", routine="get_task")
            raise

    async def update_task(self, task: Task) -> bool:
        """
        Update an existing task.

        Args:
            task: Updated task object

        Returns:
            True if updated successfully, False if task not found
        """
        try:
            task_dict = task.model_dump()
            task_dict = self._serialize_datetime_fields(task_dict)

            result = await self.tasks_collection.replace_one(
                {"task_id": task.task_id},
                task_dict
            )

            if result.matched_count == 0:
                logger.warning("🔍 Task not found for update: {task.task_id}", module="BACKEND-API", routine="update_task")
                return False

            logger.info("📝 Updated task: {task.task_id}", module="BACKEND-API", routine="update_task")
            return True

        except Exception as e:
            logger.error("❌ Failed to update task {task.task_id}: {e}", module="BACKEND-API", routine="update_task")
            raise



    async def get_mission_tasks(self, mission_id: str) -> List[Task]:
        """
        Get all tasks for a specific mission.

        Args:
            mission_id: Mission identifier

        Returns:
            List of Task objects for the mission
        """
        try:
            cursor = self.tasks_collection.find({"mission_id": mission_id}).sort("created_at", 1)

            tasks = []
            async for task_dict in cursor:
                task_dict.pop("_id", None)
                task_dict = self._deserialize_datetime_fields(task_dict)
                tasks.append(Task(**task_dict))

            logger.debug(f"📋 Retrieved {len(tasks)} tasks for mission: {mission_id}", module="BACKEND-API", routine="get_mission_tasks")
            return tasks

        except Exception as e:
            logger.error(f"❌ Failed to get tasks for mission {mission_id}: {e}", module="BACKEND-API", routine="get_mission_tasks")
            raise

    async def get_executable_tasks(self, mission_id: str) -> List[Task]:
        """
        Get all tasks that can currently be executed for a mission.

        Args:
            mission_id: Mission identifier

        Returns:
            List of executable Task objects
        """
        try:
            # Get all tasks for the mission
            all_tasks = await self.get_mission_tasks(mission_id)

            # Get completed task IDs
            completed_task_ids = [
                task.task_id for task in all_tasks
                if task.status == TaskStatus.DONE
            ]

            # Filter for executable tasks (pending tasks whose dependencies are met)
            executable_tasks = []
            for task in all_tasks:
                if task.status == TaskStatus.PENDING:
                    # Check if all parent tasks are completed
                    if not task.parent_tasks or all(parent_id in completed_task_ids for parent_id in task.parent_tasks):
                        executable_tasks.append(task)

            logger.info(f"Found {len(executable_tasks)} executable tasks for mission {mission_id}", module="BACKEND-API", routine="get_executable_tasks")
            return executable_tasks

        except Exception as e:
            logger.error(f"❌ Failed to get executable tasks for mission {mission_id}: {e}", module="BACKEND-API", routine="get_executable_tasks")
            raise

    async def get_mission_progress(self, mission_id: str) -> Dict[str, Any]:
        """
        Get progress summary for a mission.

        Args:
            mission_id: Mission identifier

        Returns:
            Dictionary with progress statistics
        """
        try:
            tasks = await self.get_mission_tasks(mission_id)

            if not tasks:
                return {
                    "total_tasks": 0,
                    "completed": 0,
                    "in_progress": 0,
                    "pending": 0,
                    "failed": 0,
                    "completion_percentage": 0
                }

            # Count tasks by status
            stats = {status.value: 0 for status in TaskStatus}
            for task in tasks:
                stats[task.status] += 1

            total_tasks = len(tasks)
            completed = stats.get(TaskStatus.DONE.value, 0)
            completion_percentage = (completed / total_tasks * 100) if total_tasks > 0 else 0

            return {
                "total_tasks": total_tasks,
                "completed": completed,
                "in_progress": stats.get(TaskStatus.IN_PROGRESS.value, 0),
                "pending": stats.get(TaskStatus.PENDING.value, 0),
                "failed": stats.get(TaskStatus.FAILED.value, 0),
                "completion_percentage": completion_percentage
            }

        except Exception as e:
            logger.error("❌ Failed to get mission progress for {mission_id}: {e}", module="BACKEND-API", routine="get_mission_progress")
            raise

    async def is_mission_complete(self, mission_id: str) -> bool:
        """
        Check if all tasks in a mission are completed successfully.

        Args:
            mission_id: Mission identifier

        Returns:
            True if all tasks are successfully done, False otherwise
        """
        try:
            # Get mission to check if there's a current task waiting for user input
            mission = await self.get_mission(mission_id)
            if not mission:
                return False

            # If there's a current task, the mission is not complete (waiting for user input)
            if mission.current_task_id:
                return False

            tasks = await self.get_mission_tasks(mission_id)

            if not tasks:
                return True  # No tasks means complete

            # Mission is complete ONLY if ALL tasks are successfully DONE
            # Failed tasks mean the mission has NOT completed successfully
            return all(
                task.status == TaskStatus.DONE
                for task in tasks
            )

        except Exception as e:
            logger.error("❌ Failed to check mission completion for {mission_id}: {e}", module="BACKEND-API", routine="is_mission_complete")
            raise

    async def is_mission_failed(self, mission_id: str) -> bool:
        """
        Check if a mission has failed (has any failed tasks and no pending/in-progress tasks).

        Args:
            mission_id: Mission identifier

        Returns:
            True if mission has failed tasks and no more tasks to execute, False otherwise
        """
        try:
            # Get mission to check if there's a current task waiting for user input
            mission = await self.get_mission(mission_id)
            if not mission:
                return False

            # If there's a current task, the mission is not failed (still in progress)
            if mission.current_task_id:
                return False

            tasks = await self.get_mission_tasks(mission_id)

            if not tasks:
                return False  # No tasks means not failed

            # Check if there are any failed tasks
            has_failed_tasks = any(task.status == TaskStatus.FAILED for task in tasks)

            # Check if there are any tasks still pending or in progress
            has_active_tasks = any(
                task.status in [TaskStatus.PENDING, TaskStatus.IN_PROGRESS]
                for task in tasks
            )

            # Mission is failed if it has failed tasks and no more active tasks to execute
            return has_failed_tasks and not has_active_tasks

        except Exception as e:
            logger.error("❌ Failed to check mission completion for {mission_id}: {e}", module="BACKEND-API", routine="is_mission_failed")
            raise

    @retry_on_connection_error(max_retries=3, delay=2.0)
    async def clear_all_data(self) -> Dict[str, int]:
        """
        Clear all missions and tasks from the database.

        WARNING: This will permanently delete ALL data!

        Returns:
            Dictionary with counts of deleted records
        """
        try:
            # Count records before deletion
            missions_count = await self.missions_collection.count_documents({})
            tasks_count = await self.tasks_collection.count_documents({})

            # Delete all missions
            missions_result = await self.missions_collection.delete_many({})

            # Delete all tasks
            tasks_result = await self.tasks_collection.delete_many({})

            deleted_counts = {
                "missions_deleted": missions_result.deleted_count,
                "tasks_deleted": tasks_result.deleted_count,
                "missions_before": missions_count,
                "tasks_before": tasks_count
            }

            logger.warning("🗑️ CLEARED ALL DATABASE DATA: {deleted_counts}", module="BACKEND-API", routine="clear_all_data")
            return deleted_counts

        except Exception as e:
            logger.error("❌ Failed to clear database: {e}", module="BACKEND-API", routine="clear_all_data")
            raise



    # Utility methods
    
    def _serialize_datetime_fields(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert datetime objects to ISO strings for MongoDB storage"""
        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, datetime):
                    data[key] = value.isoformat()
                elif isinstance(value, dict):
                    data[key] = self._serialize_datetime_fields(value)
                elif isinstance(value, list):
                    data[key] = [
                        self._serialize_datetime_fields(item) if isinstance(item, dict) else item
                        for item in value
                    ]
        return data
    
    def _deserialize_datetime_fields(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert ISO strings back to datetime objects"""
        datetime_fields = [
            "created_at", "started_at", "completed_at"
        ]
        
        if isinstance(data, dict):
            for key, value in data.items():
                if key in datetime_fields and isinstance(value, str):
                    try:
                        data[key] = datetime.fromisoformat(value.replace('Z', '+00:00'))
                    except (ValueError, AttributeError):
                        pass  # Keep original value if parsing fails
                elif isinstance(value, dict):
                    data[key] = self._deserialize_datetime_fields(value)
                elif isinstance(value, list):
                    data[key] = [
                        self._deserialize_datetime_fields(item) if isinstance(item, dict) else item
                        for item in value
                    ]
        return data

    # Task CRUD Operations

    @retry_on_connection_error(max_retries=3, delay=2.0)
    async def create_task(self, task: Task) -> str:
        """
        Create a new task in the database.

        Args:
            task: Task object to create

        Returns:
            The task_id of the created task
        """
        try:
            task_dict = task.model_dump()

            # Convert datetime objects to ISO strings for MongoDB
            task_dict = self._serialize_datetime_fields(task_dict)

            result = await self.tasks_collection.insert_one(task_dict)

            logger.info("📝 Created task: {task.task_id}", module="BACKEND-API", routine="create_task")
            return task.task_id

        except Exception as e:
            logger.error("❌ Failed to create task {task.task_id}: {e}", module="BACKEND-API", routine="create_task")
            raise

    @retry_on_connection_error(max_retries=3, delay=2.0)
    async def get_task(self, task_id: str) -> Optional[Task]:
        """
        Get a task by ID.

        Args:
            task_id: Task identifier

        Returns:
            Task object if found, None otherwise
        """
        try:
            task_dict = await self.tasks_collection.find_one(
                {"task_id": task_id}
            )

            if not task_dict:
                logger.warning("🔍 Task not found: {task_id}", module="BACKEND-API", routine="get_task")
                return None

            # Convert ISO strings back to datetime objects
            task_dict = self._deserialize_datetime_fields(task_dict)

            # Remove MongoDB's _id field
            task_dict.pop('_id', None)

            return Task(**task_dict)

        except Exception as e:
            logger.error("❌ Failed to get task {task_id}: {e}", module="BACKEND-API", routine="get_task")
            raise

    @retry_on_connection_error(max_retries=3, delay=2.0)
    async def update_task(self, task: Task) -> bool:
        """
        Update a task in the database.

        Args:
            task: Task object with updated data

        Returns:
            True if update was successful
        """
        try:
            task_dict = task.model_dump()
            task_dict = self._serialize_datetime_fields(task_dict)

            result = await self.tasks_collection.replace_one(
                {"task_id": task.task_id},
                task_dict
            )

            if result.matched_count == 0:
                logger.warning("⚠️ Task not found for update: {task.task_id}", module="BACKEND-API", routine="update_task")
                return False

            logger.info("📝 Updated task: {task.task_id}", module="BACKEND-API", routine="update_task")
            return True

        except Exception as e:
            logger.error("❌ Failed to update task {task.task_id}: {e}", module="BACKEND-API", routine="update_task")
            raise

    @retry_on_connection_error(max_retries=3, delay=2.0)
    async def get_mission_tasks(self, mission_id: str) -> List[Task]:
        """
        Get all tasks for a mission.

        Args:
            mission_id: Mission identifier

        Returns:
            List of Task objects
        """
        try:
            tasks = []
            cursor = self.tasks_collection.find({"mission_id": mission_id})

            # Handle both async and sync cursors (for mock database)
            if hasattr(cursor, '__aiter__'):
                async for task_dict in cursor:
                    task_dict = self._deserialize_datetime_fields(task_dict)
                    task_dict.pop('_id', None)
                    tasks.append(Task(**task_dict))
            else:
                # For mock database
                for task_dict in cursor:
                    task_dict = self._deserialize_datetime_fields(task_dict)
                    task_dict.pop('_id', None)
                    tasks.append(Task(**task_dict))

            logger.info(f"📋 Retrieved {len(tasks)} tasks for mission: {mission_id}", module="BACKEND-API", routine="get_mission_tasks")
            return tasks

        except Exception as e:
            logger.error(f"❌ Failed to get tasks for mission {mission_id}: {e}", module="BACKEND-API", routine="get_mission_tasks")
            raise

    @retry_on_connection_error(max_retries=3, delay=2.0)
    async def get_pending_tasks(self) -> List[Task]:
        """
        Get all pending tasks across all missions.

        Returns:
            List of pending Task objects
        """
        try:
            tasks = []
            cursor = self.tasks_collection.find({"status": TaskStatus.PENDING.value})

            # Handle both async and sync cursors (for mock database)
            if hasattr(cursor, '__aiter__'):
                async for task_dict in cursor:
                    task_dict = self._deserialize_datetime_fields(task_dict)
                    task_dict.pop('_id', None)
                    tasks.append(Task(**task_dict))
            else:
                # For mock database
                for task_dict in cursor:
                    task_dict = self._deserialize_datetime_fields(task_dict)
                    task_dict.pop('_id', None)
                    tasks.append(Task(**task_dict))

            # Only log when task count changes to avoid spam
            from shared.unified_logging import should_log_status_change
            task_count = len(tasks)
            status = f"pending_tasks_{task_count}"
            if should_log_status_change("BACKEND-API", "get_pending_tasks", status):
                logger.info(f"📋 Retrieved {task_count} pending tasks", module="BACKEND-API", routine="get_pending_tasks")
            return tasks

        except Exception as e:
            logger.error(f"❌ Failed to get pending tasks: {e}", module="BACKEND-API", routine="get_pending_tasks")
            raise

    async def ping(self):
        """Ping the database to check connectivity - BULLETPROOF version"""
        try:
            if self.client is None or self.database is None:
                logger.debug("❌ Database not connected - attempting to connect", module="BACKEND-API", routine="ping")
                await self.connect()

            if self.client is None or self.database is None:
                logger.warning("❌ Database still not available after connection attempt", module="BACKEND-API", routine="ping")
                return False

            # Try to ping the database using the client
            await self.client.admin.command('ping')
            logger.debug("✅ Database ping successful", module="BACKEND-API", routine="ping")
            return True

        except Exception as e:
            logger.warning(f"❌ Database ping failed: {str(e)}", module="BACKEND-API", routine="ping")
            return False

    async def health_check(self):
        """Comprehensive database health check"""
        try:
            health_status = {
                "database_connected": False,
                "ping_successful": False,
                "collections_accessible": False,
                "last_check": datetime.utcnow().isoformat(),
                "error": None
            }

            # Check connection
            if self.database:
                health_status["database_connected"] = True

                # Check ping
                ping_result = await self.ping()
                health_status["ping_successful"] = ping_result

                if ping_result:
                    # Check if we can access collections
                    try:
                        collections = await self.database.list_collection_names()
                        health_status["collections_accessible"] = True
                        health_status["collection_count"] = len(collections)
                    except Exception as coll_error:
                        health_status["error"] = f"Collections not accessible: {str(coll_error)}"

            else:
                health_status["error"] = "Database not connected"

            return health_status

        except Exception as e:
            logger.error(f"❌ Health check failed: {str(e)}", module="BACKEND-API", routine="health_check")
            return {
                "database_connected": False,
                "ping_successful": False,
                "collections_accessible": False,
                "last_check": datetime.utcnow().isoformat(),
                "error": str(e)
            }

    # ========================================
    # PASSWORD MIGRATION METHODS
    # ========================================

    async def update_user_password_hash(self, username: str, password: str) -> bool:
        """
        Update user with PBKDF2 password hash.

        Args:
            username: Username to update
            password: Plain text password to hash

        Returns:
            True if successful, False otherwise
        """
        try:
            from shared_models.password_utils import hash_password
            from datetime import datetime

            # Generate PBKDF2 hash
            password_hash = hash_password(password)

            # Update user in database
            result = await self.users_collection.update_one(
                {"username": {"$regex": f"^{username}$", "$options": "i"}},
                {
                    "$set": {
                        "password_hash": password_hash,
                        "updated_at": datetime.now().isoformat()
                    }
                }
            )

            if result.modified_count > 0:
                logger.info(f"✅ Updated password hash for user: {username}", module="BACKEND-API", routine="update_user_password_hash")
                return True
            else:
                logger.warning(f"⚠️ No user found to update: {username}", module="BACKEND-API", routine="update_user_password_hash")
                return False

        except Exception as e:
            logger.error(f"❌ Failed to update password hash for {username}: {e}", module="BACKEND-API", routine="update_user_password_hash")
            return False

    async def migrate_legacy_users_passwords(self) -> Dict[str, Any]:
        """
        Migrate legacy users to have proper password hashes.

        Returns:
            Dictionary with migration results
        """
        try:
            from shared_models.password_utils import hash_password
            from datetime import datetime

            # Default passwords for existing users
            default_passwords = {
                "admin": "admin123",
                "guest": "guest123"
            }

            results = {
                "success": True,
                "updated_users": [],
                "errors": [],
                "skipped_users": []
            }

            for username, password in default_passwords.items():
                try:
                    # Check if user exists and needs password hash
                    user_doc = await self.users_collection.find_one({
                        "username": {"$regex": f"^{username}$", "$options": "i"}
                    })

                    if user_doc:
                        # Check if user already has password_hash
                        if not user_doc.get("password_hash"):
                            # Generate PBKDF2 hash
                            password_hash = hash_password(password)

                            # Update user
                            update_result = await self.users_collection.update_one(
                                {"_id": user_doc["_id"]},
                                {
                                    "$set": {
                                        "password_hash": password_hash,
                                        "updated_at": datetime.now().isoformat()
                                    }
                                }
                            )

                            if update_result.modified_count > 0:
                                results["updated_users"].append(username)
                                logger.info(f"✅ Added password hash for user: {username}", module="BACKEND-API", routine="migrate_legacy_users_passwords")
                            else:
                                results["errors"].append(f"Failed to update {username}")
                        else:
                            results["skipped_users"].append(f"{username} (already has password hash)")
                            logger.info(f"ℹ️ User {username} already has password hash", module="BACKEND-API", routine="migrate_legacy_users_passwords")
                    else:
                        results["errors"].append(f"User {username} not found")

                except Exception as e:
                    error_msg = f"Error updating {username}: {str(e)}"
                    results["errors"].append(error_msg)
                    logger.error(f"❌ {error_msg}", module="BACKEND-API", routine="migrate_legacy_users_passwords")

            if results["errors"]:
                results["success"] = False

            return results

        except Exception as e:
            logger.error(f"❌ Failed to migrate legacy user passwords: {e}", module="BACKEND-API", routine="migrate_legacy_users_passwords")
            return {
                "success": False,
                "updated_users": [],
                "errors": [str(e)],
                "skipped_users": []
            }
