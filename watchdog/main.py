#!/usr/bin/env python3
"""
🐕 Deeplica Watchdog Microservice - FIXED SYNTAX ERRORS

This microservice runs continuously in the background and monitors:
- All Deeplica processes and their states
- Port usage and network connections
- Service health and availability
- Error messages and crashes
- Terminal activity and operations
- System resource usage
- Transaction logs and API calls

It provides comprehensive real-time logging and monitoring.
"""

import os
import sys
import time
import json
import asyncio
import logging
import psutil
import requests
import subprocess
import signal
import traceback
import atexit
from datetime import datetime
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from shared.port_manager import get_service_port
from typing import Dict, List, Optional, Any
import threading
from collections import defaultdict, deque
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import uvicorn

# Add project root to path for shared modules
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.unified_logging import get_watchdog_logger, should_log_status_change
from shared.port_manager import ensure_service_port_free, get_service_host
from shared.port_cache import get_port_cache

# Initialize unified logger
logger = get_watchdog_logger()

# Process Registration Models
class ProcessRegistration(BaseModel):
    """Model for process registration"""
    service_name: str
    process_id: int
    port: Optional[int] = None
    start_time: str
    command: str = ""
    terminal_name: str = ""

class StopRequest(BaseModel):
    """Stop request model"""
    force: bool = False
    reason: str = "Manual stop request"
    shutdown_self: bool = False
import uvicorn

# Add project root to Python path
PROJECT_ROOT = Path(__file__).parent.parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

from shared.port_manager import get_service_port, ensure_service_port_free, get_localhost

# Note: Process name will be set after emergency_log is defined

# FastAPI app for receiving logs from other services
app = FastAPI(title="Watchdog Log Receiver")

class LogMessage(BaseModel):
    service: str
    routine: str
    level: str
    category: str
    message: str
    data: Optional[dict] = None

@app.post("/log")
async def receive_log(log_msg: LogMessage):
    """Receive log messages from other services (especially CLI)"""
    try:
        # Format the log message for display
        timestamp = datetime.now().strftime("%H:%M:%S")
        level_icon = {"INFO": "ℹ️", "ERROR": "❌", "DEBUG": "🔍", "WARNING": "⚠️"}.get(log_msg.level, "📝")

        formatted_msg = f"{timestamp} {level_icon} [{log_msg.service}:{log_msg.routine}] {log_msg.category} | {log_msg.message}"

        if log_msg.data:
            formatted_msg += f" | {json.dumps(log_msg.data, separators=(',', ':'))}"

        # Display in watchdog terminal
        emergency_log(formatted_msg)

        return {"status": "logged"}
    except Exception as e:
        emergency_log(f"🚨 WATCHDOG LOG ENDPOINT ERROR: {e}")
        return {"status": "error", "message": str(e)}

# 🔒 PROCESS REGISTRY ENDPOINTS
@app.post("/register")
async def register_process(registration: ProcessRegistration):
    """Register a new Deeplica process with the Watchdog"""
    try:
        if hasattr(watchdog_instance, 'register_process'):
            success = watchdog_instance.register_process(registration)
            if success:
                return {"status": "success", "message": f"Process {registration.service_name} registered"}
            else:
                raise HTTPException(status_code=400, detail="Failed to register process")
        else:
            raise HTTPException(status_code=503, detail="Watchdog not ready")
    except Exception as e:
        emergency_log(f"🚨 WATCHDOG REGISTER ERROR: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/status")
async def get_registry_status():
    """Get status of all registered processes"""
    try:
        if hasattr(watchdog_instance, 'registered_processes'):
            processes = {}
            for service_name, registration in watchdog_instance.registered_processes.items():
                processes[service_name] = {
                    "pid": registration.process_id,
                    "port": registration.port,
                    "running": watchdog_instance.is_process_running(registration.process_id),
                    "start_time": registration.start_time,
                    "command": registration.command,
                    "terminal": registration.terminal_name
                }

            return {
                "service_name": "WATCHDOG",
                "port": get_service_port("watchdog"),
                "registered_processes": len(processes),
                "registered_ports": list(watchdog_instance.registered_ports) if hasattr(watchdog_instance, 'registered_ports') else [],
                "processes": processes
            }
        else:
            return {"service_name": "WATCHDOG", "port": get_service_port("watchdog"), "registered_processes": 0, "processes": {}}
    except Exception as e:
        emergency_log(f"🚨 WATCHDOG STATUS ERROR: {e}")
        return {"service_name": "WATCHDOG", "port": get_service_port("watchdog"), "error": str(e)}

@app.post("/discover")
async def discover_services():
    """Manually trigger auto-discovery of running Deeplica services"""
    try:
        if hasattr(watchdog_instance, 'auto_discover_services'):
            watchdog_instance.auto_discover_services()
            # Get updated status
            status_response = await get_registry_status()
            return {
                "status": "success",
                "message": "Auto-discovery completed",
                "discovered_processes": status_response.get('registered_processes', 0),
                "processes": status_response.get('processes', {})
            }
        else:
            raise HTTPException(status_code=503, detail="Watchdog not ready")
    except Exception as e:
        emergency_log(f"🚨 WATCHDOG DISCOVER ERROR: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/stop_all")
async def stop_all_processes(request: StopRequest):
    """Stop all registered Deeplica processes"""
    try:
        if hasattr(watchdog_instance, 'stop_all_registered_processes'):
            results = watchdog_instance.stop_all_registered_processes(request.force, request.shutdown_self)
            return {
                "status": "success",
                "message": "Stop operation completed",
                "results": results,
                "reason": request.reason,
                "shutdown_self": request.shutdown_self
            }
        else:
            raise HTTPException(status_code=503, detail="Watchdog not ready")
    except Exception as e:
        emergency_log(f"🚨 WATCHDOG STOP_ALL ERROR: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/stop/{service_name}")
async def stop_specific_process(service_name: str, force: bool = False):
    """Stop a specific registered process"""
    try:
        if hasattr(watchdog_instance, 'stop_registered_process'):
            success = watchdog_instance.stop_registered_process(service_name, force)
            if success:
                return {"status": "success", "message": f"Process {service_name} stopped"}
            else:
                raise HTTPException(status_code=400, detail="Failed to stop process")
        else:
            raise HTTPException(status_code=503, detail="Watchdog not ready")
    except Exception as e:
        emergency_log(f"🚨 WATCHDOG STOP_PROCESS ERROR: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Global reference to watchdog instance for endpoints
watchdog_instance = None

def safe_execute(func, *args, **kwargs):
    """🛡️ Ultra-safe function executor that NEVER crashes the Watchdog"""
    try:
        return func(*args, **kwargs)
    except KeyboardInterrupt:
        # Allow graceful shutdown
        raise
    except SystemExit:
        # Allow system exit
        raise
    except Exception as e:
        # Log error with detailed information but NEVER crash
        try:
            import traceback
            import inspect

            # Get detailed error information
            func_name = getattr(func, '__name__', str(func))
            func_module = getattr(func, '__module__', 'unknown')

            # Get the line number where the error occurred
            tb = traceback.extract_tb(e.__traceback__)
            if tb:
                last_frame = tb[-1]
                error_location = f"{last_frame.filename}:{last_frame.lineno} in {last_frame.name}"
            else:
                error_location = "unknown location"

            # Create verbose error message
            error_details = {
                "function": func_name,
                "module": func_module,
                "error_type": type(e).__name__,
                "error_message": str(e),
                "location": error_location,
                "args": str(args)[:100] if args else "none",
                "kwargs": str(kwargs)[:100] if kwargs else "none"
            }

            emergency_log(f"🚨 WATCHDOG SAFE_EXECUTE ERROR in {func_module}.{func_name}: {type(e).__name__}: {e} at {error_location}")

        except:
            # If even detailed error logging fails, use simple logging
            try:
                emergency_log(f"🛡️ WATCHDOG SAFE EXECUTE: {getattr(func, '__name__', 'unknown')} failed: {e}")
            except:
                pass  # Even error logging can fail, but we survive
        return None


def emergency_log(message):
    """🚨 Emergency logging that works even when everything else fails"""
    try:
        timestamp = datetime.now().isoformat()
        emergency_msg = f"{timestamp} | EMERGENCY | {message}"

        # Try file output first
        try:
            with open("watchdog_emergency.log", "a") as f:
                f.write(emergency_msg + "\n")
                f.flush()
        except:
            pass

        # Try stderr as backup (not stdout to avoid duplicates)
        try:
            sys.stderr.write(emergency_msg + "\n")
            sys.stderr.flush()
        except:
            pass
    except:
        # If even emergency logging fails, we still don't crash
        pass


# Set process name now that emergency_log is available
try:
    import setproctitle
    setproctitle.setproctitle("DEEPLICA-WATCHDOG")
    emergency_log("🐕 WATCHDOG process name set to: DEEPLICA-WATCHDOG")
except ImportError:
    emergency_log("⚠️ WATCHDOG setproctitle not available - process name unchanged")

# Process detection removed per user request - manual management only


class DeepplicaWatchdog:
    """🛡️ CRASH-RESISTANT Comprehensive system monitoring and logging

    This Watchdog is designed to NEVER crash and survive ANY system failure.
    Like an airplane's black box, it records everything and survives crashes.
    """

    def __init__(self):
        try:
            self.running = True
            self.start_time = datetime.now()
            self.crash_count = 0
            self.last_crash_time = None
            self.survival_mode = False
            self.emergency_mode = False

            # Setup crash-resistant logging first
            safe_execute(self.setup_logging)

            # Setup signal handlers for graceful shutdown
            safe_execute(self.setup_signal_handlers)

            # Setup exit handler
            try:
                atexit.register(self.emergency_shutdown)
            except:
                pass

            # Initialize services configuration - will be loaded later
            self.services = {}

            # Anti-spam tracking for repetitive messages
            self._last_communication_failures = {}
            self._last_error_detections = {}
            self._message_cooldown = 60  # seconds

            # Load service configurations
            self.load_service_configurations()

            # Cache service ports to avoid excessive port manager calls
            self._cache_service_ports()

            # 🔒 PROCESS REGISTRY - Watchdog is now the central registry
            self.registered_processes: Dict[str, ProcessRegistration] = {}
            self.registered_ports: Set[int] = set()
            self.registry_file = Path("watchdog/registered_processes.json")
            self.registry_file.parent.mkdir(exist_ok=True)

            # Deeplica service definitions for auto-discovery (use port manager)
            try:
                self.deeplica_services = {
                    get_service_port("backend"): "BACKEND-API",  # Constant
                    get_service_port("dispatcher"): "DISPATCHER",
                    get_service_port("dialogue"): "DIALOGUE-AGENT",
                    get_service_port("planner"): "PLANNER-AGENT",
                    get_service_port("phone"): "PHONE-AGENT",
                    get_service_port("web-chat"): "WEB-CHAT",
                    get_service_port("cli"): "CLI-TERMINAL-API"
                }
            except Exception as e:
                emergency_log(f"❌ Failed to load port manager for service definitions: {e}")
                # Fallback to hardcoded values (using port manager where possible)
                try:
                    self.deeplica_services = {
                        get_service_port("backend"): "BACKEND-API",
                        get_service_port("dispatcher"): "DISPATCHER",
                        get_service_port("dialogue"): "DIALOGUE-AGENT",
                        get_service_port("planner"): "PLANNER-AGENT",
                        get_service_port("phone"): "PHONE-AGENT",
                        get_service_port("web-chat"): "WEB-CHAT",
                        get_service_port("cli"): "CLI-TERMINAL-API"
                    }
                except Exception:
                    # Ultimate fallback
                    self.deeplica_services = {
                        get_service_port("backend"): "BACKEND-API",
                        get_service_port("dispatcher"): "DISPATCHER",
                        get_service_port("dialogue"): "DIALOGUE-AGENT",
                        get_service_port("planner"): "PLANNER-AGENT",
                        get_service_port("phone"): "PHONE-AGENT",
                        get_service_port("web-chat"): "WEB-CHAT",
                        get_service_port("cli"): "CLI-TERMINAL-API"
                    }

            # 🔍 ENHANCED MONITORING - External Services
            self.external_services = {
                "mongodb": {
                    "name": "MongoDB Atlas",
                    "type": "database",
                    "check_method": "connection_string",
                    "critical": True,
                    "last_status": None,
                    "failure_count": 0
                },
                "twilio": {
                    "name": "Twilio API",
                    "type": "api",
                    "check_method": "api_call",
                    "critical": True,
                    "last_status": None,
                    "failure_count": 0
                },
                "ngrok": {
                    "name": "Ngrok Tunnel",
                    "type": "tunnel",
                    "check_method": "tunnel_status",
                    "critical": True,
                    "last_status": None,
                    "failure_count": 0
                },
                "gemini": {
                    "name": "Google Gemini API",
                    "type": "llm",
                    "check_method": "api_call",
                    "critical": True,
                    "last_status": None,
                    "failure_count": 0
                }
            }

            # 📊 SERVICE CRASH DETECTION
            self.service_crash_history = {}
            self.service_restart_counts = {}
            self.service_last_seen = {}

            # 🚨 ALERT THRESHOLDS
            self.crash_alert_threshold = 3  # Alert after 3 crashes
            self.restart_alert_threshold = 5  # Alert after 5 restarts
            self.service_timeout_threshold = 30  # Consider service dead after 30s

            # Load existing registrations and auto-discover
            safe_execute(self.load_registrations)
            safe_execute(self.auto_discover_services)

            # Enhanced monitoring targets with recovery capabilities
            self.external_services = {
                "twilio": {
                    "check_method": "twilio_status",
                    "recovery_method": "attempt_twilio_recovery",
                    "failure_count": 0,
                    "last_status": "unknown",
                    "last_recovery": 0,
                    "critical": True
                },
                "ngrok": {
                    "check_method": "ngrok_status",
                    "recovery_method": "attempt_ngrok_recovery",
                    "failure_count": 0,
                    "last_status": "unknown",
                    "last_recovery": 0,
                    "critical": True
                },
                "mongodb": {
                    "check_method": "database_status",
                    "recovery_method": "attempt_database_recovery",
                    "failure_count": 0,
                    "last_status": "unknown",
                    "last_recovery": 0,
                    "critical": True
                },
                "gemini": {
                    "check_method": "gemini_status",
                    "recovery_method": "attempt_gemini_recovery",
                    "failure_count": 0,
                    "last_status": "unknown",
                    "last_recovery": 0,
                    "critical": False
                }
            }

            # Text and conversation monitoring
            self.conversation_log = deque(maxlen=1000)
            self.processed_log_lines = set()  # Track processed log lines to avoid duplicates
            self.normalized_messages = set()  # Track normalized messages to ignore counters/timestamps
            self.error_call_patterns = [
                "error", "failed", "exception", "crash", "timeout", "connection refused"
            ]
            self.auto_recovery_enabled = True
            self.stop_service_running = False
            self.recovery_attempts = {}  # Track recovery attempts per service
            self.max_recovery_attempts = 3  # Max attempts before giving up
            self.recovery_cooldown = 60  # Seconds between recovery attempts
            self.last_recovery_time = {}  # Track last recovery time per service

            # Service startup commands with proper module paths
            self.service_commands = {
                "backend": "python3 -m app.main",
                "dispatcher": "python3 -m app.main",
                "dialogue": "python3 -m app.main",
                "planner": "python3 -m app.main",
                "phone": "python3 -m app.main",
                "cli": "python3 terminal_ui.py",
                "web-chat": "python3 main.py",
                "twilio-echo-bot": "python3 echo_bot.py",
                "webhook-server": "python3 webhook_server.py"
            }

            # Service working directories
            self.service_directories = {
                "backend": "backend",
                "dispatcher": "dispatcher",
                "dialogue": "agents/dialogue",
                "planner": "agents/planner",
                "phone": "agents/phone",
                "cli": "cli",
                "web-chat": "web_chat",
                "twilio-echo-bot": "twilio-echo-bot",
                "webhook-server": "webhook-server"
            }

            # External service recovery commands
            self.external_recovery_commands = {
                "ngrok": [
                    "pkill -f ngrok",  # Kill existing ngrok processes
                    "sleep 2",
                    f"ngrok http {get_service_port('phone')} --log=stdout > /dev/null 2>&1 &"  # Start ngrok for phone agent
                ],
                "twilio": [
                    # Twilio recovery involves restarting phone agent with fresh webhook URL
                    f"curl -X POST http://localhost:{get_service_port('phone')}/restart_twilio"
                ],
                "mongodb": [
                    # MongoDB Atlas is cloud service, recovery involves connection retry
                    f"curl -X POST http://localhost:{get_service_port('backend')}/database/reconnect"  # Backend API port
                ],
                "gemini": [
                    # Gemini API recovery involves API key validation
                    f"curl -X POST http://localhost:{get_service_port('backend')}/gemini/validate"  # Backend API constant port
                ]
            }

            # State tracking
            self.service_states = {}
            self.port_states = {}
            self.process_history = deque(maxlen=1000)
            self.error_log = deque(maxlen=500)
            self.transaction_log = deque(maxlen=1000)
            self.terminal_activity = deque(maxlen=200)

            # Status change tracking - only log when status changes
            self.last_health_status = {}      # Track health status per service
            self.last_port_status = {}        # Track port status per port
            self.last_process_status = {}     # Track process status per service
            self.last_external_status = {}    # Track external service status
            self.last_system_status = {}      # Track system metrics status

            # Performance tracking
            self.system_metrics = {
                "cpu_usage": deque(maxlen=100),
                "memory_usage": deque(maxlen=100),
                "disk_usage": deque(maxlen=100)
            }

            # Initialize logging
            self.setup_logging()

            # Last known states for change detection
            self.last_process_count = 0
            self.last_port_states = {}
            self.last_service_states = {}

        except Exception as e:
            # Even initialization errors won't crash us
            emergency_log(f"🚨 WATCHDOG INIT ERROR: {e}")
            self.survival_mode = True
            self.emergency_mode = True
            # Set minimal defaults to keep running
            self.running = True
            self.start_time = datetime.now()
            self.crash_count = 1
            self.services = {}
            self.conversation_log = deque(maxlen=100)
            self.error_log = deque(maxlen=100)

    def _cache_service_ports(self):
        """Cache service ports to avoid excessive port manager calls"""
        try:
            self.cached_ports = {
                'backend': get_service_port("backend"),
                'dispatcher': get_service_port("dispatcher"),
                'dialogue': get_service_port("dialogue"),
                'planner': get_service_port("planner"),
                'phone': get_service_port("phone"),
                'web-chat': get_service_port("web-chat"),
                'cli': get_service_port("cli"),
                'watchdog': get_service_port("watchdog"),
                'ngrok-api': get_service_port("ngrok-api")
            }
            self.cached_host = get_service_host()
            emergency_log(f"✅ Service ports cached successfully")
        except Exception as e:
            emergency_log(f"⚠️ Failed to cache service ports: {e}")
            # Fallback to direct calls if caching fails
            self.cached_ports = {}
            self.cached_host = "localhost"

    def get_cached_port(self, service_name):
        """Get cached port or fallback to direct call"""
        try:
            if hasattr(self, 'cached_ports') and service_name in self.cached_ports:
                return self.cached_ports[service_name]
            else:
                return get_service_port(service_name)
        except Exception:
            # Ultimate fallback - try to read from port settings file
            try:
                import json
                from pathlib import Path
                port_file = Path(__file__).parent.parent / "shared" / "deeplica_port_settings.json"
                if port_file.exists():
                    with open(port_file, 'r') as f:
                        data = json.load(f)
                        return data.get(service_name.upper(), 8000)
            except Exception:
                pass

            # Final fallback - return a safe default
            emergency_log(f"⚠️ Could not get port for {service_name} - using default 8000")
            return 8000

    def get_cached_host(self):
        """Get cached host or fallback with improved caching"""
        try:
            if hasattr(self, 'cached_host') and self.cached_host:
                return self.cached_host
            else:
                # Cache the host for future use
                self.cached_host = get_service_host()
                return self.cached_host
        except Exception as e:
            emergency_log(f"⚠️ Failed to get service host: {e}")
            return "localhost"

    def _is_service_healthy(self, service_name):
        """Check if a service is actually healthy before attempting recovery"""
        try:
            import requests
            port = self.get_cached_port(service_name)
            host = self.get_cached_host()
            response = requests.get(f"http://{host}:{port}/health", timeout=5)
            return response.status_code == 200
        except Exception:
            return False

    def load_service_configurations(self):
        """Load service configurations using port manager with caching"""
        try:
            from shared.port_manager import get_service_port
            from shared.port_manager import get_service_host as get_localhost

            # Cache the host to avoid repeated calls
            host = get_localhost()

            # Cache all ports in one go to reduce port manager calls
            ports = {
                "backend": get_service_port("BACKEND-API"),
                "dispatcher": get_service_port("DISPATCHER"),
                "dialogue": get_service_port("DIALOGUE-AGENT"),
                "planner": get_service_port("PLANNER-AGENT"),
                "phone": get_service_port("PHONE-AGENT"),
                "web_chat": get_service_port("WEB-CHAT"),
                "cli": get_service_port("CLI-TERMINAL")
            }

            self.services = {
                "backend": {"port": ports["backend"], "health_url": f"http://{host}:{ports['backend']}/health", "process_name": "DEEPLICA-BACKEND-API"},
                "dispatcher": {"port": ports["dispatcher"], "health_url": f"http://{host}:{ports['dispatcher']}/health", "process_name": "DEEPLICA-DISPATCHER"},
                "dialogue": {"port": ports["dialogue"], "health_url": f"http://{host}:{ports['dialogue']}/health", "process_name": "DEEPLICA-DIALOGUE-AGENT"},
                "planner": {"port": ports["planner"], "health_url": f"http://{host}:{ports['planner']}/health", "process_name": "DEEPLICA-PLANNER-AGENT"},
                "phone": {
                    "port": ports["phone"],
                    "health_url": f"http://{host}:{ports['phone']}/health",
                    "process_name": "DEEPLICA-PHONE-AGENT",
                    "consecutive_failures": 0,
                    "last_successful_check": 0,
                    "critical_service": True,
                    "auto_recovery": True,
                    "max_consecutive_failures": 3
                },
                "web_chat": {"port": ports["web_chat"], "health_url": f"http://{host}:{ports['web_chat']}/health", "process_name": "DEEPLICA-WEB-CHAT"},
                "cli": {"port": ports["cli"], "health_url": f"http://{host}:{ports['cli']}/health", "process_name": "DEEPLICA-CLI-TERMINAL"},
                "orchestrator": {"port": None, "health_url": None, "process_name": "DEEPLICA-ORCHESTRATOR"},
            }
            emergency_log("✅ Service configurations loaded successfully")
        except Exception as e:
            emergency_log(f"❌ Failed to load service configurations: {e}")
            # Fallback to hardcoded values - use port manager for dynamic ports
            self.services = {
                "backend": {"port": get_service_port("backend"), "health_url": f"http://localhost:{get_service_port('backend')}/health", "process_name": "DEEPLICA-BACKEND-API"},
                "dispatcher": {"port": get_service_port("dispatcher"), "health_url": f"http://localhost:{get_service_port('dispatcher')}/health", "process_name": "DEEPLICA-DISPATCHER"},
                "dialogue": {"port": get_service_port("dialogue"), "health_url": f"http://localhost:{get_service_port('dialogue')}/health", "process_name": "DEEPLICA-DIALOGUE-AGENT"},
                "planner": {"port": get_service_port("planner"), "health_url": f"http://localhost:{get_service_port('planner')}/health", "process_name": "DEEPLICA-PLANNER-AGENT"},
                "phone": {
                    "port": get_service_port("phone"),
                    "health_url": f"http://localhost:{get_service_port('phone')}/health",
                    "process_name": "DEEPLICA-PHONE-AGENT",
                    "consecutive_failures": 0,
                    "last_successful_check": 0,
                    "critical_service": True,
                    "auto_recovery": True,
                    "max_consecutive_failures": 3
                },
                "web_chat": {"port": get_service_port("web-chat"), "health_url": f"http://localhost:{get_service_port('web-chat')}/health", "process_name": "DEEPLICA-WEB-CHAT"},
                "cli": {"port": get_service_port("cli"), "health_url": f"http://localhost:{get_service_port('cli')}/health", "process_name": "DEEPLICA-CLI-TERMINAL"},
                "orchestrator": {"port": None, "health_url": None, "process_name": "DEEPLICA-ORCHESTRATOR"},
            }

    def setup_signal_handlers(self):
        """🛡️ Setup signal handlers for graceful shutdown"""
        try:
            def signal_handler(signum, frame):
                emergency_log(f"🛡️ WATCHDOG received signal {signum} - initiating graceful shutdown")
                self.running = False

            signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
            signal.signal(signal.SIGTERM, signal_handler)  # Termination

            # On Unix systems, also handle SIGHUP
            if hasattr(signal, 'SIGHUP'):
                signal.signal(signal.SIGHUP, signal_handler)

        except Exception as e:
            emergency_log(f"🚨 Failed to setup signal handlers: {e}")

    def cleanup_memory(self):
        """🧹 Clean up memory and prevent leaks"""
        try:
            import gc

            # Clear old log entries if they get too large
            if hasattr(self, 'recent_logs') and len(self.recent_logs) > 1000:
                self.recent_logs = self.recent_logs[-500:]  # Keep last 500

            # Clear old process data
            if hasattr(self, 'process_history') and len(self.process_history) > 100:
                self.process_history = self.process_history[-50:]  # Keep last 50

            # Force garbage collection
            collected = gc.collect()

            # Reset crash count if system has been stable
            if hasattr(self, 'last_crash_time') and self.last_crash_time:
                time_since_crash = (datetime.now() - self.last_crash_time).total_seconds()
                if time_since_crash > 3600:  # 1 hour
                    self.crash_count = 0

        except Exception as e:
            emergency_log(f"⚠️ Memory cleanup error: {e}")

    def emergency_shutdown(self):
        """🚨 Emergency shutdown handler"""
        try:
            emergency_log("🛡️ WATCHDOG emergency shutdown initiated")
            if hasattr(self, 'running'):
                self.running = False
        except:
            pass  # Even emergency shutdown can't crash us

    # 🔒 PROCESS REGISTRY METHODS
    def load_registrations(self):
        """Load existing process registrations from file"""
        try:
            if self.registry_file.exists():
                with open(self.registry_file, 'r') as f:
                    data = json.load(f)
                    for service_name, reg_data in data.items():
                        # Verify process is still running
                        if self.is_process_running(reg_data.get('process_id')):
                            self.registered_processes[service_name] = ProcessRegistration(**reg_data)
                            if reg_data.get('port'):
                                self.registered_ports.add(reg_data['port'])
                emergency_log(f"[WATCHDOG] 📋 Loaded {len(self.registered_processes)} active registrations")
            else:
                emergency_log(f"[WATCHDOG] 📋 No existing registrations found")
        except Exception as e:
            emergency_log(f"[WATCHDOG] ⚠️ Failed to load registrations: {e}")

    def save_registrations(self):
        """Save current registrations to file"""
        try:
            data = {}
            for service_name, registration in self.registered_processes.items():
                data[service_name] = registration.model_dump()

            with open(self.registry_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            emergency_log(f"[WATCHDOG] ⚠️ Failed to save registrations: {e}")

    def register_process(self, registration: ProcessRegistration) -> bool:
        """Register a new process"""
        try:
            service_name = registration.service_name

            # Check if already registered
            if service_name in self.registered_processes:
                emergency_log(f"[WATCHDOG] ⚠️ Service {service_name} already registered")
                return False

            # Check if port is already in use by another service
            if registration.port and registration.port in self.registered_ports:
                emergency_log(f"[WATCHDOG] ⚠️ Port {registration.port} already registered")
                return False

            # Register the process
            self.registered_processes[service_name] = registration
            if registration.port:
                self.registered_ports.add(registration.port)

            # Save to file
            self.save_registrations()

            emergency_log(f"[WATCHDOG] ✅ Registered: {service_name} (PID {registration.process_id}, Port {registration.port})")
            return True

        except Exception as e:
            emergency_log(f"[WATCHDOG] ❌ Failed to register {registration.service_name}: {e}")
            return False

    def auto_discover_services(self):
        """Auto-discover running Deeplica services and register them"""
        try:
            emergency_log(f"discovering running Deeplica services...")
            discovered_count = 0

            for service_port, service_name in self.deeplica_services.items():
                # Skip if already registered
                if service_name in self.registered_processes:
                    continue

                # Check if service is running on this port
                if self.is_port_in_use(service_port):
                    try:
                        # Try to get process info for this port
                        pid = self.get_pid_for_port(service_port)
                        if pid:
                            # Create registration for discovered service
                            registration = ProcessRegistration(
                                service_name=service_name,
                                process_id=pid,
                                port=service_port,
                                start_time=datetime.now().isoformat(),
                                command=f"Auto-discovered on port {service_port}",
                                terminal_name=f"🔍 {service_name} (Auto-discovered)"
                            )

                            # Register the discovered service
                            self.registered_processes[service_name] = registration
                            self.registered_ports.add(service_port)
                            discovered_count += 1

                            emergency_log(f"discovered: {service_name} (PID {pid}, Port {service_port})")

                    except Exception as e:
                        emergency_log(f"discover {service_name} on port {service_port}: {e}")

            if discovered_count > 0:
                emergency_log(f"discovered {discovered_count} running Deeplica services")
                self.save_registrations()
            else:
                emergency_log(f"[WATCHDOG] ℹ️ No additional Deeplica services found running")

        except Exception as e:
            emergency_log(f"discovery failed: {e}")

    def is_port_in_use(self, port: int) -> bool:
        """Check if a port is in use"""
        try:
            import socket
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                result = s.connect_ex(({get_localhost()}, port))
                return result == 0
        except:
            return False

    def get_pid_for_port(self, port: int) -> Optional[int]:
        """Get the PID of the process using a specific port"""
        try:
            import subprocess
            result = subprocess.run(
                ['lsof', '-ti', f':{port}'],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0 and result.stdout.strip():
                pid = int(result.stdout.strip().split('\n')[0])
                return pid
            return None

        except Exception as e:
            emergency_log(f"[WATCHDOG] ⚠️ Could not get PID for port {port}: {e}")
            return None

    def is_process_running(self, pid: int) -> bool:
        """Check if a process is still running"""
        try:
            return psutil.pid_exists(pid)
        except:
            return False

    def stop_registered_process(self, service_name: str, force: bool = False) -> bool:
        """Stop a specific registered process"""
        try:
            if service_name not in self.registered_processes:
                emergency_log(f"[WATCHDOG] ⚠️ Service {service_name} not registered")
                return False

            registration = self.registered_processes[service_name]
            pid = registration.process_id

            emergency_log(f"[WATCHDOG] 🛑 Stopping {service_name} (PID {pid})...")

            # Try graceful shutdown first
            if not force:
                try:
                    process = psutil.Process(pid)
                    process.terminate()
                    # Wait up to 5 seconds for graceful shutdown
                    process.wait(timeout=5)
                    emergency_log(f"[WATCHDOG] ✅ Gracefully stopped {service_name}")
                except psutil.TimeoutExpired:
                    emergency_log(f"[WATCHDOG] ⚠️ {service_name} didn't stop gracefully, forcing...")
                    force = True
                except psutil.NoSuchProcess:
                    emergency_log(f"[WATCHDOG] ℹ️ {service_name} already stopped")

            # Force kill if needed
            if force:
                try:
                    process = psutil.Process(pid)
                    process.kill()
                    emergency_log(f"[WATCHDOG] 💀 Force killed {service_name}")
                except psutil.NoSuchProcess:
                    emergency_log(f"[WATCHDOG] ℹ️ {service_name} already stopped")

            # Remove from registry
            del self.registered_processes[service_name]
            if registration.port:
                self.registered_ports.discard(registration.port)

            # Save updated registry
            self.save_registrations()

            return True

        except Exception as e:
            emergency_log(f"[WATCHDOG] ❌ Failed to stop {service_name}: {e}")
            return False

    def stop_all_registered_processes(self, force: bool = False, shutdown_self: bool = False) -> Dict[str, bool]:
        """Stop all registered Deeplica processes in proper order"""
        results = {}

        try:
            emergency_log(f"[WATCHDOG] 🛑 Stopping {len(self.registered_processes)} registered processes...")

            # CRITICAL: Stop services in proper dependency order
            # 1. CLI Terminal first (user interface)
            # 2. Application services (Phone, Planner, Dialogue)
            # 3. Core services (Dispatcher, Backend)
            # 4. Stop Deeplica Service
            # 5. Watchdog LAST (this service)

            stop_order = [
                "CLI-TERMINAL",
                "PHONE-AGENT",
                "PLANNER-AGENT",
                "DIALOGUE-AGENT",
                "DISPATCHER",
                "BACKEND-API",
                "STOP-DEEPLICA-SERVICE"
            ]

            # Stop services in order
            for service_name in stop_order:
                if service_name in self.registered_processes:
                    emergency_log(f"[WATCHDOG] 🛑 Stopping {service_name}...")
                    results[service_name] = self.stop_registered_process(service_name, force)

            # Stop any remaining services not in the order list
            for service_name in list(self.registered_processes.keys()):
                if service_name not in results:
                    emergency_log(f"[WATCHDOG] 🛑 Stopping remaining service: {service_name}...")
                    results[service_name] = self.stop_registered_process(service_name, force)

            emergency_log(f"[WATCHDOG] ✅ Stop operation completed")

            # Only stop self if explicitly requested
            if shutdown_self:
                emergency_log(f"[WATCHDOG] 🛑 Shutting down Watchdog itself...")
                import threading
                import time
                import sys
                # Schedule self-shutdown after a brief delay to allow response to be sent
                def delayed_shutdown():
                    time.sleep(1)  # Allow response to be sent
                    emergency_log(f"[WATCHDOG] 👋 Watchdog shutdown requested...")
                    # 🛡️ BULLETPROOF: Never exit - set shutdown flag instead
                    global shutdown_requested
                    shutdown_requested = True
                    emergency_log(f"[WATCHDOG] 🛡️ BULLETPROOF: Shutdown flag set - service will stop gracefully")

                shutdown_thread = threading.Thread(target=delayed_shutdown, daemon=True)
                shutdown_thread.start()

            return results

        except Exception as e:
            emergency_log(f"[WATCHDOG] ❌ Stop operation failed: {e}")
            return results

    def setup_logging(self):
        """🛡️ Setup crash-resistant logging system"""
        try:
            # Create logs directory
            log_dir = PROJECT_ROOT / "logs"
            log_dir.mkdir(exist_ok=True)

            # Setup main logger
            self.logger = logging.getLogger("DeepplicaWatchdog")
            self.logger.setLevel(logging.DEBUG)

            # Clear any existing handlers to avoid duplicates
            self.logger.handlers.clear()

            # Console handler for terminal output
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(logging.INFO)

            # File handler for persistent logging
            file_handler = logging.FileHandler(log_dir / f"watchdog_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
            file_handler.setLevel(logging.DEBUG)

            # Detailed formatter
            formatter = logging.Formatter(
                '%(asctime)s | %(levelname)8s | %(name)s | %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )

            console_handler.setFormatter(formatter)
            file_handler.setFormatter(formatter)

            self.logger.addHandler(console_handler)
            self.logger.addHandler(file_handler)

        except Exception as e:
            # If logging setup fails, use emergency logging
            emergency_log(f"🚨 Logging setup failed: {e}")
            # Create a minimal logger that won't crash
            try:
                self.logger = logging.getLogger("DeepplicaWatchdog")
                self.logger.setLevel(logging.INFO)
                self.logger.handlers.clear()  # Clear handlers
            except:
                # If even basic logger fails, create a dummy logger
                class DummyLogger:
                    def info(self, msg): emergency_log(f"INFO: {msg}")
                    def warning(self, msg): emergency_log(f"WARNING: {msg}")
                    def error(self, msg): emergency_log(f"ERROR: {msg}")
                    def debug(self, msg): pass  # Skip debug in emergency mode
                self.logger = DummyLogger()
        
    def log_event(self, level: str, category: str, message: str, data: Optional[Dict] = None,
                  service: str = "WATCHDOG", routine: str = "monitor", force_log: bool = False):
        """Log an event with structured data, smart deduplication, and improved formatting"""
        timestamp = datetime.now()

        # Check for duplicates unless forced or it's an error
        if not force_log and level != "ERROR" and level != "WARNING":
            if self.is_message_duplicate(message, service, routine, category):
                return  # Skip duplicate message

        # Create structured log entry
        log_entry = {
            "timestamp": timestamp.isoformat(),
            "level": level,
            "category": category,
            "service": service,
            "routine": routine,
            "message": message,
            "data": data or {}
        }

        # Add to appropriate log
        if level == "ERROR":
            self.error_log.append(log_entry)
        elif category == "TRANSACTION":
            self.transaction_log.append(log_entry)
        elif category == "TERMINAL":
            self.terminal_activity.append(log_entry)

        # Create readable message format
        # Format: [SERVICE:ROUTINE] CATEGORY | MESSAGE | Key details
        service_routine = f"{service}:{routine}"

        # Extract key details from data for readable display
        key_details = ""
        if data:
            important_keys = ["pid", "port", "status", "cpu_percent", "memory_percent",
                            "old_status", "new_status", "error", "service_name"]
            details = []
            for key in important_keys:
                if key in data:
                    details.append(f"{key}={data[key]}")
            if details:
                key_details = f" | {', '.join(details[:3])}"  # Limit to 3 key details

        # Format final message
        log_msg = f"[{service_routine:20}] {category:10} | {message}{key_details}"

        # Log to appropriate level using standard Python logger
        if level == "ERROR":
            self.logger.error(log_msg)
        elif level == "WARNING":
            self.logger.warning(log_msg)
        elif level == "INFO":
            self.logger.info(log_msg)
        else:
            self.logger.debug(log_msg)
    
    def monitor_processes(self):
        """Monitor all Deeplica processes"""
        try:
            current_processes = {}
            
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'status', 'cpu_percent', 'memory_percent']):
                try:
                    proc_info = proc.info
                    cmdline_list = proc_info.get('cmdline', []) or []
                    
                    if isinstance(cmdline_list, list):
                        cmdline = ' '.join(str(arg) for arg in cmdline_list if arg is not None)
                    else:
                        cmdline = str(cmdline_list) if cmdline_list else ''
                    
                    # Check if this is a Deeplica process
                    for service_name, service_info in self.services.items():
                        process_name = service_info['process_name']
                        
                        if (process_name in cmdline or 
                            any(pattern in cmdline for pattern in [
                                'backend/app/main.py',
                                'dispatcher/app/main.py',
                                'agents/dialogue/app/main.py',
                                'agents/planner/app/main.py',
                                'agents/phone/app/main.py',
                                'cli/terminal_ui.py',
                                'orchestrator/main.py',
                                'stop_deeplica/main.py'
                            ])):
                            
                            current_processes[service_name] = {
                                "pid": proc_info['pid'],
                                "name": proc_info['name'],
                                "status": proc_info['status'],
                                "cpu_percent": proc_info.get('cpu_percent', 0),
                                "memory_percent": proc_info.get('memory_percent', 0),
                                "cmdline": cmdline
                            }
                            
                            # Check for state changes
                            if service_name not in self.service_states:
                                self.log_event("INFO", "PROCESS", f"Started", current_processes[service_name],
                                             service=service_name.upper(), routine="startup")
                            elif self.service_states[service_name]['status'] != proc_info['status']:
                                self.log_event("WARNING", "PROCESS", f"Status changed", {
                                    "old_status": self.service_states[service_name]['status'],
                                    "new_status": proc_info['status'],
                                    "pid": proc_info['pid']
                                }, service=service_name.upper(), routine="status_change")
                            
                            break
                            
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                except Exception as e:
                    import traceback
                    tb_str = traceback.format_exc()
                    self.log_event("ERROR", "PROCESS", f"Error in monitor_processes.process_iteration", {
                        "error_type": type(e).__name__,
                        "error_message": str(e),
                        "process_pid": getattr(proc, 'pid', 'unknown'),
                        "traceback": tb_str[:500],  # Limit traceback length
                        "routine": "monitor_processes",
                        "module": "watchdog.main",
                        "function": "monitor_processes"
                    }, service="WATCHDOG", routine="monitor_processes", force_log=True)
            
            # Check for stopped services and attempt recovery
            for service_name in self.service_states:
                if service_name not in current_processes:
                    self.log_event("WARNING", "PROCESS", f"Stopped", self.service_states[service_name],
                                 service=service_name.upper(), routine="shutdown")

                    # Attempt auto-recovery for critical services
                    if service_name in self.service_commands:
                        safe_execute(self.attempt_service_recovery, service_name)

            self.service_states = current_processes

            # Log process count changes
            if len(current_processes) != self.last_process_count:
                self.log_event("INFO", "SYSTEM", f"Active services changed from {self.last_process_count} to {len(current_processes)}", {
                    "old_count": self.last_process_count,
                    "new_count": len(current_processes),
                    "active_services": list(current_processes.keys())
                }, service="WATCHDOG", routine="process_counter")
                self.last_process_count = len(current_processes)

        except Exception as e:
            # Only log process monitoring errors if they're new
            error_key = f"process_monitor_error_{type(e).__name__}"
            if not hasattr(self, error_key):
                import traceback
                tb_str = traceback.format_exc()
                self.log_event("ERROR", "MONITOR", f"Critical error in monitor_processes main loop", {
                    "error_type": type(e).__name__,
                    "error_message": str(e),
                    "traceback": tb_str[:500],
                    "routine": "monitor_processes",
                    "module": "watchdog.main",
                    "function": "monitor_processes",
                    "location": "main_process_monitoring_loop"
                }, service="WATCHDOG", routine="monitor_processes", force_log=True)
                setattr(self, error_key, True)
    
    def monitor_ports(self):
        """Monitor port usage for all Deeplica services"""
        try:
            current_port_states = {}
            
            for service_name, service_info in self.services.items():
                port = service_info.get('port')
                if port:
                    try:
                        # Check if port is in use
                        result = subprocess.run(
                            ["lsof", "-i", f": {port}"],
                            capture_output=True,
                            text=True
                        )
                        
                        port_in_use = result.returncode == 0
                        current_port_states[port] = {
                            "service": service_name,
                            "in_use": port_in_use,
                            "details": result.stdout if port_in_use else None
                        }
                        
                        # Check for port state changes
                        if port in self.last_port_states:
                            if self.last_port_states[port]['in_use'] != port_in_use:
                                status = "opened" if port_in_use else "closed"
                                self.log_event("INFO", "PORT", f"Port {status}", {"port": port, "status": status},
                                             service=service_name.upper(), routine="port_monitor")
                        else:
                            if port_in_use:
                                self.log_event("INFO", "PORT", f"Port detected in use", {"port": port},
                                             service=service_name.upper(), routine="port_detection")

                    except subprocess.TimeoutExpired:
                        # lsof timeout is not an error, just skip this check
                        continue
                    except FileNotFoundError:
                        # lsof not available on this system
                        if not hasattr(self, 'lsof_unavailable_warned'):
                            self.log_event("INFO", "PORT", "lsof command not available for port monitoring",
                                         service="WATCHDOG", routine="port_monitor")
                            self.lsof_unavailable_warned = True
                        break
                    except Exception as e:
                        # Only log port errors once per port
                        error_key = f"port_error_{port}"
                        if not hasattr(self, error_key):
                            self.log_event("WARNING", "PORT", f"Port check issue",
                                         {"port": port, "error": str(e)[:50]},
                                         service=service_name.upper(), routine="port_monitor")
                            setattr(self, error_key, True)
            
            self.last_port_states = current_port_states
            
        except Exception as e:
            self.log_event("ERROR", "MONITOR", f"Port monitoring failed", {"error": str(e)})
    
    def monitor_service_health(self):
        """Monitor service health endpoints"""
        try:
            for service_name, service_info in self.services.items():
                health_url = service_info.get('health_url')
                if health_url:
                    try:
                        response = requests.get(health_url, timeout=2)
                        
                        health_data = {
                            "service": service_name,
                            "status_code": response.status_code,
                            "response_time": response.elapsed.total_seconds(),
                            "healthy": response.status_code == 200
                        }
                        
                        if response.status_code == 200:
                            try:
                                response_data = response.json()
                                health_data["response_data"] = response_data

                                # Special handling for web chat service
                                if service_name == "web_chat":
                                    safe_execute(self.monitor_webchat_specific_health, response_data)

                            except:
                                health_data["response_text"] = response.text
                        last_health = self.last_health_status.get(service_name)
                        current_health = health_data['healthy']

                        # Only log if this is the first check or status changed
                        if last_health is None:
                            # First time checking this service
                            status = "healthy" if current_health else "unhealthy"
                            self.log_event("INFO", "HEALTH", f"Initial status: {status}", health_data,
                                         service=service_name.upper(), routine="health_check")
                        elif last_health != current_health:
                            # Status changed
                            status = "healthy" if current_health else "unhealthy"
                            self.log_event("WARNING", "HEALTH", f"Status changed to {status}", health_data,
                                         service=service_name.upper(), routine="health_check")

                            # If service became healthy, reset recovery attempts
                            if current_health:
                                safe_execute(self.reset_recovery_attempts, service_name)

                        # Update tracking
                        self.last_health_status[service_name] = current_health
                        self.last_service_states[service_name] = health_data

                    except requests.exceptions.ConnectionError:
                        # Service not available - only log if state changed
                        last_health = self.last_health_status.get(service_name)
                        current_health = False

                        # Only log if this is first check or status changed from healthy to unhealthy
                        if last_health is None:
                            self.log_event("WARNING", "HEALTH", f"Initial status: unavailable", {"error": "Connection refused"},
                                         service=service_name.upper(), routine="health_check")
                        elif last_health == True:  # Was healthy, now unhealthy
                            self.log_event("WARNING", "HEALTH", f"Status changed to unavailable", {"error": "Connection refused"},
                                         service=service_name.upper(), routine="health_check")

                            # Attempt auto-recovery for unavailable services
                            if service_name in self.service_commands:
                                safe_execute(self.attempt_service_recovery, service_name)

                        # Update tracking
                        self.last_health_status[service_name] = current_health
                        self.last_service_states[service_name] = {"healthy": False, "error": "Connection refused"}

                    except requests.exceptions.Timeout:
                        # Timeout - only log if state changed
                        if (service_name in self.last_service_states and
                            self.last_service_states[service_name].get('healthy', False)):
                            self.log_event("WARNING", "HEALTH", f"Health check timeout", {"timeout": "2s"},
                                         service=service_name.upper(), routine="health_check")
                        self.last_service_states[service_name] = {"healthy": False, "error": "Timeout"}

                    except Exception as e:
                        # Only log health errors once per service per error type
                        error_key = f"health_error_{service_name}_{type(e).__name__}"
                        if not hasattr(self, error_key):
                            self.log_event("WARNING", "HEALTH", f"Health check issue: {type(e).__name__}",
                                         {"error": str(e)[:50]}, service=service_name.upper(), routine="health_check")
                            setattr(self, error_key, True)
                        
        except Exception as e:
            self.log_event("ERROR", "MONITOR", f"Health monitoring failed", {"error": str(e)})

    def monitor_webchat_specific_health(self, health_data: dict):
        """🌐 Specialized monitoring for web chat service"""
        try:
            # Extract web chat specific metrics
            connections = health_data.get("connections", {})
            backend = health_data.get("backend", {})
            browser_auto_launch = health_data.get("browser_auto_launch")

            active_connections = connections.get("active", 0)
            unique_users = connections.get("unique_users", 0)
            total_messages = connections.get("total_messages", 0)
            circuit_breaker_open = backend.get("circuit_breaker_open", False)
            deeplica_failures = backend.get("deeplica_failures", 0)

            # Track web chat activity
            last_webchat_state = getattr(self, 'last_webchat_state', {})

            # Log significant changes in web chat activity
            if active_connections != last_webchat_state.get('active_connections', 0):
                if active_connections > 0:
                    self.log_event("INFO", "WEBCHAT", f"Active connections: {active_connections} users", {
                        "active_connections": active_connections,
                        "unique_users": unique_users,
                        "total_messages": total_messages
                    }, service="WEB-CHAT", routine="connection_monitor")
                elif last_webchat_state.get('active_connections', 0) > 0:
                    self.log_event("INFO", "WEBCHAT", "All users disconnected", {
                        "previous_connections": last_webchat_state.get('active_connections', 0)
                    }, service="WEB-CHAT", routine="connection_monitor")

            # Monitor circuit breaker status
            if circuit_breaker_open != last_webchat_state.get('circuit_breaker_open', False):
                if circuit_breaker_open:
                    self.log_event("WARNING", "WEBCHAT", "Circuit breaker opened - backend issues detected", {
                        "deeplica_failures": deeplica_failures,
                        "active_connections": active_connections
                    }, service="WEB-CHAT", routine="circuit_breaker_monitor")

                    # Attempt automatic recovery
                    safe_execute(self.attempt_webchat_recovery)
                else:
                    self.log_event("INFO", "WEBCHAT", "Circuit breaker closed - backend recovered", {
                        "deeplica_failures": deeplica_failures
                    }, service="WEB-CHAT", routine="circuit_breaker_monitor")

            # Monitor browser auto-launch status
            if browser_auto_launch and browser_auto_launch != last_webchat_state.get('browser_auto_launch'):
                self.log_event("INFO", "WEBCHAT", f"Browser auto-launch: {browser_auto_launch}", {
                    "browser_auto_launch": browser_auto_launch
                }, service="WEB-CHAT", routine="browser_monitor")

            # Update state tracking
            self.last_webchat_state = {
                'active_connections': active_connections,
                'unique_users': unique_users,
                'total_messages': total_messages,
                'circuit_breaker_open': circuit_breaker_open,
                'deeplica_failures': deeplica_failures,
                'browser_auto_launch': browser_auto_launch
            }

        except Exception as e:
            self.log_event("ERROR", "WEBCHAT", f"Web chat specific monitoring failed", {
                "error": str(e)
            }, service="WEB-CHAT", routine="specific_monitor")

    def attempt_webchat_recovery(self):
        """🔧 Attempt to recover web chat service issues"""
        try:
            self.log_event("INFO", "WEBCHAT", "Attempting web chat recovery", {},
                         service="WEB-CHAT", routine="recovery")

            # Check dependencies
            dependencies_ok = True

            # Check CLI terminal health
            cli_service = self.services.get("cli", {})
            if cli_service.get("health_url"):
                try:
                    import httpx
                    response = httpx.get(cli_service["health_url"], timeout=3.0)
                    if response.status_code != 200:
                        dependencies_ok = False
                        self.log_event("WARNING", "WEBCHAT", "CLI Terminal dependency unhealthy", {
                            "cli_status_code": response.status_code
                        }, service="WEB-CHAT", routine="recovery")
                except Exception as e:
                    dependencies_ok = False
                    self.log_event("WARNING", "WEBCHAT", "CLI Terminal dependency unreachable", {
                        "error": str(e)
                    }, service="WEB-CHAT", routine="recovery")

            # Check backend API health
            backend_service = self.services.get("backend", {})
            if backend_service.get("health_url"):
                try:
                    import httpx
                    response = httpx.get(backend_service["health_url"], timeout=3.0)
                    if response.status_code != 200:
                        dependencies_ok = False
                        self.log_event("WARNING", "WEBCHAT", "Backend API dependency unhealthy", {
                            "backend_status_code": response.status_code
                        }, service="WEB-CHAT", routine="recovery")
                except Exception as e:
                    dependencies_ok = False
                    self.log_event("WARNING", "WEBCHAT", "Backend API dependency unreachable", {
                        "error": str(e)
                    }, service="WEB-CHAT", routine="recovery")

            if dependencies_ok:
                self.log_event("INFO", "WEBCHAT", "All dependencies healthy - web chat should recover automatically", {},
                             service="WEB-CHAT", routine="recovery")
            else:
                self.log_event("WARNING", "WEBCHAT", "Dependencies unhealthy - web chat issues may persist", {},
                             service="WEB-CHAT", routine="recovery")

        except Exception as e:
            self.log_event("ERROR", "WEBCHAT", f"Web chat recovery attempt failed", {
                "error": str(e)
            }, service="WEB-CHAT", routine="recovery")

    def monitor_system_resources(self):
        """Monitor system resource usage"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=None)
            self.system_metrics["cpu_usage"].append(cpu_percent)
            
            # Memory usage
            memory = psutil.virtual_memory()
            self.system_metrics["memory_usage"].append(memory.percent)
            
            # Disk usage
            disk = psutil.disk_usage('/')
            self.system_metrics["disk_usage"].append(disk.percent)
            
            # Log high resource usage (only if state changed)
            if cpu_percent > 80:
                if not getattr(self, 'high_cpu_warned', False):
                    self.log_event("WARNING", "SYSTEM", f"High CPU usage detected", {"cpu_percent": cpu_percent},
                                 service="SYSTEM", routine="resource_monitor")
                    self.high_cpu_warned = True
            else:
                if getattr(self, 'high_cpu_warned', False):
                    self.log_event("INFO", "SYSTEM", f"CPU usage normalized", {"cpu_percent": cpu_percent},
                                 service="SYSTEM", routine="resource_monitor")
                    self.high_cpu_warned = False

            if memory.percent > 85:
                if not getattr(self, 'high_memory_warned', False):
                    self.log_event("WARNING", "SYSTEM", f"High memory usage detected", {"memory_percent": memory.percent},
                                 service="SYSTEM", routine="resource_monitor")
                    self.high_memory_warned = True
            else:
                if getattr(self, 'high_memory_warned', False):
                    self.log_event("INFO", "SYSTEM", f"Memory usage normalized", {"memory_percent": memory.percent},
                                 service="SYSTEM", routine="resource_monitor")
                    self.high_memory_warned = False

            if disk.percent > 90:
                if not getattr(self, 'high_disk_warned', False):
                    self.log_event("WARNING", "SYSTEM", f"High disk usage detected", {"disk_percent": disk.percent},
                                 service="SYSTEM", routine="resource_monitor")
                    self.high_disk_warned = True
            else:
                if getattr(self, 'high_disk_warned', False):
                    self.log_event("INFO", "SYSTEM", f"Disk usage normalized", {"disk_percent": disk.percent},
                                 service="SYSTEM", routine="resource_monitor")
                    self.high_disk_warned = False

        except Exception as e:
            # Only log system monitoring errors once
            if not hasattr(self, 'system_monitor_error_logged'):
                self.log_event("ERROR", "MONITOR", f"System resource monitoring issue",
                             {"error": str(e)[:100]}, service="WATCHDOG", routine="system_monitor")
                self.system_monitor_error_logged = True
    
    def log_system_summary(self):
        """Log periodic system summary"""
        try:
            uptime = datetime.now() - self.start_time
            
            summary = {
                "watchdog_uptime": str(uptime),
                "active_services": len(self.service_states),
                "active_ports": len([p for p in self.last_port_states.values() if p['in_use']]),
                "total_errors": len(self.error_log),
                "total_transactions": len(self.transaction_log),
                "conversation_events": len(self.conversation_log),
                "error_conversations": len([c for c in self.conversation_log if c.get("is_error", False)]),
                "avg_cpu": sum(self.system_metrics["cpu_usage"]) / len(self.system_metrics["cpu_usage"]) if self.system_metrics["cpu_usage"] else 0,
                "avg_memory": sum(self.system_metrics["memory_usage"]) / len(self.system_metrics["memory_usage"]) if self.system_metrics["memory_usage"] else 0,
                "external_services": {
                    "twilio_status": getattr(self, 'last_twilio_status', 'unknown'),
                    "ngrok_status": getattr(self, 'last_ngrok_status', 'unknown'),
                    "database_status": getattr(self, 'last_db_status', 'unknown'),
                    "circuit_breaker_state": getattr(self, 'last_circuit_breaker_state', 'unknown')
                },
                "auto_recovery": {
                    "enabled": getattr(self, 'auto_recovery_enabled', False),
                    "stop_service_running": getattr(self, 'stop_service_running', False),
                    "total_recovery_attempts": sum(getattr(self, 'recovery_attempts', {}).values()),
                    "services_with_attempts": list(getattr(self, 'recovery_attempts', {}).keys())
                }
            }
            
            self.log_event("INFO", "SUMMARY", "System status", summary,
                         service="WATCHDOG", routine="system_summary")

        except Exception as e:
            # Only log summary errors once
            if not hasattr(self, 'summary_error_logged'):
                self.log_event("ERROR", "MONITOR", f"System summary issue",
                             {"error": str(e)[:100]}, service="WATCHDOG", routine="system_summary")
                self.summary_error_logged = True

    def monitor_terminal_activity(self):
        """Monitor terminal and VS Code activity"""
        try:
            # Look for VS Code processes and terminal activity
            vscode_processes = []
            terminal_processes = []

            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    proc_info = proc.info
                    name = proc_info.get('name', '').lower()
                    cmdline_list = proc_info.get('cmdline', []) or []

                    if isinstance(cmdline_list, list):
                        cmdline = ' '.join(str(arg) for arg in cmdline_list if arg is not None).lower()
                    else:
                        cmdline = str(cmdline_list).lower() if cmdline_list else ''

                    # Track VS Code processes
                    if 'code' in name or 'visual studio code' in name:
                        vscode_processes.append({
                            "pid": proc_info['pid'],
                            "name": proc_info['name'],
                            "cmdline": cmdline[:100]  # Truncate for logging
                        })

                    # Track terminal processes
                    if any(term in name for term in ['terminal', 'bash', 'zsh', 'sh']) and 'deeplica' in cmdline:
                        terminal_processes.append({
                            "pid": proc_info['pid'],
                            "name": proc_info['name'],
                            "cmdline": cmdline[:100]
                        })

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            # Log significant changes in terminal activity
            current_terminal_count = len(terminal_processes)
            last_terminal_count = getattr(self, 'last_terminal_count', 0)

            if current_terminal_count != last_terminal_count:
                self.log_event("INFO", "TERMINAL", f"Terminal count changed from {last_terminal_count} to {current_terminal_count}", {
                    "old_count": last_terminal_count,
                    "new_count": current_terminal_count
                }, service="VSCODE", routine="terminal_monitor")
                self.last_terminal_count = current_terminal_count

            # Log VS Code activity
            current_vscode_count = len(vscode_processes)
            last_vscode_count = getattr(self, 'last_vscode_count', 0)

            if current_vscode_count != last_vscode_count:
                self.log_event("INFO", "TERMINAL", f"VS Code processes changed from {last_vscode_count} to {current_vscode_count}", {
                    "old_count": last_vscode_count,
                    "new_count": current_vscode_count
                }, service="VSCODE", routine="process_monitor")
                self.last_vscode_count = current_vscode_count

        except Exception as e:
            # Only log terminal monitoring errors once
            if not hasattr(self, 'terminal_monitor_error_logged'):
                self.log_event("ERROR", "MONITOR", f"Terminal monitoring issue",
                             {"error": str(e)[:100]}, service="WATCHDOG", routine="terminal_monitor")
                self.terminal_monitor_error_logged = True

    def monitor_log_files(self):
        """Monitor log files for errors and important events"""
        try:
            # In a full implementation, you would use file watchers to monitor log files in real-time
            # For now, we'll just check if new log files appear
            log_patterns = [
                "backend/logs/*.log",
                "dispatcher/logs/*.log",
                "agents/*/logs/*.log",
                "*.log"
            ]

            import glob
            current_log_files = []

            for pattern in log_patterns:
                try:
                    files = glob.glob(str(PROJECT_ROOT / pattern))
                    current_log_files.extend(files)
                except Exception:
                    continue

            # Check for new log files
            last_log_files = getattr(self, 'last_log_files', set())
            current_log_files_set = set(current_log_files)

            new_files = current_log_files_set - last_log_files
            if new_files:
                # Only show first few new files to avoid spam
                displayed_files = list(new_files)[:5]  # Show max 5 files
                more_count = max(0, len(new_files) - len(displayed_files))
                files_msg = f"{len(displayed_files)} new log files" + (f" (+{more_count} more)" if more_count > 0 else "")

                self.log_event("INFO", "LOGS", files_msg, {
                    "new_files_count": len(new_files),
                    "total_log_files": len(current_log_files),
                    "sample_files": [f.split('/')[-1] for f in displayed_files]  # Just filenames
                }, service="WATCHDOG", routine="log_monitor")

            self.last_log_files = current_log_files_set

        except Exception as e:
            # Only log file monitoring errors once
            if not hasattr(self, 'log_monitor_error_logged'):
                self.log_event("ERROR", "MONITOR", f"Log file monitoring issue",
                             {"error": str(e)[:100]}, service="WATCHDOG", routine="log_monitor")
                self.log_monitor_error_logged = True

    def monitor_network_activity(self):
        """Monitor network connections for Deeplica services"""
        try:
            connections = []

            # Get all network connections - handle permission errors gracefully
            try:
                network_connections = psutil.net_connections(kind='inet')
            except psutil.AccessDenied:
                # On some systems, this requires elevated privileges
                # This is not an error, just a limitation
                if not hasattr(self, 'network_access_warned'):
                    self.log_event("INFO", "NETWORK", "Network monitoring requires elevated privileges",
                                 service="WATCHDOG", routine="network_monitor")
                    self.network_access_warned = True
                return
            except Exception as e:
                # Only log if this is a new error
                error_key = f"network_error_{type(e).__name__}"
                if not hasattr(self, error_key):
                    self.log_event("WARNING", "NETWORK", f"Network monitoring limited: {type(e).__name__}",
                                 {"error_type": type(e).__name__}, service="WATCHDOG", routine="network_monitor")
                    setattr(self, error_key, True)
                return

            # Process connections safely
            for conn in network_connections:
                try:
                    if (conn.laddr and hasattr(conn.laddr, 'port') and
                        conn.laddr.port in [get_service_port("backend"), get_service_port('dispatcher'), get_service_port('dialogue'), get_service_port('planner'), get_service_port('phone')]):

                        connection_info = {
                            "local_address": f"{conn.laddr.ip}:{conn.laddr.port}",
                            "remote_address": f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else "None",
                            "status": conn.status,
                            "pid": conn.pid if conn.pid else "Unknown"
                        }
                        connections.append(connection_info)

                except (AttributeError, psutil.AccessDenied, psutil.NoSuchProcess):
                    # These are normal - some connections may disappear or be inaccessible
                    continue
                except Exception:
                    # Ignore other connection-specific errors
                    continue

            # Log significant network activity changes
            current_conn_count = len(connections)
            last_conn_count = getattr(self, 'last_conn_count', 0)

            if current_conn_count != last_conn_count:
                self.log_event("INFO", "NETWORK", f"Connections changed from {last_conn_count} to {current_conn_count}",
                             {"old_count": last_conn_count, "new_count": current_conn_count},
                             service="WATCHDOG", routine="network_monitor")
                self.last_conn_count = current_conn_count

        except Exception as e:
            # Only log unexpected errors, and only once per error type
            error_key = f"network_unexpected_{type(e).__name__}"
            if not hasattr(self, error_key):
                self.log_event("WARNING", "NETWORK", f"Unexpected network monitoring issue",
                             {"error_type": type(e).__name__, "error": str(e)[:100]},
                             service="WATCHDOG", routine="network_monitor")
                setattr(self, error_key, True)

    def capture_service_errors(self):
        """Attempt to capture errors from running services"""
        try:
            # This would ideally integrate with service logging
            # For now, we'll monitor for process crashes and restarts

            for service_name, service_info in self.services.items():
                if service_name in self.service_states:
                    process_info = self.service_states[service_name]

                    # Check for high CPU or memory usage (potential issues)
                    cpu_percent = process_info.get('cpu_percent', 0)
                    memory_percent = process_info.get('memory_percent', 0)

                    # Only warn about high usage if it's a new issue for this service
                    if cpu_percent > 90:
                        warning_key = f"high_cpu_{service_name}"
                        if not getattr(self, warning_key, False):
                            self.log_event("WARNING", "PERFORMANCE", f"High CPU usage detected", {
                                "cpu_percent": cpu_percent,
                                "pid": process_info['pid']
                            }, service=service_name.upper(), routine="performance_monitor")
                            setattr(self, warning_key, True)
                    else:
                        warning_key = f"high_cpu_{service_name}"
                        if getattr(self, warning_key, False):
                            self.log_event("INFO", "PERFORMANCE", f"CPU usage normalized", {
                                "cpu_percent": cpu_percent
                            }, service=service_name.upper(), routine="performance_monitor")
                            setattr(self, warning_key, False)

                    if memory_percent > 80:
                        warning_key = f"high_memory_{service_name}"
                        if not getattr(self, warning_key, False):
                            self.log_event("WARNING", "PERFORMANCE", f"High memory usage detected", {
                                "memory_percent": memory_percent,
                                "pid": process_info['pid']
                            }, service=service_name.upper(), routine="performance_monitor")
                            setattr(self, warning_key, True)
                    else:
                        warning_key = f"high_memory_{service_name}"
                        if getattr(self, warning_key, False):
                            self.log_event("INFO", "PERFORMANCE", f"Memory usage normalized", {
                                "memory_percent": memory_percent
                            }, service=service_name.upper(), routine="performance_monitor")
                            setattr(self, warning_key, False)

        except Exception as e:
            # Only log performance monitoring errors once
            if not hasattr(self, 'performance_monitor_error_logged'):
                self.log_event("ERROR", "MONITOR", f"Performance monitoring issue",
                             {"error": str(e)[:100]}, service="WATCHDOG", routine="performance_monitor")
                self.performance_monitor_error_logged = True

    def monitor_twilio_status(self):
        """Monitor Twilio service status and configuration with auto-recovery"""
        try:
            # Check if Twilio credentials are configured
            twilio_configured = False
            twilio_status = "unknown"
            twilio_connection_ok = False

            # Try to get Twilio status from phone agent
            try:
                import httpx
                response = httpx.get(f"http://{{get_localhost()}}:{get_service_port('phone')}/health", timeout=3.0)
                if response.status_code == 200:
                    health_data = response.json()
                    # Look for Twilio-related information in health response
                    if "twilio" in str(health_data).lower():
                        twilio_configured = True
                        twilio_status = "configured"
                        twilio_connection_ok = True
                    else:
                        twilio_status = "not_configured"
                else:
                    twilio_status = "phone_agent_unavailable"
            except Exception:
                twilio_status = "phone_agent_unreachable"

            # Check for Twilio environment variables
            import os
            twilio_sid = os.getenv('TWILIO_ACCOUNT_SID')
            twilio_token = os.getenv('TWILIO_AUTH_TOKEN')
            twilio_phone = os.getenv('TWILIO_PHONE_NUMBER')

            if twilio_sid and twilio_token and twilio_phone:
                twilio_configured = True
                if twilio_status == "unknown":
                    twilio_status = "env_configured"

            # Test actual Twilio connection if configured
            if twilio_configured and twilio_status != "phone_agent_unreachable":
                try:
                    from twilio.rest import Client
                    client = Client(twilio_sid, twilio_token)
                    # Test connection by fetching account info
                    account = client.api.accounts(twilio_sid).fetch()
                    twilio_connection_ok = True
                    twilio_status = "connected"
                except Exception as e:
                    twilio_connection_ok = False
                    twilio_status = "connection_failed: " + str(e)[:50]

            # Log status changes
            last_twilio_status = getattr(self, 'last_twilio_status', None)
            if last_twilio_status != twilio_status:
                self.log_event("INFO", "TWILIO", "Status changed to " + str(twilio_status), {
                    "configured": twilio_configured,
                    "connection_ok": twilio_connection_ok,
                    "status": twilio_status,
                    "has_sid": bool(twilio_sid),
                    "has_token": bool(twilio_token),
                    "has_phone": bool(twilio_phone)
                }, service="TWILIO", routine="status_check")

                # Auto-recovery: If Twilio is not working, try to fix it
                if not twilio_connection_ok and twilio_configured:
                    safe_execute(self.attempt_twilio_recovery)

                # Store the current status for next comparison
                setattr(self, 'last_twilio_status', twilio_status)

        except Exception as e:
            # Only log Twilio monitoring errors once
            if not hasattr(self, 'twilio_monitor_error_logged'):
                self.log_event("WARNING", "TWILIO", "Monitoring issue",
                             {"error": str(e)[:100]}, service="WATCHDOG", routine="twilio_monitor")
                self.twilio_monitor_error_logged = True

    def monitor_ngrok_status(self):
        """Monitor ngrok tunnel status"""
        try:
            # Check if ngrok is running
            ngrok_running = False
            tunnel_url = None

            # Try to connect to ngrok API (default port get_service_port("ngrok-api"))
            try:
                import httpx
                response = httpx.get(f"http://{get_localhost()}:{get_service_port('ngrok-api')}/api/tunnels", timeout=2.0)
                if response.status_code == 200:
                    tunnels_data = response.json()
                    tunnels = tunnels_data.get('tunnels', [])
                    if tunnels:
                        ngrok_running = True
                        # Get the first HTTPS tunnel
                        for tunnel in tunnels:
                            if tunnel.get('proto') == 'https':
                                tunnel_url = tunnel.get('public_url')
                                break
                        if not tunnel_url and tunnels:
                            tunnel_url = tunnels[0].get('public_url')
                else:
                    ngrok_running = False
            except Exception:
                ngrok_running = False

            # Log status changes
            last_ngrok_status = getattr(self, 'last_ngrok_status', None)
            current_status = "running" if ngrok_running else "stopped"

            if last_ngrok_status != current_status:
                self.log_event("INFO", "NGROK", f"Status changed to {current_status}", {
                    "running": ngrok_running,
                    "tunnel_url": tunnel_url,
                    "status": current_status
                }, service="NGROK", routine="status_monitor")

                # Auto-recovery: If ngrok stopped, attempt recovery
                if current_status == "stopped":
                    safe_execute(self.attempt_ngrok_recovery)
                elif current_status == "running" and last_ngrok_status == "stopped":
                    # ngrok recovered, reset attempts
                    safe_execute(self.reset_recovery_attempts, 'ngrok')
                    # Update webhook URL in environment
                    safe_execute(self.update_webhook_url, tunnel_url)

                self.last_ngrok_status = current_status

        except Exception as e:
            # Only log ngrok monitoring errors once
            if not hasattr(self, 'ngrok_monitor_error_logged'):
                self.log_event("WARNING", "NGROK", f"Monitoring issue",
                             {"error": str(e)[:100]}, service="WATCHDOG", routine="ngrok_monitor")
                self.ngrok_monitor_error_logged = True

    def monitor_database_status(self):
        """Monitor MongoDB Atlas database connectivity with auto-recovery"""
        try:
            # Check database status through backend API
            database_status = "unknown"
            database_ready = False
            connection_details = {}

            try:
                import httpx
                response = httpx.get(f"http://{get_localhost()}:{get_service_port('backend')}/health", timeout=5.0)
                if response.status_code == 200:
                    health_data = response.json()
                    database_ready = health_data.get('database_ready', False)
                    database_status = "connected" if database_ready else "disconnected"

                    # Get additional database info if available
                    connection_details = {
                        "backend_response": health_data,
                        "response_time": "< 5s"
                    }
                else:
                    database_status = "backend_unavailable"
                    connection_details = {"backend_status_code": response.status_code}
            except Exception as e:
                database_status = "backend_unreachable"
                connection_details = {"backend_error": str(e)[:100]}

            # Additional direct MongoDB Atlas connectivity check
            if database_status == "disconnected" or database_status == "unknown":
                try:
                    # Try to check MongoDB Atlas connectivity directly
                    import os
                    mongo_uri = os.getenv('MONGODB_URI')
                    if mongo_uri and 'mongodb+srv://' in mongo_uri:
                        # This is MongoDB Atlas
                        try:
                            from pymongo import MongoClient
                            client = MongoClient(mongo_uri, serverSelectionTimeoutMS=3000)
                            # Test connection
                            client.admin.command('ping')
                            database_status = "atlas_direct_connected"
                            database_ready = True
                            connection_details["direct_atlas_test"] = "success"
                        except Exception as mongo_e:
                            database_status = "atlas_connection_failed"
                            connection_details["direct_atlas_error"] = str(mongo_e)[:100]
                    else:
                        connection_details["mongo_uri_status"] = "not_atlas_or_missing"
                except Exception as e:
                    connection_details["direct_test_error"] = str(e)[:100]

            # Log status changes
            last_db_status = getattr(self, 'last_db_status', None)
            if last_db_status != database_status:
                self.log_event("INFO", "DATABASE", f"Status changed to {database_status}", {
                    "ready": database_ready,
                    "status": database_status,
                    "details": connection_details
                }, service="MONGODB", routine="status_monitor")

                # Auto-recovery: If database is not ready, attempt recovery
                if not database_ready and database_status in ["disconnected", "atlas_connection_failed", "backend_unreachable"]:
                    safe_execute(self.attempt_database_recovery, database_status)

                self.last_db_status = database_status

        except Exception as e:
            # Only log database monitoring errors once
            if not hasattr(self, 'database_monitor_error_logged'):
                self.log_event("WARNING", "DATABASE", f"Monitoring issue",
                             {"error": str(e)[:100]}, service="WATCHDOG", routine="database_monitor")
                self.database_monitor_error_logged = True

    def monitor_stop_service_registrations(self):
        """DEPRECATED: Stop Deeplica Service has been removed from the system"""
        # This function is deprecated - stop service no longer exists
        return

    def monitor_conversation_logs(self):
        """Monitor all text communications and detect error calls"""
        try:
            # Check phone agent logs for conversation text
            self.check_phone_conversations()

            # Check service logs for text communications
            self.check_service_communications()

            # Analyze recent conversations for error patterns
            self.analyze_error_communications()

        except Exception as e:
            # Only log conversation monitoring errors once
            if not hasattr(self, 'conversation_monitor_error_logged'):
                self.log_event("WARNING", "CONVERSATION", f"Monitoring issue",
                             {"error": str(e)[:100]}, service="WATCHDOG", routine="conversation_monitor")
                self.conversation_monitor_error_logged = True

    def check_phone_conversations(self):
        """Check phone agent for conversation logs"""
        try:
            # Try to get recent phone conversations from phone agent
            import httpx
            response = httpx.get(f"http://{get_localhost()}:{get_service_port('phone')}/health", timeout=2.0)
            if response.status_code == 200:
                # Phone agent is available - could add endpoint to get recent conversations
                pass

            # Check for phone agent log files
            import glob
            phone_log_files = glob.glob("agents/phone/logs/*.log")
            for log_file in phone_log_files[-3:]:  # Check last 3 log files
                try:
                    with open(log_file, 'r') as f:
                        recent_lines = f.readlines()[-50:]  # Last 50 lines
                        for line in recent_lines:
                            if any(pattern in line.lower() for pattern in self.error_call_patterns):
                                self.log_conversation_event("PHONE", "ERROR_DETECTED", line.strip())
                except Exception:
                    continue

        except Exception as e:
            pass  # Silently handle phone conversation check errors

    def check_service_communications(self):
        """Check all service logs for text communications"""
        try:
            # Check backend API logs for error responses
            import glob

            # Check various service log patterns
            log_patterns = [
                "backend/logs/*.log",
                "dispatcher/logs/*.log",
                "agents/*/logs/*.log"
            ]

            for pattern in log_patterns:
                try:
                    log_files = glob.glob(pattern)
                    for log_file in log_files[-2:]:  # Check last 2 files per service
                        try:
                            with open(log_file, 'r') as f:
                                recent_lines = f.readlines()[-20:]  # Last 20 lines
                                for line in recent_lines:
                                    line_stripped = line.strip()
                                    # Create a hash of the line to avoid processing duplicates
                                    line_hash = hash(line_stripped)

                                    # Skip if we've already processed this line
                                    if line_hash in self.processed_log_lines:
                                        continue

                                    # Look for error communications
                                    if any(pattern in line.lower() for pattern in self.error_call_patterns):
                                        service_name = self.extract_service_name(log_file)
                                        self.log_conversation_event(service_name, "ERROR_COMMUNICATION", line_stripped)

                                        # Mark this line as processed
                                        self.processed_log_lines.add(line_hash)

                                        # Limit the size of processed lines set
                                        if len(self.processed_log_lines) > 10000:
                                            # Remove oldest half
                                            self.processed_log_lines = set(list(self.processed_log_lines)[5000:])
                        except Exception:
                            continue
                except Exception:
                    continue

        except Exception:
            pass  # Silently handle service communication check errors

    def extract_service_name(self, log_file_path):
        """Extract service name from log file path"""
        try:
            if "backend" in log_file_path:
                return "BACKEND"
            elif "dispatcher" in log_file_path:
                return "DISPATCHER"
            elif "phone" in log_file_path:
                return "PHONE"
            elif "dialogue" in log_file_path:
                return "DIALOGUE"
            elif "planner" in log_file_path:
                return "PLANNER"
            else:
                return "UNKNOWN"
        except:
            return "UNKNOWN"

    def log_conversation_event(self, service_name, event_type, content):
        """Log conversation events with error detection"""
        try:
            # Add to conversation log
            conversation_entry = {
                "timestamp": datetime.now().isoformat(),
                "service": service_name,
                "event_type": event_type,
                "content": content[:200],  # Limit content length
                "is_error": event_type == "ERROR_DETECTED" or event_type == "ERROR_COMMUNICATION"
            }
            self.conversation_log.append(conversation_entry)

            # Log critical error communications (with throttling to prevent spam)
            if conversation_entry["is_error"]:
                # Throttle error messages - only log once per minute per service
                current_time = time.time()
                throttle_key = f"error_detection_{service_name}"
                last_error_time = getattr(self, 'last_error_log_times', {}).get(throttle_key, 0)

                if current_time - last_error_time > 60:  # 60 seconds throttle
                    if not hasattr(self, 'last_error_log_times'):
                        self.last_error_log_times = {}
                    self.last_error_log_times[throttle_key] = current_time

                    self.log_event("WARNING", "CONVERSATION", f"Error communication detected in {service_name}", {
                        "service": service_name,
                        "event_type": event_type,
                        "content_preview": content[:100]
                    }, service=service_name, routine="error_detection")

        except Exception:
            pass  # Silently handle conversation logging errors

    def normalize_message_for_deduplication(self, message):
        """🔍 Normalize message by removing counters, timestamps, and variable data"""
        try:
            import re

            # Convert to lowercase for comparison
            normalized = message.lower()

            # Remove common variable patterns
            patterns_to_remove = [
                r'\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}[.\d]*',  # Timestamps
                r'\d{2}:\d{2}:\d{2}',                              # Time only
                r'pid=\d+',                                        # Process IDs
                r'port=\d+',                                       # Port numbers
                r'attempt=\d+',                                    # Attempt counters
                r'count=\d+',                                      # General counters
                r'#\d+',                                           # Hash numbers
                r'from \d+ to \d+',                               # Range changes
                r'cpu_percent=[\d.]+',                            # CPU percentages
                r'memory_percent=[\d.]+',                         # Memory percentages
                r'\(\+\d+ more\)',                               # "+X more" patterns
                r'[\da-f]{12}',  # UUIDs
                r'[\da-f]{8}\.\.\.?',                             # Shortened IDs
                r'\d+\.\d+\.\d+\.\d+',                           # IP addresses
                r'error: [^|]+',                                  # Error details (keep category)
            ]

            # Apply all patterns
            for pattern in patterns_to_remove:
                normalized = re.sub(pattern, '[VAR]', normalized)

            # Remove extra whitespace and normalize separators
            normalized = re.sub(r'\s+', ' ', normalized)
            normalized = re.sub(r'\s*\|\s*', '|', normalized)
            normalized = normalized.strip()

            return normalized

        except Exception:
            # If normalization fails, return original message
            return message.lower()

    def is_message_duplicate(self, message, service, routine, category):
        """🔍 Check if message is a duplicate, ignoring variable data"""
        try:
            # Create a message signature including context
            message_signature = f"{service}:{routine}:{category}:{self.normalize_message_for_deduplication(message)}"

            # Check if we've seen this normalized message before
            if message_signature in self.normalized_messages:
                return True

            # Add to tracking set
            self.normalized_messages.add(message_signature)

            # Limit the size of normalized messages set
            if len(self.normalized_messages) > 5000:
                # Remove oldest half
                self.normalized_messages = set(list(self.normalized_messages)[2500:])

            return False

        except Exception:
            # If duplicate checking fails, allow the message
            return False

    def analyze_error_communications(self):
        """Analyze recent conversations for patterns"""
        try:
            # Count recent error communications
            recent_errors = [entry for entry in self.conversation_log
                           if entry.get("is_error", False) and
                           (datetime.now() - datetime.fromisoformat(entry["timestamp"])).seconds < 300]

            if len(recent_errors) > 3:  # More than 3 error communications in 5 minutes
                self.log_event("CRITICAL", "CONVERSATION", f"High error communication rate detected", {
                    "error_count": len(recent_errors),
                    "time_window": "5_minutes",
                    "services": list(set(entry["service"] for entry in recent_errors))
                }, service="WATCHDOG", routine="error_analysis")

        except Exception:
            pass  # Silently handle error analysis

    def monitor_active_phone_calls(self):
        """Monitor active phone calls for error messages being spoken"""
        try:
            # Check if phone agent has active calls
            import httpx
            response = httpx.get(f"http://{get_localhost()}:{get_service_port('phone')}/health", timeout=2.0)
            if response.status_code == 200:
                health_data = response.json()

                # Look for active call information
                if "active_calls" in health_data or "calls" in health_data:
                    self.log_event("INFO", "PHONE_CALLS", "Active calls detected", {
                        "health_data": str(health_data)[:200]
                    }, service="PHONE", routine="call_monitor")

                # Check for circuit breaker status
                circuit_breaker = health_data.get("circuit_breaker", {})
                if circuit_breaker:
                    cb_state = circuit_breaker.get("state", "unknown")
                    last_cb_state = getattr(self, 'last_circuit_breaker_state', None)

                    if last_cb_state != cb_state:
                        self.log_event("INFO", "PHONE_CALLS", f"Circuit breaker state changed to {cb_state}", {
                            "state": cb_state,
                            "can_execute": circuit_breaker.get("can_execute", False),
                            "failure_count": circuit_breaker.get("failure_count", 0)
                        }, service="PHONE", routine="circuit_breaker_monitor")
                        self.last_circuit_breaker_state = cb_state

                        # Alert if circuit breaker opens (calls being blocked)
                        if cb_state == "OPEN":
                            self.log_event("WARNING", "PHONE_CALLS", "Circuit breaker OPEN - calls blocked", {
                                "state": cb_state,
                                "remaining_time": circuit_breaker.get("remaining_recovery_time", 0)
                            }, service="PHONE", routine="circuit_breaker_alert")

        except Exception as e:
            # Only log phone call monitoring errors once
            if not hasattr(self, 'phone_call_monitor_error_logged'):
                self.log_event("WARNING", "PHONE_CALLS", f"Call monitoring issue",
                             {"error": str(e)[:100]}, service="WATCHDOG", routine="phone_call_monitor")
                self.phone_call_monitor_error_logged = True

    def check_for_error_calls(self):
        """Specifically check if the system is making calls to report errors"""
        try:
            # Check recent phone agent activity for error-related calls
            import httpx

            # Try to get recent call history or status
            try:
                response = httpx.get(f"http://{get_localhost()}:{get_service_port('phone')}/health", timeout=2.0)
                if response.status_code == 200:
                    health_data = response.json()

                    # Check if there are any error-related messages in the health data
                    health_str = str(health_data).lower()
                    if any(pattern in health_str for pattern in self.error_call_patterns):
                        self.log_event("CRITICAL", "ERROR_CALLS", "Potential error call detected in phone agent", {
                            "health_data_preview": str(health_data)[:300]
                        }, service="PHONE", routine="error_call_detection")

            except Exception:
                pass

            # Check for recent tasks that might be error calls
            try:
                # Check backend for recent phone tasks
                response = httpx.get(f"http://{get_localhost()}:{get_service_port('backend')}/health", timeout=2.0)
                if response.status_code == 200:
                    # Could add endpoint to check recent phone tasks
                    pass
            except Exception:
                pass

        except Exception:
            pass  # Silently handle error call checking

    def check_stop_service_status(self):
        """🛑 Check if STOP DEEPLICA service is running"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    proc_info = proc.info
                    cmdline_list = proc_info.get('cmdline', []) or []

                    if isinstance(cmdline_list, list):
                        cmdline = ' '.join(str(arg) for arg in cmdline_list if arg is not None).lower()
                    else:
                        cmdline = str(cmdline_list).lower() if cmdline_list else ''

                    # Check for STOP service
                    if 'deeplica-stop' in cmdline or 'stop_deeplica' in cmdline:
                        if not self.stop_service_running:
                            self.log_event("WARNING", "AUTO_RECOVERY", "STOP DEEPLICA detected - disabling auto-recovery", {
                                "pid": proc_info['pid']
                            }, service="STOP_SERVICE", routine="detection")
                            self.stop_service_running = True
                            self.auto_recovery_enabled = False
                        return True

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            # If we get here, STOP service is not running
            if self.stop_service_running:
                self.log_event("INFO", "AUTO_RECOVERY", "STOP DEEPLICA no longer running - enabling auto-recovery", {},
                             service="WATCHDOG", routine="recovery_enabler")
                self.stop_service_running = False
                self.auto_recovery_enabled = True

            return False

        except Exception as e:
            # Don't let stop service checking crash us
            pass
        return False

    def attempt_service_recovery(self, service_name):
        """🔧 Attempt to restart a crashed service"""
        if not self.auto_recovery_enabled or self.stop_service_running:
            return False

        try:
            # First check if service is actually down before attempting recovery
            if self._is_service_healthy(service_name):
                self.log_event("INFO", "AUTO_RECOVERY", f"Service {service_name} is healthy, skipping recovery", {},
                             service=service_name.upper(), routine="health_check_passed")
                return True

            current_time = time.time()

            # Check recovery cooldown
            last_recovery = self.last_recovery_time.get(service_name, 0)
            if current_time - last_recovery < self.recovery_cooldown:
                return False

            # Check max attempts
            attempts = self.recovery_attempts.get(service_name, 0)
            if attempts >= self.max_recovery_attempts:
                return False

            # Get service command
            command = self.service_commands.get(service_name)
            if not command:
                return False

            self.log_event("WARNING", "AUTO_RECOVERY", f"Attempting to restart {service_name}", {
                "command": command,
                "attempt": attempts + 1,
                "max_attempts": self.max_recovery_attempts
            }, service=service_name.upper(), routine="auto_restart")

            # Start the service in background with proper working directory
            try:
                import subprocess

                # Get the proper working directory for the service
                service_dir = self.service_directories.get(service_name, ".")
                working_dir = str(PROJECT_ROOT / service_dir)

                self.log_event("INFO", "AUTO_RECOVERY", f"Starting {service_name} in directory: {working_dir}", {
                    "command": command,
                    "working_dir": working_dir
                }, service=service_name.upper(), routine="directory_setup")

                process = subprocess.Popen(
                    command.split(),
                    cwd=working_dir,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                    start_new_session=True  # Detach from parent
                )

                # Update recovery tracking
                self.recovery_attempts[service_name] = attempts + 1
                self.last_recovery_time[service_name] = current_time

                self.log_event("INFO", "AUTO_RECOVERY", f"Started {service_name} recovery process", {
                    "pid": process.pid,
                    "command": command,
                    "attempt": attempts + 1
                }, service=service_name.upper(), routine="restart_success")

                return True

            except Exception as e:
                self.log_event("ERROR", "AUTO_RECOVERY", f"Failed to restart {service_name}", {
                    "error": str(e),
                    "command": command
                }, service=service_name.upper(), routine="restart_failure")
                return False

        except Exception as e:
            # Don't let recovery attempts crash the watchdog
            emergency_log(f"🚨 Recovery attempt failed for {service_name}: {e}")
            return False

    def attempt_ngrok_recovery(self):
        """🌐 Attempt to restart ngrok tunnel with comprehensive recovery"""
        if not self.auto_recovery_enabled or self.stop_service_running:
            return False

        try:
            current_time = time.time()
            last_recovery = self.last_recovery_time.get('ngrok', 0)

            if current_time - last_recovery < self.recovery_cooldown:
                return False

            attempts = self.recovery_attempts.get('ngrok', 0)
            if attempts >= self.max_recovery_attempts:
                self.log_event("ERROR", "AUTO_RECOVERY", "Max ngrok recovery attempts reached", {
                    "max_attempts": self.max_recovery_attempts
                }, service="NGROK", routine="max_attempts_reached")
                return False

            self.log_event("WARNING", "AUTO_RECOVERY", "Attempting comprehensive ngrok recovery", {
                "attempt": attempts + 1,
                "max_attempts": self.max_recovery_attempts
            }, service="NGROK", routine="auto_restart")

            # Step 1: Kill all existing ngrok processes
            try:
                subprocess.run(["pkill", "-f", "ngrok"], capture_output=True, timeout=10)
                time.sleep(2)
                self.log_event("INFO", "AUTO_RECOVERY", "Killed existing ngrok processes", {},
                             service="NGROK", routine="cleanup")
            except Exception as e:
                self.log_event("WARNING", "AUTO_RECOVERY", f"Failed to kill ngrok processes: {e}", {},
                             service="NGROK", routine="cleanup_failed")

            # Step 2: Check if phone agent is running (ngrok target)
            phone_running = self.check_service_health("localhost", get_service_port("phone"))
            if not phone_running:
                self.log_event("WARNING", "AUTO_RECOVERY", "Phone agent not running, attempting to start", {},
                             service="NGROK", routine="phone_check")
                # Try to restart phone agent first
                phone_recovery = self.attempt_service_recovery("phone")
                if phone_recovery:
                    time.sleep(5)  # Wait for phone agent to be ready

            # Step 3: Try to start ngrok with multiple strategies
            ngrok_strategies = [
                {
                    "command": f"ngrok http {get_service_port('phone')} --log=stdout",
                    "description": "Standard ngrok with logging"
                },
                {
                    "command": f"/usr/local/bin/ngrok http {get_service_port('phone')} --log=stdout",
                    "description": "Full path ngrok with logging"
                },
                {
                    "command": f"ngrok http localhost:{get_service_port('phone')} --log=stdout",
                    "description": "Explicit localhost ngrok"
                },
                {
                    "command": f"ngrok http {get_service_port('phone')} --region=us --log=stdout",
                    "description": "US region ngrok"
                }
            ]

            for strategy in ngrok_strategies:
                try:
                    self.log_event("INFO", "AUTO_RECOVERY", f"Trying ngrok strategy: {strategy['description']}", {
                        "command": strategy["command"]
                    }, service="NGROK", routine="strategy_attempt")

                    process = subprocess.Popen(
                        strategy["command"].split(),
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        start_new_session=True,
                        text=True
                    )

                    # Wait and check if ngrok starts successfully
                    time.sleep(5)
                    if process.poll() is None:  # Still running
                        # Try to get ngrok URL
                        ngrok_url = self.get_ngrok_url()
                        if ngrok_url:
                            self.recovery_attempts['ngrok'] = attempts + 1
                            self.last_recovery_time['ngrok'] = current_time

                            self.log_event("INFO", "AUTO_RECOVERY", "ngrok tunnel successfully restarted", {
                                "pid": process.pid,
                                "command": strategy["command"],
                                "url": ngrok_url,
                                "strategy": strategy["description"]
                            }, service="NGROK", routine="restart_success")

                            # Update phone agent with new webhook URL
                            self.update_phone_webhook_url(ngrok_url)
                            return True
                        else:
                            # ngrok started but no URL yet, wait a bit more
                            time.sleep(3)
                            ngrok_url = self.get_ngrok_url()
                            if ngrok_url:
                                self.recovery_attempts['ngrok'] = attempts + 1
                                self.last_recovery_time['ngrok'] = current_time
                                self.update_phone_webhook_url(ngrok_url)
                                return True

                    # If we get here, this strategy failed
                    if process.poll() is not None:
                        stdout, stderr = process.communicate()
                        self.log_event("WARNING", "AUTO_RECOVERY", f"ngrok strategy failed: {strategy['description']}", {
                            "exit_code": process.returncode,
                            "stdout": stdout[:200] if stdout else "",
                            "stderr": stderr[:200] if stderr else ""
                        }, service="NGROK", routine="strategy_failed")

                except Exception as e:
                    self.log_event("ERROR", "AUTO_RECOVERY", f"ngrok strategy error: {strategy['description']}", {
                        "error": str(e)
                    }, service="NGROK", routine="strategy_error")
                    continue

            # If all strategies failed
            self.recovery_attempts['ngrok'] = attempts + 1
            self.last_recovery_time['ngrok'] = current_time

            self.log_event("ERROR", "AUTO_RECOVERY", "All ngrok recovery strategies failed", {
                "attempted_strategies": len(ngrok_strategies),
                "total_attempts": attempts + 1
            }, service="NGROK", routine="all_strategies_failed")
            return False

        except Exception as e:
            emergency_log(f"🚨 ngrok recovery attempt failed: {e}")
            return False

    def get_ngrok_url(self):
        """Get the current ngrok public URL"""
        try:
            import requests
            response = requests.get(f"http://localhost:{get_service_port('ngrok-api')}/api/tunnels", timeout=5)
            if response.status_code == 200:
                data = response.json()
                tunnels = data.get("tunnels", [])
                for tunnel in tunnels:
                    if tunnel.get("proto") == "https":
                        return tunnel.get("public_url")
            return None
        except Exception:
            return None

    def update_phone_webhook_url(self, ngrok_url):
        """Update phone agent with new webhook URL"""
        try:
            import requests
            response = requests.post(f"http://localhost:{get_service_port('phone')}/update_webhook",
                                   json={"webhook_url": ngrok_url},
                                   timeout=10)
            if response.status_code == 200:
                self.log_event("INFO", "AUTO_RECOVERY", "Phone agent webhook URL updated", {
                    "new_url": ngrok_url
                }, service="NGROK", routine="webhook_update")
            else:
                self.log_event("WARNING", "AUTO_RECOVERY", "Failed to update phone webhook URL", {
                    "status_code": response.status_code,
                    "url": ngrok_url
                }, service="NGROK", routine="webhook_update_failed")
        except Exception as e:
            self.log_event("ERROR", "AUTO_RECOVERY", f"Error updating phone webhook: {e}", {
                "url": ngrok_url
            }, service="NGROK", routine="webhook_update_error")

    # REMOVED: Duplicate attempt_database_recovery method - using the one with failure_reason parameter

    def attempt_twilio_recovery(self):
        """📞 Attempt to fix Twilio connection issues"""
        if not self.auto_recovery_enabled or self.stop_service_running:
            return False

        try:
            current_time = time.time()
            last_recovery = self.last_recovery_time.get('twilio', 0)

            if current_time - last_recovery < self.recovery_cooldown:
                return False

            attempts = self.recovery_attempts.get('twilio', 0)
            if attempts >= self.max_recovery_attempts:
                self.log_event("ERROR", "AUTO_RECOVERY", "Max Twilio recovery attempts reached", {
                    "max_attempts": self.max_recovery_attempts
                }, service="TWILIO", routine="max_attempts_reached")
                return False

            self.log_event("WARNING", "AUTO_RECOVERY", "Attempting Twilio service recovery", {
                "attempt": attempts + 1
            }, service="TWILIO", routine="auto_recovery")

            # Step 1: Check if phone agent is running
            phone_running = self.check_service_health("localhost", get_service_port("phone"))
            if not phone_running:
                self.log_event("INFO", "AUTO_RECOVERY", "Phone agent not running, attempting restart", {},
                             service="TWILIO", routine="phone_restart")
                phone_recovery = self.attempt_service_recovery("phone")
                if not phone_recovery:
                    self.log_event("ERROR", "AUTO_RECOVERY", "Failed to restart phone agent", {},
                                 service="TWILIO", routine="phone_restart_failed")
                    return False
                time.sleep(5)  # Wait for phone agent to be ready

            # Step 2: Test Twilio connection via phone agent
            try:
                import requests
                response = requests.post(f"http://localhost:{get_service_port('phone')}/test_twilio", timeout=15)
                if response.status_code == 200:
                    test_result = response.json()
                    if test_result.get("status") == "success":
                        self.recovery_attempts['twilio'] = attempts + 1
                        self.last_recovery_time['twilio'] = current_time

                        self.log_event("INFO", "AUTO_RECOVERY", "Twilio connection test successful", {
                            "test_result": test_result
                        }, service="TWILIO", routine="test_success")
                        return True
                    else:
                        self.log_event("WARNING", "AUTO_RECOVERY", "Twilio connection test failed", {
                            "test_result": test_result
                        }, service="TWILIO", routine="test_failed")
                else:
                    self.log_event("WARNING", "AUTO_RECOVERY", "Twilio test endpoint error", {
                        "status_code": response.status_code
                    }, service="TWILIO", routine="test_endpoint_error")
            except Exception as e:
                self.log_event("ERROR", "AUTO_RECOVERY", f"Twilio test request failed: {e}", {},
                             service="TWILIO", routine="test_request_failed")

            # Step 3: Try to restart phone agent with fresh configuration
            self.log_event("INFO", "AUTO_RECOVERY", "Restarting phone agent for Twilio recovery", {},
                         service="TWILIO", routine="phone_restart_recovery")

            # Kill existing phone agent
            try:
                subprocess.run(["pkill", "-f", "agents/phone"], capture_output=True, timeout=10)
                time.sleep(2)
            except Exception:
                pass

            # Restart phone agent
            phone_recovery = self.attempt_service_recovery("phone")
            if phone_recovery:
                time.sleep(8)  # Wait for phone agent to initialize Twilio

                # Test again
                try:
                    response = requests.post(f"http://localhost:{get_service_port('phone')}/test_twilio", timeout=15)
                    if response.status_code == 200:
                        test_result = response.json()
                        if test_result.get("status") == "success":
                            self.recovery_attempts['twilio'] = attempts + 1
                            self.last_recovery_time['twilio'] = current_time

                            self.log_event("INFO", "AUTO_RECOVERY", "Twilio recovery via phone restart successful", {
                                "test_result": test_result
                            }, service="TWILIO", routine="restart_recovery_success")
                            return True
                except Exception as e:
                    self.log_event("ERROR", "AUTO_RECOVERY", f"Twilio test after restart failed: {e}", {},
                                 service="TWILIO", routine="test_after_restart_failed")

            # If we get here, all recovery attempts failed
            self.recovery_attempts['twilio'] = attempts + 1
            self.last_recovery_time['twilio'] = current_time

            self.log_event("ERROR", "AUTO_RECOVERY", "All Twilio recovery attempts failed", {
                "total_attempts": attempts + 1
            }, service="TWILIO", routine="all_attempts_failed")
            return False

        except Exception as e:
            emergency_log(f"🚨 Twilio recovery attempt failed: {e}")
            return False

    def attempt_gemini_recovery(self):
        """🤖 Attempt to fix Gemini API connection issues"""
        if not self.auto_recovery_enabled or self.stop_service_running:
            return False

        try:
            current_time = time.time()
            last_recovery = self.last_recovery_time.get('gemini', 0)

            if current_time - last_recovery < self.recovery_cooldown:
                return False

            attempts = self.recovery_attempts.get('gemini', 0)
            if attempts >= self.max_recovery_attempts:
                return False

            self.log_event("WARNING", "AUTO_RECOVERY", "Attempting Gemini API recovery", {
                "attempt": attempts + 1
            }, service="GEMINI", routine="auto_recovery")

            # Test Gemini API via backend
            try:
                import requests
                response = requests.post(f"http://localhost:{get_service_port('backend')}/gemini/test", timeout=20)
                if response.status_code == 200:
                    test_result = response.json()
                    if test_result.get("status") == "success":
                        self.recovery_attempts['gemini'] = attempts + 1
                        self.last_recovery_time['gemini'] = current_time

                        self.log_event("INFO", "AUTO_RECOVERY", "Gemini API test successful", {
                            "test_result": test_result
                        }, service="GEMINI", routine="test_success")
                        return True
                    else:
                        self.log_event("WARNING", "AUTO_RECOVERY", "Gemini API test failed", {
                            "test_result": test_result
                        }, service="GEMINI", routine="test_failed")

                        # Try restarting dialogue and planner agents that use Gemini
                        dialogue_recovery = self.attempt_service_recovery("dialogue")
                        planner_recovery = self.attempt_service_recovery("planner")

                        if dialogue_recovery or planner_recovery:
                            self.recovery_attempts['gemini'] = attempts + 1
                            self.last_recovery_time['gemini'] = current_time
                            return True

            except Exception as e:
                self.log_event("ERROR", "AUTO_RECOVERY", f"Gemini test failed: {e}", {},
                             service="GEMINI", routine="test_error")

            return False

        except Exception as e:
            emergency_log(f"🚨 Gemini recovery attempt failed: {e}")
            return False

    def monitor_external_services_enhanced(self):
        """🔍 Enhanced monitoring of all external services with auto-recovery"""
        try:
            for service_name, service_config in self.external_services.items():
                try:
                    # Get the check method
                    check_method = getattr(self, service_config["check_method"], None)
                    if not check_method:
                        continue

                    # Perform health check
                    is_healthy = check_method()
                    current_status = "healthy" if is_healthy else "unhealthy"

                    # Track status changes
                    previous_status = service_config.get("last_status", "unknown")
                    service_config["last_status"] = current_status

                    if current_status != previous_status:
                        self.log_event("INFO", "EXTERNAL_MONITOR", f"{service_name} status changed", {
                            "previous_status": previous_status,
                            "current_status": current_status,
                            "service": service_name
                        }, service=service_name.upper(), routine="status_change")

                    # Handle unhealthy services
                    if not is_healthy:
                        service_config["failure_count"] = service_config.get("failure_count", 0) + 1

                        # Log the failure
                        self.log_event("WARNING", "EXTERNAL_MONITOR", f"{service_name} health check failed", {
                            "failure_count": service_config["failure_count"],
                            "consecutive_failures": service_config["failure_count"],
                            "is_critical": service_config.get("critical", False)
                        }, service=service_name.upper(), routine="health_check_failed")

                        # Attempt recovery for critical services after 2 consecutive failures
                        if (service_config.get("critical", False) and
                            service_config["failure_count"] >= 2):

                            recovery_method = getattr(self, service_config["recovery_method"], None)
                            if recovery_method:
                                self.log_event("WARNING", "AUTO_RECOVERY", f"Attempting recovery for critical service: {service_name}", {
                                    "failure_count": service_config["failure_count"],
                                    "recovery_method": service_config["recovery_method"]
                                }, service=service_name.upper(), routine="auto_recovery_triggered")

                                recovery_success = recovery_method()
                                if recovery_success:
                                    service_config["failure_count"] = 0  # Reset failure count on successful recovery
                                    self.log_event("INFO", "AUTO_RECOVERY", f"Recovery successful for {service_name}", {
                                        "recovery_method": service_config["recovery_method"]
                                    }, service=service_name.upper(), routine="recovery_success")
                                else:
                                    self.log_event("ERROR", "AUTO_RECOVERY", f"Recovery failed for {service_name}", {
                                        "failure_count": service_config["failure_count"],
                                        "recovery_method": service_config["recovery_method"]
                                    }, service=service_name.upper(), routine="recovery_failed")
                    else:
                        # Reset failure count on successful health check
                        if service_config.get("failure_count", 0) > 0:
                            self.log_event("INFO", "EXTERNAL_MONITOR", f"{service_name} recovered", {
                                "previous_failure_count": service_config["failure_count"]
                            }, service=service_name.upper(), routine="service_recovered")
                        service_config["failure_count"] = 0

                except Exception as e:
                    self.log_event("ERROR", "EXTERNAL_MONITOR", f"Error monitoring {service_name}", {
                        "error": str(e)
                    }, service=service_name.upper(), routine="monitor_error")

        except Exception as e:
            emergency_log(f"🚨 External service monitoring failed: {e}")

    def monitor_service_crashes(self):
        """🔍 Enhanced service crash detection and recovery"""
        try:
            current_time = time.time()

            for service_name in self.service_commands.keys():
                try:
                    # Check if service is supposed to be running
                    service_info = self.services.get(service_name, {})
                    if not service_info:
                        continue

                    # Check if service is actually running
                    is_running = self.check_service_health("localhost", service_info.get("port", 0))

                    if not is_running:
                        # Service is not running - potential crash
                        crash_count = self.service_crash_history.get(service_name, 0) + 1
                        self.service_crash_history[service_name] = crash_count

                        self.log_event("ERROR", "CRASH_DETECTION", f"Service crash detected: {service_name}", {
                            "crash_count": crash_count,
                            "service_port": service_info.get("port", 0),
                            "last_seen": self.service_last_seen.get(service_name, "unknown")
                        }, service=service_name.upper(), routine="crash_detected")

                        # Attempt auto-recovery for critical services
                        if crash_count <= 3:  # Don't keep trying forever
                            self.log_event("WARNING", "AUTO_RECOVERY", f"Attempting crash recovery for {service_name}", {
                                "crash_count": crash_count,
                                "max_attempts": 3
                            }, service=service_name.upper(), routine="crash_recovery")

                            recovery_success = self.attempt_service_recovery(service_name)
                            if recovery_success:
                                self.service_crash_history[service_name] = 0  # Reset on successful recovery
                                self.log_event("INFO", "AUTO_RECOVERY", f"Crash recovery successful for {service_name}", {
                                    "previous_crash_count": crash_count
                                }, service=service_name.upper(), routine="crash_recovery_success")
                            else:
                                self.log_event("ERROR", "AUTO_RECOVERY", f"Crash recovery failed for {service_name}", {
                                    "crash_count": crash_count
                                }, service=service_name.upper(), routine="crash_recovery_failed")
                    else:
                        # Service is running - update last seen and reset crash count
                        self.service_last_seen[service_name] = current_time
                        if self.service_crash_history.get(service_name, 0) > 0:
                            self.log_event("INFO", "CRASH_DETECTION", f"Service {service_name} recovered from crash", {
                                "previous_crash_count": self.service_crash_history[service_name]
                            }, service=service_name.upper(), routine="crash_recovery_confirmed")
                        self.service_crash_history[service_name] = 0

                except Exception as e:
                    self.log_event("ERROR", "CRASH_DETECTION", f"Error checking {service_name} for crashes", {
                        "error": str(e)
                    }, service=service_name.upper(), routine="crash_check_error")

        except Exception as e:
            emergency_log(f"🚨 Service crash monitoring failed: {e}")

    def fix_port_conflicts(self):
        """🔧 Fix port conflicts and ensure services use correct ports"""
        try:
            from shared.port_manager import port_manager

            self.log_event("INFO", "PORT_MANAGEMENT", "Checking for port conflicts", {},
                         service="WATCHDOG", routine="fix_port_conflicts")

            # Get all official port assignments
            official_ports = port_manager.get_all_ports()
            conflicts_found = False

            for service_name, expected_port in official_ports.items():
                try:
                    # Check if port is free or used by correct service
                    if not port_manager.is_port_free(expected_port):
                        # Port is in use, check if it's the correct service
                        current_service = self.identify_service_on_port(expected_port)

                        # Normalize service names for comparison
                        expected_service = service_name.lower().replace('-', '_')
                        current_service_normalized = current_service.lower().replace('-', '_')

                        # Special case: Don't flag watchdog's own port as a conflict
                        if service_name == "WATCHDOG" and (
                            current_service_normalized == "watchdog" or
                            "watchdog" in current_service_normalized or
                            current_service_normalized.startswith("unknown_") or
                            current_service_normalized.startswith("error_")
                        ):
                            continue  # Skip - this is the watchdog's own port or unidentifiable process

                        if current_service_normalized != expected_service:
                            # DISABLED: Port conflict resolution is too aggressive and kills legitimate services
                            # Just log the observation without taking destructive action
                            self.log_event("INFO", "PORT_OBSERVATION", f"Port {expected_port} in use by different service", {
                                "expected_service": service_name,
                                "current_service": current_service,
                                "port": expected_port,
                                "action": "monitoring_only"
                            }, service="WATCHDOG", routine="port_observation")

                            # DO NOT resolve conflicts automatically - services manage their own ports
                            # conflicts_found = True  # DISABLED
                            # self.resolve_port_conflict(...)  # DISABLED

                except Exception as e:
                    self.log_event("ERROR", "PORT_MANAGEMENT", f"Error checking port {expected_port} for {service_name}", {
                        "error": str(e)
                    }, service="WATCHDOG", routine="port_check_error")

            if not conflicts_found:
                self.log_event("INFO", "PORT_MANAGEMENT", "No port conflicts detected", {
                    "total_ports_checked": len(official_ports)
                }, service="WATCHDOG", routine="no_conflicts")

        except Exception as e:
            emergency_log(f"🚨 Port conflict checking failed: {e}")

    def check_service_health(self, host, port):
        """Check if a service is healthy by attempting to connect to its port"""
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex((host, port))
            sock.close()
            return result == 0
        except Exception:
            return False

    def identify_service_on_port(self, port):
        """Identify which service is running on a specific port"""
        try:
            import psutil

            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'connections']):
                try:
                    connections = proc.info.get('connections', [])
                    for conn in connections:
                        if hasattr(conn, 'laddr') and conn.laddr and conn.laddr.port == port:
                            cmdline = ' '.join(proc.info.get('cmdline', []))

                            # Identify service by command line (case insensitive)
                            cmdline_lower = cmdline.lower()
                            if 'backend' in cmdline_lower:
                                return 'backend'
                            elif 'dispatcher' in cmdline_lower:
                                return 'dispatcher'
                            elif 'dialogue' in cmdline_lower:
                                return 'dialogue'
                            elif 'planner' in cmdline_lower:
                                return 'planner'
                            elif 'phone' in cmdline_lower:
                                return 'phone'
                            elif 'watchdog' in cmdline_lower or 'deeplica-watchdog' in cmdline_lower:
                                return 'watchdog'
                            elif 'web_chat' in cmdline_lower or 'webchat' in cmdline_lower:
                                return 'web-chat'
                            elif 'cli' in cmdline_lower:
                                return 'cli'
                            else:
                                return f"unknown_{proc.info.get('name', 'process')}"

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            return "unknown"

        except Exception as e:
            return f"error_{str(e)[:20]}"

    def resolve_port_conflict(self, expected_service, port, current_service):
        """Resolve a port conflict by stopping the wrong service and starting the correct one"""
        try:
            self.log_event("WARNING", "PORT_RESOLUTION", f"Resolving port conflict on {port}", {
                "expected_service": expected_service,
                "current_service": current_service
            }, service="WATCHDOG", routine="resolve_conflict")

            # Kill the process using the wrong port
            from shared.port_manager import port_manager
            if port_manager.kill_process_on_port(port, force=True):
                time.sleep(2)  # Wait for port to be released

                # Start the correct service if it's in our service commands
                service_key = expected_service.lower().replace('-', '_')
                if service_key in self.service_commands:
                    return self.attempt_service_recovery(service_key)
                else:
                    self.log_event("WARNING", "PORT_RESOLUTION", f"No recovery command for {expected_service}", {
                        "service": expected_service
                    }, service="WATCHDOG", routine="no_recovery_command")
                    return True  # Port was cleared at least

            return False

        except Exception as e:
            self.log_event("ERROR", "PORT_RESOLUTION", f"Error resolving port conflict: {e}", {
                "port": port,
                "expected_service": expected_service
            }, service="WATCHDOG", routine="resolution_error")
            return False

    def ensure_service_communication(self):
        """🔗 Ensure all services can communicate with each other"""
        try:
            self.log_event("INFO", "COMMUNICATION_CHECK", "Testing inter-service communication", {},
                         service="WATCHDOG", routine="communication_check")

            # Test critical communication paths
            communication_tests = [
                {
                    "name": "Backend to Database",
                    "test": lambda: self.test_backend_database_connection()
                },
                {
                    "name": "Dispatcher to Backend",
                    "test": lambda: self.test_dispatcher_backend_connection()
                },
                {
                    "name": "Agents to Backend",
                    "test": lambda: self.test_agents_backend_connection()
                },
                {
                    "name": "Phone Agent to Twilio",
                    "test": lambda: self.test_phone_twilio_connection()
                }
            ]

            failed_tests = []
            for test in communication_tests:
                try:
                    if not test["test"]():
                        failed_tests.append(test["name"])
                        # Anti-spam: only log if this is a new failure or enough time has passed
                        test_key = f"comm_test_{test['name']}"
                        current_time = time.time()
                        if (test_key not in self._last_communication_failures or
                            current_time - self._last_communication_failures[test_key] > self._message_cooldown):
                            self.log_event("WARNING", "COMMUNICATION_CHECK", f"Communication test failed: {test['name']}", {},
                                         service="WATCHDOG", routine="test_failed")
                            self._last_communication_failures[test_key] = current_time
                except Exception as e:
                    failed_tests.append(test["name"])
                    self.log_event("ERROR", "COMMUNICATION_CHECK", f"Communication test error: {test['name']}", {
                        "error": str(e)
                    }, service="WATCHDOG", routine="test_error")

            if failed_tests:
                self.log_event("WARNING", "COMMUNICATION_CHECK", f"Communication issues detected", {
                    "failed_tests": failed_tests,
                    "total_tests": len(communication_tests)
                }, service="WATCHDOG", routine="communication_issues")

                # Attempt to fix communication issues
                self.fix_communication_issues(failed_tests)
            else:
                self.log_event("INFO", "COMMUNICATION_CHECK", "All communication tests passed", {
                    "total_tests": len(communication_tests)
                }, service="WATCHDOG", routine="all_tests_passed")

        except Exception as e:
            emergency_log(f"🚨 Service communication check failed: {e}")

    def test_backend_database_connection(self):
        """Test backend to database connection"""
        try:
            import requests
            backend_port = self.get_cached_port('backend')
            response = requests.get(f"http://localhost:{backend_port}/health", timeout=10)
            if response.status_code == 200:
                health_data = response.json()
                db_status = health_data.get("database", {}).get("status")
                # Accept both "connected" and "ready" as valid states
                return db_status in ["connected", "ready", "healthy"]
            return False
        except Exception as e:
            # Don't spam logs with connection failures
            return False

    def test_dispatcher_backend_connection(self):
        """Test dispatcher to backend connection"""
        try:
            import requests
            dispatcher_port = self.get_cached_port('dispatcher')
            response = requests.get(f"http://localhost:{dispatcher_port}/health", timeout=10)
            return response.status_code == 200
        except Exception:
            return False

    def test_agents_backend_connection(self):
        """Test agents to backend connection"""
        agent_services = ["dialogue", "planner", "phone"]

        for service in agent_services:
            try:
                import requests
                port = self.get_cached_port(service)
                response = requests.get(f"http://localhost:{port}/health", timeout=5)
                if response.status_code != 200:
                    return False
            except Exception:
                return False
        return True

    def test_phone_twilio_connection(self):
        """Test phone agent to Twilio connection"""
        try:
            import requests
            phone_port = self.get_cached_port('phone')
            # First check if phone agent is running
            health_response = requests.get(f"http://localhost:{phone_port}/health", timeout=5)
            if health_response.status_code != 200:
                return False

            # Then test Twilio connection (but don't fail if Twilio is not configured)
            try:
                response = requests.post(f"http://localhost:{phone_port}/test_twilio", timeout=10)
                if response.status_code == 200:
                    result = response.json()
                    return result.get("status") == "success"
                # If Twilio test endpoint doesn't exist, consider it non-critical
                return True
            except Exception:
                # Twilio test is non-critical, phone agent health is what matters
                return True
        except Exception:
            return False

    def detect_and_fix_port_conflicts(self):
        """🔌 IMMEDIATE SAME-SERVICE PORT CONFLICT DETECTION AND RESOLUTION"""
        try:
            # Check for same-service conflicts (multiple instances of same service)
            same_service_conflicts = self._detect_same_service_conflicts()

            if same_service_conflicts:
                self.log_event("WARNING", "SAME_SERVICE_CONFLICTS",
                              f"Found {len(same_service_conflicts)} same-service conflicts - MONITORING ONLY", {
                                  "conflicts": same_service_conflicts,
                                  "policy": "monitor_only_no_kill"
                              }, service="WATCHDOG", routine="same_service_conflict_detection")

                # WATCHDOG POLICY: MONITOR ONLY, DO NOT RESOLVE BY KILLING
                self.log_event("INFO", "WATCHDOG_POLICY_NO_RESOLUTION",
                              f"WATCHDOG POLICY: Will NOT resolve conflicts by killing - services are bulletproof and self-managing", {
                                  "conflict_count": len(same_service_conflicts),
                                  "policy": "bulletproof_services_self_manage"
                              }, service="WATCHDOG", routine="same_service_conflict_detection")
                return True

            # Also check for general port conflicts
            self.log_event("INFO", "PORT_CONFLICT_CHECK", "Checking for port conflicts", {},
                          service="WATCHDOG", routine="port_conflict_detection")

            # DEEPLICA ports to check
            deeplica_ports = [8001, 8002, 8003, 8004, 8005, 8888]
            conflicts_detected = []

            for port in deeplica_ports:
                try:
                    result = subprocess.run(['lsof', '-i', f':{port}'],
                                          capture_output=True, text=True, timeout=5)

                    if result.returncode == 0 and result.stdout.strip():
                        lines = result.stdout.strip().split('\n')[1:]  # Skip header
                        processes = []

                        for line in lines:
                            parts = line.split()
                            if len(parts) >= 2:
                                processes.append({
                                    'pid': parts[1],
                                    'name': parts[0],
                                    'user': parts[2] if len(parts) > 2 else 'unknown'
                                })

                        # Check if multiple processes or non-DEEPLICA processes
                        if len(processes) > 1:
                            # Check if these are actually different DEEPLICA services
                            deeplica_count = 0
                            for proc in processes:
                                if self._is_deeplica_process(proc['pid']):
                                    deeplica_count += 1

                            # Only report conflict if multiple non-DEEPLICA processes
                            if deeplica_count < len(processes):
                                conflicts_detected.append({
                                    'port': port,
                                    'type': 'multiple_processes',
                                    'processes': processes
                                })
                        elif processes and not self._is_deeplica_process(processes[0]['pid']):
                            conflicts_detected.append({
                                'port': port,
                                'type': 'non_deeplica_process',
                                'processes': processes
                            })

                except Exception as e:
                    self.log_event("WARNING", "PORT_CHECK_ERROR", f"Error checking port {port}: {e}", {},
                                  service="WATCHDOG", routine="port_conflict_detection")

            if conflicts_detected:
                self.log_event("WARNING", "PORT_CONFLICTS_DETECTED",
                              f"Found {len(conflicts_detected)} port conflicts", {
                                  "conflicts": conflicts_detected
                              }, service="WATCHDOG", routine="port_conflict_detection")

                # Resolve conflicts
                self._resolve_port_conflicts(conflicts_detected)
                return True
            else:
                self.log_event("INFO", "NO_PORT_CONFLICTS", "No port conflicts detected", {},
                              service="WATCHDOG", routine="port_conflict_detection")
                return False

        except Exception as e:
            self.log_event("ERROR", "PORT_CONFLICT_CHECK_FAILED", f"Port conflict check failed: {e}", {},
                          service="WATCHDOG", routine="port_conflict_detection")
            return False

    def _resolve_port_conflicts(self, conflicts):
        """Resolve detected port conflicts"""
        try:
            self.log_event("INFO", "PORT_CONFLICT_RESOLUTION", "Starting port conflict resolution", {
                "conflict_count": len(conflicts)
            }, service="WATCHDOG", routine="port_conflict_resolution")

            for conflict in conflicts:
                port = conflict['port']
                conflict_type = conflict['type']
                processes = conflict['processes']

                self.log_event("INFO", "RESOLVING_PORT_CONFLICT",
                              f"Resolving {conflict_type} on port {port}", {
                                  "port": port,
                                  "type": conflict_type,
                                  "processes": [p['pid'] for p in processes]
                              }, service="WATCHDOG", routine="port_conflict_resolution")

                # WATCHDOG POLICY: NEVER KILL DEEPLICA SERVICES
                # Monitor and log conflicts but let services handle them
                for process in processes:
                    pid = process['pid']
                    service_name = process.get('service_name', 'unknown')

                    self.log_event("WARNING", "PORT_CONFLICT_DETECTED_MONITORING_ONLY",
                                  f"Port conflict detected for PID {pid} ({service_name}) - MONITORING ONLY (not killing)", {
                                      "pid": pid,
                                      "service_name": service_name,
                                      "port": port,
                                      "policy": "monitor_only_no_kill"
                                  }, service="WATCHDOG", routine="port_conflict_resolution")

                    self.log_event("INFO", "WATCHDOG_POLICY_NO_KILL_PORT_CONFLICT",
                                  f"WATCHDOG POLICY: Will NOT kill PID {pid} for port conflict - services are bulletproof", {
                                      "pid": pid,
                                      "service_name": service_name,
                                      "port": port,
                                      "policy": "bulletproof_services_self_manage"
                                  }, service="WATCHDOG", routine="port_conflict_resolution")

                # Wait for port to be freed
                time.sleep(3)

                # Verify port is now free
                try:
                    result = subprocess.run(['lsof', '-i', f':{port}'],
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode != 0:
                        self.log_event("INFO", "PORT_FREED", f"Port {port} is now free", {},
                                      service="WATCHDOG", routine="port_conflict_resolution")
                    else:
                        self.log_event("WARNING", "PORT_STILL_IN_USE", f"Port {port} still in use after cleanup", {},
                                      service="WATCHDOG", routine="port_conflict_resolution")
                except Exception as e:
                    self.log_event("WARNING", "PORT_VERIFICATION_FAILED", f"Failed to verify port {port}: {e}", {},
                                  service="WATCHDOG", routine="port_conflict_resolution")

            self.log_event("INFO", "PORT_CONFLICT_RESOLUTION_COMPLETE", "Port conflict resolution completed", {},
                          service="WATCHDOG", routine="port_conflict_resolution")

        except Exception as e:
            self.log_event("ERROR", "PORT_CONFLICT_RESOLUTION_FAILED", f"Port conflict resolution failed: {e}", {},
                          service="WATCHDOG", routine="port_conflict_resolution")

    def _detect_same_service_conflicts(self):
        """🔍 Detect multiple instances of the same service using the same port"""
        conflicts = []
        try:
            from shared.port_manager import port_manager

            # Get all DEEPLICA ports
            deeplica_ports = [8001, 8002, 8003, 8004, 8005, 8888]

            for port in deeplica_ports:
                try:
                    # Get all processes using this port
                    result = subprocess.run(['lsof', '-i', f':{port}'],
                                          capture_output=True, text=True, timeout=5)

                    if result.returncode == 0 and result.stdout.strip():
                        lines = result.stdout.strip().split('\n')[1:]  # Skip header
                        deeplica_processes = []

                        for line in lines:
                            parts = line.split()
                            if len(parts) >= 2:
                                process_name = parts[0]
                                pid = parts[1]

                                # Check if this is a DEEPLICA process
                                if 'DEEPLICA' in process_name or 'python' in process_name.lower():
                                    # Get more details about the process
                                    try:
                                        proc_result = subprocess.run(['ps', '-p', pid, '-o', 'pid,comm,args'],
                                                                   capture_output=True, text=True, timeout=5)
                                        if proc_result.returncode == 0:
                                            proc_lines = proc_result.stdout.strip().split('\n')
                                            if len(proc_lines) > 1:
                                                proc_parts = proc_lines[1].strip().split(None, 2)
                                                if len(proc_parts) >= 3:
                                                    command = proc_parts[2]
                                                    # Check if this is a DEEPLICA service
                                                    if any(service in command.lower() for service in
                                                          ['phone', 'backend', 'dispatcher', 'planner', 'dialogue', 'watchdog']):
                                                        deeplica_processes.append({
                                                            'pid': pid,
                                                            'command': command,
                                                            'service_type': self._identify_service_type(command)
                                                        })
                                    except Exception:
                                        continue

                        # Check for multiple instances of the same service type
                        if len(deeplica_processes) > 1:
                            service_types = {}
                            for proc in deeplica_processes:
                                service_type = proc['service_type']
                                if service_type not in service_types:
                                    service_types[service_type] = []
                                service_types[service_type].append(proc)

                            # Look for multiple instances of the same service
                            for service_type, processes in service_types.items():
                                if len(processes) > 1:
                                    # CRITICAL FIX: Ensure we actually have different PIDs
                                    unique_pids = set(proc['pid'] for proc in processes)
                                    if len(unique_pids) > 1:
                                        # Only report conflict if we have genuinely different processes
                                        conflicts.append({
                                            'port': port,
                                            'service_type': service_type,
                                            'processes': processes,
                                            'conflict_type': 'same_service_multiple_instances'
                                        })
                                        self.log_event("DEBUG", "REAL_CONFLICT_DETECTED",
                                                      f"Real conflict: {service_type} has {len(unique_pids)} different PIDs on port {port}", {
                                                          "service_type": service_type,
                                                          "port": port,
                                                          "pids": list(unique_pids)
                                                      }, service="WATCHDOG", routine="same_service_conflict_detection")
                                    else:
                                        self.log_event("DEBUG", "FALSE_CONFLICT_AVOIDED",
                                                      f"Avoided false conflict: {service_type} has only 1 unique PID on port {port}", {
                                                          "service_type": service_type,
                                                          "port": port,
                                                          "pid": list(unique_pids)[0]
                                                      }, service="WATCHDOG", routine="same_service_conflict_detection")

                except Exception as e:
                    self.log_event("WARNING", "SAME_SERVICE_CHECK_ERROR", f"Error checking port {port}: {e}", {},
                                  service="WATCHDOG", routine="same_service_conflict_detection")

            return conflicts

        except Exception as e:
            self.log_event("ERROR", "SAME_SERVICE_DETECTION_FAILED", f"Same-service conflict detection failed: {e}", {},
                          service="WATCHDOG", routine="same_service_conflict_detection")
            return []

    def _identify_service_type(self, command):
        """Identify the type of DEEPLICA service from command line"""
        command_lower = command.lower()

        if 'phone' in command_lower:
            return 'phone-agent'
        elif 'backend' in command_lower:
            return 'backend-api'
        elif 'dispatcher' in command_lower:
            return 'dispatcher'
        elif 'planner' in command_lower:
            return 'planner-agent'
        elif 'dialogue' in command_lower:
            return 'dialogue-agent'
        elif 'watchdog' in command_lower:
            return 'watchdog'
        else:
            return 'unknown'

    def _is_deeplica_process(self, pid):
        """Check if a process is a DEEPLICA service by examining its command line"""
        try:
            # Get detailed process information
            result = subprocess.run(['ps', '-p', str(pid), '-o', 'pid,comm,args'],
                                  capture_output=True, text=True, timeout=5)

            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                if len(lines) > 1:  # Skip header
                    proc_parts = lines[1].strip().split(None, 2)
                    if len(proc_parts) >= 3:
                        command = proc_parts[2].lower()

                        # Check for DEEPLICA service indicators
                        deeplica_indicators = [
                            'deeplica',
                            'phone/app/main.py',
                            'backend/app/main.py',
                            'dispatcher/main.py',
                            'planner/main.py',
                            'dialogue/main.py',
                            'watchdog/main.py',
                            'bulletproof_phone_agent.py',
                            'agents/phone',
                            'agents/planner',
                            'agents/dialogue'
                        ]

                        # Check if any DEEPLICA indicators are present
                        for indicator in deeplica_indicators:
                            if indicator in command:
                                return True

                        # Also check process name for DEEPLICA
                        if len(proc_parts) >= 2:
                            process_name = proc_parts[1].lower()
                            if 'deeplica' in process_name:
                                return True

            return False

        except Exception as e:
            # If we can't determine, assume it's not DEEPLICA to be safe
            return False

    def _resolve_same_service_conflicts(self, conflicts):
        """🔧 Resolve same-service conflicts by keeping newest and killing older instances"""
        try:
            self.log_event("INFO", "SAME_SERVICE_RESOLUTION", "Starting same-service conflict resolution", {
                "conflict_count": len(conflicts)
            }, service="WATCHDOG", routine="same_service_conflict_resolution")

            for conflict in conflicts:
                port = conflict['port']
                service_type = conflict['service_type']
                processes = conflict['processes']

                self.log_event("WARNING", "RESOLVING_SAME_SERVICE_CONFLICT",
                              f"Resolving {service_type} conflict on port {port}", {
                                  "port": port,
                                  "service_type": service_type,
                                  "process_count": len(processes)
                              }, service="WATCHDOG", routine="same_service_conflict_resolution")

                # CRITICAL SAFETY CHECK: Ensure we have unique PIDs
                unique_processes = {}
                for proc in processes:
                    pid = proc['pid']
                    if pid not in unique_processes:
                        unique_processes[pid] = proc

                processes_unique = list(unique_processes.values())

                # If only one unique process, no conflict to resolve
                if len(processes_unique) <= 1:
                    self.log_event("INFO", "NO_REAL_CONFLICT",
                                  f"No real conflict for {service_type} on port {port} - only {len(processes_unique)} unique process(es)", {
                                      "service_type": service_type,
                                      "port": port,
                                      "unique_processes": len(processes_unique)
                                  }, service="WATCHDOG", routine="same_service_conflict_resolution")
                    continue

                # Sort processes by PID (newer processes typically have higher PIDs)
                processes_sorted = sorted(processes_unique, key=lambda x: int(x['pid']), reverse=True)

                # Keep the newest process (first in sorted list) and kill the rest
                newest_process = processes_sorted[0]
                older_processes = processes_sorted[1:]

                self.log_event("INFO", "KEEPING_NEWEST_PROCESS",
                              f"Keeping newest {service_type} process PID {newest_process['pid']}", {
                                  "service_type": service_type,
                                  "kept_pid": newest_process['pid'],
                                  "killing_count": len(older_processes)
                              }, service="WATCHDOG", routine="same_service_conflict_resolution")

                # Kill older processes (with additional safety check)
                for old_process in older_processes:
                    pid = old_process['pid']

                    # WATCHDOG POLICY: NEVER KILL DEEPLICA SERVICES
                    # Services are designed to be bulletproof and handle conflicts themselves
                    self.log_event("WARNING", "DEEPLICA_SERVICE_CONFLICT_DETECTED",
                                  f"Detected duplicate {service_type} process PID {pid} - MONITORING ONLY (not killing)", {
                                      "service_type": service_type,
                                      "pid": pid,
                                      "policy": "monitor_only_no_kill",
                                      "reason": "deeplica_services_are_bulletproof"
                                  }, service="WATCHDOG", routine="same_service_conflict_resolution")

                    # Log for admin awareness but DO NOT KILL
                    self.log_event("INFO", "WATCHDOG_POLICY_NO_KILL",
                                  f"WATCHDOG POLICY: Will NOT kill {service_type} PID {pid} - services must handle conflicts themselves", {
                                      "service_type": service_type,
                                      "pid": pid,
                                      "policy": "bulletproof_services_self_manage"
                                  }, service="WATCHDOG", routine="same_service_conflict_resolution")

                # Wait for processes to die
                time.sleep(3)

                # Verify only one process remains
                try:
                    result = subprocess.run(['lsof', '-i', f':{port}'],
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0 and result.stdout.strip():
                        lines = result.stdout.strip().split('\n')[1:]  # Skip header
                        remaining_count = len(lines)

                        if remaining_count == 1:
                            self.log_event("INFO", "SAME_SERVICE_CONFLICT_RESOLVED",
                                          f"Successfully resolved {service_type} conflict on port {port}", {
                                              "port": port,
                                              "service_type": service_type,
                                              "remaining_processes": 1
                                          }, service="WATCHDOG", routine="same_service_conflict_resolution")
                        else:
                            self.log_event("WARNING", "SAME_SERVICE_CONFLICT_PARTIAL",
                                          f"Partial resolution for {service_type} on port {port} - {remaining_count} processes remain", {
                                              "port": port,
                                              "service_type": service_type,
                                              "remaining_processes": remaining_count
                                          }, service="WATCHDOG", routine="same_service_conflict_resolution")
                    else:
                        self.log_event("INFO", "PORT_FREED_AFTER_RESOLUTION", f"Port {port} is now free after {service_type} conflict resolution", {},
                                      service="WATCHDOG", routine="same_service_conflict_resolution")

                except Exception as e:
                    self.log_event("WARNING", "CONFLICT_VERIFICATION_FAILED", f"Failed to verify {service_type} conflict resolution on port {port}: {e}", {},
                                  service="WATCHDOG", routine="same_service_conflict_resolution")

            self.log_event("INFO", "SAME_SERVICE_RESOLUTION_COMPLETE", "Same-service conflict resolution completed", {},
                          service="WATCHDOG", routine="same_service_conflict_resolution")

        except Exception as e:
            self.log_event("ERROR", "SAME_SERVICE_RESOLUTION_FAILED", f"Same-service conflict resolution failed: {e}", {},
                          service="WATCHDOG", routine="same_service_conflict_resolution")

    def fix_communication_issues(self, failed_tests):
        """Attempt to fix communication issues"""
        try:
            self.log_event("WARNING", "COMMUNICATION_FIX", "Attempting to fix communication issues", {
                "failed_tests": failed_tests
            }, service="WATCHDOG", routine="fix_communication")

            # Check for port conflicts first
            self.detect_and_fix_port_conflicts()

            # Restart services that have communication issues
            if "Backend to Database" in failed_tests:
                self.attempt_database_recovery("communication_test_failed")

            if "Dispatcher to Backend" in failed_tests:
                self.attempt_service_recovery("dispatcher")

            if "Agents to Backend" in failed_tests:
                for agent in ["dialogue", "planner", "phone"]:
                    self.attempt_service_recovery(agent)

            if "Phone Agent to Twilio" in failed_tests:
                self.attempt_twilio_recovery()

        except Exception as e:
            emergency_log(f"🚨 Communication fix failed: {e}")

        try:
            # Increment recovery attempts
            self.recovery_attempts['twilio'] = self.recovery_attempts.get('twilio', 0) + 1

            if self.recovery_attempts['twilio'] > 3:
                emergency_log(f"🚨 TWILIO recovery failed after 3 attempts - manual intervention required")
                return False

            emergency_log(f"🔧 TWILIO recovery attempt #{self.recovery_attempts['twilio']}")

            # Check if phone agent is running
            import httpx
            try:
                response = httpx.get(f"http://{get_localhost()}:{get_service_port('phone')}/health", timeout=3.0)
                if response.status_code != 200:
                    emergency_log(f"🔧 TWILIO recovery: Phone agent not responding, attempting restart")
                    # Try to restart phone agent
                    safe_execute(self.attempt_service_recovery, 'phone-agent')
                    return True
            except Exception:
                emergency_log(f"🔧 TWILIO recovery: Phone agent unreachable, attempting restart")
                safe_execute(self.attempt_service_recovery, 'phone-agent')
                return True

            # Check Twilio credentials
            import os
            twilio_sid = os.getenv('TWILIO_ACCOUNT_SID')
            twilio_token = os.getenv('TWILIO_AUTH_TOKEN')
            twilio_phone = os.getenv('TWILIO_PHONE_NUMBER')

            if not all([twilio_sid, twilio_token, twilio_phone]):
                emergency_log(f"🚨 TWILIO recovery: Missing credentials - manual configuration required")
                return False

            # Test Twilio connection
            try:
                from twilio.rest import Client
                client = Client(twilio_sid, twilio_token)
                account = client.api.accounts(twilio_sid).fetch()
                emergency_log(f"✅ TWILIO recovery: Connection restored successfully")
                self.recovery_attempts['twilio'] = 0
                return True
            except Exception as e:
                emergency_log(f"🚨 TWILIO recovery: Connection test failed - {str(e)[:100]}")
                return False

        except Exception as e:
            emergency_log(f"🚨 TWILIO recovery attempt failed: {e}")
            return False

    def update_webhook_url(self, tunnel_url):
        """🔗 Update webhook URL in environment when ngrok tunnel changes"""
        try:
            if not tunnel_url:
                return False

            emergency_log(f"🔗 WEBHOOK URL update: {tunnel_url}")

            # Update .env file
            import os
            env_file_path = ".env"
            if os.path.exists(env_file_path):
                # Read current .env file
                with open(env_file_path, 'r') as f:
                    lines = f.readlines()

                # Update TWILIO_WEBHOOK_URL line
                updated = False
                for i, line in enumerate(lines):
                    if line.startswith('TWILIO_WEBHOOK_URL='):
                        lines[i] = f'TWILIO_WEBHOOK_URL={tunnel_url}\n'
                        updated = True
                        break

                # If not found, add it
                if not updated:
                    lines.append(f'TWILIO_WEBHOOK_URL={tunnel_url}\n')

                # Write back to file
                with open(env_file_path, 'w') as f:
                    f.writelines(lines)

                emergency_log(f"✅ WEBHOOK URL updated in .env file")

                # Notify phone service to reload configuration
                try:
                    import httpx
                    response = httpx.post(f"http://{get_localhost()}:{get_service_port('phone')}/reload_config", timeout=3.0)
                    if response.status_code == 200:
                        emergency_log(f"✅ WEBHOOK URL: Phone service configuration reloaded")
                    else:
                        emergency_log(f"⚠️ WEBHOOK URL: Phone service reload failed: {response.status_code}")
                except Exception as e:
                    emergency_log(f"⚠️ WEBHOOK URL: Could not notify phone service: {e}")

                return True
            else:
                emergency_log(f"⚠️ WEBHOOK URL: .env file not found")
                return False

        except Exception as e:
            emergency_log(f"🚨 WEBHOOK URL update failed: {e}")
            return False

    def attempt_database_recovery(self, failure_reason):
        """🗄️ Attempt to recover MongoDB Atlas database connection"""
        if not self.auto_recovery_enabled or self.stop_service_running:
            return False

        try:
            # Increment recovery attempts
            self.recovery_attempts['database'] = self.recovery_attempts.get('database', 0) + 1

            if self.recovery_attempts['database'] > 3:
                emergency_log(f"🚨 DATABASE recovery failed after 3 attempts - manual intervention required")
                return False

            emergency_log(f"🔧 DATABASE recovery attempt #{self.recovery_attempts['database']} (reason: {failure_reason})")

            # Step 1: Check MongoDB Atlas credentials
            import os
            mongo_uri = os.getenv('MONGODB_URI')
            if not mongo_uri:
                # Try to load from .env file as fallback
                try:
                    from dotenv import load_dotenv
                    load_dotenv()
                    mongo_uri = os.getenv('MONGODB_URI')
                    if mongo_uri:
                        emergency_log("✅ DATABASE recovery: Found MONGODB_URI in .env file")
                    else:
                        emergency_log("🚨 DATABASE recovery: No MONGODB_URI found in environment or .env file")
                        # Set a default for development
                        mongo_uri = "mongodb+srv://eranfire:<EMAIL>/deeplica_db?retryWrites=true&w=majority"
                        os.environ['MONGODB_URI'] = mongo_uri
                        emergency_log("🔧 DATABASE recovery: Using default MongoDB URI for development")
                except Exception as env_error:
                    emergency_log(f"🚨 DATABASE recovery: Error loading .env file: {env_error}")
                    return False

            if 'mongodb+srv://' not in mongo_uri:
                emergency_log(f"🚨 DATABASE recovery: URI is not MongoDB Atlas format")
                return False

            # Step 2: Test direct MongoDB Atlas connection
            try:
                from pymongo import MongoClient
                client = MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
                # Test connection
                result = client.admin.command('ping')
                emergency_log(f"✅ DATABASE recovery: Direct Atlas connection successful")

                # Test database operations
                db_name = os.getenv('MONGODB_DATABASE', 'deeplica-dev')
                db = client[db_name]
                collections = db.list_collection_names()
                emergency_log(f"✅ DATABASE recovery: Database operations working, collections: {len(collections)}")

                self.recovery_attempts['database'] = 0
                return True

            except Exception as mongo_e:
                emergency_log(f"🚨 DATABASE recovery: Direct Atlas connection failed - {str(mongo_e)[:100]}")

            # Step 3: Try to restart backend service (which manages database connections)
            if failure_reason in ["backend_unreachable", "backend_unavailable"]:
                emergency_log(f"🔧 DATABASE recovery: Backend issue detected, attempting backend restart")
                safe_execute(self.attempt_service_recovery, 'backend')
                return True

            # Step 4: Check network connectivity to MongoDB Atlas
            try:
                import socket
                import urllib.parse

                # Parse MongoDB URI to get hostname
                parsed = urllib.parse.urlparse(mongo_uri)
                hostname = parsed.hostname

                if hostname:
                    # Test DNS resolution
                    socket.gethostbyname(hostname)
                    emergency_log(f"✅ DATABASE recovery: DNS resolution successful for {hostname}")

                    # Test network connectivity
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(5)
                    result = sock.connect_ex((hostname, 27017))
                    sock.close()

                    if result == 0:
                        emergency_log(f"✅ DATABASE recovery: Network connectivity to Atlas successful")
                    else:
                        emergency_log(f"🚨 DATABASE recovery: Network connectivity failed to {hostname}:27017")
                        return False

            except Exception as net_e:
                emergency_log(f"🚨 DATABASE recovery: Network test failed - {str(net_e)[:100]}")
                return False

            emergency_log(f"🚨 DATABASE recovery: All tests passed but connection still failing")
            return False

        except Exception as e:
            emergency_log(f"🚨 DATABASE recovery attempt failed: {e}")
            return False

    def reset_recovery_attempts(self, service_name):
        """🔄 Reset recovery attempts for a service (when it comes back online)"""
        if service_name in self.recovery_attempts:
            old_attempts = self.recovery_attempts[service_name]
            self.recovery_attempts[service_name] = 0

            if old_attempts > 0:
                self.log_event("INFO", "AUTO_RECOVERY", f"Service {service_name} recovered - resetting attempt counter", {
                    "previous_attempts": old_attempts
                }, service=service_name.upper(), routine="recovery_reset")

    async def run_monitoring_loop(self):
        """🛡️ CRASH-RESISTANT Main monitoring loop - NEVER stops monitoring"""
        # Initial startup log - wrapped in safe_execute
        safe_execute(self.log_event, "INFO", "WATCHDOG", "Started monitoring system", {
            "start_time": self.start_time.isoformat(),
            "monitoring_services": list(self.services.keys()) if hasattr(self, 'services') else []
        }, service="WATCHDOG", routine="startup")
        
        loop_count = 0
        
        while self.running:
            try:
                # Core monitoring (every cycle) - all wrapped in safe_execute
                safe_execute(self.monitor_processes)
                safe_execute(self.monitor_ports)
                safe_execute(self.monitor_service_health)
                safe_execute(self.monitor_system_resources)

                # 🔌 Port conflict detection and resolution (every cycle)
                safe_execute(self.detect_and_fix_port_conflicts)

                # Extended monitoring (every few cycles)
                if loop_count % 2 == 0:  # Every 10 seconds
                    safe_execute(self.monitor_terminal_activity)
                    safe_execute(self.monitor_network_activity)

                if loop_count % 6 == 0:  # Every 30 seconds
                    safe_execute(self.monitor_log_files)
                    safe_execute(self.capture_service_errors)

                # Enhanced monitoring (every few cycles)
                if loop_count % 4 == 0:  # Every 20 seconds
                    safe_execute(self.monitor_twilio_status)
                    safe_execute(self.monitor_ngrok_status)
                    safe_execute(self.monitor_database_status)
                    safe_execute(self.monitor_stop_service_registrations)
                    # 🔍 NEW: Enhanced external service monitoring
                    safe_execute(self.monitor_external_services_enhanced)

                if loop_count % 3 == 0:  # Every 15 seconds
                    safe_execute(self.monitor_conversation_logs)
                    safe_execute(self.monitor_active_phone_calls)
                    safe_execute(self.monitor_mission_failures)
                    safe_execute(self.monitor_phone_service_health)
                    safe_execute(self.monitor_phone_task_failures)

                # Enhanced phone service monitoring - every 10 seconds
                if loop_count % 2 == 0:  # Every 10 seconds
                    safe_execute(self.enhanced_phone_monitoring)
                    safe_execute(self.monitor_phone_circuit_breaker)
                    safe_execute(self.monitor_phone_system_health)

                if loop_count % 5 == 0:  # Every 25 seconds
                    safe_execute(self.check_for_error_calls)

                # Auto-recovery monitoring
                if loop_count % 2 == 0:  # Every 10 seconds
                    safe_execute(self.check_stop_service_status)
                    # 🔍 NEW: Enhanced crash detection
                    safe_execute(self.monitor_service_crashes)

                # Port and communication monitoring
                # DISABLED: Port conflict resolution is too aggressive
                # if loop_count % 8 == 0:  # Every 40 seconds
                #     safe_execute(self.fix_port_conflicts)

                if loop_count % 12 == 0:  # Every 60 seconds
                    safe_execute(self.ensure_service_communication)

                # Log summary every 60 loops (approximately 5 minutes)
                if loop_count % 60 == 0:
                    safe_execute(self.log_system_summary)
                    # Memory cleanup every 5 minutes
                    safe_execute(self.cleanup_memory)

                loop_count += 1

                # Wait before next monitoring cycle
                try:
                    await asyncio.sleep(5)  # Monitor every 5 seconds
                except:
                    # If even sleep fails, use time.sleep
                    time.sleep(5)

            except KeyboardInterrupt:
                emergency_log("🛡️ WATCHDOG shutdown requested via KeyboardInterrupt")
                safe_execute(self.log_event, "INFO", "WATCHDOG", "Watchdog shutdown requested", {}, service="WATCHDOG", routine="shutdown")
                self.running = False
                break
            except SystemExit:
                emergency_log("🛡️ WATCHDOG shutdown requested via SystemExit")
                self.running = False
                break
            except Exception as e:
                self.crash_count += 1
                self.last_crash_time = datetime.now()

                emergency_log(f"🚨 WATCHDOG LOOP ERROR #{self.crash_count}: {e}")

                # Log the error if possible
                safe_execute(self.log_event, "ERROR", "WATCHDOG", f"Monitoring loop error",
                           {"error": str(e), "crash_count": self.crash_count},
                           service="WATCHDOG", routine="error_recovery")

                # Brief pause before retrying - NEVER stop monitoring
                try:
                    await asyncio.sleep(5)
                except:
                    time.sleep(5)
        
        self.log_event("INFO", "WATCHDOG", "Deeplica Watchdog stopped", {
            "stop_time": datetime.now().isoformat(),
            "total_uptime": str(datetime.now() - self.start_time)
        })

    # 🔍 ENHANCED MONITORING METHODS

    def monitor_service_crashes(self):
        """Enhanced service crash detection and alerting"""
        try:
            current_time = datetime.now()

            for service_name, registration in self.registered_processes.items():
                if not registration:
                    continue

                # Check if service is still running
                try:
                    process = psutil.Process(registration.process_id)
                    if process.is_running():
                        # Service is alive, update last seen
                        self.service_last_seen[service_name] = current_time

                        # Reset crash count if service has been stable
                        if service_name in self.service_crash_history:
                            last_crash = self.service_crash_history[service_name][-1] if self.service_crash_history[service_name] else None
                            if last_crash and (current_time - last_crash).total_seconds() > 300:  # 5 minutes stable
                                self.service_crash_history[service_name] = []
                    else:
                        # Process exists but not running - crashed
                        self.handle_service_crash(service_name, "process_not_running")

                except psutil.NoSuchProcess:
                    # Process doesn't exist - crashed
                    self.handle_service_crash(service_name, "process_not_found")
                except Exception as e:
                    # Error checking process - might be crashed
                    self.handle_service_crash(service_name, f"check_error: {str(e)[:50]}")

        except Exception as e:
            emergency_log(f"[WATCHDOG] ❌ Service crash monitoring error: {e}")

    def handle_service_crash(self, service_name: str, reason: str):
        """Handle detected service crash"""
        try:
            current_time = datetime.now()

            # Record crash
            if service_name not in self.service_crash_history:
                self.service_crash_history[service_name] = []
            self.service_crash_history[service_name].append(current_time)

            # Count recent crashes (last 10 minutes)
            recent_crashes = [
                crash_time for crash_time in self.service_crash_history[service_name]
                if (current_time - crash_time).total_seconds() < 600
            ]

            crash_count = len(recent_crashes)

            # Log crash with appropriate severity
            if crash_count >= self.crash_alert_threshold:
                severity = "ERROR"
                message = f"CRITICAL: Service crashed {crash_count} times in 10 minutes"
            else:
                severity = "WARNING"
                message = f"Service crash detected"

            self.log_event(severity, "CRASH", message, {
                "service": service_name,
                "reason": reason,
                "crash_count": crash_count,
                "recent_crashes": len(recent_crashes)
            }, service="WATCHDOG", routine="crash_detector")

            # Remove from registry if crashed
            if service_name in self.registered_processes:
                registration = self.registered_processes[service_name]
                if registration and registration.port:
                    self.registered_ports.discard(registration.port)
                del self.registered_processes[service_name]
                self.save_registrations()

        except Exception as e:
            emergency_log(f"[WATCHDOG] ❌ Error handling crash for {service_name}: {e}")

    def monitor_external_services_enhanced(self):
        """Enhanced monitoring of external services with failure tracking"""
        try:
            for service_name, service_info in self.external_services.items():
                try:
                    if service_name == "mongodb":
                        self.check_mongodb_status(service_info)
                    elif service_name == "twilio":
                        self.check_twilio_status_enhanced(service_info)
                    elif service_name == "ngrok":
                        self.check_ngrok_status_enhanced(service_info)
                    elif service_name == "gemini":
                        self.check_gemini_status(service_info)

                except Exception as e:
                    # Ensure failure_count exists before incrementing
                    if "failure_count" not in service_info:
                        service_info["failure_count"] = 0
                    service_info["failure_count"] += 1
                    service_info["last_status"] = f"check_error: {str(e)[:50]}"

                    self.log_event("WARNING", service_name.upper(), f"Monitoring check failed", {
                        "error": str(e)[:100],
                        "failure_count": service_info["failure_count"]
                    }, service="WATCHDOG", routine="external_monitor")

        except Exception as e:
            emergency_log(f"[WATCHDOG] ❌ External services monitoring error: {e}")

    def check_mongodb_status(self, service_info: dict):
        """Check MongoDB Atlas connection status"""
        try:
            import os
            connection_string = os.getenv("MONGODB_CONNECTION_STRING")

            if not connection_string:
                service_info["last_status"] = "not_configured"
                service_info["failure_count"] += 1
                return

            # Try to connect to MongoDB via backend API
            try:
                import httpx
                response = httpx.get(f"http://{get_localhost()}:{get_service_port('backend')}/health", timeout=5.0)
                if response.status_code == 200:
                    health_data = response.json()
                    db_status = health_data.get("database", {}).get("status", "unknown")

                    if db_status == "connected":
                        service_info["last_status"] = "healthy"
                        service_info["failure_count"] = 0
                    else:
                        service_info["last_status"] = f"backend_reports: {db_status}"
                        service_info["failure_count"] += 1
                else:
                    service_info["last_status"] = "backend_unavailable"
                    service_info["failure_count"] += 1

            except Exception as e:
                service_info["last_status"] = f"connection_failed: {str(e)[:50]}"
                service_info["failure_count"] += 1

                if service_info["failure_count"] >= self.crash_alert_threshold:
                    self.log_event("ERROR", "MONGODB", "Critical database connection failure", {
                        "error": str(e)[:100],
                        "failure_count": service_info["failure_count"]
                    }, service="WATCHDOG", routine="mongodb_monitor")

        except Exception as e:
            service_info["last_status"] = f"monitor_error: {str(e)[:50]}"
            service_info["failure_count"] += 1

    def check_twilio_status_enhanced(self, service_info: dict):
        """Enhanced Twilio status checking"""
        try:
            # Check if phone agent is running (it manages Twilio)
            phone_agent_running = False
            for port, service_name in self.deeplica_services.items():
                if service_name == "PHONE-AGENT":
                    phone_agent_running = self.check_port_in_use(port)
                    break

            if phone_agent_running:
                # Try to get Twilio status from phone agent
                try:
                    import httpx
                    response = httpx.get(f"http://{get_localhost()}:{get_service_port('phone')}/health", timeout=3.0)
                    if response.status_code == 200:
                        health_data = response.json()
                        twilio_status = health_data.get('twilio_status', 'unknown')

                        if twilio_status == 'healthy':
                            service_info["last_status"] = "healthy"
                            service_info["failure_count"] = 0
                        else:
                            service_info["last_status"] = f"unhealthy: {twilio_status}"
                            service_info["failure_count"] += 1
                    else:
                        service_info["last_status"] = "phone_agent_unhealthy"
                        service_info["failure_count"] += 1
                except Exception as e:
                    service_info["last_status"] = f"phone_agent_unreachable: {str(e)[:30]}"
                    service_info["failure_count"] += 1
            else:
                service_info["last_status"] = "phone_agent_down"
                service_info["failure_count"] += 1

            # Alert on critical failures
            if service_info["failure_count"] >= self.crash_alert_threshold:
                self.log_event("ERROR", "TWILIO", f"Critical Twilio service failure", {
                    "failure_count": service_info["failure_count"],
                    "last_status": service_info["last_status"]
                }, service="WATCHDOG", routine="twilio_monitor")

        except Exception as e:
            service_info["last_status"] = f"monitor_error: {str(e)[:50]}"
            service_info["failure_count"] += 1

    def check_ngrok_status_enhanced(self, service_info: dict):
        """Enhanced ngrok tunnel status checking"""
        try:
            import httpx

            # Check ngrok API
            try:
                response = httpx.get(f"http://{get_localhost()}:{get_service_port('ngrok-api')}/api/tunnels", timeout=3.0)
                if response.status_code == 200:
                    tunnels_data = response.json()
                    tunnels = tunnels_data.get('tunnels', [])

                    if tunnels:
                        # Find HTTPS tunnel
                        tunnel_url = None
                        for tunnel in tunnels:
                            if tunnel.get('proto') == 'https':
                                tunnel_url = tunnel.get('public_url')
                                break

                        if tunnel_url:
                            service_info["last_status"] = f"healthy: {tunnel_url}"
                            service_info["failure_count"] = 0
                        else:
                            service_info["last_status"] = "no_https_tunnel"
                            service_info["failure_count"] += 1
                    else:
                        service_info["last_status"] = "no_tunnels"
                        service_info["failure_count"] += 1
                else:
                    service_info["last_status"] = f"api_error: {response.status_code}"
                    service_info["failure_count"] += 1
            except Exception as e:
                service_info["last_status"] = f"api_unreachable: {str(e)[:30]}"
                service_info["failure_count"] += 1

            # Alert on critical failures (with anti-spam)
            if service_info["failure_count"] >= self.crash_alert_threshold:
                ngrok_key = "ngrok_critical_failure"
                current_time = time.time()
                if (ngrok_key not in self._last_communication_failures or
                    current_time - self._last_communication_failures[ngrok_key] > self._message_cooldown):
                    self.log_event("ERROR", "NGROK", f"Critical ngrok tunnel failure", {
                        "failure_count": service_info["failure_count"],
                        "last_status": service_info["last_status"]
                    }, service="WATCHDOG", routine="ngrok_monitor")
                    self._last_communication_failures[ngrok_key] = current_time

        except Exception as e:
            service_info["last_status"] = f"monitor_error: {str(e)[:50]}"
            service_info["failure_count"] += 1

    def check_gemini_status(self, service_info: dict):
        """Check Google Gemini API status"""
        try:
            import os
            api_key = os.getenv("GEMINI_API_KEY")

            if not api_key:
                service_info["last_status"] = "not_configured"
                service_info["failure_count"] += 1
                return

            # Check via backend API
            try:
                import httpx
                response = httpx.get(f"http://{get_localhost()}:{get_service_port('backend')}/health", timeout=5.0)
                if response.status_code == 200:
                    health_data = response.json()
                    llm_status = health_data.get("llm", {}).get("status", "unknown")

                    if llm_status == "healthy":
                        service_info["last_status"] = "healthy"
                        service_info["failure_count"] = 0
                    else:
                        service_info["last_status"] = f"backend_reports: {llm_status}"
                        service_info["failure_count"] += 1
                else:
                    service_info["last_status"] = "backend_unavailable"
                    service_info["failure_count"] += 1

            except Exception as e:
                service_info["last_status"] = f"check_failed: {str(e)[:30]}"
                service_info["failure_count"] += 1

                if service_info["failure_count"] >= self.crash_alert_threshold:
                    self.log_event("ERROR", "GEMINI", "Critical LLM API failure", {
                        "error": str(e)[:100],
                        "failure_count": service_info["failure_count"]
                    }, service="WATCHDOG", routine="gemini_monitor")

        except Exception as e:
            service_info["last_status"] = f"monitor_error: {str(e)[:50]}"
            service_info["failure_count"] += 1

    def monitor_mission_failures(self):
        """🎯 Monitor for failed missions and attempt recovery"""
        try:
            # Check for recent failed missions
            import httpx
            try:
                response = httpx.get(f"http://{get_localhost()}:{get_service_port('backend')}/api/v1/missions?limit=5", timeout=5.0)
                if response.status_code == 200:
                    missions = response.json()

                    for mission in missions:
                        # Check for missions with failed tasks
                        progress = mission.get('progress', {})
                        failed_count = progress.get('failed', 0)
                        total_tasks = progress.get('total_tasks', 0)

                        if failed_count > 0 and total_tasks > 0:
                            mission_id = mission.get('mission_id')
                            user_input = mission.get('user_input', '')

                            # Check if this is a phone call mission that failed
                            if 'call' in user_input.lower() and '+' in user_input:
                                # Get task details to understand the failure
                                try:
                                    task_response = httpx.get(
                                        f"http://{get_localhost()}:{get_service_port('backend')}/api/v1/internal/missions/{mission_id}/tasks",
                                        timeout=5.0
                                    )
                                    if task_response.status_code == 200:
                                        tasks = task_response.json()
                                        for task in tasks:
                                            if task.get('status') == 'failed':
                                                error_msg = task.get('error_message', '')
                                                task_type = task.get('task_type', '')

                                                # Log the failure
                                                self.log_event("WARNING", "MISSION_FAILURE",
                                                             f"Phone call mission failed: {error_msg[:100]}", {
                                                    "mission_id": mission_id,
                                                    "user_input": user_input,
                                                    "task_type": task_type,
                                                    "error": error_msg
                                                }, service="PLANNER", routine="mission_failure")

                                                # Attempt recovery based on error type
                                                if 'planner' in error_msg.lower() or task_type == 'planning':
                                                    self.log_event("INFO", "AUTO_RECOVERY",
                                                                 "Attempting planner service recovery for failed mission", {
                                                        "mission_id": mission_id,
                                                        "error": error_msg[:50]
                                                    }, service="PLANNER", routine="recovery_attempt")

                                                    # Restart planner service
                                                    recovery_success = self.attempt_service_recovery("planner")
                                                    if recovery_success:
                                                        self.log_event("INFO", "AUTO_RECOVERY",
                                                                     "Planner service restarted successfully", {
                                                            "mission_id": mission_id
                                                        }, service="PLANNER", routine="recovery_success")

                                except Exception as task_error:
                                    self.log_event("WARNING", "MISSION_FAILURE",
                                                 f"Could not get task details: {task_error}", {
                                        "mission_id": mission_id
                                    }, service="WATCHDOG", routine="task_details_error")

            except Exception as e:
                # Only log mission monitoring errors occasionally
                if not hasattr(self, 'mission_monitor_error_logged'):
                    self.log_event("WARNING", "MISSION_MONITOR", f"Mission monitoring issue: {e}", {},
                                 service="WATCHDOG", routine="mission_monitor_error")
                    self.mission_monitor_error_logged = True

        except Exception as e:
            emergency_log(f"🚨 Mission failure monitoring crashed: {e}")

    def monitor_phone_service_health(self):
        """📞 Enhanced phone service monitoring with crash detection and auto-recovery"""
        try:
            import httpx
            phone_port = get_service_port('phone')

            try:
                # Check if phone service is responsive
                response = httpx.get(f"http://{get_localhost()}:{phone_port}/health", timeout=5.0)

                if response.status_code == 200:
                    health_data = response.json()
                    agent_available = health_data.get("agent_available", False)
                    backend_ready = health_data.get("backend_ready", False)

                    # Reset failure count on successful health check
                    if "phone" in self.services:
                        self.services["phone"]["consecutive_failures"] = 0

                    # If phone agent is not initialized, force initialize it
                    if not agent_available:
                        self.log_event("WARNING", "PHONE_SERVICE",
                                     "Phone agent not initialized - forcing initialization", {
                            "agent_available": agent_available,
                            "backend_ready": backend_ready
                        }, service="PHONE", routine="health_recovery")

                        try:
                            # Force initialize the phone agent
                            init_response = httpx.post(f"http://{get_localhost()}:{phone_port}/force_initialize", timeout=15.0)
                            if init_response.status_code == 200:
                                init_result = init_response.json()
                                if init_result.get("status") == "success":
                                    self.log_event("INFO", "PHONE_SERVICE",
                                                 "Phone agent successfully re-initialized", {
                                        "agent_available": init_result.get("agent_available"),
                                        "ready": init_result.get("ready")
                                    }, service="PHONE", routine="health_recovery")
                                else:
                                    self.log_event("ERROR", "PHONE_SERVICE",
                                                 f"Phone agent initialization failed: {init_result.get('message')}", {},
                                                 service="PHONE", routine="health_recovery")
                            else:
                                self.log_event("ERROR", "PHONE_SERVICE",
                                             f"Phone agent initialization request failed: {init_response.status_code}", {},
                                             service="PHONE", routine="health_recovery")
                        except Exception as init_error:
                            self.log_event("ERROR", "PHONE_SERVICE",
                                         f"Failed to initialize phone agent: {init_error}", {},
                                         service="PHONE", routine="health_recovery")

                    # Update service status
                    if "phone" in self.services:
                        self.services["phone"]["last_status"] = "healthy" if agent_available else "agent_not_initialized"
                        self.services["phone"]["last_check"] = time.time()
                        self.services["phone"]["last_successful_check"] = time.time()

                else:
                    # Phone service returned error status
                    self.log_event("WARNING", "PHONE_SERVICE",
                                 f"Phone service health check failed: {response.status_code}", {},
                                 service="PHONE", routine="health_check")

                    if "phone" in self.services:
                        self.services["phone"]["last_status"] = f"error_{response.status_code}"
                        self.services["phone"]["failure_count"] += 1
                        self.services["phone"]["consecutive_failures"] = self.services["phone"].get("consecutive_failures", 0) + 1

                        # If multiple consecutive failures, attempt recovery
                        if self.services["phone"]["consecutive_failures"] >= 3:
                            self.log_event("ERROR", "PHONE_SERVICE",
                                         f"Phone service has {self.services['phone']['consecutive_failures']} consecutive failures - attempting recovery", {},
                                         service="PHONE", routine="failure_recovery")
                            self.attempt_phone_service_recovery()

            except httpx.ConnectError:
                # Phone service is not responding - critical failure
                self.log_event("CRITICAL", "PHONE_SERVICE",
                             "Phone service not responding - connection failed", {},
                             service="PHONE", routine="connection_failure")

                if "phone" in self.services:
                    self.services["phone"]["consecutive_failures"] = self.services["phone"].get("consecutive_failures", 0) + 1
                    self.services["phone"]["last_status"] = "connection_failed"

                # Immediate recovery attempt for connection failures
                self.attempt_phone_service_recovery()

            except httpx.TimeoutError:
                # Phone service is responding but too slowly
                self.log_event("WARNING", "PHONE_SERVICE",
                             "Phone service health check timed out", {},
                             service="PHONE", routine="timeout_error")

                if "phone" in self.services:
                    self.services["phone"]["consecutive_failures"] = self.services["phone"].get("consecutive_failures", 0) + 1
                    self.services["phone"]["last_status"] = "timeout"

            except Exception as health_error:
                self.log_event("ERROR", "PHONE_SERVICE",
                             f"Phone service health check error: {health_error}", {},
                             service="PHONE", routine="health_check_error")

                if "phone" in self.services:
                    self.services["phone"]["consecutive_failures"] = self.services["phone"].get("consecutive_failures", 0) + 1

        except Exception as e:
            emergency_log(f"🚨 Phone service health monitoring crashed: {e}")

    def attempt_phone_service_recovery(self):
        """🔧 Comprehensive phone service recovery procedure"""
        try:
            self.log_event("INFO", "PHONE_SERVICE",
                         "Starting comprehensive phone service recovery", {},
                         service="PHONE", routine="recovery_start")

            # Step 1: Try to restart the service process
            restart_success = self.attempt_service_recovery("phone")

            if restart_success:
                # Step 2: Wait for service to come online
                import time
                time.sleep(10)

                # Step 3: Verify service is responding
                phone_port = get_service_port('phone')
                try:
                    import httpx
                    response = httpx.get(f"http://{get_localhost()}:{phone_port}/health", timeout=10.0)

                    if response.status_code == 200:
                        # Step 4: Force initialization
                        init_response = httpx.post(f"http://{get_localhost()}:{phone_port}/force_initialize", timeout=15.0)

                        if init_response.status_code == 200:
                            init_result = init_response.json()
                            if init_result.get("status") == "success":
                                self.log_event("INFO", "PHONE_SERVICE",
                                             "Phone service recovery completed successfully", {
                                    "agent_available": init_result.get("agent_available"),
                                    "ready": init_result.get("ready")
                                }, service="PHONE", routine="recovery_success")

                                # Reset failure counters
                                if "phone" in self.services:
                                    self.services["phone"]["consecutive_failures"] = 0
                                    self.services["phone"]["last_status"] = "recovered"

                                return True
                            else:
                                self.log_event("ERROR", "PHONE_SERVICE",
                                             f"Phone service recovery failed at initialization: {init_result.get('message')}", {},
                                             service="PHONE", routine="recovery_failed")
                        else:
                            self.log_event("ERROR", "PHONE_SERVICE",
                                         f"Phone service recovery failed at initialization request: {init_response.status_code}", {},
                                         service="PHONE", routine="recovery_failed")
                    else:
                        self.log_event("ERROR", "PHONE_SERVICE",
                                     f"Phone service recovery failed - service not healthy: {response.status_code}", {},
                                     service="PHONE", routine="recovery_failed")

                except Exception as verify_error:
                    self.log_event("ERROR", "PHONE_SERVICE",
                                 f"Phone service recovery failed during verification: {verify_error}", {},
                                 service="PHONE", routine="recovery_failed")
            else:
                self.log_event("ERROR", "PHONE_SERVICE",
                             "Phone service recovery failed - could not restart service", {},
                             service="PHONE", routine="recovery_failed")

            return False

        except Exception as e:
            self.log_event("CRITICAL", "PHONE_SERVICE",
                         f"Phone service recovery procedure crashed: {e}", {},
                         service="PHONE", routine="recovery_crashed")
            emergency_log(f"🚨 Phone service recovery crashed: {e}")
            return False

    def enhanced_phone_monitoring(self):
        """🔍 Enhanced phone service monitoring with crash detection"""
        try:
            if "phone" not in self.services:
                return

            phone_service = self.services["phone"]
            current_time = time.time()

            # Check if phone service has been failing for too long
            last_successful = phone_service.get("last_successful_check", 0)
            if last_successful > 0 and (current_time - last_successful) > 300:  # 5 minutes without success
                consecutive_failures = phone_service.get("consecutive_failures", 0)

                if consecutive_failures >= 5:
                    self.log_event("CRITICAL", "PHONE_SERVICE",
                                 f"Phone service has been failing for {int((current_time - last_successful)/60)} minutes with {consecutive_failures} consecutive failures - forcing recovery", {},
                                 service="PHONE", routine="enhanced_monitoring")

                    # Force immediate recovery
                    self.attempt_phone_service_recovery()

            # Check for service process crashes
            try:
                import httpx
                phone_port = get_service_port('phone')

                # Quick ping test
                response = httpx.get(f"http://{get_localhost()}:{phone_port}/health", timeout=3.0)

                if response.status_code != 200:
                    phone_service["consecutive_failures"] = phone_service.get("consecutive_failures", 0) + 1

                    if phone_service["consecutive_failures"] >= 2:
                        self.log_event("WARNING", "PHONE_SERVICE",
                                     f"Phone service responding with error {response.status_code} - consecutive failures: {phone_service['consecutive_failures']}", {},
                                     service="PHONE", routine="enhanced_monitoring")

                        if phone_service["consecutive_failures"] >= 4:
                            self.attempt_phone_service_recovery()
                else:
                    # Service is responding - check if agent is initialized
                    health_data = response.json()
                    agent_available = health_data.get("agent_available", False)

                    if not agent_available:
                        self.log_event("WARNING", "PHONE_SERVICE",
                                     "Phone service responding but agent not initialized - forcing initialization", {},
                                     service="PHONE", routine="enhanced_monitoring")

                        try:
                            init_response = httpx.post(f"http://{get_localhost()}:{phone_port}/force_initialize", timeout=10.0)
                            if init_response.status_code == 200:
                                init_result = init_response.json()
                                if init_result.get("status") == "success":
                                    phone_service["consecutive_failures"] = 0
                                    phone_service["last_successful_check"] = current_time
                                    self.log_event("INFO", "PHONE_SERVICE",
                                                 "Phone agent successfully initialized via enhanced monitoring", {},
                                                 service="PHONE", routine="enhanced_monitoring")
                        except Exception as init_error:
                            self.log_event("ERROR", "PHONE_SERVICE",
                                         f"Enhanced monitoring failed to initialize phone agent: {init_error}", {},
                                         service="PHONE", routine="enhanced_monitoring")
                    else:
                        # All good - reset failure count
                        phone_service["consecutive_failures"] = 0
                        phone_service["last_successful_check"] = current_time

            except httpx.ConnectError:
                phone_service["consecutive_failures"] = phone_service.get("consecutive_failures", 0) + 1

                if phone_service["consecutive_failures"] >= 3:
                    self.log_event("CRITICAL", "PHONE_SERVICE",
                                 f"Enhanced monitoring detected phone service connection failure - consecutive failures: {phone_service['consecutive_failures']}", {},
                                 service="PHONE", routine="enhanced_monitoring")
                    self.attempt_phone_service_recovery()

            except Exception as monitor_error:
                self.log_event("ERROR", "PHONE_SERVICE",
                             f"Enhanced monitoring error: {monitor_error}", {},
                             service="PHONE", routine="enhanced_monitoring")

        except Exception as e:
            emergency_log(f"🚨 Enhanced phone monitoring crashed: {e}")

    def monitor_phone_circuit_breaker(self):
        """🔧 Monitor and automatically reset phone service circuit breaker when stuck"""
        try:
            import httpx
            phone_port = get_service_port('phone')

            try:
                # Check circuit breaker status
                response = httpx.get(f"http://{get_localhost()}:{phone_port}/circuit_breaker/status", timeout=5.0)

                if response.status_code == 200:
                    status_data = response.json()
                    if status_data.get("status") == "success":
                        circuit_status = status_data.get("circuit_breaker", {})
                        state = circuit_status.get("state", "UNKNOWN")
                        remaining_time = circuit_status.get("remaining_recovery_time", 0)
                        failure_count = circuit_status.get("failure_count", 0)

                        # Auto-reset circuit breaker if it's been open for too long
                        if state == "OPEN" and remaining_time > 240:  # More than 4 minutes remaining
                            self.log_event("WARNING", "PHONE_CIRCUIT_BREAKER",
                                         f"Circuit breaker has been open for too long - attempting reset", {
                                "state": state,
                                "remaining_time": remaining_time,
                                "failure_count": failure_count
                            }, service="PHONE", routine="circuit_breaker_monitoring")

                            # Attempt to reset circuit breaker
                            try:
                                reset_response = httpx.post(f"http://{get_localhost()}:{phone_port}/circuit_breaker/reset", timeout=10.0)
                                if reset_response.status_code == 200:
                                    reset_result = reset_response.json()
                                    if reset_result.get("status") == "success":
                                        self.log_event("INFO", "PHONE_CIRCUIT_BREAKER",
                                                     "Circuit breaker successfully reset by watchdog", {
                                            "previous_state": state,
                                            "new_state": "CLOSED"
                                        }, service="PHONE", routine="circuit_breaker_reset")
                                    else:
                                        self.log_event("ERROR", "PHONE_CIRCUIT_BREAKER",
                                                     f"Failed to reset circuit breaker: {reset_result.get('message')}", {},
                                                     service="PHONE", routine="circuit_breaker_reset")
                                else:
                                    self.log_event("ERROR", "PHONE_CIRCUIT_BREAKER",
                                                 f"Circuit breaker reset request failed: {reset_response.status_code}", {},
                                                 service="PHONE", routine="circuit_breaker_reset")
                            except Exception as reset_error:
                                self.log_event("ERROR", "PHONE_CIRCUIT_BREAKER",
                                             f"Exception during circuit breaker reset: {reset_error}", {},
                                             service="PHONE", routine="circuit_breaker_reset")

                        # Log circuit breaker state changes
                        if "phone" in self.services:
                            last_cb_state = self.services["phone"].get("last_circuit_breaker_state", "UNKNOWN")
                            if state != last_cb_state:
                                self.log_event("INFO", "PHONE_CIRCUIT_BREAKER",
                                             f"Circuit breaker state changed: {last_cb_state} → {state}", {
                                    "previous_state": last_cb_state,
                                    "new_state": state,
                                    "failure_count": failure_count
                                }, service="PHONE", routine="circuit_breaker_monitoring")
                                self.services["phone"]["last_circuit_breaker_state"] = state

            except httpx.ConnectError:
                self.log_event("WARNING", "PHONE_CIRCUIT_BREAKER",
                             "Cannot check circuit breaker - phone service not responding", {},
                             service="PHONE", routine="circuit_breaker_monitoring")

            except Exception as cb_error:
                self.log_event("ERROR", "PHONE_CIRCUIT_BREAKER",
                             f"Circuit breaker monitoring error: {cb_error}", {},
                             service="PHONE", routine="circuit_breaker_monitoring")

        except Exception as e:
            emergency_log(f"🚨 Phone circuit breaker monitoring crashed: {e}")

    def monitor_phone_system_health(self):
        """🔍 Monitor phone service system health and fix issues automatically"""
        try:
            import httpx
            phone_port = get_service_port('phone')

            try:
                # Check system health
                response = httpx.get(f"http://{get_localhost()}:{phone_port}/system_health", timeout=5.0)

                if response.status_code == 200:
                    health_data = response.json()
                    if health_data.get("status") == "success":
                        system_healthy = health_data.get("system_healthy", False)
                        can_make_calls = health_data.get("can_make_calls", False)

                        if not system_healthy:
                            self.log_event("WARNING", "PHONE_SYSTEM_HEALTH",
                                         "Phone service reports system unhealthy - investigating", {
                                "system_healthy": system_healthy,
                                "can_make_calls": can_make_calls
                            }, service="PHONE", routine="system_health_monitoring")

                            # Try to fix system health issues
                            self.attempt_phone_system_health_recovery()

                        elif not can_make_calls:
                            self.log_event("WARNING", "PHONE_SYSTEM_HEALTH",
                                         "Phone service cannot make calls - checking circuit breaker", {
                                "system_healthy": system_healthy,
                                "can_make_calls": can_make_calls
                            }, service="PHONE", routine="system_health_monitoring")

                            # This will be handled by circuit breaker monitoring

                        # Update service health tracking
                        if "phone" in self.services:
                            self.services["phone"]["last_system_health"] = system_healthy
                            self.services["phone"]["last_can_make_calls"] = can_make_calls

            except httpx.ConnectError:
                self.log_event("WARNING", "PHONE_SYSTEM_HEALTH",
                             "Cannot check system health - phone service not responding", {},
                             service="PHONE", routine="system_health_monitoring")

            except Exception as health_error:
                self.log_event("ERROR", "PHONE_SYSTEM_HEALTH",
                             f"System health monitoring error: {health_error}", {},
                             service="PHONE", routine="system_health_monitoring")

        except Exception as e:
            emergency_log(f"🚨 Phone system health monitoring crashed: {e}")

    def attempt_phone_system_health_recovery(self):
        """🔧 Attempt to recover phone service system health"""
        try:
            self.log_event("INFO", "PHONE_SYSTEM_HEALTH",
                         "Attempting phone service system health recovery", {},
                         service="PHONE", routine="system_health_recovery")

            # Step 1: Force re-initialize phone agent
            import httpx
            phone_port = get_service_port('phone')

            try:
                init_response = httpx.post(f"http://{get_localhost()}:{phone_port}/force_initialize", timeout=15.0)
                if init_response.status_code == 200:
                    init_result = init_response.json()
                    if init_result.get("status") == "success":
                        self.log_event("INFO", "PHONE_SYSTEM_HEALTH",
                                     "Phone agent re-initialization successful", {
                            "agent_available": init_result.get("agent_available"),
                            "ready": init_result.get("ready")
                        }, service="PHONE", routine="system_health_recovery")

                        # Step 2: Reset circuit breaker after successful initialization
                        reset_response = httpx.post(f"http://{get_localhost()}:{phone_port}/circuit_breaker/reset", timeout=10.0)
                        if reset_response.status_code == 200:
                            self.log_event("INFO", "PHONE_SYSTEM_HEALTH",
                                         "Circuit breaker reset after health recovery", {},
                                         service="PHONE", routine="system_health_recovery")

                        return True
                    else:
                        self.log_event("ERROR", "PHONE_SYSTEM_HEALTH",
                                     f"Phone agent re-initialization failed: {init_result.get('message')}", {},
                                     service="PHONE", routine="system_health_recovery")
                else:
                    self.log_event("ERROR", "PHONE_SYSTEM_HEALTH",
                                 f"Phone agent re-initialization request failed: {init_response.status_code}", {},
                                 service="PHONE", routine="system_health_recovery")

            except Exception as recovery_error:
                self.log_event("ERROR", "PHONE_SYSTEM_HEALTH",
                             f"System health recovery failed: {recovery_error}", {},
                             service="PHONE", routine="system_health_recovery")

            return False

        except Exception as e:
            self.log_event("CRITICAL", "PHONE_SYSTEM_HEALTH",
                         f"System health recovery crashed: {e}", {},
                         service="PHONE", routine="system_health_recovery")
            emergency_log(f"🚨 Phone system health recovery crashed: {e}")
            return False

    def monitor_phone_task_failures(self):
        """📞 Monitor phone task failures and automatically fix common issues"""
        try:
            import httpx
            backend_port = get_service_port('backend')

            try:
                # Get recent missions with failed phone tasks
                response = httpx.get(f"http://{get_localhost()}:{backend_port}/api/v1/missions", timeout=10.0)

                if response.status_code == 200:
                    missions = response.json()

                    for mission in missions[:5]:  # Check last 5 missions
                        if mission.get("progress", {}).get("failed", 0) > 0:
                            mission_id = mission.get("mission_id")

                            # Get failed tasks for this mission
                            tasks_response = httpx.get(f"http://{get_localhost()}:{backend_port}/api/v1/internal/missions/{mission_id}/tasks", timeout=10.0)

                            if tasks_response.status_code == 200:
                                tasks = tasks_response.json()

                                for task in tasks:
                                    if task.get("status") == "failed" and task.get("task_type") == "phone_call":
                                        error_message = task.get("error_message", "")
                                        task_id = task.get("task_id", "")

                                        # Handle specific error types
                                        if "Circuit breaker open" in error_message:
                                            self.log_event("INFO", "PHONE_TASK_RECOVERY",
                                                         f"Detected circuit breaker failure in task {task_id} - resetting circuit breaker", {
                                                "mission_id": mission_id,
                                                "task_id": task_id,
                                                "error": error_message
                                            }, service="PHONE", routine="task_failure_recovery")

                                            # Reset circuit breaker
                                            self.reset_phone_circuit_breaker()

                                        elif "System health check failed" in error_message:
                                            self.log_event("INFO", "PHONE_TASK_RECOVERY",
                                                         f"Detected health check failure in task {task_id} - attempting system recovery", {
                                                "mission_id": mission_id,
                                                "task_id": task_id,
                                                "error": error_message
                                            }, service="PHONE", routine="task_failure_recovery")

                                            # Attempt system health recovery
                                            self.attempt_phone_system_health_recovery()

                                        elif "Invalid phone number format" in error_message:
                                            self.log_event("WARNING", "PHONE_TASK_RECOVERY",
                                                         f"Detected invalid phone number in task {task_id} - this requires user input", {
                                                "mission_id": mission_id,
                                                "task_id": task_id,
                                                "error": error_message
                                            }, service="PHONE", routine="task_failure_recovery")

                                            # This error requires user intervention - log for awareness

                                        elif "preventive_abort" in str(task.get("result", {})):
                                            self.log_event("INFO", "PHONE_TASK_RECOVERY",
                                                         f"Detected preventive abort in task {task_id} - system was protecting user from error call", {
                                                "mission_id": mission_id,
                                                "task_id": task_id,
                                                "error": error_message
                                            }, service="PHONE", routine="task_failure_recovery")

                                            # Preventive aborts are good - system working as intended

            except httpx.ConnectError:
                self.log_event("WARNING", "PHONE_TASK_RECOVERY",
                             "Cannot check phone task failures - backend not responding", {},
                             service="PHONE", routine="task_failure_monitoring")

            except Exception as task_error:
                self.log_event("ERROR", "PHONE_TASK_RECOVERY",
                             f"Phone task failure monitoring error: {task_error}", {},
                             service="PHONE", routine="task_failure_monitoring")

        except Exception as e:
            emergency_log(f"🚨 Phone task failure monitoring crashed: {e}")

    def reset_phone_circuit_breaker(self):
        """🔧 Reset phone service circuit breaker"""
        try:
            import httpx
            phone_port = get_service_port('phone')

            response = httpx.post(f"http://{get_localhost()}:{phone_port}/circuit_breaker/reset", timeout=10.0)
            if response.status_code == 200:
                result = response.json()
                if result.get("status") == "success":
                    self.log_event("INFO", "PHONE_CIRCUIT_BREAKER",
                                 "Circuit breaker reset successfully by watchdog", {},
                                 service="PHONE", routine="circuit_breaker_reset")
                    return True
                else:
                    self.log_event("ERROR", "PHONE_CIRCUIT_BREAKER",
                                 f"Circuit breaker reset failed: {result.get('message')}", {},
                                 service="PHONE", routine="circuit_breaker_reset")
            else:
                self.log_event("ERROR", "PHONE_CIRCUIT_BREAKER",
                             f"Circuit breaker reset request failed: {response.status_code}", {},
                             service="PHONE", routine="circuit_breaker_reset")

        except Exception as e:
            self.log_event("ERROR", "PHONE_CIRCUIT_BREAKER",
                         f"Failed to reset circuit breaker: {e}", {},
                         service="PHONE", routine="circuit_breaker_reset")

        return False

async def main():
    """🛡️ CRASH-RESISTANT Main entry point - NEVER gives up"""
    watchdog = None
    restart_count = 0
    max_restarts = 100  # Allow many restarts

    while restart_count < max_restarts:
        try:
            emergency_log(f"🛡️ WATCHDOG starting (attempt #{restart_count + 1})")

            # Create watchdog instance
            watchdog = DeepplicaWatchdog()

            # Set global reference for API endpoints
            global watchdog_instance
            watchdog_instance = watchdog

            # Start FastAPI server in background thread for receiving logs
            def start_log_server():
                try:
                    # Ensure watchdog port is free using centralized port manager
                    watchdog_port = ensure_service_port_free("WATCHDOG", force=True)
                    uvicorn.run(app, host=get_service_host(), port=watchdog_port, log_level="critical", access_log=False)
                except Exception as e:
                    emergency_log(f"🚨 WATCHDOG LOG SERVER ERROR: {e}")
                    # Fallback to hardcoded port if port manager fails
                    try:
                        uvicorn.run(app, host=get_service_host(), port=get_service_port("watchdog"), log_level="critical", access_log=False)
                    except Exception as e2:
                        emergency_log(f"🚨 WATCHDOG FALLBACK SERVER ERROR: {e2}")

            log_server_thread = threading.Thread(target=start_log_server, daemon=True)
            log_server_thread.start()

            try:
                watchdog_port = get_service_port("WATCHDOG")
                emergency_log(f"🌐 WATCHDOG log server started on port {watchdog_port}")
            except:
                emergency_log(f"🌐 WATCHDOG log server started on port {get_service_port('watchdog')} (fallback)")

            # Run monitoring loop
            await watchdog.run_monitoring_loop()

            # If we get here, monitoring stopped normally
            emergency_log("🛡️ WATCHDOG monitoring loop ended normally")
            break

        except KeyboardInterrupt:
            emergency_log("🛡️ WATCHDOG interrupted by user - shutting down gracefully")
            break
        except SystemExit:
            emergency_log("🛡️ WATCHDOG system exit requested - shutting down")
            break
        except Exception as e:
            restart_count += 1
            emergency_log(f"🚨 WATCHDOG CRASHED (restart #{restart_count}): {e}")

            # Log full traceback to emergency log
            try:
                import traceback
                tb = traceback.format_exc()
                emergency_log(f"🚨 WATCHDOG CRASH TRACEBACK:\n{tb}")
            except:
                pass

            # If we have too many restarts, wait longer
            if restart_count > 10:
                emergency_log(f"🚨 WATCHDOG high restart count ({restart_count}) - waiting 60 seconds")
                try:
                    await asyncio.sleep(60)
                except:
                    time.sleep(60)
            else:
                # Brief pause before restart
                try:
                    await asyncio.sleep(5)
                except:
                    time.sleep(5)

    if restart_count >= max_restarts:
        emergency_log(f"🚨 WATCHDOG GIVING UP after {max_restarts} restart attempts")
    else:
        emergency_log("🛡️ WATCHDOG shutdown complete")


def run_watchdog():
    """🛡️ Ultimate crash-resistant wrapper"""
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        emergency_log("🛡️ WATCHDOG final KeyboardInterrupt")
    except Exception as e:
        emergency_log(f"🚨 WATCHDOG FINAL CRASH: {e}")
        # Even if asyncio fails, try one more time with basic loop
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(main())
        except Exception as e2:
            emergency_log(f"🚨 WATCHDOG ULTIMATE FAILURE: {e2}")


if __name__ == "__main__":
    # Set terminal title and clear identification banner - FORCE RENAME EVEN IF ALREADY NAMED
    service_name = os.getenv("SERVICE_NAME", "WATCHDOG")

    # Multiple methods to ensure terminal gets renamed
    print(f"\033]0;🐕 {service_name}\007", end="")  # xterm title
    print(f"\033]2;🐕 {service_name}\007", end="")  # window title
    print(f"\033]1;🐕 {service_name}\007", end="")  # icon title

    # Also try VS Code specific terminal naming
    import sys
    if hasattr(sys, 'ps1') or hasattr(sys, 'ps2'):
        try:
            import os
            os.system(f'echo -ne "\\033]0;🐕 {service_name}\\007"')
        except:
            pass

    print("\n" + "="*80)
    print(f"🐕 {service_name} TERMINAL")
    print("="*80 + "\n")

    run_watchdog()
