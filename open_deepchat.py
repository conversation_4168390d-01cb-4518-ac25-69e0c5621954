#!/usr/bin/env python3
"""
🌐 Open DeepChat Browser
Opens the DeepChat web interface in the default browser
"""

import webbrowser
import time
import requests
import sys
import os
from shared.port_manager import get_service_port

def check_web_chat_ready():
    """Check if web chat service is ready"""
    try:
        web_chat_port = get_service_port("web-chat")
        url = f"http://localhost:{web_chat_port}"
        
        print(f"🔍 Checking if Web Chat is ready at {url}...")
        
        for attempt in range(30):  # Wait up to 30 seconds
            try:
                response = requests.get(url, timeout=2)
                if response.status_code == 200:
                    print(f"✅ Web Chat is ready!")
                    return url
            except requests.exceptions.RequestException:
                pass
            
            print(f"⏳ Waiting for Web Chat... (attempt {attempt + 1}/30)")
            time.sleep(1)
        
        print("⚠️ Web Chat not ready, but opening browser anyway...")
        return url
        
    except Exception as e:
        print(f"❌ Error checking Web Chat: {e}")
        # Fallback to default port
        from shared.port_manager import get_service_port
        return f"http://localhost:{get_service_port('web-chat')}"

def main():
    """Main function"""
    print("🌐 Opening DeepChat Browser...")
    print("=" * 50)
    
    try:
        # Check if web chat is ready
        url = check_web_chat_ready()
        
        # Open browser
        print(f"🚀 Opening browser to: {url}")
        webbrowser.open(url)
        
        print("✅ Browser opened successfully!")
        print("💡 If the page doesn't load, make sure Web Chat service is running")
        print("🎯 Use '🌐 Web Chat Service' in VS Code Debug Panel to start it")
        
    except Exception as e:
        print(f"❌ Error opening browser: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
