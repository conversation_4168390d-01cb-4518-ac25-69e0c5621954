#!/usr/bin/env python3
"""
🔧 DEEPLICA PORT CONFLICT RESOLUTION TOOL
Comprehensive port conflict detection and resolution
"""

import subprocess
import time
import sys
import os
import json
from pathlib import Path

# Add shared directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'shared'))

def run_command(cmd, timeout=10):
    """Run a command and return result"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout)
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Command timed out"
    except Exception as e:
        return -1, "", str(e)

def kill_processes_on_port(port):
    """Kill all processes on a specific port"""
    print(f"🔧 Killing all processes on port {port}...")
    
    # Get processes on port
    returncode, stdout, stderr = run_command(f"lsof -ti :{port}")
    
    if returncode == 0 and stdout.strip():
        pids = stdout.strip().split('\n')
        for pid in pids:
            if pid.strip():
                print(f"  🎯 Killing PID {pid}")
                run_command(f"kill -9 {pid}")
        
        # Wait for processes to die
        time.sleep(2)
        
        # Check if port is now free
        returncode, stdout, stderr = run_command(f"lsof -i :{port}")
        if returncode != 0:
            print(f"  ✅ Port {port} is now free")
            return True
        else:
            print(f"  ⚠️ Port {port} still has processes:")
            print(f"    {stdout}")
            return False
    else:
        print(f"  ✅ No processes found on port {port}")
        return True

def kill_all_deeplica_processes():
    """Kill all DEEPLICA processes"""
    print("🧹 Killing all DEEPLICA processes...")
    
    # Kill by process name patterns
    patterns = [
        "DEEPLICA",
        "bulletproof_phone_agent",
        "phone/app/main.py",
        "agents/phone",
        "uvicorn.*8004"
    ]
    
    for pattern in patterns:
        print(f"  🎯 Killing processes matching: {pattern}")
        run_command(f"pkill -f '{pattern}'")
    
    # Wait for processes to die
    time.sleep(3)
    
    print("✅ DEEPLICA process cleanup completed")

def clear_port_leases():
    """Clear all port leases"""
    print("🧹 Clearing port leases...")
    
    lease_file = Path("shared/port_leases.json")
    if lease_file.exists():
        try:
            lease_file.unlink()
            print("  ✅ Removed port_leases.json")
        except Exception as e:
            print(f"  ⚠️ Error removing port_leases.json: {e}")
    else:
        print("  ✅ No port_leases.json found")

def check_port_conflicts():
    """Check for port conflicts"""
    print("🔍 Checking for port conflicts...")
    
    # DEEPLICA ports
    ports_to_check = [8001, 8002, 8003, 8004, 8005, 8888]
    
    conflicts = []
    
    for port in ports_to_check:
        returncode, stdout, stderr = run_command(f"lsof -i :{port}")
        if returncode == 0 and stdout.strip():
            print(f"  ⚠️ Port {port} is in use:")
            lines = stdout.strip().split('\n')[1:]  # Skip header
            for line in lines:
                parts = line.split()
                if len(parts) >= 2:
                    print(f"    PID {parts[1]}: {parts[0]}")
                    conflicts.append({
                        'port': port,
                        'pid': parts[1],
                        'name': parts[0]
                    })
        else:
            print(f"  ✅ Port {port} is free")
    
    return conflicts

def main():
    """Main conflict resolution function"""
    print("🔧 DEEPLICA PORT CONFLICT RESOLUTION")
    print("=" * 50)
    
    # Step 1: Kill all DEEPLICA processes
    kill_all_deeplica_processes()
    
    # Step 2: Clear port leases
    clear_port_leases()
    
    # Step 3: Check for conflicts
    conflicts = check_port_conflicts()
    
    # Step 4: Resolve conflicts
    if conflicts:
        print(f"\n🚨 Found {len(conflicts)} port conflicts - resolving...")
        
        # Group by port
        ports_with_conflicts = set(conflict['port'] for conflict in conflicts)
        
        for port in ports_with_conflicts:
            kill_processes_on_port(port)
    else:
        print("\n✅ No port conflicts detected")
    
    # Step 5: Final verification
    print("\n🔍 Final verification...")
    final_conflicts = check_port_conflicts()
    
    if final_conflicts:
        print(f"❌ Still have {len(final_conflicts)} conflicts after cleanup")
        return False
    else:
        print("✅ All port conflicts resolved!")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
