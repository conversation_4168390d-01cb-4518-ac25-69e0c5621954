#!/bin/bash
# =============================================================================
# DEEPLICA PORT HELPER FUNCTIONS
# =============================================================================
# Helper functions for shell scripts to get dynamic ports from port manager
# =============================================================================

# Get service port from port manager
get_service_port() {
    local service_name="$1"
    python3 -c "from shared.port_manager import get_service_port; print(get_service_port('$service_name'))"
}

# Get backend port
get_backend_port() {
    get_service_port "backend"
}

# Get dispatcher port
get_dispatcher_port() {
    get_service_port "dispatcher"
}

# Get dialogue agent port
get_dialogue_port() {
    get_service_port "dialogue"
}

# Get planner agent port
get_planner_port() {
    get_service_port "planner"
}

# Get phone agent port
get_phone_port() {
    get_service_port "phone"
}

# Get watchdog port
get_watchdog_port() {
    get_service_port "watchdog"
}

# Get web chat port
get_webchat_port() {
    get_service_port "web-chat"
}

# Get ngrok tunnel port
get_ngrok_port() {
    get_service_port "ngrok-tunnel"
}

# Get webhook server port
get_webhook_port() {
    get_service_port "webhook-server"
}

# Get twilio echo bot port
get_twilio_port() {
    get_service_port "twilio-echo-bot"
}
