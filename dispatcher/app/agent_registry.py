"""
Agent Registry - Maps agent types to service URLs.
Hard-coded routing logic for task types to agents.
"""

import os
import sys
from typing import Dict, Optional
from shared.unified_logging import get_dispatcher_logger

# Initialize unified logger
logger = get_dispatcher_logger()

# Add project root to path for shared models
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared_models import AgentInfo
from shared.port_manager import get_service_port, get_localhost
from shared.api_manager import get_service_url, get_system_config


class AgentRegistry:
    """
    Simple registry that maps agent types to service URLs.
    Handles environment-specific configuration.
    """
    
    def __init__(self):
        self.agents = self._load_agent_configuration()
        self.task_routing = self._setup_task_routing()
        logger.info(f"🗂️ Agent registry initialized with {len(self.agents)} agents", module="DISPATCHER", routine="__init__")
    
    def _load_agent_configuration(self) -> Dict[str, AgentInfo]:
        """Load agent service URLs based on environment"""
        environment = os.getenv("ENVIRONMENT", "local")

        if environment == "local":
            # Use API manager for service URLs
            dialogue_url = get_service_url("dialogue")
            planner_url = get_service_url("planner")
            phone_url = get_service_url("phone")

            logger.info(f"Dialogue: {dialogue_url}, Planner: {planner_url}, Phone: {phone_url}", module="DISPATCHER:_load_agent_configuration", routine="SYSTEM | 🔧 Agent URLs")

            return {
                "dialogue": AgentInfo(
                    agent_type="dialogue",
                    base_url=dialogue_url
                ),
                "planner": AgentInfo(
                    agent_type="planner",
                    base_url=planner_url
                ),
                "phone": AgentInfo(
                    agent_type="phone",
                    base_url=phone_url
                )
            }
        else:  # GCP Cloud Run
            return {
                "dialogue": AgentInfo(
                    agent_type="dialogue",
                    base_url=get_service_url("dialogue")
                ),
                "planner": AgentInfo(
                    agent_type="planner",
                    base_url=get_service_url("planner")
                ),
                "phone": AgentInfo(
                    agent_type="phone",
                    base_url=get_service_url("phone")
                )
            }
    
    def _setup_task_routing(self) -> Dict[str, str]:
        """Hard-coded routing from task types to agent types"""
        return {
            # Planning tasks
            "planning": "planner",
            "mission_planning": "planner",
            
            # Dialogue tasks  
            "dialogue": "dialogue",
            "user_interaction": "dialogue",
            "conversation": "dialogue",
            "information_gathering": "dialogue",
            
            # Phone tasks
            "phone_call": "phone",

            # Future agents (commented for now)
            # "guardian_check": "guardian",
            # "email_send": "email"
        }
    
    def get_agent_for_task(self, task_type: str) -> Optional[str]:
        """Get the agent type that should handle this task type"""
        agent_type = self.task_routing.get(task_type)
        if not agent_type:
            logger.warning(f"⚠️ No agent found for task type: {task_type}", module="DISPATCHER", routine="get_agent_for_task")
        return agent_type
    
    def get_agent_info(self, agent_type: str) -> Optional[AgentInfo]:
        """Get agent service information"""
        return self.agents.get(agent_type)
    
    def get_agent_url(self, agent_type: str) -> Optional[str]:
        """Get agent service base URL"""
        agent_info = self.get_agent_info(agent_type)
        return agent_info.base_url if agent_info else None
    
    def get_execute_url(self, agent_type: str) -> Optional[str]:
        """Get full execute endpoint URL for agent"""
        agent_info = self.get_agent_info(agent_type)
        if not agent_info:
            return None
        execute_url = f"{agent_info.base_url}{agent_info.execute_endpoint}"
        logger.debug(f"> base_url: {agent_info.base_url}, execute_url: {execute_url}", module="DISPATCHER:get_execute_url", routine=f"TASK | 🔍 Agent '{agent_type}'")
        return execute_url
    
    def list_available_agents(self) -> Dict[str, str]:
        """List all available agents and their URLs"""
        return {
            agent_type: info.base_url 
            for agent_type, info in self.agents.items()
        }
    
    def is_agent_available(self, agent_type: str) -> bool:
        """Check if agent type is available"""
        return agent_type in self.agents
