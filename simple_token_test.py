#!/usr/bin/env python3
"""
🔍 SIMPLE TOKEN GENERATION TEST

This script tests the exact token generation logic to identify the issue
between server-side and client-side token generation.
"""

import hashlib
import hmac
import base64
import time

# Same secret as in the main system
TOKEN_SECRET = "deeplica-tab-secret-key-2025"

def test_browser_fingerprint():
    """Test browser fingerprint generation"""
    # Simulate browser headers
    user_agent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
    accept_language = "en-US,en;q=0.9"
    accept_encoding = "gzip, deflate, br"
    
    # Method 1: Simple fingerprint (like in debug test)
    fingerprint_data_simple = f"{user_agent}:{accept_language}"
    fingerprint_simple = hashlib.sha256(fingerprint_data_simple.encode()).hexdigest()[:16]
    
    # Method 2: Complex fingerprint (like in main system)
    fingerprint_data_complex = f"{user_agent}:{accept_language}:{accept_encoding}"
    fingerprint_complex = hashlib.sha256(fingerprint_data_complex.encode()).hexdigest()[:16]
    
    print("🔍 BROWSER FINGERPRINT TEST")
    print("=" * 50)
    print(f"User-Agent: {user_agent}")
    print(f"Accept-Language: {accept_language}")
    print(f"Accept-Encoding: {accept_encoding}")
    print()
    print(f"Simple Fingerprint Data: {fingerprint_data_simple}")
    print(f"Simple Fingerprint: {fingerprint_simple}")
    print()
    print(f"Complex Fingerprint Data: {fingerprint_data_complex}")
    print(f"Complex Fingerprint: {fingerprint_complex}")
    print()
    
    return fingerprint_simple, fingerprint_complex

def test_token_generation(username, fingerprint, timestamp=None):
    """Test token generation with debugging"""
    if timestamp is None:
        timestamp = int(time.time())
    
    print(f"🔐 TOKEN GENERATION TEST")
    print("=" * 50)
    print(f"Username: {username}")
    print(f"Fingerprint: {fingerprint}")
    print(f"Timestamp: {timestamp}")
    print(f"Secret: {TOKEN_SECRET}")
    print()
    
    # Step 1: Create token data
    token_data = f"{username}:{fingerprint}:{timestamp}"
    print(f"Step 1 - Token Data: {token_data}")
    
    # Step 2: Generate HMAC signature
    signature = hmac.new(
        TOKEN_SECRET.encode(),
        token_data.encode(),
        hashlib.sha256
    ).hexdigest()
    print(f"Step 2 - HMAC Signature: {signature}")
    
    # Step 3: Combine data and signature
    full_token_data = f"{token_data}:{signature}"
    print(f"Step 3 - Full Token Data: {full_token_data}")
    
    # Step 4: Base64 encode
    token_base64 = base64.urlsafe_b64encode(full_token_data.encode()).decode()
    print(f"Step 4 - Base64 Token: {token_base64}")
    print()
    
    return {
        "token_data": token_data,
        "signature": signature,
        "full_token_data": full_token_data,
        "token_base64": token_base64
    }

def test_token_verification(token_base64, expected_username, expected_fingerprint):
    """Test token verification with debugging"""
    print(f"🔍 TOKEN VERIFICATION TEST")
    print("=" * 50)
    print(f"Token to verify: {token_base64}")
    print(f"Expected username: {expected_username}")
    print(f"Expected fingerprint: {expected_fingerprint}")
    print()
    
    try:
        # Step 1: Decode the token
        full_token_data = base64.urlsafe_b64decode(token_base64.encode()).decode()
        print(f"Step 1 - Decoded Token Data: {full_token_data}")
        
        # Step 2: Split into components
        parts = full_token_data.split(':')
        print(f"Step 2 - Token Parts: {parts}")
        print(f"Step 2 - Number of parts: {len(parts)}")
        
        if len(parts) != 4:
            print(f"❌ ERROR: Expected 4 parts, got {len(parts)}")
            return False
        
        token_username, token_fingerprint, token_timestamp, token_signature = parts
        print(f"Step 3 - Parsed Username: {token_username}")
        print(f"Step 3 - Parsed Fingerprint: {token_fingerprint}")
        print(f"Step 3 - Parsed Timestamp: {token_timestamp}")
        print(f"Step 3 - Parsed Signature: {token_signature}")
        
        # Step 4: Recreate expected signature
        expected_data = f"{token_username}:{token_fingerprint}:{token_timestamp}"
        expected_signature = hmac.new(
            TOKEN_SECRET.encode(),
            expected_data.encode(),
            hashlib.sha256
        ).hexdigest()
        print(f"Step 4 - Expected Data: {expected_data}")
        print(f"Step 4 - Expected Signature: {expected_signature}")
        
        # Step 5: Verify signature
        signature_valid = hmac.compare_digest(token_signature, expected_signature)
        print(f"Step 5 - Signature Valid: {signature_valid}")
        
        # Step 6: Verify data matches
        username_match = token_username == expected_username
        fingerprint_match = token_fingerprint == expected_fingerprint
        print(f"Step 6 - Username Match: {username_match}")
        print(f"Step 6 - Fingerprint Match: {fingerprint_match}")
        
        # Step 7: Check token age
        token_age = time.time() - float(token_timestamp)
        token_expired = token_age > 3600  # 1 hour
        print(f"Step 7 - Token Age: {token_age:.2f} seconds")
        print(f"Step 7 - Token Expired: {token_expired}")
        
        # Final result
        valid = signature_valid and username_match and fingerprint_match and not token_expired
        print(f"Final Result: {'✅ VALID' if valid else '❌ INVALID'}")
        print()
        
        return valid
        
    except Exception as e:
        print(f"❌ ERROR during verification: {e}")
        return False

def test_javascript_compatibility():
    """Test JavaScript compatibility"""
    print(f"🧪 JAVASCRIPT COMPATIBILITY TEST")
    print("=" * 50)
    
    # Test data
    username = "admin"
    fingerprint = "test123fingerprint"
    timestamp = 1752265000  # Fixed timestamp for consistency
    
    # Generate token
    token_info = test_token_generation(username, fingerprint, timestamp)
    
    # Create JavaScript equivalent code
    js_code = f"""
// JavaScript equivalent (for browser console testing)
const TOKEN_SECRET = "{TOKEN_SECRET}";
const username = "{username}";
const fingerprint = "{fingerprint}";
const timestamp = {timestamp};

// Generate token data
const tokenData = `${{username}}:${{fingerprint}}:${{timestamp}}`;
console.log('Token Data:', tokenData);

// Generate HMAC signature (requires crypto-js library)
// const signature = CryptoJS.HmacSHA256(tokenData, TOKEN_SECRET).toString();
// console.log('Signature:', signature);

// Create full token
// const fullTokenData = `${{tokenData}}:${{signature}}`;
// const tokenBase64 = btoa(fullTokenData);
// console.log('Base64 Token:', tokenBase64);
"""
    
    print("JavaScript code for browser console:")
    print(js_code)
    
    return token_info

def main():
    """Run all token tests"""
    print("🔒 DEEPLICA TOKEN GENERATION DEBUG TESTS")
    print("=" * 80)
    print()
    
    # Test 1: Browser fingerprint generation
    fingerprint_simple, fingerprint_complex = test_browser_fingerprint()
    
    # Test 2: Token generation with simple fingerprint
    print()
    token_info_simple = test_token_generation("admin", fingerprint_simple)
    
    # Test 3: Token verification with simple fingerprint
    print()
    verification_result = test_token_verification(
        token_info_simple["token_base64"], 
        "admin", 
        fingerprint_simple
    )
    
    # Test 4: Token generation with complex fingerprint
    print()
    token_info_complex = test_token_generation("admin", fingerprint_complex)
    
    # Test 5: Cross-verification (should fail)
    print()
    print("🔄 CROSS-VERIFICATION TEST (should fail)")
    print("=" * 50)
    cross_verification = test_token_verification(
        token_info_simple["token_base64"], 
        "admin", 
        fingerprint_complex  # Different fingerprint
    )
    
    # Test 6: JavaScript compatibility
    print()
    test_javascript_compatibility()
    
    # Summary
    print()
    print("📊 TEST SUMMARY")
    print("=" * 50)
    print(f"Simple fingerprint verification: {'✅ PASS' if verification_result else '❌ FAIL'}")
    print(f"Cross-verification (should fail): {'✅ PASS' if not cross_verification else '❌ FAIL'}")
    print()
    print("🔧 DEBUGGING RECOMMENDATIONS:")
    if verification_result:
        print("✅ Token generation and verification logic is working correctly")
        print("🔍 Issue might be in browser fingerprint generation differences")
        print("🔍 Check if server and client are using same fingerprint method")
    else:
        print("❌ Token generation or verification has issues")
        print("🔍 Check secret key consistency")
        print("🔍 Check token data format")
        print("🔍 Check HMAC signature generation")

if __name__ == "__main__":
    main()
