#!/usr/bin/env python3
"""
Start DEEPLICA with Mock Database Mode
This script starts DEEPLICA with the database in mock mode for development
when MongoDB Atlas is not accessible.
"""

import os
import sys
import subprocess
import time
from pathlib import Path
from shared.port_manager import get_service_port

def update_env_for_mock_db():
    """Update .env file to enable mock database mode"""
    env_file = Path(".env")
    
    if not env_file.exists():
        print("❌ .env file not found")
        return False
    
    # Read current .env content
    with open(env_file, 'r') as f:
        lines = f.readlines()
    
    # Update or add USE_MOCK_DATABASE setting
    updated = False
    for i, line in enumerate(lines):
        if line.startswith("USE_MOCK_DATABASE="):
            lines[i] = "USE_MOCK_DATABASE=true\n"
            updated = True
            break
    
    if not updated:
        # Add the setting
        lines.append("\n# Development Database Setting\n")
        lines.append("USE_MOCK_DATABASE=true\n")
    
    # Write back to file
    with open(env_file, 'w') as f:
        f.writelines(lines)
    
    print("✅ Updated .env to enable mock database mode")
    return True

def start_backend_with_mock_db():
    """Start the backend service with mock database"""
    print("🚀 Starting DEEPLICA Backend with Mock Database...")
    
    # Set environment variable for this session
    env = os.environ.copy()
    env["USE_MOCK_DATABASE"] = "true"
    env["SERVICE_NAME"] = "BACKEND-API"
    
    try:
        # Start backend service
        process = subprocess.Popen(
            [sys.executable, "backend/app/main.py"],
            env=env,
            cwd=os.getcwd()
        )
        
        print(f"✅ Backend started with PID: {process.pid}")
        print("🎭 Using MOCK DATABASE MODE - all data will be simulated")
        print(f"🔗 Backend will be available at: http://localhost:{get_service_port('backend')}")
        print(f"📊 Health check: http://localhost:{get_service_port('backend')}/health")
        
        return process
        
    except Exception as e:
        print(f"❌ Failed to start backend: {e}")
        return None

def check_backend_health():
    """Check if backend is responding"""
    import requests
    
    for attempt in range(10):
        try:
            response = requests.get(f"http://localhost:{get_service_port('backend')}/health", timeout=2)
            if response.status_code == 200:
                print("✅ Backend is healthy and responding")
                return True
        except:
            pass
        
        print(f"⏳ Waiting for backend to start... (attempt {attempt + 1}/10)")
        time.sleep(2)
    
    print("⚠️ Backend health check failed - but it might still be starting")
    return False

def main():
    """Main function to start DEEPLICA with mock database"""
    print("🎭 DEEPLICA MOCK DATABASE STARTUP")
    print("=" * 50)
    print("This will start DEEPLICA with a simulated database")
    print("Perfect for development when MongoDB is not available")
    print()
    
    # Update .env file
    if not update_env_for_mock_db():
        return 1
    
    # Start backend
    backend_process = start_backend_with_mock_db()
    if not backend_process:
        return 1
    
    print()
    print("⏳ Waiting for backend to initialize...")
    time.sleep(5)
    
    # Check health
    check_backend_health()
    
    print()
    print("🎉 DEEPLICA Backend started successfully!")
    print("📝 Next steps:")
    print("   1. Start other services (dispatcher, web chat, etc.)")
    print("   2. All database operations will be simulated")
    print("   3. Data will not persist between restarts")
    print("   4. Perfect for development and testing")
    print()
    print("🛑 To stop: Press Ctrl+C or kill the backend process")
    
    try:
        # Wait for user to stop
        backend_process.wait()
    except KeyboardInterrupt:
        print("\n⏹️ Stopping backend...")
        backend_process.terminate()
        backend_process.wait()
        print("✅ Backend stopped")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
