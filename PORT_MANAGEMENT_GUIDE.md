# DEEPLICA PORT MANAGEMENT REFERENCE

## Overview
All ports in the DEEPLICA system are managed centrally through `shared/port_manager.py`.

## Port Categories

### CONSTANT PORTS (Never Change)
These ports are fixed for external service compatibility:
- **Backend API**: 8888 (required for external integrations)
- **Twilio Echo Bot**: 8009 (Twilio webhook compatibility)
- **Webhook Server**: 8010 (external webhook compatibility)
- **Ngrok API**: 4040 (standard ngrok API port)
- **Ngrok Tunnel**: 8080 (default tunnel port)

### CONFIGURABLE PORTS (Can Be Changed)
These ports can be modified via admin interface:
- **Dispatcher**: 8001 (default)
- **Dialogue Agent**: 8002 (default)
- **Planner Agent**: 8003 (default)
- **Phone Agent**: 8004 (default)
- **Watchdog**: 8005 (default)
- **Web Chat**: 8007 (default)
- **CLI Terminal**: 8008 (default)
- **Test Server**: 8011 (default)
- **Dev Server**: 8012 (default)
- **Proxy Server**: 8013 (default)

## How to Change Ports

### Method 1: Admin Interface
1. Open the web chat interface
2. Navigate to the admin panel
3. Go to "Service Manager" section
4. Edit the port assignments
5. Restart all services

### Method 2: Direct Configuration
1. Edit `shared/deeplica_port_settings.json`
2. Update the port assignments
3. Restart all services

### Method 3: Environment Variables
Set environment variables to override defaults:
- `DISPATCHER_PORT=8001`
- `DIALOGUE_AGENT_PORT=8002`
- etc.

## Important Notes
- Backend API port (8888) cannot be changed
- External service ports (Twilio, Ngrok) should not be changed
- Always restart all services after changing ports
- Port conflicts are automatically detected and resolved
