"""
Shared LLM Client for Microservices.
HTTP client wrapper to communicate with backend LLM service.
Used by all agent services to avoid code duplication.
"""

import httpx
import os
from typing import Dict, Any, Optional
from loguru import logger


class BackendLLMClient:
    """
    Shared HTTP client wrapper for LLM operations.
    Communicates with backend service instead of direct LLM access.
    
    This eliminates code duplication across agent services.
    """
    
    def __init__(self):
        from shared.port_manager import get_service_port, get_service_host
        default_backend_url = f"http://{get_service_host('backend')}:{get_service_port('backend')}"
        self.backend_url = os.getenv("BACKEND_URL", default_backend_url)
        self.http_client = httpx.AsyncClient(timeout=60.0)
        logger.info(f"🤖 LLM client initialized with backend: {self.backend_url}")
    
    async def generate_response(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        response_format: Optional[str] = None,
        provider: Optional[str] = None
    ) -> Dict[str, Any]:
        """Generate response via backend LLM service"""
        try:
            payload = {
                "prompt": prompt,
                "system_prompt": system_prompt,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "response_format": response_format,
                "provider": provider
            }
            
            response = await self.http_client.post(
                f"{self.backend_url}/api/v1/llm/generate",
                json=payload
            )
            response.raise_for_status()
            return response.json()
            
        except Exception as e:
            logger.error(f"❌ Failed to generate LLM response: {e}")
            # Return fallback response
            return {
                "content": f"I apologize, but I'm having trouble processing your request right now. Error: {str(e)}",
                "provider": "fallback",
                "model": "error-fallback",
                "usage": {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}
            }
    
    async def close(self):
        """Close HTTP client"""
        await self.http_client.aclose()
        logger.info("🤖 LLM client closed")


__all__ = ["BackendLLMClient"]
