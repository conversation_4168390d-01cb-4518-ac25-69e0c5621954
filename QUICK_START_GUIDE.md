# 🚀 DEEPLICA V3 - <PERSON><PERSON><PERSON><PERSON> START GUIDE
## **Get Up and Running in 5 Minutes**

---

## 📋 **PREREQUISITES**

### **Required Software**
- **Python 3.9+** with pip
- **VS Code** (recommended IDE)
- **MongoDB Atlas** account (free tier available)
- **Twilio** account (for phone features)
- **ngrok** account (free tier available)

### **Optional but Recommended**
- **Git** for version control
- **Docker** (for containerized deployment)
- **Postman** (for API testing)

---

## ⚡ **5-MINUTE SETUP**

### **Step 1: Clone and Install (2 minutes)**
```bash
# Clone the repository
git clone <repository-url>
cd deeplica-v3

# Install dependencies
pip install -r requirements.txt
pip install -r stop_deeplica_service/requirements.txt

# Install ngrok
# Download from https://ngrok.com/download
# Or: brew install ngrok (macOS)
```

### **Step 2: Configure Environment (2 minutes)**
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your credentials
nano .env
```

**Required Environment Variables:**
```bash
# MongoDB Atlas (get from MongoDB Atlas dashboard)
MONGODB_CONNECTION_STRING=mongodb+srv://username:<EMAIL>/
MONGODB_DATABASE=deeplica-dev

# Gemini API (get from Google AI Studio)
GEMINI_API_KEY=your_gemini_api_key

# Twilio (get from Twilio Console)
TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_PHONE_NUMBER=your_twilio_number

# ngrok webhook URL (will be auto-updated)
TWILIO_WEBHOOK_URL=https://your-ngrok-url.ngrok-free.app
```

### **Step 3: Start the System (1 minute)**
```bash
# Option 1: VS Code (Recommended)
code .
# Press F5 and select "🚀 START DEEPLICA"

# Option 2: Command Line
python3 orchestrator/main.py
```

---

## 🎯 **VERIFICATION CHECKLIST**

### **✅ System Health Check**
```bash
# Check all services are running
curl http://localhost:8888/health  # Backend API
curl http://localhost:8001/health  # Dispatcher
curl http://localhost:8002/health  # Dialogue Agent
curl http://localhost:8003/health  # Planner Agent
curl http://localhost:8004/health  # Phone Agent
curl http://localhost:8005/health  # Watchdog
curl http://localhost:8006/health  # Stop Service

# All should return: {"status": "healthy"}
```

### **✅ Database Connection**
```bash
# Check database connectivity
curl http://localhost:8888/health
# Should show: "database_ready": true
```

### **✅ Phone System**
```bash
# Test phone system (optional)
python3 phone_test_system.py
# Follow prompts to test phone calls
```

---

## 🎮 **BASIC USAGE**

### **Create Your First Mission**
```bash
# Create a simple mission
curl -X POST http://localhost:8888/api/v1/missions \
  -H "Content-Type: application/json" \
  -d '{
    "user_input": "Call +********** and say hello",
    "title": "Test Phone Call",
    "description": "My first Deeplica mission"
  }'
```

### **Check Mission Status**
```bash
# Get mission status (replace with actual mission ID)
curl http://localhost:8888/api/v1/missions/{mission_id}
```

### **Use CLI Interface**
```bash
# Start interactive CLI
python3 cli/main.py

# In CLI, type:
create mission "Call +********** and introduce our company"
```

---

## 🛑 **STOPPING THE SYSTEM**

### **Safe Stop (VS Code)**
1. In VS Code Run and Debug panel
2. Select "🛑 STOP DEEPLICA (Emergency Stop)"
3. Press F5
4. **VS Code will remain open** ✅

### **Safe Stop (Command Line)**
```bash
python3 stop_deeplica_client.py
```

---

## 🔧 **DEVELOPMENT WORKFLOW**

### **VS Code Setup**
1. **Open Project**: `code .`
2. **Install Extensions**: Python, REST Client
3. **Debug Panel**: Use F5 to start/stop system
4. **Integrated Terminals**: Each service runs in separate terminal

### **Individual Service Debugging**
1. Go to Run and Debug panel
2. Select "Individual Services (for debugging)"
3. Choose specific service to debug
4. Set breakpoints and debug normally

### **Making Changes**
1. **Edit Code**: Make your changes
2. **Restart Service**: Stop and start specific service
3. **Test Changes**: Use health checks and API calls
4. **Full Restart**: Use STOP → START if needed

---

## 📞 **PHONE SYSTEM SETUP**

### **Twilio Configuration**
1. **Create Account**: https://www.twilio.com/
2. **Get Phone Number**: Purchase or use trial number
3. **Get Credentials**: Account SID and Auth Token
4. **Configure Webhooks**: Will be auto-configured by system

### **ngrok Setup**
1. **Create Account**: https://ngrok.com/
2. **Install ngrok**: Download and install
3. **Start Tunnel**: `ngrok http 8004` (auto-started by system)
4. **Webhook URL**: Automatically updated in .env

### **Test Phone Calls**
```bash
# Comprehensive phone test
python3 phone_test_system.py

# Manual test call
curl -X POST http://localhost:8004/execute \
  -H "Content-Type: application/json" \
  -d '{
    "task_id": "test_call_123",
    "task_type": "phone_call",
    "task_data": {
      "phone_number": "+**********",
      "question": "Hello, this is a test call from Deeplica"
    }
  }'
```

---

## 🚨 **COMMON ISSUES & QUICK FIXES**

### **❌ Services Won't Start**
```bash
# Clean up ports and restart
python3 stop_deeplica_client.py
# Wait 5 seconds, then restart system
```

### **❌ Database Connection Failed**
- Check MongoDB Atlas credentials in .env
- Verify network connectivity
- Check IP whitelist in MongoDB Atlas

### **❌ Phone Calls Don't Work**
```bash
# Restart ngrok and reload phone config
pkill ngrok
ngrok http 8004 &
sleep 5
curl -X POST http://localhost:8004/reload_config
```

### **❌ VS Code Closes When Stopping**
- Make sure you're using the new Stop Deeplica Service
- Check launch.json uses `stop_deeplica_client.py`
- Update to latest version if needed

---

## 📚 **NEXT STEPS**

### **Learn More**
- **Full Documentation**: `COMPREHENSIVE_SYSTEM_DOCUMENTATION.md`
- **API Reference**: Check `/docs` endpoint on each service
- **Architecture Deep Dive**: See system documentation

### **Customize the System**
- **Add New Services**: Follow microservice pattern
- **Extend Phone Features**: Modify phone agent
- **Custom Missions**: Create new task types
- **UI Development**: Build web interface

### **Production Deployment**
- **Docker Containers**: Use provided Dockerfiles
- **Load Balancing**: Deploy multiple instances
- **Monitoring**: Set up production monitoring
- **Security**: Configure production security

---

## 🎯 **SUCCESS INDICATORS**

You've successfully set up Deeplica V3 when:

✅ All 8 services show "healthy" status  
✅ Database connection is established  
✅ Phone test system works without errors  
✅ You can create and execute missions  
✅ VS Code stop/start works without closing IDE  
✅ Watchdog shows all services monitored  

---

## 📞 **GETTING HELP**

### **Self-Service**
- **Health Checks**: `curl http://localhost:PORT/health`
- **Service Status**: `curl http://localhost:8006/status`
- **Logs**: Check VS Code terminal outputs
- **Documentation**: Comprehensive system docs

### **Troubleshooting Tools**
```bash
# System status overview
python3 -c "
import requests
ports = [8888, 8001, 8002, 8003, 8004, 8005, 8006]
for port in ports:
    try:
        r = requests.get(f'http://localhost:{port}/health', timeout=2)
        print(f'Port {port}: {r.json().get(\"status\", \"unknown\")}')
    except:
        print(f'Port {port}: offline')
"

# Registered processes
curl -s http://localhost:8006/status | python3 -m json.tool
```

---

**🎉 Welcome to Deeplica V3!**  
*You're now ready to build autonomous AI missions*
