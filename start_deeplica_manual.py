#!/usr/bin/env python3
"""
🚀 DEEPLICA MANUAL STARTUP GUIDE
Provides step-by-step instructions for starting all DEEPLICA services
Each service runs as a separate microservice in its own Python Debug Console
"""

import asyncio
from shared.port_manager import cleanup_all_ports
from shared.port_manager import get_service_port

async def main():
    print("🚀 DEEPLICA MANUAL STARTUP GUIDE")
    print("=" * 80)
    print("🎯 Each service MUST run as a separate microservice!")
    print("🖥️ Each service gets its own Python Debug Console in VS Code")
    print("=" * 80)
    
    # Clean up ports first
    print("\n🧹 STEP 0: Cleaning up all ports...")
    try:
        cleanup_results = cleanup_all_ports(force=True)
        successful = sum(1 for success in cleanup_results.values() if success)
        total = len(cleanup_results)
        print(f"✅ Port cleanup: {successful}/{total} ports cleaned")
    except Exception as e:
        print(f"⚠️ Port cleanup error: {e}")
    
    print("\n🚀 STARTUP SEQUENCE:")
    print("Follow this order and start each service in VS Code Debug Panel:")
    
    services = [
        ("🐕 Watchdog Service", "System monitoring and registry", "CRITICAL"),
        ("🌐 Backend API Service", "Core API services", "CRITICAL"),
        ("🎯 Dispatcher Service", "Request routing", "CRITICAL"),
        ("💬 Dialogue Agent Service", "Conversation handling", "OPTIONAL"),
        ("🧠 Planner Agent Service", "Mission planning", "OPTIONAL"),
        ("📞 Phone Agent Service", "Phone integration", "OPTIONAL"),
        ("🌐 Web Chat Service", "Web interface", "CRITICAL"),
        ("🌐 Ngrok Tunnel (Backend API)", "External backend access", "OPTIONAL"),
        ("🌐 Ngrok Tunnel (Phone Webhooks)", "External phone webhooks", "OPTIONAL")
    ]
    
    for i, (service_name, description, priority) in enumerate(services, 1):
        priority_icon = "🔴" if priority == "CRITICAL" else "🟡"
        print(f"\n{priority_icon} STEP {i}: {service_name}")
        print(f"   📋 Description: {description}")
        print(f"   🎯 Action: Go to VS Code Debug Panel → Click '{service_name}'")
        print(f"   ⏳ Wait for service to start before proceeding to next step")
        
        if i <= 3:  # Critical services
            input(f"   ✅ Press ENTER after starting {service_name}...")
    
print("\n🎉 ALL SERVICES STARTED!")
print("=" * 80)
print(f"🌐 Web Chat should be available at: http://localhost:{get_service_port('web-chat')}")
print(f"🔗 Backend API available at: http://localhost:{get_service_port('backend')}")
print(f"🐕 Watchdog monitoring at: http://localhost:{get_service_port('watchdog')}")
print("=" * 80)
print("🎯 DEEPLICA is now fully operational as separate microservices!")

if __name__ == "__main__":
    asyncio.run(main())
