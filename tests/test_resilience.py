#!/usr/bin/env python3
"""
Test script to verify microservice resilience.
This script tests that all microservices can handle various failure scenarios without crashing.
"""

import asyncio
import httpx
import time
import subprocess
import signal
import os
import sys
from typing import List, Dict
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from shared.port_manager import get_service_port

# Service configurations - using port manager
SERVICES = {
    "dispatcher": {"port": get_service_port("dispatcher"), "path": "dispatcher"},
    "planner": {"port": get_service_port("planner"), "path": "agents/planner"},
    "dialogue": {"port": get_service_port("dialogue"), "path": "agents/dialogue"},
    "phone": {"port": get_service_port("phone"), "path": "agents/phone"}
}

# CLI Terminal UI (doesn't have a health endpoint, so we test it separately)
CLI_SERVICE = {"path": "cli", "name": "CLI Terminal UI"}

BACKEND_PORT = get_service_port("backend")

def print_status(message: str):
    # [SYSTEM:print_status] SYSTEM | 🔍 {message}
    pass

def print_success(message: str):
    # [SYSTEM:print_success] SYSTEM | ✅ {message}
    pass

def print_error(message: str):
    # [SYSTEM:print_error] SYSTEM | ❌ {message}
    pass

def print_warning(message: str):
    # [SYSTEM:print_warning] SYSTEM | ⚠️  {message}

    pass
def kill_processes_on_port(port: int):
    """Kill any processes using the specified port"""
    try:
        result = subprocess.run(
            ["lsof", "-ti", f": {port}"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0 and result.stdout.strip():
            pids = result.stdout.strip().split('\n')
            for pid in pids:
                if pid.strip():
                    try:
                        pid_int = int(pid.strip())
                        # [SYSTEM:kill_processes_on_port] SYSTEM | 🔪 Killing process {pid_int} on port {port}
                        os.kill(pid_int, signal.SIGKILL)
                        time.sleep(0.1)
                    except (ProcessLookupError, ValueError, PermissionError):
                        pass
    except Exception as e:
        # [SYSTEM:kill_processes_on_port] ERROR | Error cleaning port {port}: {e}

        pass
def start_service(service_name: str, service_config: Dict) -> subprocess.Popen:
    """Start a microservice"""
    print_status(f"Starting {service_name} on port {service_config['port']}")
    
    # Clean up port first
    kill_processes_on_port(service_config['port'])
    time.sleep(1)
    
    # Start the service
    process = subprocess.Popen(
        ["python3", "-m", "app.main"],
        cwd=service_config['path'],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    return process

async def check_service_health(port: int, service_name: str, timeout: int = 5) -> bool:
    """Check if a service is healthy"""
    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(f"http://localhost:{port}/health")
            return response.status_code == 200
    except Exception:
        return False

async def wait_for_service_startup(port: int, service_name: str, max_wait: int = 30) -> bool:
    """Wait for a service to start up"""
    print_status(f"Waiting for {service_name} to start (max {max_wait}s)")
    
    for attempt in range(max_wait):
        if await check_service_health(port, service_name):
            print_success(f"{service_name} is healthy!")
            return True
        await asyncio.sleep(1)
    
    print_error(f"{service_name} failed to start within {max_wait} seconds")
    return False

def check_process_alive(process: subprocess.Popen, service_name: str) -> bool:
    """Check if a process is still alive"""
    if process.poll() is None:
        return True
    else:
        print_error(f"{service_name} process has crashed! Return code: {process.returncode}")
        # Print stderr for debugging
        try:
            stderr = process.stderr.read()
            if stderr:
                # [SYSTEM:check_process_alive] ERROR | Error output: {stderr}
                pass
        except:
            pass
        return False

async def test_resilience():
    """Test microservice resilience"""
    # [SYSTEM:test_resilience] TEST | 🚀 Testing Microservice Resilience
    # [SYSTEM:test_resilience] SYSTEM | =" * 5
    
    # Step 1: Start all services WITHOUT backend
    print_status("Step 1: Starting all services WITHOUT backend API")
    processes = {}
    
    for service_name, config in SERVICES.items():
        processes[service_name] = start_service(service_name, config)
    
    # Wait a bit for services to start
    await asyncio.sleep(5)
    
    # Step 2: Verify all services are alive (not crashed)
    print_status("Step 2: Verifying services are alive and waiting (not crashed)")
    all_alive = True
    
    for service_name, process in processes.items():
        if check_process_alive(process, service_name):
            print_success(f"{service_name} is alive and waiting")
        else:
            print_error(f"{service_name} has crashed!")
            all_alive = False
    
    if not all_alive:
        print_error("Some services crashed! Test FAILED.")
        return False
    
    # Step 3: Wait longer to ensure services keep waiting
    print_status("Step 3: Waiting 15 seconds to ensure services don't crash while waiting")
    await asyncio.sleep(15)
    
    for service_name, process in processes.items():
        if check_process_alive(process, service_name):
            print_success(f"{service_name} is still alive after 15 seconds")
        else:
            print_error(f"{service_name} crashed while waiting!")
            all_alive = False
    
    if not all_alive:
        print_error("Some services crashed while waiting! Test FAILED.")
        return False
    
    # Step 4: Start backend API
    print_status("Step 4: Starting backend API")
    kill_processes_on_port(BACKEND_PORT)
    time.sleep(1)
    
    backend_process = subprocess.Popen(
        ["python3", "-m", "app.main"],
        cwd="backend",
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    # Wait for backend to be ready
    if not await wait_for_service_startup(BACKEND_PORT, "Backend API", 60):
        print_error("Backend API failed to start!")
        return False
    
    # Step 5: Wait for all services to connect
    print_status("Step 5: Waiting for all services to connect to backend")
    await asyncio.sleep(10)
    
    # Step 6: Verify all services are healthy
    print_status("Step 6: Verifying all services are healthy")
    all_healthy = True
    
    for service_name, config in SERVICES.items():
        if await check_service_health(config['port'], service_name):
            print_success(f"{service_name} is healthy")
        else:
            print_error(f"{service_name} is not healthy!")
            all_healthy = False
    
    # Step 7: Clean up
    print_status("Step 7: Cleaning up processes")
    
    # Kill backend
    backend_process.terminate()
    try:
        backend_process.wait(timeout=5)
    except subprocess.TimeoutExpired:
        backend_process.kill()
    
    # Kill all services
    for service_name, process in processes.items():
        process.terminate()
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
    
    # Final result
    if all_alive and all_healthy:
        print_success("🎉 RESILIENCE TEST PASSED! All services are bulletproof!")
        return True
    else:
        print_error("💥 RESILIENCE TEST FAILED! Some services crashed or failed to connect.")
        return False

if __name__ == "__main__":
    result = asyncio.run(test_resilience())
    exit(0 if result else 1)
