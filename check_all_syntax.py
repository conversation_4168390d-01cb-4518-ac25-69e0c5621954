#!/usr/bin/env python3
"""
Comprehensive syntax checker for all Python files in the Deeplica codebase.
This script will find and report ALL syntax errors across the entire project.
"""

import os
import ast
import glob
from typing import List, <PERSON><PERSON>

def check_file_syntax(file_path: str) -> Tuple[bool, str]:
    """Check if a Python file has valid syntax"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Try to parse the file
        ast.parse(content)
        return True, ""
    except SyntaxError as e:
        error_msg = f"Syntax error at line {e.lineno}: {e.msg}"
        if e.text:
            error_msg += f"\n  Code: {e.text.strip()}"
        return False, error_msg
    except Exception as e:
        return False, f"Error reading file: {e}"

def main():
    """Main function to check syntax of all Python files"""
    print("🔍 [CHECK-SYNTAX:main] SYSTEM | Starting comprehensive syntax check...")
    print("=" * 80)
    
    # Find all Python files in the project
    python_files = []
    
    # Add specific directories
    directories = [
        "backend/**/*.py",
        "dispatcher/**/*.py", 
        "agents/**/*.py",
        "cli/**/*.py",
        "watchdog/**/*.py",
        "orchestrator/**/*.py",
        "stop_deeplica/**/*.py",
        "*.py"
    ]
    
    for pattern in directories:
        python_files.extend(glob.glob(pattern, recursive=True))
    
    # Remove duplicates and filter out __pycache__ and .pyc files
    python_files = list(set([f for f in python_files if '__pycache__' not in f and f.endswith('.py')]))
    
    print(f"📁 [CHECK-SYNTAX:main] SYSTEM | Found {len(python_files)} Python files to check")
    print()
    
    error_count = 0
    total_count = len(python_files)
    error_files = []
    
    for file_path in sorted(python_files):
        is_valid, error_msg = check_file_syntax(file_path)
        
        if is_valid:
            print(f"✅ [CHECK-SYNTAX:main] SUCCESS | {file_path}")
        else:
            print(f"❌ [CHECK-SYNTAX:main] ERROR | {file_path}")
            print(f"   {error_msg}")
            print()
            error_count += 1
            error_files.append(file_path)
    
    print("=" * 80)
    print(f"📊 [CHECK-SYNTAX:main] SYSTEM | Syntax check complete!")
    print(f"📊 [CHECK-SYNTAX:main] SYSTEM | Files checked: {total_count}")
    print(f"📊 [CHECK-SYNTAX:main] SYSTEM | Files with errors: {error_count}")
    print(f"📊 [CHECK-SYNTAX:main] SYSTEM | Files valid: {total_count - error_count}")
    
    if error_files:
        print()
        print("🚨 [CHECK-SYNTAX:main] ERROR | Files with syntax errors:")
        for file_path in error_files:
            print(f"   - {file_path}")
    else:
        print()
        print("🎉 [CHECK-SYNTAX:main] SUCCESS | All files have valid syntax!")

if __name__ == "__main__":
    main()
