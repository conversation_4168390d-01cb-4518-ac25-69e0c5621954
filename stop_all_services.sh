#!/bin/bash
# =============================================================================
# DEEPLICA SERVICE SCRIPT - ALL PORTS MANAGED DYNAMICALLY
# =============================================================================
# ALL ports are assigned by shared/port_manager.py - NO HARDCODED PORTS
# External services adapt to assigned ports through configuration
# =============================================================================
# =============================================================================
# DEEPLICA SERVICE STARTUP SCRIPT
# =============================================================================
# NOTE: All ports are managed by shared/port_manager.py
# - Backend API: 8888 (CONSTANT - never changes)
# - Other services: configurable via port manager
# =============================================================================

# 🛑 Stop All Deeplica Services
# This script stops all running Deeplica microservices

echo "🛑 Stopping all Deeplica microservices..."

# Find all Deeplica processes
PROCESSES=$(ps aux | grep DEEPLICA | grep -v grep | awk '{print $2}')

if [ -z "$PROCESSES" ]; then
    echo "ℹ️  No Deeplica services are currently running."
    exit 0
fi

echo "📋 Found running Deeplica services:"
ps aux | grep DEEPLICA | grep -v grep | while read line; do
    echo "  🔍 $line"
done

echo ""
echo "🛑 Stopping services..."

# Kill all Deeplica processes
for pid in $PROCESSES; do
    if kill -TERM "$pid" 2>/dev/null; then
        echo "✅ Stopped process $pid"
    else
        echo "⚠️  Could not stop process $pid (may have already stopped)"
    fi
done

# Wait a moment for graceful shutdown
sleep 2

# Check if any processes are still running and force kill if necessary
REMAINING=$(ps aux | grep DEEPLICA | grep -v grep | awk '{print $2}')

if [ ! -z "$REMAINING" ]; then
    echo "⚠️  Some processes still running, force killing..."
    for pid in $REMAINING; do
        if kill -KILL "$pid" 2>/dev/null; then
            echo "💀 Force killed process $pid"
        fi
    done
fi

# Final check
sleep 1
FINAL_CHECK=$(ps aux | grep DEEPLICA | grep -v grep)

if [ -z "$FINAL_CHECK" ]; then
    echo "✅ All Deeplica services stopped successfully!"
else
    echo "⚠️  Some processes may still be running:"
    echo "$FINAL_CHECK"
fi

echo ""
echo "🔍 To verify all services are stopped:"
echo "  ps aux | grep DEEPLICA | grep -v grep"
echo ""
echo "🚀 To restart services:"
echo "  ./launch_separate_terminals.sh"
