#!/usr/bin/env python3
"""
Test script to verify DeepChat auto-opening functionality
"""

import asyncio
import time
import webbrowser
import httpx
from datetime import datetime
import sys
import os

# Add project root to path for shared modules
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.port_manager import get_service_port, get_localhost


async def test_deepchat_autoopen():
    """Test the DeepChat auto-opening functionality"""
    print("🧪 Testing DeepChat Auto-Opening Functionality")
    print("=" * 60)
    
    # Test 1: Check if web chat is running
    print("\n1️⃣ Testing Web Chat Service...")
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"http://{get_localhost()}:{get_service_port('web-chat')}/health", timeout=5.0)
            if response.status_code == 200:
                print("✅ Web Chat service is running")
                health_data = response.json()
                print(f"   Status: {health_data.get('status', 'unknown')}")
            else:
                print(f"❌ Web Chat service returned status {response.status_code}")
                return False
    except Exception as e:
        print(f"❌ Web Chat service is not accessible: {e}")
        return False
    
    # Test 2: Check backend API
    print("\n2️⃣ Testing Backend API...")
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"http://{get_localhost()}:{get_service_port('backend')}/health", timeout=5.0)
            if response.status_code == 200:
                print("✅ Backend API is running")
            else:
                print(f"⚠️ Backend API returned status {response.status_code}")
    except Exception as e:
        print(f"⚠️ Backend API is not accessible: {e}")
    
    # Test 3: Test manual browser launch
    print("\n3️⃣ Testing Manual Browser Launch...")
    try:
        url = f"http://{get_localhost()}:{get_service_port('web-chat')}/login"
        print(f"🚀 Opening browser to: {url}")
        success = webbrowser.open(url)
        if success:
            print("✅ Browser opened successfully!")
            print("👤 Demo credentials: admin/admin123")
        else:
            print("❌ Failed to open browser")
            return False
    except Exception as e:
        print(f"❌ Browser launch failed: {e}")
        return False
    
    # Test 4: Test browser launch endpoint
    print("\n4️⃣ Testing Browser Launch Endpoint...")
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(f"http://{get_localhost()}:{get_service_port('web-chat')}/launch_browser", timeout=5.0)
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Browser launch endpoint responded: {result.get('message', 'success')}")
            else:
                print(f"⚠️ Browser launch endpoint returned status {response.status_code}")
    except Exception as e:
        print(f"⚠️ Browser launch endpoint failed: {e}")
    
    print("\n🎉 DeepChat Auto-Opening Test Complete!")
    print("💡 If the browser opened, the auto-opening functionality is working correctly.")
    return True


async def main():
    """Run the test"""
    print(f"🚀 Starting DeepChat Auto-Opening Test at {datetime.now()}")
    
    success = await test_deepchat_autoopen()
    
    if success:
        print("\n✅ Test completed successfully!")
        return 0
    else:
        print("\n❌ Test failed!")
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(asyncio.run(main()))
