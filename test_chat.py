#!/usr/bin/env python3
"""
Simple test script to verify DeepChat functionality
"""

import asyncio
import json
from datetime import datetime

# Mock classes for testing
class MockWebSocket:
    def __init__(self):
        self.messages = []
        
    async def accept(self):
        print("✅ WebSocket accepted")
        
    async def receive_text(self):
        # Simulate receiving a message
        return json.dumps({"type": "message", "content": "Hello Deeplica"})
        
    async def send_text(self, message):
        self.messages.append(message)
        print(f"📤 Sent: {message}")

class MockConnectionManager:
    def __init__(self):
        self.active_connections = {}
        
    async def connect(self, websocket, user_id, username):
        connection_id = f"conn_{len(self.active_connections)}"
        self.active_connections[connection_id] = websocket
        print(f"🔗 Connected: {username} ({connection_id})")
        return connection_id
        
    async def send_personal_message(self, message, connection_id):
        if connection_id in self.active_connections:
            await self.active_connections[connection_id].send_text(message)
            
    def check_rate_limit(self, username):
        return True  # Always allow for testing
        
    def record_message(self, username, connection_id):
        print(f"📝 Recorded message from {username}")

class MockDeepplicaClient:
    async def send_message(self, message, username):
        # Simulate Deeplica response
        responses = [
            "Hello! I'm Deeplica, your AI assistant. How can I help you today?",
            "That's an interesting question. Let me think about that...",
            "I understand what you're asking. Here's my response...",
            "Thank you for your message. I'm here to help!"
        ]
        import random
        return random.choice(responses)

async def test_chat_functionality():
    """Test the core chat functionality"""
    print("🧪 Testing DeepChat functionality...")
    
    # Create mock objects
    websocket = MockWebSocket()
    manager = MockConnectionManager()
    deeplica_client = MockDeepplicaClient()
    
    # Simulate user connection
    user = {
        "username": "TestUser",
        "user_id": "test_123",
        "full_name": "Test User"
    }
    
    # Connect WebSocket
    await websocket.accept()
    connection_id = await manager.connect(websocket, user["user_id"], user["username"])
    
    # Send connection confirmation
    connection_msg = {
        "type": "connection",
        "status": "connected",
        "user": user["username"],
        "timestamp": datetime.now().isoformat()
    }
    await manager.send_personal_message(json.dumps(connection_msg), connection_id)
    
    # Simulate receiving a message
    user_message = "Hello Deeplica"
    print(f"💬 User message: {user_message}")
    
    # Check rate limiting
    if manager.check_rate_limit(user["username"]):
        # Record message
        manager.record_message(user["username"], connection_id)
        
        # Echo user message back
        user_msg = {
            "type": "message",
            "sender": "user",
            "content": user_message,
            "timestamp": datetime.now().isoformat()
        }
        await manager.send_personal_message(json.dumps(user_msg), connection_id)
        
        # Send typing indicator
        typing_msg = {
            "type": "typing",
            "sender": "deeplica"
        }
        await manager.send_personal_message(json.dumps(typing_msg), connection_id)
        
        # Get response from Deeplica
        deeplica_response = await deeplica_client.send_message(user_message, user["username"])
        print(f"🤖 Deeplica response: {deeplica_response}")
        
        # Send Deeplica response
        response_msg = {
            "type": "message",
            "sender": "deeplica",
            "content": deeplica_response,
            "timestamp": datetime.now().isoformat()
        }
        await manager.send_personal_message(json.dumps(response_msg), connection_id)
        
    print("✅ Chat functionality test completed successfully!")
    print(f"📊 Total messages sent: {len(websocket.messages)}")
    for i, msg in enumerate(websocket.messages):
        print(f"  {i+1}. {msg}")

if __name__ == "__main__":
    asyncio.run(test_chat_functionality())
