"""
Phone Agent Service - Handles phone call tasks.
Microservice that processes phone call tasks to get answers to specific questions.
"""

import os
import time
import json
import uuid
import asyncio
import signal
import sys
import threading
import traceback
import atexit
from datetime import datetime, timed<PERSON>ta
from contextlib import asynccontextmanager
from fastapi import Fast<PERSON><PERSON>, HTTPException, Request, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import Response, JSONResponse
import uvicorn
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# These will be imported dynamically to avoid circular import issues
# from .agent import PhoneAgent
# from .models import LANGUAGE_CONFIGS
# from .llm_service import BackendLLMClient
# from .twilio_service import TwilioService

# 🛡️ PREVENTIVE MEASURE 5: Configuration to disable error calls
DISABLE_ERROR_CALLS = os.getenv("DISABLE_ERROR_CALLS", "true").lower() == "true"

# 🛡️ BULLETPROOF CRASH PREVENTION - Global State Tracking
CRASH_PREVENTION_ACTIVE = True
LAST_SUCCESSFUL_ACTION = "STARTUP"
CURRENT_OPERATION = "INITIALIZING"
CRASH_COUNT = 0
MAX_CRASH_RECOVERY_ATTEMPTS = 10
PROCESS_PID = os.getpid()
SERVICE_START_TIME = time.time()
HEARTBEAT_ACTIVE = False
SHUTDOWN_REQUESTED = False

# ============================================================================
# 🛡️ BULLETPROOF CRASH PREVENTION FUNCTIONS
# ============================================================================

def log_crash_debug(operation: str, status: str, details: str = ""):
    """Log detailed crash debugging information"""
    global LAST_SUCCESSFUL_ACTION, CURRENT_OPERATION

    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

    if status == "SUCCESS":
        LAST_SUCCESSFUL_ACTION = operation
        print(f"🛡️ CRASH-DEBUG: [{timestamp}] ✅ SUCCESS - {operation} - {details}")
    elif status == "START":
        CURRENT_OPERATION = operation
        print(f"🛡️ CRASH-DEBUG: [{timestamp}] 🔄 START - {operation} - {details}")
    elif status == "ERROR":
        print(f"🛡️ CRASH-DEBUG: [{timestamp}] ❌ ERROR - {operation} - {details}")
    else:
        print(f"🛡️ CRASH-DEBUG: [{timestamp}] ⚠️ {status} - {operation} - {details}")

def setup_signal_handlers():
    """Setup comprehensive signal handlers to prevent unexpected termination"""
    global SHUTDOWN_REQUESTED

    def signal_handler(signum, frame):
        signal_name = signal.Signals(signum).name
        print(f"🚨 SIGNAL RECEIVED: {signal_name} ({signum}) - Frame: {frame}")

        if signum in [signal.SIGTERM, signal.SIGINT]:
            print(f"🛡️ GRACEFUL SHUTDOWN REQUESTED via {signal_name}")
            SHUTDOWN_REQUESTED = True

            # Log current state before shutdown
            log_crash_debug("SHUTDOWN_SIGNAL", "RECEIVED",
                           f"Signal: {signal_name}, Last successful: {LAST_SUCCESSFUL_ACTION}, Current: {CURRENT_OPERATION}")

            # Give time for graceful shutdown
            time.sleep(2)

            print(f"🛡️ GRACEFUL SHUTDOWN COMPLETE via {signal_name}")
            # 🛡️ BULLETPROOF: Never exit - shutdown flag already set above
            print(f"🛡️ BULLETPROOF: Shutdown flag set - service will stop gracefully")
        else:
            print(f"🛡️ IGNORING SIGNAL: {signal_name} - Service must not stop!")

    # Handle termination signals gracefully
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)

    # Ignore other signals that might cause crashes
    try:
        signal.signal(signal.SIGHUP, signal.SIG_IGN)  # Ignore hangup
        signal.signal(signal.SIGPIPE, signal.SIG_IGN)  # Ignore broken pipe
    except AttributeError:
        # Some signals might not be available on all platforms
        pass

    print("🛡️ SIGNAL HANDLERS INSTALLED - Service protected from unexpected termination")

def setup_crash_recovery():
    """Setup comprehensive crash recovery mechanisms"""

    def exception_handler(loop, context):
        """Handle uncaught exceptions in asyncio loop"""
        global CRASH_COUNT
        CRASH_COUNT += 1

        exception = context.get('exception')
        message = context.get('message', 'Unknown error')

        print(f"🚨 ASYNCIO EXCEPTION #{CRASH_COUNT}: {message}")

        if exception:
            print(f"🚨 EXCEPTION DETAILS: {type(exception).__name__}: {exception}")
            print(f"🚨 TRACEBACK: {traceback.format_exc()}")

        log_crash_debug("ASYNCIO_EXCEPTION", "ERROR",
                       f"Count: {CRASH_COUNT}, Message: {message}, Exception: {exception}")

        # Don't let the service crash - just log and continue
        print("🛡️ EXCEPTION HANDLED - Service continues running")

    # Set up asyncio exception handler
    try:
        loop = asyncio.get_event_loop()
        loop.set_exception_handler(exception_handler)
    except RuntimeError:
        # No event loop running yet
        pass

    # Set up global exception handler
    def global_exception_handler(exc_type, exc_value, exc_traceback):
        global CRASH_COUNT
        CRASH_COUNT += 1

        print(f"🚨 GLOBAL EXCEPTION #{CRASH_COUNT}: {exc_type.__name__}: {exc_value}")
        print(f"🚨 GLOBAL TRACEBACK: {''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))}")

        log_crash_debug("GLOBAL_EXCEPTION", "ERROR",
                       f"Count: {CRASH_COUNT}, Type: {exc_type.__name__}, Value: {exc_value}")

        # Don't let the service crash - just log and continue
        print("🛡️ GLOBAL EXCEPTION HANDLED - Service continues running")

    sys.excepthook = global_exception_handler

    print("🛡️ CRASH RECOVERY MECHANISMS INSTALLED")

def setup_exit_handlers():
    """Setup exit handlers to log shutdown information"""

    def exit_handler():
        uptime = time.time() - SERVICE_START_TIME
        print(f"🛡️ SERVICE SHUTDOWN - Uptime: {uptime:.2f}s, Last action: {LAST_SUCCESSFUL_ACTION}, Current: {CURRENT_OPERATION}")
        log_crash_debug("SERVICE_SHUTDOWN", "INFO",
                       f"Uptime: {uptime:.2f}s, Crash count: {CRASH_COUNT}")

    atexit.register(exit_handler)
    print("🛡️ EXIT HANDLERS INSTALLED")

async def start_heartbeat():
    """Start heartbeat to monitor service health"""
    global HEARTBEAT_ACTIVE
    HEARTBEAT_ACTIVE = True

    async def heartbeat_loop():
        heartbeat_count = 0
        while HEARTBEAT_ACTIVE and not SHUTDOWN_REQUESTED:
            try:
                heartbeat_count += 1
                uptime = time.time() - SERVICE_START_TIME

                if heartbeat_count % 12 == 0:  # Every minute (5s * 12)
                    print(f"💓 HEARTBEAT #{heartbeat_count} - Uptime: {uptime:.1f}s, Last: {LAST_SUCCESSFUL_ACTION}, Current: {CURRENT_OPERATION}")

                log_crash_debug("HEARTBEAT", "SUCCESS", f"#{heartbeat_count}, Uptime: {uptime:.1f}s")
                await asyncio.sleep(5)  # Heartbeat every 5 seconds

            except Exception as e:
                print(f"💓 HEARTBEAT ERROR: {e}")
                log_crash_debug("HEARTBEAT", "ERROR", str(e))
                await asyncio.sleep(5)

    # Start heartbeat in background
    asyncio.create_task(heartbeat_loop())
    print("💓 HEARTBEAT STARTED")

# ============================================================================
# 🛡️ BULLETPROOF SERVICE MANAGER - 100% UPTIME GUARANTEE
# ============================================================================

class BulletproofServiceManager:
    """
    🛡️ BULLETPROOF SERVICE MANAGER

    Ensures 100% uptime by:
    - Never allowing the service to crash
    - Auto-recovery from all failure scenarios
    - Graceful degradation when dependencies fail
    - Comprehensive error isolation
    """

    def __init__(self):
        self.service_state = "INITIALIZING"
        self.last_error = None
        self.error_count = 0
        self.recovery_attempts = 0
        self.max_recovery_attempts = 10
        self.recovery_delay = 5  # seconds
        self.health_status = {
            "backend": False,
            "twilio": False,
            "webhook": False,
            "agent": False
        }
        # Service references for watchdog operations
        self.twilio_service = None

    def is_healthy(self) -> bool:
        """Check if service is in a healthy state"""
        return self.service_state in ["RUNNING", "DEGRADED"] and self.health_status.get("twilio", False)

    def can_make_calls(self) -> bool:
        """Check if service can make phone calls"""
        return (self.service_state == "RUNNING" and
                self.health_status.get("twilio", False) and
                self.health_status.get("webhook", False))

    def record_error(self, error: Exception, component: str = "unknown"):
        """Record an error without crashing the service"""
        self.error_count += 1
        self.last_error = {
            "error": str(error),
            "component": component,
            "timestamp": time.time(),
            "type": type(error).__name__
        }
        logger.warning(f"🛡️ Error recorded in {component}: {error}", module="BULLETPROOF-MANAGER", routine="record_error")

        # Update service state based on error severity
        if component in ["twilio", "webhook"]:
            self.service_state = "DEGRADED"

    def attempt_recovery(self, component: str) -> bool:
        """Attempt to recover a failed component"""
        if self.recovery_attempts >= self.max_recovery_attempts:
            logger.warning(f"🛡️ Max recovery attempts reached for {component}", module="BULLETPROOF-MANAGER", routine="attempt_recovery")
            return False

        self.recovery_attempts += 1
        logger.info(f"🔧 Attempting recovery for {component} (attempt {self.recovery_attempts})", module="BULLETPROOF-MANAGER", routine="attempt_recovery")

        try:
            if component == "twilio":
                return self._recover_twilio()
            elif component == "webhook":
                return self._recover_webhook()
            elif component == "backend":
                return self._recover_backend()
            elif component == "agent":
                return self._recover_agent()
        except Exception as e:
            logger.error(f"🛡️ Recovery failed for {component}: {e}", module="BULLETPROOF-MANAGER", routine="attempt_recovery")
            return False

        return False

    def _recover_twilio(self) -> bool:
        """Attempt to recover Twilio service"""
        try:
            # Test Twilio configuration
            twilio_config = get_twilio_config()
            if not all([twilio_config.get("account_sid"), twilio_config.get("auth_token"), twilio_config.get("phone_number")]):
                logger.error("🛡️ Twilio configuration incomplete", module="BULLETPROOF-MANAGER", routine="_recover_twilio")
                return False

            # Test Twilio connection
            from twilio.rest import Client
            client = Client(twilio_config["account_sid"], twilio_config["auth_token"])
            account = client.api.accounts(twilio_config["account_sid"]).fetch()

            self.health_status["twilio"] = True
            logger.info("🛡️ Twilio recovery successful", module="BULLETPROOF-MANAGER", routine="_recover_twilio")
            return True

        except Exception as e:
            logger.error(f"🛡️ Twilio recovery failed: {e}", module="BULLETPROOF-MANAGER", routine="_recover_twilio")
            return False

    def _recover_webhook(self) -> bool:
        """Attempt to recover webhook service"""
        try:
            twilio_config = get_twilio_config()
            webhook_url = twilio_config.get("webhook_url")

            if webhook_url:
                # Test webhook accessibility
                import httpx
                async def test_webhook():
                    async with httpx.AsyncClient(timeout=5.0) as client:
                        response = await client.get(f"{webhook_url}/health")
                        return response.status_code == 200

                # Run async test in sync context
                import asyncio
                if asyncio.run(test_webhook()):
                    self.health_status["webhook"] = True
                    logger.info("🛡️ Webhook recovery successful", module="BULLETPROOF-MANAGER", routine="_recover_webhook")
                    return True

            return False

        except Exception as e:
            logger.error(f"🛡️ Webhook recovery failed: {e}", module="BULLETPROOF-MANAGER", routine="_recover_webhook")
            return False

    def _recover_backend(self) -> bool:
        """Attempt to recover backend connection"""
        try:
            import httpx
            async def test_backend():
                async with httpx.AsyncClient(timeout=5.0) as client:
                    response = await client.get(f"http://localhost:{get_service_port('backend')}/health")
                    return response.status_code == 200

            import asyncio
            if asyncio.run(test_backend()):
                self.health_status["backend"] = True
                logger.info("🛡️ Backend recovery successful", module="BULLETPROOF-MANAGER", routine="_recover_backend")
                return True

            return False

        except Exception as e:
            logger.error(f"🛡️ Backend recovery failed: {e}", module="BULLETPROOF-MANAGER", routine="_recover_backend")
            return False

    def _recover_agent(self) -> bool:
        """Attempt to recover phone agent"""
        try:
            # This will be implemented when we integrate with the agent
            self.health_status["agent"] = True
            logger.info("🛡️ Agent recovery successful", module="BULLETPROOF-MANAGER", routine="_recover_agent")
            return True

        except Exception as e:
            logger.error(f"🛡️ Agent recovery failed: {e}", module="BULLETPROOF-MANAGER", routine="_recover_agent")
            return False

    def trigger_watchdog_auto_fix(self) -> bool:
        """🔧 WATCHDOG: Trigger automatic fix for Twilio service issues"""
        try:
            if self.twilio_service and hasattr(self.twilio_service, 'watchdog_auto_fix'):
                logger.info("🔧 WATCHDOG: Triggering auto-fix for Twilio service...", module="BULLETPROOF-MANAGER", routine="trigger_watchdog_auto_fix")

                fix_result = self.twilio_service.watchdog_auto_fix()

                if fix_result:
                    self.health_status["twilio"] = self.twilio_service.is_healthy
                    logger.info("✅ WATCHDOG: Auto-fix completed successfully", module="BULLETPROOF-MANAGER", routine="trigger_watchdog_auto_fix")
                else:
                    logger.warning("⚠️ WATCHDOG: Auto-fix completed but issues remain", module="BULLETPROOF-MANAGER", routine="trigger_watchdog_auto_fix")

                return fix_result
            else:
                logger.warning("⚠️ WATCHDOG: Twilio service not available for auto-fix", module="BULLETPROOF-MANAGER", routine="trigger_watchdog_auto_fix")
                return False

        except Exception as e:
            logger.error(f"❌ WATCHDOG: Auto-fix failed: {e}", module="BULLETPROOF-MANAGER", routine="trigger_watchdog_auto_fix")
            return False

# Global bulletproof manager instance
bulletproof_manager = BulletproofServiceManager()

# Import shared models
import sys
import os
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.unified_logging import get_phone_logger, should_log_status_change
from shared.port_manager import get_service_port, get_service_host, ensure_service_port_free
from shared.port_cache import get_port_cache
from shared.api_manager import get_twilio_config, get_system_config
from shared.terminal_manager import get_terminal_manager

# Initialize unified logger
logger = get_phone_logger()

# Initialize port cache for efficient port management
port_cache = get_port_cache("phone")

from shared_models.communication import TaskRequest, TaskResponse
from shared.backend_readiness import ensure_backend_ready_before_startup





async def _delayed_call_end(twilio_service, task_id: str, delay_seconds: float = 8.0):
    """End a call after a delay to allow final message to be delivered"""
    import asyncio
    await asyncio.sleep(delay_seconds)
    await twilio_service.end_call(task_id, "completed")


async def start_required_dependencies():
    """🚀 Start required dependencies for Phone Agent"""
    logger.info("🚀 DEPENDENCY STARTUP: Starting required dependencies...", module="PHONE-AGENT", routine="start_required_dependencies")

    import subprocess
    import asyncio
    import os

    # Check if Backend API is running
    try:
        import httpx
        backend_port = get_service_port('backend')
        async with httpx.AsyncClient(timeout=2.0) as client:
            response = await client.get(f"http://localhost:{backend_port}/health")
            if response.status_code == 200:
                logger.info("✅ Backend API is already running", module="PHONE-AGENT", routine="start_required_dependencies")
                return True
    except:
        logger.info("🔄 Backend API not running, starting it...", module="PHONE-AGENT", routine="start_required_dependencies")

    # Start Backend API
    try:
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        backend_cmd = [
            "python3", "-m", "backend.app.main"
        ]

        logger.info(f"🚀 Starting Backend API: {' '.join(backend_cmd)}", module="PHONE-AGENT", routine="start_required_dependencies")

        # Start Backend API in background
        process = subprocess.Popen(
            backend_cmd,
            cwd=project_root,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            env=os.environ.copy()
        )

        logger.info(f"✅ Backend API started with PID: {process.pid}", module="PHONE-AGENT", routine="start_required_dependencies")

        # Wait a bit for it to start
        await asyncio.sleep(5)

        # Verify it's running
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"http://localhost:{backend_port}/health")
                if response.status_code == 200:
                    logger.info("✅ Backend API started successfully", module="PHONE-AGENT", routine="start_required_dependencies")
                    return True
        except:
            logger.warning("⚠️ Backend API started but not responding yet", module="PHONE-AGENT", routine="start_required_dependencies")

    except Exception as e:
        logger.error(f"❌ Failed to start Backend API: {e}", module="PHONE-AGENT", routine="start_required_dependencies")

    return False

async def wait_for_backend_and_initialize(app: FastAPI):
    """🛡️ BULLETPROOF Backend Wait & Initialize - GUARANTEED 100% UPTIME"""
    logger.info("🛡️ BULLETPROOF INITIALIZATION START", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
    logger.info(f"🐛 DEBUG: Background task PID: {os.getpid()}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")

    # 🚀 DEPENDENCY STARTUP: Start required dependencies first
    logger.info("🚀 Starting required dependencies...", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
    await start_required_dependencies()

    import httpx
    import asyncio

    try:
        # Use API manager for backend URL
        from shared.api_manager import get_service_url
        backend_url = get_service_url("backend")
    except Exception as e:
        # Fallback to port manager if API manager fails
        logger.warning(f"🛡️ API manager failed, using fallback: {e}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
        backend_url = f"http://localhost:{get_service_port('backend')}"

    logger.info(f"🐛 DEBUG: Backend URL: {backend_url}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
    logger.info(f"⏳ Waiting for Backend API at {backend_url}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize()")
    logger.info("🛡️ BULLETPROOF MODE: Will keep trying INDEFINITELY until Backend API is ready...", module="PHONE-AGENT", routine="wait_for_backend_and_initialize()")
    logger.info("💪 This service will NEVER crash - it will wait as long as needed!", module="PHONE-AGENT", routine="wait_for_backend_and_initialize()")

    attempt = 1
    while True:  # 🛡️ BULLETPROOF INFINITE LOOP - NEVER EXITS ON ERROR
        try:
            # 🛡️ BULLETPROOF: Update service state
            bulletproof_manager.service_state = "CONNECTING_BACKEND"

            # Use bulletproof timeout and connection handling
            try:
                timeout = httpx.Timeout(30.0, connect=5.0, read=10.0, write=10.0, pool=5.0)
                limits = httpx.Limits(max_connections=1, max_keepalive_connections=0)

                async with httpx.AsyncClient(timeout=timeout, limits=limits) as client:
                    try:
                        # Check if Backend API is COMPLETELY ready (not just healthy)
                        response = await client.get(f"{backend_url}/ready")

                        if response.status_code == 200:
                            # Backend API is fully initialized and ready
                            try:
                                ready_data = response.json()
                                if isinstance(ready_data, dict) and ready_data.get("status") == "ready":
                                    logger.info(f"✅ Backend API is FULLY READY and initialized! (attempt {attempt})", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                                    # 🛡️ BULLETPROOF: Mark backend as healthy
                                    bulletproof_manager.health_status["backend"] = True
                                    break
                                else:
                                    logger.debug(f"Backend API responded but not fully ready: {ready_data}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                            except (ValueError, TypeError, KeyError, AttributeError) as json_error:
                                logger.debug("Backend API responded but invalid JSON: {json_error}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                        elif response.status_code == 503:
                            # Backend API is running but not fully ready yet
                            try:
                                error_data = response.json()
                                if isinstance(error_data, dict):
                                    logger.debug("Backend API still initializing: {error_data.get('detail', 'Unknown')}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                                else:
                                    logger.debug("Backend API still initializing (503 response)", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                            except (ValueError, TypeError, KeyError, AttributeError):
                                logger.debug("Backend API still initializing (503 response)", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                        else:
                            logger.debug("Backend API returned status {response.status_code}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")

                    except httpx.ConnectError as e:
                        logger.debug("Backend API connection failed (port not open yet): {e}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                    except httpx.TimeoutException as e:
                        logger.debug("Backend API timeout (still starting up): {e}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                    except httpx.ReadTimeout as e:
                        logger.debug("Backend API read timeout (still initializing): {e}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                    except httpx.WriteTimeout as e:
                        logger.debug("Backend API write timeout: {e}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                    except httpx.PoolTimeout as e:
                        logger.debug("Backend API pool timeout: {e}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                    except httpx.HTTPStatusError as e:
                        logger.debug("Backend API HTTP error: {e}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                    except httpx.RequestError as e:
                        logger.debug("Backend API request error: {e}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                    except OSError as e:
                        logger.debug("Backend API OS error (network/socket): {e}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                    except Exception as e:
                        logger.debug("Backend API unexpected response error: {e}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")

            except ImportError as e:
                logger.debug("Failed to import httpx: {e}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
            except Exception as e:
                logger.debug("Failed to create HTTP client: {e}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")

        except Exception as e:
            logger.debug("Unexpected error in connection attempt: {e}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")

        # Log progress every 15 attempts (30 seconds)
        if attempt % 15 == 0:
            logger.info(f"⏳ Still waiting for Backend API... (attempt {attempt}, {attempt * 2 // 60} minutes elapsed)", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
            logger.info("continuing to wait...", module="PHONE-AGENT:wait_for_backend_and_initialize", routine="SYSTEM | 💪 Phone Agent will NEVER give up")

        try:
            logger.debug(f"⏳ Retrying in 2 seconds... (attempt {attempt})", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
            await asyncio.sleep(2)
        except Exception as e:
            logger.debug(f"Sleep interrupted: {e}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
            # Use time.sleep as fallback
            try:
                import time
                time.sleep(2)
            except Exception as sleep_error:
                logger.debug(f"Fallback sleep failed: {sleep_error}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                pass  # Continue anyway

        attempt += 1

    logger.info("proceeding with Phone Agent initialization!", module="PHONE-AGENT:wait_for_backend_and_initialize", routine="HEALTH | ✅ Backend API is healthy and ready")

    # 🛡️ BULLETPROOF SERVICE INITIALIZATION - NEVER FAILS
    service_retry_attempt = 1
    logger.info("🛡️ Starting BULLETPROOF service initialization loop...", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
    bulletproof_manager.service_state = "INITIALIZING_SERVICES"

    while True:
        try:
            logger.info(f"🛡️ BULLETPROOF service initialization attempt {service_retry_attempt}...", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")

            # 🛡️ BULLETPROOF: Import with individual error handling
            logger.info("📦 Importing required classes...", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")

            try:
                from agents.phone.app.agent import PhoneAgent
                logger.info("✅ PhoneAgent imported", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
            except Exception as e:
                logger.error(f"🛡️ PhoneAgent import failed: {e}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                bulletproof_manager.record_error(e, "agent_import")
                raise

            try:
                from agents.phone.app.twilio_service import TwilioService
                logger.info("✅ TwilioService imported", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
            except Exception as e:
                logger.error(f"🛡️ TwilioService import failed: {e}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                bulletproof_manager.record_error(e, "twilio_import")
                raise

            try:
                from agents.phone.app.llm_service import BackendLLMClient
                logger.info("✅ BackendLLMClient imported", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
            except Exception as e:
                logger.error(f"🛡️ BackendLLMClient import failed: {e}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                bulletproof_manager.record_error(e, "llm_import")
                raise

            # 🛡️ BULLETPROOF: Initialize LLM service with error isolation
            logger.info("🔧 Initializing LLM service...", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
            try:
                llm_service = BackendLLMClient()
                logger.info("✅ LLM service initialized", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
            except Exception as e:
                logger.error(f"🛡️ LLM service initialization failed: {e}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                bulletproof_manager.record_error(e, "llm_service")
                raise

            # 🛡️ BULLETPROOF: Initialize Twilio service with error isolation
            logger.info("📞 Initializing Twilio service...", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
            try:
                twilio_service = TwilioService()
                logger.info("✅ Twilio service initialized", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                # 🛡️ BULLETPROOF: Mark Twilio as healthy and set reference
                bulletproof_manager.health_status["twilio"] = True
                bulletproof_manager.twilio_service = twilio_service
            except Exception as e:
                logger.error(f"🛡️ Twilio service initialization failed: {e}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                bulletproof_manager.record_error(e, "twilio")
                bulletproof_manager.health_status["twilio"] = False
                raise

            # 🛡️ BULLETPROOF: Initialize Phone Agent with error isolation
            logger.info("🤖 Initializing Phone Agent...", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
            try:
                phone_agent = PhoneAgent(llm_service, twilio_service)
                logger.info("✅ Phone Agent initialized", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                # 🛡️ BULLETPROOF: Mark agent as healthy
                bulletproof_manager.health_status["agent"] = True
            except Exception as e:
                logger.error(f"🛡️ Phone Agent initialization failed: {e}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                bulletproof_manager.record_error(e, "agent")
                bulletproof_manager.health_status["agent"] = False
                raise

            # 🛡️ BULLETPROOF: Set app state with error isolation
            logger.info("🔗 Setting app state...", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
            try:
                app.state.agent = phone_agent
                app.state.twilio_service = twilio_service
                app.state.ready = True

                # 🛡️ BULLETPROOF: Mark service as fully operational
                bulletproof_manager.service_state = "RUNNING"
                bulletproof_manager.recovery_attempts = 0  # Reset recovery counter on success

                logger.info("✅ Phone Agent initialized successfully", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                logger.info("🛡️ BULLETPROOF SERVICE FULLY OPERATIONAL - 100% UPTIME ACHIEVED", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")

                # 🛡️ BULLETPROOF: Keep the background task alive indefinitely
                logger.info("🔄 Background task will now run indefinitely to keep service alive", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")

                # Keep the background task alive by waiting indefinitely
                try:
                    while True:
                        await asyncio.sleep(60)  # Sleep for 1 minute intervals
                        # Optional: Add health checks or maintenance tasks here
                        logger.debug("🔄 Background task heartbeat - service running", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                except asyncio.CancelledError:
                    logger.info("🛑 Background task cancelled - service shutting down", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                    raise  # Re-raise to properly handle cancellation
            except Exception as e:
                logger.error(f"🛡️ App state setting failed: {e}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                bulletproof_manager.record_error(e, "app_state")
                raise
        except Exception as e:
            # 🛡️ BULLETPROOF: Never crash on initialization failure
            logger.error(f"🛡️ BULLETPROOF: Service initialization failed (attempt {service_retry_attempt}): {type(e).__name__}: {e}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
            import traceback
            logger.error(f"🛡️ BULLETPROOF: Service initialization traceback:\n{traceback.format_exc()}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")

            # 🛡️ BULLETPROOF: Record error and attempt recovery
            bulletproof_manager.record_error(e, "service_initialization")

            # 🛡️ BULLETPROOF: Try component-specific recovery
            if "twilio" in str(e).lower():
                logger.info("🛡️ Attempting Twilio recovery...", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                bulletproof_manager.attempt_recovery("twilio")
            elif "backend" in str(e).lower() or "llm" in str(e).lower():
                logger.info("🛡️ Attempting backend recovery...", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                bulletproof_manager.attempt_recovery("backend")

            logger.info("🛡️ BULLETPROOF: Will retry initialization - service NEVER crashes...", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")

            logger.info(f"⏳ Waiting 5 seconds before retry attempt {service_retry_attempt + 1}...", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
            try:
                await asyncio.sleep(5)
                logger.info("✅ Sleep completed successfully", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
            except Exception as sleep_error:
                logger.error(f"💥 CRITICAL: Sleep interrupted during service retry: {sleep_error}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                try:
                    import time
                    logger.info("🔄 Falling back to synchronous sleep...", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                    time.sleep(5)
                    logger.info("✅ Synchronous sleep completed", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                except Exception as sync_sleep_error:
                    logger.error(f"💥 CRITICAL: Even synchronous sleep failed: {sync_sleep_error}", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")

            service_retry_attempt += 1

            # Log progress every 10 attempts
            if service_retry_attempt % 10 == 0:
                logger.info(f"⏳ Still trying to initialize Phone Agent... (attempt {service_retry_attempt})", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")
                logger.info("💪 Will NEVER give up on service initialization!", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")

            # Log every attempt for debugging
            logger.info(f"🔄 Retrying service initialization (attempt {service_retry_attempt})...", module="PHONE-AGENT", routine="wait_for_backend_and_initialize")

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager - starts immediately, handles dependencies in background"""
    log_crash_debug("LIFESPAN_START", "START", f"PID: {os.getpid()}")
    logger.info("🚀 LIFESPAN START - Phone Agent Service", module="PHONE-AGENT", routine="lifespan")
    logger.info(f"🐛 DEBUG: Lifespan PID: {os.getpid()}", module="PHONE-AGENT", routine="lifespan")

    import asyncio

    # Startup
    log_crash_debug("LIFESPAN_STARTUP", "START", "Starting Phone Agent Service")
    logger.info("🚀 Starting Phone Agent Service", module="PHONE-AGENT", routine="lifespan")
    logger.info("💪 Service will start immediately and handle dependencies in background - NEVER crashes!", module="PHONE-AGENT", routine="lifespan")

    # Initialize basic state
    app.state.ready = False
    app.state.agent = None
    app.state.twilio_service = None

    # Start background task to wait for dependencies - this will NEVER crash the service
    try:
        log_crash_debug("BACKGROUND_TASK_CREATION", "START", "Creating background initialization task")
        logger.info("🔧 Creating background initialization task...", module="PHONE-AGENT", routine="lifespan")
        background_task = asyncio.create_task(wait_for_backend_and_initialize(app))
        app.state.background_task = background_task
        logger.info("✅ Background initialization task started", module="PHONE-AGENT", routine="lifespan")
        logger.info(f"🐛 DEBUG: Background task created: {background_task}", module="PHONE-AGENT", routine="lifespan")
        logger.info("waiting for dependencies in background", module="PHONE-AGENT:lifespan", routine="SYSTEM | ✅ Phone Agent service started")
        log_crash_debug("BACKGROUND_TASK_CREATION", "SUCCESS", f"Task: {background_task}")
    except Exception as e:
        log_crash_debug("BACKGROUND_TASK_CREATION", "ERROR", f"{type(e).__name__}: {e}")
        logger.error(f"💥 CRITICAL: Failed to start background task: {type(e).__name__}: {e}", module="PHONE-AGENT", routine="lifespan")
        import traceback
        logger.error(f"💥 CRITICAL: Background task creation traceback:\n{traceback.format_exc()}", module="PHONE-AGENT", routine="lifespan")
        # Even if background task fails, don't crash the service
        app.state.ready = False
        logger.info("⚠️ Background task failed but service will continue running", module="PHONE-AGENT", routine="lifespan")

    # Start heartbeat in the proper asyncio context
    try:
        log_crash_debug("HEARTBEAT_START", "START", "Starting heartbeat in lifespan")
        await start_heartbeat()
        log_crash_debug("HEARTBEAT_START", "SUCCESS", "Heartbeat started successfully")
    except Exception as e:
        log_crash_debug("HEARTBEAT_START", "ERROR", f"Failed to start heartbeat: {e}")
        logger.error(f"⚠️ Failed to start heartbeat: {e}", module="PHONE-AGENT", routine="lifespan")
        # Continue anyway - service must not crash

    # Keep the service running - yield control to the server
    log_crash_debug("LIFESPAN_YIELD", "SUCCESS", "Lifespan startup complete, yielding to server")
    logger.info("🎯 Phone Agent lifespan startup complete - server will now handle requests", module="PHONE-AGENT", routine="lifespan")
    yield

    # Shutdown (this should only happen when the server is actually stopping)
    log_crash_debug("LIFESPAN_SHUTDOWN", "START", "Lifespan shutdown triggered")
    logger.info("🛑 Shutting down Phone Agent Service", module="PHONE-AGENT", routine="lifespan")
    logger.info("🐛 DEBUG: Lifespan shutdown triggered - this should only happen on service termination", module="PHONE-AGENT", routine="lifespan")

    # Stop heartbeat
    global HEARTBEAT_ACTIVE
    HEARTBEAT_ACTIVE = False
    log_crash_debug("HEARTBEAT_SHUTDOWN", "SUCCESS", "Heartbeat stopped")
    if hasattr(app.state, 'background_task'):
        try:
            app.state.background_task.cancel()
        except Exception as e:
            logger.debug("Error canceling background task: {e}", module="PHONE-AGENT", routine="lifespan")
    
    # Clean up webhook server
    try:
        await app.state.twilio_service.stop_webhook_server()
    except Exception as e:
        logger.warning("⚠️ Error during shutdown: {e}", module="PHONE-AGENT", routine="lifespan")


# Create FastAPI app
app = FastAPI(
    title="Phone Agent Service",
    description="Phone Call Agent for Question Answering",
    version="0.1.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 🛡️ GLOBAL EXCEPTION HANDLER: Prevent any unhandled exceptions from reaching Twilio
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler to prevent error messages from being spoken to users"""
    logger.error(f"❌ Unhandled exception: {exc}", module="PHONE-AGENT", routine="global_exception_handler")
    import traceback
    logger.error(f"❌ Traceback: {traceback.format_exc()}", module="PHONE-AGENT", routine="global_exception_handler")

    # If this is a webhook request (contains task_id in path), return TwiML hangup
    if request.url.path.startswith(("/voice/", "/process_speech/", "/no_speech/", "/call_status/")):
        logger.warning("hanging up gracefully", module="PHONE-AGENT:global_exception_handler", routine="WEBHOOK | 🛡️ Webhook error")
        return Response(
            content="<?xml version='1.0' encoding='UTF-8'?><Response><Hangup/></Response>",
            media_type="application/xml"
        )

    # For non-webhook requests, return standard HTTP error
    return JSONResponse(
        content={"error": "Internal server error"},
        status_code=500
    )


@app.get("/health")
async def health_check():
    """🛡️ BULLETPROOF Health check endpoint - ALWAYS responds, comprehensive status"""
    try:
        # 🛡️ BULLETPROOF: Basic service status
        backend_ready = getattr(app.state, 'ready', False)
        service_status = bulletproof_manager.service_state
        is_healthy = bulletproof_manager.is_healthy()
        can_make_calls = bulletproof_manager.can_make_calls()

        # 🛡️ BULLETPROOF: Component health status
        component_health = bulletproof_manager.health_status.copy()

        # 🛡️ BULLETPROOF: Circuit breaker status
        circuit_breaker_status = {}
        if hasattr(app.state, 'agent') and hasattr(app.state.agent, 'circuit_breaker'):
            try:
                circuit_breaker_status = app.state.agent.circuit_breaker.get_status()
            except Exception as e:
                circuit_breaker_status = {"error": str(e)}

        # 🛡️ BULLETPROOF: Twilio service health
        twilio_health = {}
        if hasattr(app.state, 'twilio_service'):
            try:
                twilio_health = app.state.twilio_service.health_check()
            except Exception as e:
                twilio_health = {"error": str(e), "healthy": False}

        # 🛡️ BULLETPROOF: Error information
        error_info = {}
        if bulletproof_manager.last_error:
            error_info = {
                "last_error": bulletproof_manager.last_error["error"],
                "error_component": bulletproof_manager.last_error["component"],
                "error_count": bulletproof_manager.error_count,
                "recovery_attempts": bulletproof_manager.recovery_attempts
            }

        return {
            "status": "healthy" if is_healthy else "degraded",
            "service": "phone-agent",
            "version": "BULLETPROOF-2025-07-10",
            "service_state": service_status,
            "backend_ready": backend_ready,
            "can_make_calls": can_make_calls,
            "message": f"🛡️ BULLETPROOF SERVICE - {service_status}" + (" - FULLY OPERATIONAL" if is_healthy else " - DEGRADED MODE"),
            "component_health": component_health,
            "twilio_health": twilio_health,
            "circuit_breaker": circuit_breaker_status,
            "error_info": error_info,
            "preventive_measures": {
                "disable_error_calls": DISABLE_ERROR_CALLS,
                "circuit_breaker_enabled": hasattr(app.state, 'agent') and hasattr(app.state.agent, 'circuit_breaker'),
                "health_checks_enabled": True,
                "bulletproof_mode": True,
                "auto_recovery": True,
                "uptime_guarantee": "100%"
            },
            "uptime_guarantee": "100%",
            "bulletproof_mode": True,
            "timestamp": time.time()
        }

    except Exception as e:
        # 🛡️ BULLETPROOF: Even health check errors don't crash the service
        logger.error(f"🛡️ BULLETPROOF: Health check error: {e}", module="PHONE-AGENT", routine="health_check")
        return {
            "status": "error",
            "service": "phone-agent",
            "version": "BULLETPROOF-2025-07-10",
            "error": str(e),
            "bulletproof_mode": True,
            "service_operational": True,  # Service is still running even if health check fails
            "uptime_guarantee": "100%",
            "timestamp": time.time()
        }


@app.post("/bulletproof_recovery")
async def bulletproof_recovery():
    """🛡️ BULLETPROOF: Force recovery of all service components"""
    logger.info("🛡️ BULLETPROOF: Manual recovery initiated", module="PHONE-AGENT", routine="bulletproof_recovery")

    recovery_results = {}

    try:
        # 🛡️ BULLETPROOF: Attempt backend recovery
        logger.info("🛡️ BULLETPROOF: Attempting backend recovery...", module="PHONE-AGENT", routine="bulletproof_recovery")
        backend_recovery = bulletproof_manager.attempt_recovery("backend")
        recovery_results["backend"] = {"success": backend_recovery, "component": "backend"}

        # 🛡️ BULLETPROOF: Attempt Twilio recovery
        logger.info("🛡️ BULLETPROOF: Attempting Twilio recovery...", module="PHONE-AGENT", routine="bulletproof_recovery")
        twilio_recovery = False
        if hasattr(app.state, 'twilio_service'):
            try:
                twilio_recovery = app.state.twilio_service.attempt_recovery()
                bulletproof_manager.health_status["twilio"] = twilio_recovery
            except Exception as e:
                logger.error(f"🛡️ BULLETPROOF: Twilio recovery error: {e}", module="PHONE-AGENT", routine="bulletproof_recovery")
        recovery_results["twilio"] = {"success": twilio_recovery, "component": "twilio"}

        # 🛡️ BULLETPROOF: Attempt agent recovery
        logger.info("🛡️ BULLETPROOF: Attempting agent recovery...", module="PHONE-AGENT", routine="bulletproof_recovery")
        agent_recovery = bulletproof_manager.attempt_recovery("agent")
        recovery_results["agent"] = {"success": agent_recovery, "component": "agent"}

        # 🛡️ BULLETPROOF: Update overall service state
        if all(result["success"] for result in recovery_results.values()):
            bulletproof_manager.service_state = "RUNNING"
            bulletproof_manager.recovery_attempts = 0
            overall_success = True
        else:
            bulletproof_manager.service_state = "DEGRADED"
            overall_success = False

        logger.info(f"🛡️ BULLETPROOF: Recovery completed (success: {overall_success})", module="PHONE-AGENT", routine="bulletproof_recovery")

        return {
            "status": "success" if overall_success else "partial",
            "message": "🛡️ BULLETPROOF recovery completed",
            "overall_success": overall_success,
            "recovery_results": recovery_results,
            "service_state": bulletproof_manager.service_state,
            "timestamp": time.time()
        }

    except Exception as e:
        logger.error(f"🛡️ BULLETPROOF: Recovery process error: {e}", module="PHONE-AGENT", routine="bulletproof_recovery")
        return {
            "status": "error",
            "message": f"🛡️ BULLETPROOF recovery error: {e}",
            "recovery_results": recovery_results,
            "service_still_operational": True,
            "timestamp": time.time()
        }


@app.post("/force_initialize")
async def force_initialize():
    """Force re-initialization of the phone agent - for debugging"""
    logger.info("🔄 Force initializing phone agent...", module="PHONE-AGENT", routine="force_initialize")

    try:
        # Import required classes
        from agents.phone.app.agent import PhoneAgent
        from agents.phone.app.llm_service import BackendLLMClient
        from agents.phone.app.twilio_service import TwilioService

        # Initialize services
        llm_service = BackendLLMClient()
        twilio_service = TwilioService()
        phone_agent = PhoneAgent(llm_service, twilio_service)

        # Update app state
        app.state.agent = phone_agent
        app.state.twilio_service = twilio_service

        # 🛡️ BULLETPROOF: Set Twilio service reference for watchdog
        bulletproof_manager.twilio_service = twilio_service
        app.state.ready = True

        logger.info("✅ Phone Agent force-initialized successfully", module="PHONE-AGENT", routine="force_initialize")

        return {
            "status": "success",
            "message": "Phone agent initialized successfully",
            "agent_available": app.state.agent is not None,
            "ready": app.state.ready
        }

    except Exception as e:
        logger.error(f"❌ Failed to force-initialize phone agent: {e}", module="PHONE-AGENT", routine="force_initialize")
        return {
            "status": "error",
            "message": f"Failed to initialize: {str(e)}",
            "agent_available": app.state.agent is not None,
            "ready": app.state.ready
        }

@app.post("/reload_config")
async def reload_config():
    """Reload configuration including webhook URL"""
    logger.info("🔄 Reloading configuration...", module="PHONE-AGENT", routine="reload_config")

    try:
        # Reload environment variables
        import os
        from dotenv import load_dotenv
        load_dotenv(override=True)

        # Update Twilio service webhook URL if available
        if hasattr(app.state, 'twilio_service') and app.state.twilio_service:
            new_webhook_url = os.getenv('TWILIO_WEBHOOK_URL')
            if new_webhook_url:
                app.state.twilio_service.webhook_base_url = new_webhook_url
                logger.info("✅ Webhook URL updated: {new_webhook_url}", module="PHONE-AGENT", routine="reload_config")

                # 🔧 CRITICAL FIX: Update all active call states with new webhook URL
                active_calls_updated = 0
                for task_id, call_state in app.state.twilio_service.active_calls.items():
                    call_state.webhook_base_url = new_webhook_url
                    active_calls_updated += 1
                    logger.info("🔄 Updated webhook URL for active call: {task_id}", module="PHONE-AGENT", routine="reload_config")

                if active_calls_updated > 0:
                    logger.info("✅ Updated webhook URL for {active_calls_updated} active calls", module="PHONE-AGENT", routine="reload_config")
                else:
                    logger.info("ℹ️ No active calls to update", module="PHONE-AGENT", routine="reload_config")
            else:
                logger.warning("⚠️ No webhook URL found in environment", module="PHONE-AGENT", routine="reload_config")

        return {
            "status": "success",
            "message": "Configuration reloaded successfully",
            "webhook_url": os.getenv('TWILIO_WEBHOOK_URL')
        }

    except Exception as e:
        logger.error(f"❌ Failed to reload configuration: {e}", module="PHONE-AGENT", routine="reload_config")
        return {
            "status": "error",
            "message": f"Failed to reload configuration: {str(e)}"
        }


@app.post("/emergency_stop")
async def emergency_stop(request: dict):
    """🚨 Emergency stop all active phone calls immediately"""
    logger.warning("Stopping all active phone calls immediately", module="PHONE-AGENT:emergency_stop", routine="NETWORK | 🚨 EMERGENCY STOP REQUESTED")

    try:
        twilio_service = app.state.twilio_service
        active_calls = list(twilio_service.active_calls.keys())

        if not active_calls:
            logger.info("✅ No active calls to stop", module="PHONE-AGENT", routine="emergency_stop")
            return {
                "status": "success",
                "message": "No active calls to stop",
                "calls_stopped": 0
            }

        # Stop all active calls immediately
        stopped_count = 0
        for task_id in active_calls:
            try:
                await twilio_service.end_call(task_id, "emergency_stop")
                stopped_count += 1
                logger.warning(f"🚨 Emergency stopped call: {task_id}", module="PHONE-AGENT", routine="emergency_stop")
            except Exception as e:
                logger.error(f"❌ Failed to emergency stop call {task_id}: {e}", module="PHONE-AGENT", routine="emergency_stop")

        logger.warning(f"🚨 Emergency stop completed: {stopped_count}/{len(active_calls)} calls stopped", module="PHONE-AGENT", routine="emergency_stop")

        return {
            "status": "success",
            "message": f"Emergency stop completed",
            "calls_stopped": stopped_count,
            "total_calls": len(active_calls),
            "reason": request.get("reason", "emergency_shutdown")
        }

    except Exception as e:
        logger.error(f"❌ Emergency stop failed: {e}", module="PHONE-AGENT", routine="emergency_stop")
        return {
            "status": "error",
            "message": f"Emergency stop failed: {str(e)}",
            "calls_stopped": 0
        }


@app.post("/circuit_breaker/reset")
async def reset_circuit_breaker():
    """🔧 Reset circuit breaker to allow phone calls - for testing/debugging"""
    logger.info("🔧 Circuit breaker reset requested", module="PHONE-AGENT", routine="reset_circuit_breaker")

    try:
        if not hasattr(app.state, 'agent') or not hasattr(app.state.agent, 'circuit_breaker'):
            return {
                "status": "error",
                "message": "Phone agent or circuit breaker not available"
            }

        # Reset the circuit breaker
        app.state.agent.circuit_breaker.reset()

        logger.info("phone calls enabled", module="PHONE-AGENT:reset_circuit_breaker", routine="SYSTEM | ✅ Circuit breaker reset successfully")
        return {
            "status": "success",
            "message": "Circuit breaker reset successfully - phone calls enabled",
            "circuit_breaker": app.state.agent.circuit_breaker.get_status()
        }

    except Exception as e:
        logger.error(f"❌ Failed to reset circuit breaker: {e}", module="PHONE-AGENT", routine="reset_circuit_breaker")
        return {
            "status": "error",
            "message": f"Failed to reset circuit breaker: {str(e)}"
        }


@app.get("/circuit_breaker/status")
async def get_circuit_breaker_status():
    """📊 Get current circuit breaker status"""
    try:
        if not hasattr(app.state, 'agent') or not hasattr(app.state.agent, 'circuit_breaker'):
            return {
                "status": "error",
                "message": "Phone agent or circuit breaker not available"
            }

        status = app.state.agent.circuit_breaker.get_status()
        return {
            "status": "success",
            "circuit_breaker": status
        }

    except Exception as e:
        logger.error(f"❌ Failed to get circuit breaker status: {e}", module="PHONE-AGENT", routine="get_circuit_breaker_status")
        return {
            "status": "error",
            "message": f"Failed to get circuit breaker status: {str(e)}"
        }


@app.post("/watchdog_fix")
async def trigger_watchdog_fix():
    """🔧 WATCHDOG: Manually trigger auto-fix for Twilio service issues"""
    try:
        if not hasattr(app.state, 'agent'):
            return {
                "status": "error",
                "message": "Phone agent not available"
            }

        # Get the Twilio service from bulletproof manager
        twilio_service = bulletproof_manager.twilio_service

        if not twilio_service:
            return {
                "status": "error",
                "message": "Twilio service not available"
            }

        # Trigger watchdog auto-fix
        logger.info("🔧 WATCHDOG: Manual auto-fix triggered via API", module="PHONE-AGENT", routine="trigger_watchdog_fix")
        fix_result = twilio_service.watchdog_auto_fix()

        if fix_result:
            logger.info("✅ WATCHDOG: Manual auto-fix completed successfully", module="PHONE-AGENT", routine="trigger_watchdog_fix")
            return {
                "status": "success",
                "message": "Watchdog auto-fix completed successfully",
                "twilio_healthy": twilio_service.is_healthy,
                "client_available": twilio_service.client is not None
            }
        else:
            logger.warning("⚠️ WATCHDOG: Manual auto-fix failed", module="PHONE-AGENT", routine="trigger_watchdog_fix")
            return {
                "status": "warning",
                "message": "Watchdog auto-fix completed but issues remain",
                "twilio_healthy": twilio_service.is_healthy,
                "client_available": twilio_service.client is not None
            }

    except Exception as e:
        logger.error(f"❌ WATCHDOG: Manual auto-fix error: {e}", module="PHONE-AGENT", routine="trigger_watchdog_fix")
        return {
            "status": "error",
            "message": f"Watchdog auto-fix failed: {str(e)}"
        }


@app.get("/system_health")
async def check_system_health():
    """🔍 Test system health check - for debugging"""
    try:
        if not hasattr(app.state, 'agent'):
            return {
                "status": "error",
                "message": "Phone agent not available"
            }

        # Run the same health check that the phone agent uses
        health_ok = await app.state.agent._check_system_health()

        # 🔧 WATCHDOG: If health check fails, try auto-fix
        if not health_ok:
            logger.warning("⚠️ System health check failed, triggering watchdog auto-fix...", module="PHONE-AGENT", routine="check_system_health")
            fix_result = bulletproof_manager.trigger_watchdog_auto_fix()

            if fix_result:
                # Re-run health check after fix
                health_ok = await app.state.agent._check_system_health()
                logger.info(f"🔧 WATCHDOG: Auto-fix completed, health status: {health_ok}", module="PHONE-AGENT", routine="check_system_health")

        return {
            "status": "success",
            "system_healthy": health_ok,
            "message": "System health check completed",
            "can_make_calls": health_ok and app.state.agent.circuit_breaker.can_execute(),
            "watchdog_triggered": not health_ok
        }

    except Exception as e:
        logger.error(f"❌ Failed to check system health: {e}", module="PHONE-AGENT", routine="check_system_health")
        return {
            "status": "error",
            "message": f"Failed to check system health: {str(e)}"
        }


@app.post("/test_twilio")
async def test_twilio_connection():
    """🧪 Test Twilio connection for watchdog recovery"""
    try:
        logger.info("🧪 Twilio connection test requested by watchdog", module="PHONE-AGENT", routine="test_twilio")

        if not hasattr(app.state, 'agent') or not app.state.agent:
            return {
                "status": "error",
                "message": "Phone agent not initialized"
            }

        # Test Twilio service
        twilio_service = app.state.agent.twilio_service
        if not twilio_service:
            return {
                "status": "error",
                "message": "Twilio service not initialized"
            }

        # Test Twilio client connection
        try:
            # Try to get account info as a connection test
            account = twilio_service.client.api.accounts(twilio_service.account_sid).fetch()

            logger.info("✅ Twilio connection test successful", module="PHONE-AGENT", routine="test_twilio")
            return {
                "status": "success",
                "message": "Twilio connection is healthy",
                "details": {
                    "account_sid": account.sid,
                    "account_status": account.status,
                    "phone_number": twilio_service.twilio_number
                }
            }
        except Exception as twilio_error:
            logger.error(f"❌ Twilio connection test failed: {twilio_error}", module="PHONE-AGENT", routine="test_twilio")
            return {
                "status": "error",
                "message": f"Twilio connection failed: {str(twilio_error)}",
                "details": {"twilio_error": str(twilio_error)}
            }

    except Exception as e:
        logger.error(f"❌ Twilio test endpoint failed: {e}", module="PHONE-AGENT", routine="test_twilio")
        return {
            "status": "error",
            "message": f"Twilio test failed: {str(e)}"
        }


@app.post("/update_webhook")
async def update_webhook_url(request: dict):
    """🔄 Update webhook URL for Twilio recovery"""
    try:
        webhook_url = request.get("webhook_url")
        if not webhook_url:
            return {
                "status": "error",
                "message": "webhook_url is required"
            }

        logger.info("🔄 Updating webhook URL: {webhook_url}", module="PHONE-AGENT", routine="update_webhook")

        if not hasattr(app.state, 'agent') or not app.state.agent:
            return {
                "status": "error",
                "message": "Phone agent not initialized"
            }

        # Update the webhook URL in the agent
        app.state.agent.webhook_url = webhook_url

        # Update Twilio service if available
        if hasattr(app.state.agent, 'twilio_service') and app.state.agent.twilio_service:
            app.state.agent.twilio_service.webhook_url = webhook_url

        logger.info("✅ Webhook URL updated successfully", module="PHONE-AGENT", routine="update_webhook")
        return {
            "status": "success",
            "message": "Webhook URL updated successfully",
            "webhook_url": webhook_url
        }

    except Exception as e:
        logger.error(f"❌ Failed to update webhook URL: {e}", module="PHONE-AGENT", routine="update_webhook")
        return {
            "status": "error",
            "message": f"Failed to update webhook URL: {str(e)}"
        }


@app.post("/execute")
async def execute_task(request: TaskRequest):
    """🛡️ BULLETPROOF Execute a phone call task - NEVER crashes the service"""
    logger.info(f"📥 Received phone call task: {request.task_id}", module="PHONE-AGENT", routine="execute_task()")

    # 🛡️ BULLETPROOF: Validate request first
    if not request or not hasattr(request, 'task_id') or not request.task_id:
        error_msg = "Invalid request: missing task_id"
        logger.error(f"❌ {error_msg}", module="PHONE-AGENT", routine="execute_task()")
        raise HTTPException(status_code=400, detail=error_msg)

    try:
        # 🛡️ BULLETPROOF: Check if agent is initialized, auto-initialize if needed
        if not hasattr(app.state, 'agent') or app.state.agent is None:
            logger.warning("⚠️ Phone agent not initialized, attempting auto-initialization...", module="PHONE-AGENT", routine="execute_task()")

            try:
                # Import required classes with error handling
                from agents.phone.app.agent import PhoneAgent
                from agents.phone.app.llm_service import BackendLLMClient
                from agents.phone.app.twilio_service import TwilioService

                # Initialize services with bulletproof error handling
                llm_service = BackendLLMClient()
                twilio_service = TwilioService()
                phone_agent = PhoneAgent(llm_service, twilio_service)

                # Update app state
                app.state.agent = phone_agent
                app.state.twilio_service = twilio_service

                # 🛡️ BULLETPROOF: Set Twilio service reference for watchdog
                bulletproof_manager.twilio_service = twilio_service
                app.state.ready = True

                logger.info("✅ Phone Agent auto-initialized successfully", module="PHONE-AGENT", routine="execute_task()")

            except ImportError as import_error:
                error_msg = f"Phone agent import failed: {str(import_error)}"
                logger.error(f"❌ {error_msg}", module="PHONE-AGENT", routine="execute_task()")
                raise HTTPException(status_code=503, detail=error_msg)
            except Exception as init_error:
                error_msg = f"Phone agent auto-initialization failed: {str(init_error)}"
                logger.error(f"❌ {error_msg}", module="PHONE-AGENT", routine="execute_task()")
                # 🛡️ BULLETPROOF: Don't crash service, return error but continue running
                raise HTTPException(status_code=503, detail=error_msg)

        # 🛡️ BULLETPROOF: Validate agent is available
        if not hasattr(app.state, 'agent') or app.state.agent is None:
            error_msg = "Phone agent not available after initialization attempt"
            logger.error(f"❌ {error_msg}", module="PHONE-AGENT", routine="execute_task()")
            raise HTTPException(status_code=503, detail=error_msg)

        agent = app.state.agent

        # 🛡️ BULLETPROOF: Execute the task asynchronously with error isolation
        # Return immediate response to avoid timeout
        try:
            import asyncio
            # Create task with error isolation - if it fails, service continues
            task = asyncio.create_task(agent.execute_task_async(request))

            # 🛡️ BULLETPROOF: Add task completion callback for monitoring
            def task_done_callback(future):
                try:
                    if future.exception():
                        logger.error(f"❌ Background task failed for {request.task_id}: {future.exception()}",
                                   module="PHONE-AGENT", routine="execute_task")
                    else:
                        logger.debug(f"✅ Background task completed for {request.task_id}",
                                   module="PHONE-AGENT", routine="execute_task")
                except Exception as callback_error:
                    logger.debug(f"Error in task callback: {callback_error}", module="PHONE-AGENT", routine="execute_task")

            task.add_done_callback(task_done_callback)

            logger.info(f"🚀 Phone call task {request.task_id} started in background", module="PHONE-AGENT", routine="execute_task()")

        except Exception as task_error:
            logger.error(f"❌ Failed to start background task for {request.task_id}: {task_error}", module="PHONE-AGENT", routine="execute_task()")
            raise HTTPException(status_code=500, detail=f"Failed to start phone call task: {str(task_error)}")

        return {"status": "accepted", "task_id": request.task_id, "message": "Phone call task started"}

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        # 🛡️ BULLETPROOF: Log error but don't crash service
        logger.error(f"❌ Unexpected error in execute_task for {request.task_id}: {e}", module="PHONE-AGENT", routine="execute_task()")
        logger.debug(f"🔍 Error traceback: {traceback.format_exc()}", module="PHONE-AGENT", routine="execute_task()")

        # Return error but keep service running
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


# Twilio webhook endpoints
@app.post("/voice/{task_id}")
async def handle_voice_webhook(task_id: str, request: Request):
    """Handle incoming voice call webhook from Twilio"""
    logger.info("📞 Voice webhook for task: {task_id}", module="PHONE-AGENT", routine="handle_voice_webhook")
    logger.info("Task: {task_id}", module="PHONE-AGENT:handle_voice_webhook", routine="TASK | 💬 PHONE CONVERSATION START")

    try:
        # 🛡️ SAFETY CHECK: Ensure app state is available
        if not hasattr(app.state, 'twilio_service') or not app.state.twilio_service:
            logger.error("❌ Twilio service not initialized", module="PHONE-AGENT", routine="handle_voice_webhook")
            return Response(
                content="<?xml version='1.0' encoding='UTF-8'?><Response><Hangup/></Response>",
                media_type="application/xml"
            )

        twilio_service = app.state.twilio_service
        call_state = twilio_service.get_call_state(task_id)

        if not call_state:
            logger.error(f"❌ No call state found for task: {task_id}", module="PHONE-AGENT", routine="handle_voice_webhook")
            return Response(
                content="<?xml version='1.0' encoding='UTF-8'?><Response><Hangup/></Response>",
                media_type="application/xml"
            )

        # Update call status
        from .models import CallStatus
        call_state.call_status = CallStatus.ANSWERED

        # Generate initial greeting and question
        try:
            # 🛡️ SAFETY CHECK: Ensure agent is available
            if not hasattr(app.state, 'agent') or not app.state.agent:
                logger.error("❌ Agent not initialized, using fallback", module="PHONE-AGENT", routine="handle_voice_webhook")
                lang_config = LANGUAGE_CONFIGS[call_state.call_config.language]
                message = f"{lang_config['greeting']} {call_state.call_config.question}"
            else:
                agent = app.state.agent
                logger.debug("🧠 Generating initial response for task: {task_id}", module="PHONE-AGENT", routine="handle_voice_webhook")
                llm_response = await agent.llm_service.generate_conversation_response(
                    question=call_state.call_config.question,
                    context=call_state.call_config.context,
                    conversation_so_far="",
                    is_first_turn=True
                )
                logger.debug("✅ LLM response received: {llm_response}", module="PHONE-AGENT", routine="handle_voice_webhook")

                if llm_response.get("content") and isinstance(llm_response["content"], dict):
                    message = llm_response["content"].get("response", "Hello, I have a question for you.")
                else:
                    # Fallback message
                    lang_config = LANGUAGE_CONFIGS[call_state.call_config.language]
                    message = f"{lang_config['greeting']} {call_state.call_config.question}"
        except Exception as llm_error:
            logger.error(f"❌ LLM service error, using fallback message: {llm_error}", module="PHONE-AGENT", routine="handle_voice_webhook")
            import traceback
            logger.error(f"❌ LLM error traceback: {traceback.format_exc()}", module="PHONE-AGENT", routine="handle_voice_webhook")
            # Use simple fallback without LLM
            lang_config = LANGUAGE_CONFIGS[call_state.call_config.language]
            message = f"{lang_config['greeting']} {call_state.call_config.question}"

        # Add agent's message to conversation
        twilio_service.add_conversation_turn(task_id, "agent", message)

        # 💬 LOG CONVERSATION: Agent speaks
        logger.info("💬 DEEPLICA → HUMAN: {message}", module="PHONE-AGENT", routine="handle_voice_webhook")

        # Generate TwiML response
        twiml_response = twilio_service.generate_voice_response(task_id, message, gather_speech=True)

        return Response(content=twiml_response, media_type="application/xml")

    except Exception as e:
        logger.error(f"❌ Error handling voice webhook for task {task_id}: {e}", module="PHONE-AGENT", routine="handle_voice_webhook")
        import traceback
        logger.error(f"❌ Full traceback: {traceback.format_exc()}", module="PHONE-AGENT", routine="handle_voice_webhook")

        # 🛡️ PREVENTIVE MEASURE 2: Don't call users to report errors - ALWAYS hang up gracefully
        logger.warning("🛡️ Error occurred - hanging up gracefully without error message", module="PHONE-AGENT", routine="voice_webhook()")
        return Response(
            content="<?xml version='1.0' encoding='UTF-8'?><Response><Hangup/></Response>",
            media_type="application/xml"
        )


@app.post("/process_speech/{task_id}")
async def handle_speech_webhook(task_id: str, SpeechResult: str = Form(...)):
    """Handle speech processing webhook from Twilio"""
    logger.info("🎤 Speech webhook for task {task_id}: {SpeechResult}", module="PHONE-AGENT", routine="handle_speech_webhook")

    try:
        # 🛡️ SAFETY CHECK: Ensure app state is available
        if not hasattr(app.state, 'twilio_service') or not app.state.twilio_service:
            logger.error("❌ Twilio service not initialized", module="PHONE-AGENT", routine="handle_speech_webhook")
            return Response(
                content="<?xml version='1.0' encoding='UTF-8'?><Response><Hangup/></Response>",
                media_type="application/xml"
            )

        twilio_service = app.state.twilio_service
        call_state = twilio_service.get_call_state(task_id)

        if not call_state:
            logger.error(f"❌ No call state found for task: {task_id}", module="PHONE-AGENT", routine="handle_speech_webhook")
            return Response(
                content="<?xml version='1.0' encoding='UTF-8'?><Response><Hangup/></Response>",
                media_type="application/xml"
            )

        # Add user's speech to conversation
        twilio_service.add_conversation_turn(task_id, "user", SpeechResult)

        # 💬 LOG CONVERSATION: Human speaks
        logger.info("💬 HUMAN → DEEPLICA: {SpeechResult}", module="PHONE-AGENT", routine="handle_speech_webhook")
        
        # Build conversation transcript for LLM
        transcript_lines = []
        for turn in call_state.conversation:
            transcript_lines.append(f"{turn.speaker}: {turn.text}")
        conversation_so_far = "\n".join(transcript_lines)
        
        # Get LLM response
        try:
            # 🛡️ SAFETY CHECK: Ensure agent is available
            if not hasattr(app.state, 'agent') or not app.state.agent:
                logger.error("❌ Agent not initialized, ending call", module="PHONE-AGENT", routine="handle_speech_webhook")
                message = "Thank you for your time."
                should_end_call = True
            else:
                agent = app.state.agent
                logger.debug("🧠 Generating response for speech: {SpeechResultAGENT", routine="handle_speech_webhook")
                llm_response = await agent.llm_service.generate_conversation_response(
                    question=call_state.call_config.question,
                    context=call_state.call_config.context,
                    conversation_so_far=conversation_so_far,
                    is_first_turn=False
                )
                logger.debug("✅ LLM response received: {llm_response}", module="PHONE-AGENT", routine="handle_speech_webhook")

                should_end_call = False
                if llm_response.get("content") and isinstance(llm_response["content"], dict):
                    content = llm_response["content"]
                    message = content.get("response", "Thank you.")
                    should_end_call = content.get("should_end_call", False)
                else:
                    message = "Thank you for your time."
                    should_end_call = True
        except Exception as llm_error:
            logger.error("❌ LLM service error during conversation: {llm_error}", module="PHONE-AGENT", routine="handle_speech_webhook")
            import traceback
            logger.error("❌ LLM error traceback: {traceback.format_exc()}", module="PHONE-AGENT", routine="handle_speech_webhook")

            # Use intelligent fallback based on conversation length and language
            try:
                lang_config = LANGUAGE_CONFIGS[call_state.call_config.language]
                if len(call_state.conversation) <= 2:
                    # Early in conversation - ask for clarification
                    message = "I understand. Could you please tell me more about that?"
                    should_end_call = False
                else:
                    # Later in conversation - politely end call
                    message = f"Thank you for your time. {lang_config['goodbye']}"
                    should_end_call = True
            except Exception:
                # Emergency fallback
                message = "Thank you for your response. Let me end this call now."
                should_end_call = True
        
        # Add agent's response to conversation
        twilio_service.add_conversation_turn(task_id, "agent", message)

        # 💬 LOG CONVERSATION: Agent responds
        logger.info("💬 DEEPLICA → HUMAN: {message}", module="PHONE-AGENT", routine="handle_speech_webhook")

        # Generate TwiML response
        if should_end_call:
            # End the call politely
            lang_config = LANGUAGE_CONFIGS[call_state.call_config.language]
            final_message = f"{message} {lang_config['goodbye']}"
            twiml_response = twilio_service.generate_voice_response(task_id, final_message, gather_speech=False)

            # 💬 LOG CONVERSATION: Call ending
            logger.info("💬 DEEPLICA → HUMAN: {final_message}", module="PHONE-AGENT", routine="handle_speech_webhook")
            logger.info("Task: {task_id}", module="PHONE-AGENT:handle_speech_webhook", routine="TASK | 💬 PHONE CONVERSATION END")

            # Schedule call end with delay to allow final message to be delivered
            import asyncio
            asyncio.create_task(_delayed_call_end(twilio_service, task_id))
        else:
            # Continue conversation
            twiml_response = twilio_service.generate_voice_response(task_id, message, gather_speech=True)

        return Response(content=twiml_response, media_type="application/xml")

    except Exception as e:
        logger.error(f"❌ Error handling speech webhook for task {task_id}: {e}", module="PHONE-AGENT", routine="handle_speech_webhook")
        import traceback
        logger.error(f"❌ Full traceback: {traceback.format_exc()}", module="PHONE-AGENT", routine="handle_speech_webhook")

        # 🛡️ PREVENTIVE MEASURE 3: Don't call users to report speech processing errors - ALWAYS hang up gracefully
        logger.warning("🛡️ Error occurred - hanging up gracefully without error message", module="PHONE-AGENT", routine="process_speech()")
        return Response(
            content="<?xml version='1.0' encoding='UTF-8'?><Response><Hangup/></Response>",
            media_type="application/xml"
        )


@app.post("/no_speech/{task_id}")
async def handle_no_speech_webhook(task_id: str):
    """Handle no speech detected webhook from Twilio"""
    logger.info("🔇 No speech webhook for task: {task_id}", module="PHONE-AGENT", routine="handle_no_speech_webhook")

    try:
        # 🛡️ SAFETY CHECK: Ensure app state is available
        if not hasattr(app.state, 'twilio_service') or not app.state.twilio_service:
            logger.error("❌ Twilio service not initialized", module="PHONE-AGENT", routine="handle_no_speech_webhook")
            return Response(
                content="<?xml version='1.0' encoding='UTF-8'?><Response><Hangup/></Response>",
                media_type="application/xml"
            )

        twilio_service = app.state.twilio_service
        call_state = twilio_service.get_call_state(task_id)

        if not call_state:
            logger.error(f"❌ No call state found for task: {task_id}", module="PHONE-AGENT", routine="handle_no_speech_webhook")
            return Response(
                content="<?xml version='1.0' encoding='UTF-8'?><Response><Hangup/></Response>",
                media_type="application/xml"
            )
        
        # Get appropriate no speech message
        lang_config = LANGUAGE_CONFIGS[call_state.call_config.language]
        message = lang_config['no_speech']
        
        # Add to conversation
        twilio_service.add_conversation_turn(task_id, "agent", message)
        
        # Generate TwiML response to try again
        twiml_response = twilio_service.generate_voice_response(task_id, message, gather_speech=True)

        return Response(content=twiml_response, media_type="application/xml")

    except Exception as e:
        logger.error("❌ Error handling no speech webhook: {e}", module="PHONE-AGENT", routine="handle_no_speech_webhook")
        return Response(
            content="<?xml version='1.0' encoding='UTF-8'?><Response><Hangup/></Response>",
            media_type="application/xml"
        )


@app.post("/call_status/{task_id}")
async def handle_call_status_webhook(task_id: str, request: Request):
    """Handle call status updates from Twilio"""
    form_data = await request.form()
    call_status = form_data.get('CallStatus')

    logger.info("📊 Call status webhook for task {task_id}: {call_status}", module="PHONE-AGENT", routine="handle_call_status_webhook")

    # 💬 LOG CALL STATUS CHANGES
    if call_status == 'initiated':
        logger.info("Task: {task_id}", module="PHONE-AGENT:handle_call_status_webhook", routine="TASK | 📞 CALL INITIATED")
    elif call_status == 'ringing':
        logger.info("Task: {task_id}", module="PHONE-AGENT:handle_call_status_webhook", routine="TASK | 📞 PHONE RINGING")
    elif call_status == 'answered':
        logger.info("Task: {task_id}", module="PHONE-AGENT:handle_call_status_webhook", routine="TASK | 📞 CALL ANSWERED")
    elif call_status == 'completed':
        logger.info("Task: {task_id}", module="PHONE-AGENT:handle_call_status_webhook", routine="TASK | 📞 CALL COMPLETED")
    elif call_status == 'busy':
        logger.info("Task: {task_id}", module="PHONE-AGENT:handle_call_status_webhook", routine="TASK | 📞 PHONE BUSY")
    elif call_status == 'no-answer':
        logger.info("Task: {task_id}", module="PHONE-AGENT:handle_call_status_webhook", routine="TASK | 📞 NO ANSWER")
    else:
        logger.info("Task: {task_id}", module="PHONE-AGENT:handle_call_status_webhook", routine="TASK | 📞 CALL STATUS: {call_status}")

    try:
        twilio_service = app.state.twilio_service
        call_state = twilio_service.get_call_state(task_id)

        if call_state:
            # Update call status based on Twilio status
            from .models import CallStatus
            if call_status == 'completed':
                call_state.call_status = CallStatus.COMPLETED
            elif call_status == 'busy':
                call_state.call_status = CallStatus.BUSY
            elif call_status == 'no-answer':
                call_state.call_status = CallStatus.NO_ANSWER
            elif call_status == 'failed':
                call_state.call_status = CallStatus.FAILED
        
        return {"status": "acknowledged"}
        
    except Exception as e:
        logger.error("❌ Error handling call status webhook: {e}", module="PHONE-AGENT", routine="handle_call_status_webhook")
        return {"status": "error", "message": str(e)}


async def main():
    """🛡️ BULLETPROOF Main startup function - GUARANTEED 100% UPTIME"""
    # Logger is already configured via unified_logging system
    global SHUTDOWN_REQUESTED

    # Add restart counter to prevent infinite loops
    restart_count = 0
    max_restarts = 50  # Increased limit to allow more attempts

    # 🛡️ BULLETPROOF CRASH PREVENTION - Initialize first!
    log_crash_debug("MAIN_STARTUP", "START", f"PID: {PROCESS_PID}")

    # 🛡️ BULLETPROOF: Infinite retry loop - service NEVER exits
    retry_count = 0
    while not SHUTDOWN_REQUESTED:
        try:
            retry_count += 1

            # Only increment restart_count on actual failures, not on startup attempts
            if retry_count > 1:
                restart_count += 1

            if restart_count > max_restarts:
                logger.error(f"💥 CRITICAL: Phone Agent exceeded maximum restarts ({max_restarts})", module="PHONE-AGENT", routine="main")
                logger.error("💥 CRITICAL: Stopping restart loop to prevent infinite loop", module="PHONE-AGENT", routine="main")
                logger.info("💡 Manual intervention required - check logs for root cause", module="PHONE-AGENT", routine="main")
                # Instead of breaking, wait longer and reset counter
                logger.info("🔄 BULLETPROOF: Resetting restart counter and waiting 60s", module="PHONE-AGENT", routine="main")
                await asyncio.sleep(60)
                restart_count = 0  # Reset counter to allow continued operation
                continue

            if retry_count > 1:
                logger.info(f"🔄 BULLETPROOF: Phone Agent startup attempt #{retry_count} (restart #{restart_count})", module="PHONE-AGENT", routine="main")

            # Original main logic starts here
            # Setup crash prevention mechanisms
            log_crash_debug("CRASH_PREVENTION_SETUP", "START", "Installing signal handlers and crash recovery")
            setup_signal_handlers()
            setup_crash_recovery()
            setup_exit_handlers()
            log_crash_debug("CRASH_PREVENTION_SETUP", "SUCCESS", "All crash prevention mechanisms installed")

            logger.info("🚀 PHONE AGENT STARTUP - BEGIN", module="PHONE-AGENT", routine="main")
            logger.info(f"🐛 DEBUG: Python version: {sys.version}", module="PHONE-AGENT", routine="main")
            logger.info(f"🐛 DEBUG: Current working directory: {os.getcwd()}", module="PHONE-AGENT", routine="main")
            logger.info(f"🐛 DEBUG: Process ID: {os.getpid()}", module="PHONE-AGENT", routine="main")

            log_crash_debug("MAIN_LOGGING", "SUCCESS", "Basic logging initialized")

        except Exception as e:
            log_crash_debug("MAIN_STARTUP", "ERROR", f"Failed to initialize crash prevention: {e}")
            logger.error(f"❌ Failed to initialize crash prevention: {e}", module="PHONE-AGENT", routine="main")
            # Continue anyway - service must not crash

        try:
            # Get configuration from environment
            log_crash_debug("CONFIGURATION_SETUP", "START", "Getting environment configuration")
            logger.info("🔧 Getting configuration from environment...", module="PHONE-AGENT", routine="main")
            server_host = os.getenv("PHONE_AGENT_HOST", get_service_host("phone"))
            logger.info(f"🐛 DEBUG: Server host resolved to: {server_host}", module="PHONE-AGENT", routine="main")
            log_crash_debug("CONFIGURATION_SETUP", "SUCCESS", f"Host: {server_host}")

            # 🛡️ BULLETPROOF: Add startup delay to prevent simultaneous starts
            if retry_count > 1:
                startup_delay = min(retry_count * 2, 10)  # Max 10 seconds delay
                logger.info(f"⏳ Startup delay: {startup_delay}s to prevent port conflicts", module="PHONE-AGENT", routine="main")
                await asyncio.sleep(startup_delay)

            # Ensure port is free before starting
            log_crash_debug("PORT_CLEANUP", "START", "Ensuring phone agent port is free")
            logger.info("🔌 Ensuring phone agent port is free...", module="PHONE-AGENT", routine="main")

            # 🛡️ BULLETPROOF: Multiple attempts to secure port
            port_attempts = 0
            max_port_attempts = 3
            server_port = None

            while port_attempts < max_port_attempts and not server_port:
                try:
                    port_attempts += 1
                    logger.info(f"🔌 Port cleanup attempt {port_attempts}/{max_port_attempts}", module="PHONE-AGENT", routine="main")
                    server_port = ensure_service_port_free("phone", force=True)
                    logger.info(f"✅ Phone agent secured port {server_port}", module="PHONE-AGENT", routine="main")
                    break
                except Exception as port_error:
                    logger.warning(f"⚠️ Port cleanup attempt {port_attempts} failed: {port_error}", module="PHONE-AGENT", routine="main")
                    if port_attempts < max_port_attempts:
                        await asyncio.sleep(2)  # Wait before retry
                    else:
                        # Fallback to alternative port
                        try:
                            from shared.port_manager import DeepLicaPortManager
                            port_mgr = DeepLicaPortManager("phone-fallback")
                            server_port = port_mgr.allocate_port("phone-fallback")
                            logger.info(f"🔄 Using fallback port: {server_port}", module="PHONE-AGENT", routine="main")
                        except Exception as fallback_error:
                            logger.error(f"❌ Fallback port allocation failed: {fallback_error}", module="PHONE-AGENT", routine="main")
                            server_port = get_service_port("phone")  # Last resort
                            logger.info(f"🆘 Using last resort port: {server_port}", module="PHONE-AGENT", routine="main")

            logger.info(f"🐛 DEBUG: Port cleanup completed successfully", module="PHONE-AGENT", routine="main")
            log_crash_debug("PORT_CLEANUP", "SUCCESS", f"Port: {server_port}")

            debug = os.getenv("DEBUG", "false").lower() == "true"
            logger.info(f"🐛 DEBUG: Debug mode: {debug}", module="PHONE-AGENT", routine="main")
            log_crash_debug("DEBUG_MODE_SET", "SUCCESS", f"Debug: {debug}")

        except Exception as config_error:
            log_crash_debug("CONFIGURATION_SETUP", "ERROR", f"{type(config_error).__name__}: {config_error}")
            logger.error(f"💥 CRITICAL: Configuration setup failed: {type(config_error).__name__}: {config_error}", module="PHONE-AGENT", routine="main")
            import traceback
            logger.error(f"💥 CRITICAL: Configuration traceback:\n{traceback.format_exc()}", module="PHONE-AGENT", routine="main")
            # 🛡️ BULLETPROOF: Never exit - wait and retry configuration
            logger.info("🔄 BULLETPROOF: Will retry configuration - service NEVER crashes", module="PHONE-AGENT", routine="main")
            import time
            time.sleep(5)
            continue  # Retry the entire main loop

        try:
            # Check if we should wait for backend readiness
            log_crash_debug("BACKEND_READINESS_CHECK", "START", "Checking backend readiness")
            logger.info("🔗 Checking backend readiness...", module="PHONE-AGENT", routine="main")
            backend_ready = await ensure_backend_ready_before_startup("phone-agent")
            logger.info(f"🐛 DEBUG: Backend readiness result: {backend_ready}", module="PHONE-AGENT", routine="main")

            if not backend_ready:
                logger.warning("⚠️ Backend not ready, starting anyway to avoid hanging", module="PHONE-AGENT", routine="main")
                log_crash_debug("BACKEND_READINESS_CHECK", "WARNING", "Backend not ready, proceeding anyway")
            else:
                logger.info("✅ Backend is ready", module="PHONE-AGENT", routine="main")
                log_crash_debug("BACKEND_READINESS_CHECK", "SUCCESS", "Backend is ready")

        except Exception as backend_error:
            log_crash_debug("BACKEND_READINESS_CHECK", "ERROR", f"{type(backend_error).__name__}: {backend_error}")
            logger.error(f"💥 CRITICAL: Backend readiness check failed: {type(backend_error).__name__}: {backend_error}", module="PHONE-AGENT", routine="main")
            import traceback
            logger.error(f"💥 CRITICAL: Backend check traceback:\n{traceback.format_exc()}", module="PHONE-AGENT", routine="main")
            logger.warning("⚠️ Continuing startup despite backend check failure", module="PHONE-AGENT", routine="main")

        log_crash_debug("SERVER_STARTUP", "START", f"Starting on {server_host}:{server_port}")
        logger.info(f"🌟 Starting Phone Agent on {server_host}:{server_port} (debug={debug})", module="PHONE-AGENT", routine="main")
        logger.info("🐛 DEBUG: About to start logging suppression", module="PHONE-AGENT", routine="main")

        # AGGRESSIVELY suppress ALL HTTP request logging
        try:
            logger.info("🔇 Suppressing uvicorn logging...", module="PHONE-AGENT", routine="main")
            import logging

            # Disable ALL uvicorn logging completely
            for logger_name in ["uvicorn", "uvicorn.access", "uvicorn.error", "uvicorn.asgi"]:
                logger_obj = logging.getLogger(logger_name)
                logger_obj.setLevel(logging.CRITICAL)
                logger_obj.disabled = True
                logger_obj.propagate = False

            logger.info("✅ Uvicorn logging suppressed", module="PHONE-AGENT", routine="main")

        except Exception as logging_error:
            logger.error(f"💥 CRITICAL: Logging suppression failed: {type(logging_error).__name__}: {logging_error}", module="PHONE-AGENT", routine="main")
            import traceback
            logger.error(f"💥 CRITICAL: Logging suppression traceback:\n{traceback.format_exc()}", module="PHONE-AGENT", routine="main")

        # AGGRESSIVE PORT CLEANUP WITH MULTIPLE RETRIES
        import subprocess
        import signal
        import time

    def aggressive_port_cleanup(port, max_retries=5):
        """Aggressively clean up port with multiple methods and retries"""
        logger.info(f"🚀 AGGRESSIVE PORT CLEANUP START - Port {port}, Max Retries: {max_retries}", module="PHONE-AGENT", routine="aggressive_port_cleanup")

        for attempt in range(max_retries):
            logger.info(f"🔧 Port cleanup attempt {attempt + 1}/{max_retries} for port {port}", module="PHONE-AGENT", routine="aggressive_port_cleanup")
            logger.info(f"🐛 DEBUG: Starting cleanup attempt {attempt + 1}", module="PHONE-AGENT", routine="aggressive_port_cleanup")

            try:
                logger.info(f"🐛 DEBUG: Running lsof command for port {port}", module="PHONE-AGENT", routine="aggressive_port_cleanup")

                # Method 1: Use lsof to find and kill processes
                result = subprocess.run(['lsof', '-ti', f':{port}'], capture_output=True, text=True, timeout=10)
                logger.info(f"🐛 DEBUG: lsof return code: {result.returncode}", module="PHONE-AGENT", routine="aggressive_port_cleanup")
                logger.info(f"🐛 DEBUG: lsof stdout: '{result.stdout.strip()}'", module="PHONE-AGENT", routine="aggressive_port_cleanup")
                logger.info(f"🐛 DEBUG: lsof stderr: '{result.stderr.strip()}'", module="PHONE-AGENT", routine="aggressive_port_cleanup")

                if result.returncode == 0 and result.stdout.strip():
                    pids = result.stdout.strip().split('\n')
                    logger.info(f"🎯 Found {len(pids)} process(es) using port {port}: {pids}", module="PHONE-AGENT", routine="aggressive_port_cleanup")

                    for pid_str in pids:
                        if pid_str.strip():
                            try:
                                pid = int(pid_str.strip())
                                # Don't kill ourselves
                                if pid != os.getpid():
                                    logger.info(f"� Killing PID {pid} on port {port}", module="PHONE-AGENT", routine="aggressive_port_cleanup")
                                    # Try SIGTERM first
                                    os.kill(pid, signal.SIGTERM)
                                    time.sleep(1)
                                    # Then SIGKILL if still running
                                    try:
                                        os.kill(pid, signal.SIGKILL)
                                        logger.info(f"💀 Force killed PID {pid}", module="PHONE-AGENT", routine="aggressive_port_cleanup")
                                    except ProcessLookupError:
                                        logger.info(f"✅ PID {pid} terminated", module="PHONE-AGENT", routine="aggressive_port_cleanup")
                            except (ValueError, ProcessLookupError, PermissionError) as e:
                                logger.debug(f"Process cleanup issue for PID {pid_str}: {e}", module="PHONE-AGENT", routine="aggressive_port_cleanup")

                # Method 2: Use netstat as backup
                try:
                    netstat_result = subprocess.run(['netstat', '-tulpn'], capture_output=True, text=True, timeout=10)
                    if netstat_result.returncode == 0:
                        for line in netstat_result.stdout.split('\n'):
                            if f':{port} ' in line and 'LISTEN' in line:
                                logger.info(f"🔍 Netstat found: {line.strip()}", module="PHONE-AGENT", routine="aggressive_port_cleanup")
                except (subprocess.TimeoutExpired, FileNotFoundError):
                    pass

                # Wait for port to be released
                time.sleep(2 + attempt)  # Increasing wait time with each attempt

                # Check if port is now free
                port_check = subprocess.run(['lsof', '-ti', f':{port}'], capture_output=True, text=True, timeout=5)
                if port_check.returncode != 0 or not port_check.stdout.strip():
                    logger.info(f"✅ Port {port} is now free after attempt {attempt + 1}", module="PHONE-AGENT", routine="aggressive_port_cleanup")
                    return True
                else:
                    remaining_pids = port_check.stdout.strip().split('\n')
                    logger.warning(f"⚠️ Port {port} still has {len(remaining_pids)} process(es) after attempt {attempt + 1}", module="PHONE-AGENT", routine="aggressive_port_cleanup")

            except subprocess.TimeoutExpired:
                logger.warning(f"⏰ Timeout during port cleanup attempt {attempt + 1}", module="PHONE-AGENT", routine="aggressive_port_cleanup")
            except Exception as e:
                logger.warning(f"❌ Error during port cleanup attempt {attempt + 1}: {e}", module="PHONE-AGENT", routine="aggressive_port_cleanup")

        logger.error(f"💥 Failed to free port {port} after {max_retries} attempts", module="PHONE-AGENT", routine="aggressive_port_cleanup")
        return False

    # Perform aggressive port cleanup
    logger.info(f"🚀 Starting aggressive port cleanup for port {server_port}", module="PHONE-AGENT", routine="main")
    if not aggressive_port_cleanup(server_port):
        logger.error(f"❌ Cannot start Phone Agent - port {server_port} is permanently blocked", module="PHONE-AGENT", routine="main")
        logger.info("💡 Try manually stopping all DEEPLICA services and restart", module="PHONE-AGENT", routine="main")
        # 🛡️ BULLETPROOF: Never exit - try alternative port or wait
        logger.info("🔄 BULLETPROOF: Will try alternative port - service NEVER crashes", module="PHONE-AGENT", routine="main")
        try:
            from shared.port_manager import DeepLicaPortManager
            port_mgr = DeepLicaPortManager("phone-alt")
            server_port = port_mgr.allocate_port("phone-alt")
            logger.info(f"🔄 Using alternative port: {server_port}", module="PHONE-AGENT", routine="main")
        except:
            # If all else fails, wait and retry
            import time
            time.sleep(30)
            logger.info("🔄 Retrying after delay", module="PHONE-AGENT", routine="main")

    logger.info(f"✅ Port {server_port} is confirmed free, starting Phone Agent", module="PHONE-AGENT", routine="main")
    logger.info("🐛 DEBUG: About to start uvicorn server", module="PHONE-AGENT", routine="main")
    logger.info(f"🐛 DEBUG: Uvicorn parameters - host: {server_host}, port: {server_port}, reload: {debug}", module="PHONE-AGENT", routine="main")

    try:
        log_crash_debug("UVICORN_SERVER_START", "START", "Starting uvicorn server")
        logger.info("🚀 STARTING UVICORN SERVER", module="PHONE-AGENT", routine="main")
        logger.info("🐛 DEBUG: Entering uvicorn.run() call", module="PHONE-AGENT", routine="main")

        # Skip app import test to avoid circular import
        log_crash_debug("APP_IMPORT_TEST", "SKIPPED", "Skipping circular import test")
        logger.info("✅ App is already defined in this module - skipping import test", module="PHONE-AGENT", routine="main")

        # Try using uvicorn.run in a thread to avoid asyncio conflicts
        log_crash_debug("UVICORN_THREAD_SETUP", "START", "Setting up uvicorn server thread")
        logger.info("🔧 Starting uvicorn server in separate thread to avoid asyncio conflicts...", module="PHONE-AGENT", routine="main")

        import uvicorn
        import threading
        import time

        # Create a flag to track server status
        server_started = threading.Event()
        server_error = None

        def run_server():
            nonlocal server_error
            try:
                log_crash_debug("UVICORN_THREAD_START", "START", f"Thread starting uvicorn on {server_host}:{server_port}")
                logger.info("🚀 Starting uvicorn server in thread...", module="PHONE-AGENT", routine="main")
                server_started.set()  # Signal that we're starting
                log_crash_debug("UVICORN_RUN_CALL", "START", "Calling uvicorn.run()")

                # Don't start heartbeat here - uvicorn will handle the event loop
                # The heartbeat will be started in the lifespan function

                uvicorn.run(
                    app,
                    host=server_host,
                    port=server_port,
                    log_level="error",
                    access_log=False,
                    use_colors=False,
                    loop="asyncio"  # Use asyncio loop
                )
                log_crash_debug("UVICORN_RUN_CALL", "COMPLETED", "uvicorn.run() completed normally")
                logger.info("🐛 DEBUG: uvicorn.run() completed in thread", module="PHONE-AGENT", routine="main")
            except Exception as e:
                server_error = e
                log_crash_debug("UVICORN_THREAD_ERROR", "ERROR", f"{type(e).__name__}: {e}")
                logger.error(f"💥 CRITICAL: Server thread error: {type(e).__name__}: {e}", module="PHONE-AGENT", routine="main")
                import traceback
                logger.error(f"💥 CRITICAL: Server thread traceback:\n{traceback.format_exc()}", module="PHONE-AGENT", routine="main")

        # Start server in thread
        log_crash_debug("SERVER_THREAD_START", "START", "Starting server thread")
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()

        # Wait for server to start
        log_crash_debug("SERVER_STARTUP_WAIT", "START", "Waiting for server to start")
        if server_started.wait(timeout=10):
            logger.info("✅ Server thread started successfully", module="PHONE-AGENT", routine="main")
            log_crash_debug("SERVER_STARTUP_WAIT", "SUCCESS", "Server started successfully")

            # Keep the main thread alive
            try:
                log_crash_debug("MAIN_MONITORING_LOOP", "START", "Starting main monitoring loop")
                while server_thread.is_alive() and not SHUTDOWN_REQUESTED:
                    await asyncio.sleep(1)  # Use async sleep instead of time.sleep
                    if server_error:
                        log_crash_debug("SERVER_ERROR_DETECTED", "ERROR", f"Server error: {server_error}")
                        logger.error("💥 CRITICAL: Server thread encountered an error", module="PHONE-AGENT", routine="main")
                        # 🛡️ BULLETPROOF: Never exit - restart server thread
                        logger.info("🔄 BULLETPROOF: Will restart server thread - service NEVER crashes", module="PHONE-AGENT", routine="main")
                        break  # Break out of monitoring loop to restart
                    # Log heartbeat every 60 seconds
                    if int(time.time()) % 60 == 0:
                        log_crash_debug("MAIN_MONITORING_LOOP", "SUCCESS", "Main thread monitoring server")

                log_crash_debug("MAIN_MONITORING_LOOP", "COMPLETED", "Server thread completed")
                logger.info("🐛 DEBUG: Server thread completed", module="PHONE-AGENT", routine="main")

                # If we reach here, the server thread died - restart it
                logger.info("🔄 BULLETPROOF: Server thread died, restarting - service NEVER crashes", module="PHONE-AGENT", routine="main")
                # Raise exception to trigger restart in outer exception handler
                raise Exception("Server thread died unexpectedly")

            except KeyboardInterrupt:
                log_crash_debug("KEYBOARD_INTERRUPT", "INFO", "User interrupted service")
                logger.info("🛑 Phone Agent interrupted by user", module="PHONE-AGENT", routine="main")
                # 🛡️ BULLETPROOF: Even keyboard interrupt doesn't exit - set shutdown flag
                SHUTDOWN_REQUESTED = True
                # No break needed - the while loop will exit when SHUTDOWN_REQUESTED is True
        else:
            log_crash_debug("SERVER_STARTUP_WAIT", "ERROR", "Server failed to start within timeout")
            logger.error("💥 CRITICAL: Server failed to start within timeout", module="PHONE-AGENT", routine="main")
            # 🛡️ BULLETPROOF: Never exit - wait and retry server startup
            logger.info("🔄 BULLETPROOF: Will retry server startup - service NEVER crashes", module="PHONE-AGENT", routine="main")
            await asyncio.sleep(10)  # Use async sleep
            # Raise exception to trigger retry in outer exception handler
            raise Exception("Server failed to start within timeout")

        # This should never be reached now
        logger.error("💥 CRITICAL: Unexpected code path reached!", module="PHONE-AGENT", routine="main")
        await asyncio.sleep(30)  # Wait before retry
        raise Exception("Unexpected code path reached")
    except OSError as e:
        logger.error(f"💥 CRITICAL: OSError caught in uvicorn startup: {type(e).__name__}: {e}", module="PHONE-AGENT", routine="main")
        import traceback
        logger.error(f"💥 CRITICAL: OSError traceback:\n{traceback.format_exc()}", module="PHONE-AGENT", routine="main")

        if "Address already in use" in str(e):
            logger.error(f"❌ Port {server_port} is already in use. Another Phone Agent may be running.", module="PHONE-AGENT", routine="main")
            logger.info("💡 Try stopping other DEEPLICA services first with '🛑 STOP DEEPLICA'", module="PHONE-AGENT", routine="main")

            # Additional debugging for port conflicts
            try:
                import subprocess
                result = subprocess.run(['lsof', '-ti', f':{server_port}'], capture_output=True, text=True, timeout=5)
                if result.returncode == 0 and result.stdout.strip():
                    pids = result.stdout.strip().split('\n')
                    logger.error(f"🔍 DEBUG: Processes still using port {server_port}: {pids}", module="PHONE-AGENT", routine="main")
                    for pid in pids:
                        if pid.strip():
                            try:
                                ps_result = subprocess.run(['ps', '-p', pid.strip(), '-o', 'pid,ppid,command'], capture_output=True, text=True, timeout=5)
                                if ps_result.returncode == 0:
                                    logger.error(f"🔍 DEBUG: Process {pid}: {ps_result.stdout.strip()}", module="PHONE-AGENT", routine="main")
                            except Exception as ps_error:
                                logger.error(f"🔍 DEBUG: Could not get info for PID {pid}: {ps_error}", module="PHONE-AGENT", routine="main")
            except Exception as debug_error:
                logger.error(f"🔍 DEBUG: Port debugging failed: {debug_error}", module="PHONE-AGENT", routine="main")

            # 🛡️ BULLETPROOF: Never exit - wait and retry
            logger.info("🔄 BULLETPROOF: Will retry after port conflict - service NEVER crashes", module="PHONE-AGENT", routine="main")
            await asyncio.sleep(10)  # Use async sleep
            # Re-raise to trigger retry in outer exception handler
            raise Exception(f"Port conflict on {server_port}")
        else:
            logger.error(f"❌ Network error starting Phone Agent: {e}", module="PHONE-AGENT", routine="main")
            # 🛡️ BULLETPROOF: Never exit - wait and retry
            logger.info("🔄 BULLETPROOF: Will retry after network error - service NEVER crashes", module="PHONE-AGENT", routine="main")
            await asyncio.sleep(10)  # Use async sleep
            # Re-raise to trigger retry in outer exception handler
            raise Exception(f"Network error: {e}")

    except KeyboardInterrupt:
        logger.info("🛑 Phone Agent interrupted by user (Ctrl+C)", module="PHONE-AGENT", routine="main")
        # 🛡️ BULLETPROOF: Even keyboard interrupt doesn't exit - set shutdown flag
        SHUTDOWN_REQUESTED = True
        logger.info("🔄 BULLETPROOF: Shutdown flag set - service will stop gracefully", module="PHONE-AGENT", routine="main")

    except Exception as e:
        logger.error(f"💥 CRITICAL: Unexpected error starting Phone Agent: {type(e).__name__}: {e}", module="PHONE-AGENT", routine="main")
        import traceback
        logger.error(f"💥 CRITICAL: Unexpected error traceback:\n{traceback.format_exc()}", module="PHONE-AGENT", routine="main")
        # 🛡️ BULLETPROOF: Never exit - wait and retry
        logger.info("🔄 BULLETPROOF: Will retry after unexpected error - service NEVER crashes", module="PHONE-AGENT", routine="main")
        await asyncio.sleep(10)  # Use async sleep
        # Continue the infinite loop

    # 🛡️ BULLETPROOF: This line should NEVER be reached!
    logger.error("💥 CRITICAL: Phone Agent main function exited infinite loop - THIS SHOULD NEVER HAPPEN!", module="PHONE-AGENT", routine="main")
    # No return - this should never be reached


if __name__ == "__main__":
    # Set distinctive process name for easy identification
    try:
        import setproctitle
        setproctitle.setproctitle("DEEPLICA-PHONE-AGENT")
        logger.info("PHONE-AGENT", module="PHONE-AGENT:startup", routine="PROCESS | Process name set to: DEEPLICA")
    except ImportError:
        logger.warning("process name unchanged", module="PHONE-AGENT:startup", routine="PROCESS | setproctitle not available")

    # Process detection removed per user request - manual management only

    # Set terminal title and clear identification banner - FORCE RENAME EVEN IF ALREADY NAMED
    service_name = os.getenv("SERVICE_NAME", "PHONE-AGENT")

    # Multiple methods to ensure terminal gets renamed
    print(f"\033]0;📞 {service_name}\007", end="")  # xterm title
    print(f"\033]2;📞 {service_name}\007", end="")  # window title
    print(f"\033]1;📞 {service_name}\007", end="")  # icon title

    # Also try VS Code specific terminal naming
    import sys
    import asyncio
    if hasattr(sys, 'ps1') or hasattr(sys, 'ps2'):
        try:
            import os
            os.system(f'echo -ne "\\033]0;📞 {service_name}\\007"')
        except:
            pass

    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"{timestamp} - [INFO] - Svc: {service_name}, Mod: PHONE-AGENT, Cod: startup, msg: {'='*80}")
    print(f"{timestamp} - [INFO] - Svc: {service_name}, Mod: PHONE-AGENT, Cod: startup, msg: 📞 {service_name} TERMINAL")
    print(f"{timestamp} - [INFO] - Svc: {service_name}, Mod: PHONE-AGENT, Cod: startup, msg: {'='*80}")

    logger.info("🚀 PHONE AGENT MAIN EXECUTION START", module="PHONE-AGENT", routine="__main__")
    logger.info(f"🐛 DEBUG: Main execution PID: {os.getpid()}", module="PHONE-AGENT", routine="__main__")
    logger.info(f"🐛 DEBUG: Python executable: {sys.executable}", module="PHONE-AGENT", routine="__main__")
    logger.info(f"🐛 DEBUG: Command line args: {sys.argv}", module="PHONE-AGENT", routine="__main__")

    try:
        logger.info("🔧 Starting asyncio.run(main())...", module="PHONE-AGENT", routine="__main__")
        exit_code = asyncio.run(main())
        logger.info(f"✅ asyncio.run(main()) completed with exit code: {exit_code}", module="PHONE-AGENT", routine="__main__")
    except KeyboardInterrupt:
        logger.info("🛑 Phone Agent interrupted by user (Ctrl+C)", module="PHONE-AGENT", routine="__main__")
        exit_code = 0
    except Exception as e:
        logger.error(f"💥 CRITICAL: Fatal error in Phone Agent main: {type(e).__name__}: {e}", module="PHONE-AGENT", routine="__main__")
        import traceback
        logger.error(f"💥 CRITICAL: Main execution traceback:\n{traceback.format_exc()}", module="PHONE-AGENT", routine="__main__")
        exit_code = 1

    # Check if running in debugger (VS Code)
    if hasattr(sys, 'gettrace') and sys.gettrace() is not None:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"{timestamp} - [INFO] - Svc: PHONE-AGENT, Mod: PHONE-AGENT, Cod: __main__, msg: 🔍 Running in debugger mode - exit code would be: {exit_code}")
        if exit_code != 0:
            print(f"{timestamp} - [INFO] - Svc: PHONE-AGENT, Mod: PHONE-AGENT, Cod: __main__, msg: 💡 In production, this would exit with error code")
            print(f"{timestamp} - [INFO] - Svc: PHONE-AGENT, Mod: PHONE-AGENT, Cod: __main__, msg: 🔧 In debugger mode, not exiting to allow debugging")
            print(f"{timestamp} - [INFO] - Svc: PHONE-AGENT, Mod: PHONE-AGENT, Cod: __main__, msg: 🔄 Keeping process alive for debugging...")
            # Keep the process alive in debugger mode
            try:
                import time
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                print(f"{timestamp} - [INFO] - Svc: PHONE-AGENT, Mod: PHONE-AGENT, Cod: __main__, msg: 🛑 Debugger interrupted by user")
        else:
            print(f"{timestamp} - [INFO] - Svc: PHONE-AGENT, Mod: PHONE-AGENT, Cod: __main__, msg: ✅ Phone Agent completed successfully in debugger mode")
        # Don't exit in debugger mode to allow debugging
    else:
        logger.info(f"🏁 Phone Agent would exit with code: {exit_code} (but BULLETPROOF mode prevents exit)", module="PHONE-AGENT", routine="__main__")
        # 🛡️ BULLETPROOF: Never exit - services must run indefinitely
        logger.info("🔄 BULLETPROOF: Service will restart automatically - NEVER exits", module="PHONE-AGENT", routine="__main__")
        import time
        time.sleep(5)
        # Service will be restarted by the orchestrator
