# Deeplica \- **Functional Prototype**, High-Level Technical Design

## Core Objective

To develop a user-friendly, chat-based interface that empowers individuals to delegate specific, complex, routine "Missions" to an AI-powered system. This system aims to effectively simulate the user's own cognitive processes, providing a seamless and intuitive offloading mechanism for mental tasks.

## Scope Constraints

The Functional Prototype will be limited to the minimal scope that can be operated by 3-5 internal users, and demonstrated to outside stakeholders.

* The Functional Prototype will serve as the foundation for the next step, which is the MVP \- which is intended for the 1st few dozen external friendly users.

It will have the following key constraints:

* **User agnostic:** Will support several specific “hardcoded” users\*. This will postpone the need for authentication, and for real per-user adaptation.  
* **Mission Handling:** Handling one "Mission" per session at a time. Limiting the scope allows for focused development on core functionalities and ensures a stable, functioning prototype.  
* **Interface:** A chat-only interface, along with voice I/O wrapper\*, emphasizing natural language processing and interaction.

#### Other considerations

* External interfaces will include for now voice calls and emails.  
* The product should include system-wide and user specific guardrails.  
* Full logging should be used for flow tracking and error handling.  
* Entire user interaction will be saved in DB.

## Technical Design

#### "Missions" Concept

A "Mission" represents a complex, multi-step user request that requires internal & external back-and-forth communication / actions. Deeplica will be designed to comprehend and execute these Missions on behalf of the user.

Conceptually, a Mission is achieved by completing the following fundamentals:

* **Information Gathering:** Identifying and collecting necessary data.  
* **Decision Making:** Evaluating options and choosing the optimal path.  
* **Interaction:** Communicating with external systems & services, as well as the user.  
* **Execution:** Performing actions to complete the task.

**Examples:**

* **Cable Subscription Negotiation:**  
  * Gathering current subscription details, and expectations.  
  * Researching competitor pricing and offers.  
  * Initiating contact with service provider.  
  * Engaging in negotiation dialogue to lower costs.  
  * Confirming and documenting changes to the subscription.  
* **Managing Restaurant Reservations for a Large Group:**  
  * Gather details on preferred date, time, cuisine, and budget.  
  * Identify suitable restaurants based on preferences.  
  * Check restaurant availability and menus.  
  * Make reservations.  
  * Send confirmation details to the group.

#### Missions: A Task-based approach

* Every Mission demands meticulous on-going breakdown into a hierarchical task graph.  
* Mission completion involves the orchestrated execution of tasks, which can occur concurrently or sequentially.  
* An Agent is responsible for executing each individual task. Task execution may trigger the creation of further tasks within the Mission.

#### Mission representation

* A mission is represented by a hierarchical, JSON-based graph structure encompassing all its constituent tasks.  
* Each node within this graph operates under these principles:  
  * Parent nodes specify the prerequisites for execution.  
  * Child nodes denote the tasks to be triggered subsequently.  
* Each task comprises an action and its assigned agent type, for example, “Call Rob for more guidance” is assigned to the Phone agent.  
* The entire mission’s JSON configuration is stored in MongoDB.  
* Mission orchestration and execution are handled through the Agents Dispatcher.  
* The Agents Dispatcher manages mission orchestration and execution.

#### Agents Dispatcher

The Dispatcher begins by processing the current Mission JSON to determine tasks’ status: completed, in progress, or not yet started. Based on this analysis, it identifies and initiates (concurrently) the next tasks, by triggering their assigned agents.

Upon task completion, the Dispatcher component updates the Mission JSON with the task's results and status. It also manages any necessary adjustments to the remaining task hierarchy within the Mission JSON (only the Dispatcher is allowed to change the Mission).

#### The Tools & Agents

Each agent is a standalone module. For now, all(?) are prompt-based LLM agents.  
The agents divide to several categories:

1. Orchestration:  
   * Planner Agent  
     1. Identifies user intent and understands the request mission  
     2. Breaks a mission into tasks hierarchy  
     3. Breaks a complex task into multiple tasks  
     4. Lists required user clarifications  
   * Prompt Engineer Agent  
     1. Integrates context from the system, user request, and current state.  
     2. Constructs detailed clear LLM prompts.  
     3. Generates a structured JSON object for accurate LLM response.  
   * Dialogue Agent  
     1. Handles user’s feedback loop communication  
     2. Synthesizes merged response from multiple sources to the user  
   * Guardian Agent  
     1. Denies missions & tasks that are blocked or not implemented  
     2. Content rejection (for toxicity, bias, violence, etc.)  
     3. Protects the user & its data  
     4. Protects the system  
   * Flow Tool  
     1. Condition (if / else)  
     2. Logical (or / and / etc.)  
     3. Wait, Retry  
2. Data:  
   * Memory Agent  
     1. Fetches relevant memory snippets based on context  
     2. Summarizes retrieved content into concise outputs  
     3. Writes new insights and updates to memory stores  
     4. Utilizes RAG vector DB (see below)  
   * Database Tool  
     1. System data  
     2. System settings  
     3. User management  
   * Knowledge Agent  
     1. Fetches & elaborates on missing data  
3. Tools:  
   * Phone Agent  
     1. Initiate new voice calls  
     2. Real-time T2S and S2T  
     3. Pauses for user feedback loop  
     4. Summarized highlights and bottom lines  
     5. Utilizes Google Voice  
   * Email Agent  
     1. IMAP connectivity (send & receive)  
     2. Composes, reads, and summarizes emails  
     3. Utilized Google Mail

#### Memory management

TBD. RAG vector DB

#### External APIs / MCP servers

* Google Voice / Zoom  
* Google Mail  
* (user I/O) Web-based chat

Out of scope for the Functional Prototype:

* User authentication  
* User distinction  
* Multiple concurrent missions (utilizing a “Divider” Agent)  
* Additional external interfaces: SMS, calendar\*, Slack, social media, external apps, etc.  
* Voice-based user interaction  
* Payments  
* Paralleling voice calls within a task\*  
* Internal administration tools (e.g. Prompts Editor)  
* Handling of incoming calls  
* Unit / automatic testing

*All items marked with \* are not part of v0 of the Functional Prototype*  
