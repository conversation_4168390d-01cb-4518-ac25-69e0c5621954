#!/usr/bin/env python3
"""
Test the planner's output for the specific scenario to see if it creates correct placeholders.
"""

import asyncio
import sys
import os
import json
from unittest.mock import AsyncMock

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from agents.planner.app.agent import PlannerAgent
from shared_models.communication import TaskRequest, MissionContext


async def test_planner_output():
    """Test what the planner actually produces for our scenario"""
    
    print("🧪 Testing planner output for number exchange scenario...")
    
    # Mock LLM service
    mock_llm_service = AsyncMock()
    
    # Expected response from LLM (what the planner should generate)
    expected_llm_response = {
        "tasks": [
            {
                "task_id": "call_ask_number",
                "task_type": "phone_call",
                "description": "Call to ask for a number",
                "prompt": "Call the provided number to ask for a number",
                "phone_number": "+972547000430",
                "question": "Hi, can you give me a number?",
                "context": "Need to get a number from the contact for use in a follow-up call.",
                "contact_name": "Contact",
                "parent_tasks": [],
                "child_tasks": ["call_tell_number"]
            },
            {
                "task_id": "call_tell_number",
                "task_type": "phone_call",
                "description": "Call to tell the number received",
                "prompt": "Call to inform about the number received from previous call",
                "phone_number": "+972547000430",
                "question": "The number is {{task:MISSION_ID_call_ask_number:extracted_answer}}",
                "context": "Tell the contact the number that was received from the previous call.",
                "contact_name": "Contact",
                "parent_tasks": ["call_ask_number"],
                "child_tasks": []
            }
        ]
    }
    
    # Configure mock LLM to return different responses for different calls
    def mock_generate_response(prompt, system_prompt, **kwargs):
        if "mission metadata" in prompt.lower():
            # Return mission metadata
            return {"content": {
                "title": "Number Exchange Mission",
                "description": "Call to ask for a number and then tell that number back",
                "priority": "normal"
            }}
        else:
            # Return task planning response
            return {"content": expected_llm_response}

    mock_llm_service.generate_response.side_effect = mock_generate_response

    # Create planner agent
    planner = PlannerAgent(mock_llm_service)
    
    # Create mission context
    mission_id = "test_mission_123"
    user_input = "call +972547000430 and ask for a number, then call +972547000430 to tell that number"
    
    mission_context = MissionContext(
        mission_id=mission_id,
        user_input=user_input,
        description="Test mission for number exchange",
        status="in_progress",
        created_at="2025-01-01T10:00:00Z"
    )
    
    # Create mock request with proper mission metadata
    mock_request = TaskRequest(
        task_id="planner_task_123",
        mission_id=mission_id,
        task_type="planning",
        task_data={
            "description": "Plan the mission",
            "mission_metadata": {
                "description": "Test mission for number exchange",
                "priority": "normal"
            }
        },
        callback_url="http://test/callback",
        mission_context=mission_context
    )
    
    try:
        # Execute planning task
        print("📋 Executing planner task...")
        result = await planner._execute_planning_task(mock_request)
        
        print("✅ Planner execution completed")
        print(f"📊 Result keys: {list(result.keys())}")
        
        # Check if we have suggested tasks
        suggested_tasks = result.get("suggested_tasks", [])
        print(f"📋 Number of suggested tasks: {len(suggested_tasks)}")
        
        if len(suggested_tasks) >= 2:
            first_task = suggested_tasks[0]
            second_task = suggested_tasks[1]
            
            print(f"\n📞 First task:")
            print(f"   ID: {first_task.task_id}")
            print(f"   Description: {first_task.description}")
            print(f"   Context: {json.dumps(first_task.context, indent=2)}")
            
            print(f"\n📞 Second task:")
            print(f"   ID: {second_task.task_id}")
            print(f"   Description: {second_task.description}")
            print(f"   Context: {json.dumps(second_task.context, indent=2)}")
            print(f"   Has placeholders: {second_task.has_placeholders}")
            
            # Check if the second task has the correct placeholder
            question = second_task.context.get("question", "")
            print(f"\n🔍 Second task question: '{question}'")
            
            expected_placeholder = f"{{{{task:{mission_id}_call_ask_number:extracted_answer}}}}"
            if expected_placeholder in question:
                print("✅ SUCCESS: Correct placeholder found in question!")
                print(f"   Expected: {expected_placeholder}")
                return True
            else:
                print("❌ FAILED: Placeholder not found or incorrect!")
                print(f"   Expected: {expected_placeholder}")
                print(f"   Found in question: {question}")
                
                # Check if any placeholder exists
                if "{{task:" in question:
                    print("   ⚠️ Some placeholder exists, but format might be wrong")
                else:
                    print("   ❌ No placeholder found at all")
                return False
        else:
            print(f"❌ Expected 2 tasks, got {len(suggested_tasks)}")
            return False
            
    except Exception as e:
        print(f"❌ Planner execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run the test"""
    print("🚀 Starting planner output test...")
    
    success = await test_planner_output()
    
    if success:
        print("\n✅ Planner output test passed!")
        return 0
    else:
        print("\n❌ Planner output test failed!")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
