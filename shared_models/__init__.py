"""
Shared data models for the Deeplica v0 system.

This package contains all core data models used across microservices:
- Mission: Complete user request with task graph and execution state
- Task: Individual units of work within missions
- Enums: Status and type definitions

All microservices should import models from this package to ensure consistency
and avoid duplication.
"""

from .mission import Mission
from .task import Task
from .constants import MissionStatus, MissionPriority, TaskStatus, TaskType
from .communication import TaskRequest, TaskResponse, MissionContext, TaskSuggestion
from .dispatcher import TaskCompletionStatus, TaskCompletionCallback, MissionExecutionResult, AgentInfo

__all__ = [
    "Mission",
    "MissionStatus",
    "MissionPriority",
    "Task",
    "TaskStatus",
    "TaskType",
    "TaskRequest",
    "TaskResponse",
    "TaskCompletionStatus",
    "TaskCompletionCallback",
    "MissionExecutionResult",
    "AgentInfo"
]
