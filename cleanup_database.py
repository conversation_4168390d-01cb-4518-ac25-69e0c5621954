#!/usr/bin/env python3
"""
Clean up the database to fix user management issues.
This script removes invalid user records and recreates indexes.
"""

import asyncio
import os
import sys
from pathlib import Path
from motor.motor_asyncio import AsyncIOMotorClient
from dotenv import load_dotenv

# Load environment variables
PROJECT_ROOT = Path(__file__).parent.absolute()
load_dotenv(PROJECT_ROOT / ".env")


async def cleanup_database():
    """Clean up the database"""
    
    print("🧹 Starting database cleanup...")
    
    # Get connection details
    connection_string = os.getenv("MONGODB_CONNECTION_STRING")
    database_name = os.getenv("MONGODB_DATABASE", "deeplica-dev")
    
    if not connection_string:
        print("❌ No MongoDB connection string found in environment")
        return False
    
    try:
        # Connect to database
        client = AsyncIOMotorClient(
            connection_string,
            serverSelectionTimeoutMS=15000,
            connectTimeoutMS=20000,
            socketTimeoutMS=20000,
            tlsInsecure=True,  # For development
        )
        
        # Test connection
        await client.admin.command('ping')
        print("✅ Connected to MongoDB")
        
        database = client[database_name]
        users_collection = database["users"]
        sessions_collection = database["sessions"]
        
        # Check current state
        total_users = await users_collection.count_documents({})
        null_username_users = await users_collection.count_documents({"username": None})
        empty_username_users = await users_collection.count_documents({"username": ""})
        
        print(f"📊 Current state:")
        print(f"   Total users: {total_users}")
        print(f"   Users with null username: {null_username_users}")
        print(f"   Users with empty username: {empty_username_users}")
        
        # Remove invalid users
        if null_username_users > 0:
            print("🗑️ Removing users with null usernames...")
            result = await users_collection.delete_many({"username": None})
            print(f"   Deleted {result.deleted_count} users with null usernames")
        
        if empty_username_users > 0:
            print("🗑️ Removing users with empty usernames...")
            result = await users_collection.delete_many({"username": ""})
            print(f"   Deleted {result.deleted_count} users with empty usernames")
        
        # Remove invalid sessions
        print("🗑️ Cleaning up orphaned sessions...")
        sessions_result = await sessions_collection.delete_many({"username": {"$in": [None, ""]}})
        print(f"   Deleted {sessions_result.deleted_count} invalid sessions")
        
        # Drop existing indexes to recreate them
        print("🔧 Dropping existing indexes...")
        try:
            await users_collection.drop_indexes()
            print("   Dropped user collection indexes")
        except Exception as e:
            print(f"   Note: {e}")
        
        try:
            await sessions_collection.drop_indexes()
            print("   Dropped session collection indexes")
        except Exception as e:
            print(f"   Note: {e}")
        
        # Recreate indexes with proper settings
        print("🔧 Creating new indexes...")
        
        # User indexes - use sparse=True to allow null values but still enforce uniqueness for non-null values
        await users_collection.create_index("username", unique=True, sparse=True)
        await users_collection.create_index("email", unique=True, sparse=True)
        await users_collection.create_index("user_id", unique=True)
        print("   Created user collection indexes")
        
        # Session indexes
        await sessions_collection.create_index("session_id", unique=True)
        await sessions_collection.create_index("user_id")
        await sessions_collection.create_index("expires_at")
        print("   Created session collection indexes")
        
        # Check final state
        final_users = await users_collection.count_documents({})
        print(f"✅ Cleanup complete. Final user count: {final_users}")
        
        # Close connection
        client.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Database cleanup failed: {e}")
        return False


async def main():
    """Run the cleanup"""
    print("🚀 Starting database cleanup...")
    
    success = await cleanup_database()
    
    if success:
        print("\n✅ Database cleanup completed successfully!")
        print("💡 You can now restart the web chat service")
        return 0
    else:
        print("\n❌ Database cleanup failed!")
        return 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
