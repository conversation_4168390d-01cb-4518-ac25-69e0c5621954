#!/bin/bash
# =============================================================================
# DEEPLICA SERVICE SCRIPT - ALL PORTS MANAGED DYNAMICALLY
# =============================================================================
# ALL ports are assigned by shared/port_manager.py - NO HARDCODED PORTS
# External services adapt to assigned ports through configuration
# =============================================================================

# Development Setup Script
# Installs all dependencies for microservices development

set -e  # Exit on any error

echo "🚀 Setting up Microservices Development Environment"
echo "=================================================="

# Check Python version
echo "🐍 Checking Python version..."
python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
echo "Python version: $python_version"

if ! python3 -c "import sys; sys.exit(0 if sys.version_info >= (3, 8) else 1)"; then
    echo "❌ Python 3.8+ is required"
    exit 1
fi

echo "✅ Python version is compatible"

# Check if virtual environment is recommended
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo "⚠️  You're not in a virtual environment."
    echo "   It's recommended to use a virtual environment:"
    echo "   python -m venv venv"
    echo "   source venv/bin/activate  # Linux/Mac"
    echo "   venv\\Scripts\\activate     # Windows"
    echo ""
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Setup cancelled"
        exit 0
    fi
fi

# Upgrade pip
echo "📦 Upgrading pip..."
python3 -m pip install --upgrade pip

# Install base requirements
echo "📦 Installing base requirements..."
pip3 install -r requirements.txt

# Ask about development dependencies
echo ""
read -p "Install development dependencies? (testing, linting, etc.) [Y/n]: " -n 1 -r
echo
if [[ $REPLY =~ ^[Nn]$ ]]; then
    echo "⏭️  Skipping development dependencies"
else
    echo "📦 Installing development requirements..."
    pip3 install -r requirements-dev.txt
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file and set your GEMINI_API_KEY"
else
    echo "✅ .env file already exists"
fi

# Check if Docker is available
echo "🐳 Checking Docker availability..."
if command -v docker &> /dev/null; then
    if docker info &> /dev/null; then
        echo "✅ Docker is available and running"
        
        # Ask about MongoDB setup
        echo ""
        read -p "Start MongoDB container for development? [Y/n]: " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Nn]$ ]]; then
            echo "🗃️  Starting MongoDB container..."
            docker run -d \
                --name prototype-mongo-dev \
                -p 27017:27017 \
                -e MONGO_INITDB_DATABASE=prototype \
                -v prototype_mongo_dev_data:/data/db \
                mongo:7 || echo "MongoDB container may already be running"
            
            echo "✅ MongoDB started on mongodb://localhost:${get_service_port("mongodb-proxy")}"
        fi
    else
        echo "⚠️  Docker is installed but not running"
    fi
else
    echo "⚠️  Docker is not installed"
    echo "   Install Docker for local development: https://docs.docker.com/get-docker/"
fi

# Verify installation
echo ""
echo "🧪 Verifying installation..."

# Test imports
python -c "
import fastapi
import httpx
import motor
import google.generativeai
import loguru
print('✅ All core dependencies imported successfully')
" || echo "❌ Some dependencies failed to import"

# Check if services can be imported
echo "🔍 Checking service imports..."

# Test backend
if python -c "import sys; sys.path.append('backend'); from app.main import app" 2>/dev/null; then
    echo "✅ Backend API imports successfully"
else
    echo "⚠️  Backend API import issues (may need GEMINI_API_KEY)"
fi

# Test dispatcher
if python -c "import sys; sys.path.append('dispatcher'); from app.main import app" 2>/dev/null; then
    echo "✅ Dispatcher imports successfully"
else
    echo "⚠️  Dispatcher import issues"
fi

# Test agents
if python -c "import sys; sys.path.append('agents/dialogue'); from app.main import app" 2>/dev/null; then
    echo "✅ Dialogue Agent imports successfully"
else
    echo "⚠️  Dialogue Agent import issues (may need GEMINI_API_KEY)"
fi

if python -c "import sys; sys.path.append('agents/planner'); from app.main import app" 2>/dev/null; then
    echo "✅ Planner Agent imports successfully"
else
    echo "⚠️  Planner Agent import issues (may need GEMINI_API_KEY)"
fi

# Summary
echo ""
echo "🎉 Development Environment Setup Complete!"
echo ""
echo "📋 Next Steps:"
echo "  1. Edit .env file and set your GEMINI_API_KEY"
echo "  2. Open VS Code and use the debug configurations"
echo "  3. Start debugging with '🚀 All Microservices'"
echo ""
echo "🚀 Quick Start Commands:"
echo "  # Start all services in VS Code debugger"
echo "  # OR start individual services:"
echo "  cd backend && python app/main.py"
echo "  cd dispatcher && python app/main.py"
echo "  cd agents/dialogue && python app/main.py"
echo "  cd agents/planner && python app/main.py"
echo ""
echo "🧪 Test the setup:"
echo "  curl http://localhost:8000/health"
echo ""
echo "📚 Documentation:"
echo "  - Architecture: MICROSERVICES.md"
echo "  - Deployment: deploy/README.md"
echo "  - VS Code Debug: .vscode/launch.json"
