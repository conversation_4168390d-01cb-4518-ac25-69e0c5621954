"""
DEEPLICA Password Utilities
Unified PBKDF2 with salt password hashing system
"""

import hashlib
import secrets
import base64
from typing import Tuple, Optional


class PasswordHasher:
    """
    Unified password hashing utility using PBKDF2 with salt (more secure)
    This is the SELECTED ONE SYSTEM WIDE - all other methods must be replaced
    """
    
    # PBKDF2 configuration
    ALGORITHM = 'pbkdf2_sha256'
    ITERATIONS = 100000  # Recommended minimum for 2025
    SALT_LENGTH = 32     # 32 bytes = 256 bits
    HASH_LENGTH = 32     # 32 bytes = 256 bits
    
    @classmethod
    def generate_salt(cls) -> str:
        """Generate a cryptographically secure random salt"""
        salt_bytes = secrets.token_bytes(cls.SALT_LENGTH)
        return base64.b64encode(salt_bytes).decode('utf-8')
    
    @classmethod
    def hash_password(cls, password: str, salt: Optional[str] = None) -> str:
        """
        Hash password using PBKDF2 with salt (more secure)
        
        Args:
            password: Plain text password
            salt: Optional salt (if None, generates new one)
            
        Returns:
            Formatted hash string: pbkdf2_sha256$iterations$salt$hash
        """
        if salt is None:
            salt = cls.generate_salt()
        
        # Convert salt from base64 to bytes
        salt_bytes = base64.b64decode(salt.encode('utf-8'))
        
        # Generate PBKDF2 hash
        hash_bytes = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt_bytes,
            cls.ITERATIONS
        )
        
        # Encode hash to base64
        hash_b64 = base64.b64encode(hash_bytes).decode('utf-8')
        
        # Return formatted hash string
        return f"{cls.ALGORITHM}${cls.ITERATIONS}${salt}${hash_b64}"
    
    @classmethod
    def verify_password(cls, password: str, stored_hash: str) -> bool:
        """
        Verify password against stored hash
        
        Args:
            password: Plain text password to verify
            stored_hash: Stored hash string
            
        Returns:
            True if password matches, False otherwise
        """
        try:
            # Handle legacy formats for backward compatibility
            if stored_hash.startswith('pbkdf2_sha256$'):
                # New PBKDF2 format
                return cls._verify_pbkdf2(password, stored_hash)
            elif len(stored_hash) == 64:
                # Legacy SHA256 format - convert to PBKDF2
                return cls._verify_legacy_sha256(password, stored_hash)
            else:
                # Unknown format
                return False
                
        except Exception as e:
            print(f"❌ Password verification error: {e}")
            return False
    
    @classmethod
    def _verify_pbkdf2(cls, password: str, stored_hash: str) -> bool:
        """Verify PBKDF2 hash"""
        try:
            parts = stored_hash.split('$')
            if len(parts) != 4:
                return False
                
            algorithm, iterations, salt, hash_b64 = parts
            
            if algorithm != cls.ALGORITHM:
                return False
                
            # Generate hash with same salt and iterations
            test_hash = cls.hash_password(password, salt)
            
            # Compare hashes
            return secrets.compare_digest(stored_hash, test_hash)
            
        except Exception:
            return False
    
    @classmethod
    def _verify_legacy_sha256(cls, password: str, stored_hash: str) -> bool:
        """Verify legacy SHA256 hash (for backward compatibility)"""
        try:
            # Generate SHA256 hash
            sha256_hash = hashlib.sha256(password.encode('utf-8')).hexdigest()
            return secrets.compare_digest(stored_hash, sha256_hash)
        except Exception:
            return False
    
    @classmethod
    def needs_rehash(cls, stored_hash: str) -> bool:
        """
        Check if stored hash needs to be rehashed to current standard
        
        Args:
            stored_hash: Stored hash string
            
        Returns:
            True if hash needs to be updated to PBKDF2
        """
        if not stored_hash.startswith('pbkdf2_sha256$'):
            return True
            
        try:
            parts = stored_hash.split('$')
            if len(parts) != 4:
                return True
                
            algorithm, iterations, salt, hash_b64 = parts
            
            # Check if iterations are up to current standard
            if int(iterations) < cls.ITERATIONS:
                return True
                
            return False
            
        except Exception:
            return True
    
    @classmethod
    def migrate_password_hash(cls, password: str, old_hash: str) -> Optional[str]:
        """
        Migrate old hash format to new PBKDF2 format
        
        Args:
            password: Plain text password
            old_hash: Old hash format
            
        Returns:
            New PBKDF2 hash if migration successful, None otherwise
        """
        if cls.verify_password(password, old_hash):
            return cls.hash_password(password)
        return None


# Convenience functions for backward compatibility
def hash_password(password: str) -> str:
    """Hash password using PBKDF2 with salt (more secure)"""
    return PasswordHasher.hash_password(password)


def verify_password(password: str, stored_hash: str) -> bool:
    """Verify password against stored hash"""
    return PasswordHasher.verify_password(password, stored_hash)


def needs_rehash(stored_hash: str) -> bool:
    """Check if hash needs to be updated to current standard"""
    return PasswordHasher.needs_rehash(stored_hash)


# Default admin and guest credentials
DEFAULT_ADMIN_PASSWORD_HASH = PasswordHasher.hash_password("admin123")
DEFAULT_GUEST_PASSWORD_HASH = PasswordHasher.hash_password("guest123")

print(f"🔐 Default admin password hash: {DEFAULT_ADMIN_PASSWORD_HASH}")
print(f"🔐 Default guest password hash: {DEFAULT_GUEST_PASSWORD_HASH}")
