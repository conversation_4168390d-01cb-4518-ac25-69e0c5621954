#!/usr/bin/env python3
"""
🔍 MongoDB Connectivity Test
Tests MongoDB Atlas connection and basic operations
"""

import os
import sys
import asyncio
from datetime import datetime, timezone
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError
import traceback
from dotenv import load_dotenv
from shared.api_manager import get_mongodb_config

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

async def test_mongodb_connection():
    """Test MongoDB Atlas connection"""
    print("🔍 TESTING MONGODB ATLAS CONNECTIVITY")
    print("=" * 60)
    
    try:
        # Get MongoDB configuration from API manager
        mongodb_config = get_mongodb_config()
        mongodb_uri = mongodb_config["uri"]
        database_name = mongodb_config["database"]
        
        if not mongodb_uri:
            print("❌ No MongoDB URI found in environment variables")
            return False
            
        print(f"📡 MongoDB URI: {mongodb_uri[:50]}...")
        print(f"🗄️ Database: {database_name}")
        print()
        
        # Create MongoDB client
        print("🔌 Creating MongoDB client...")
        client = AsyncIOMotorClient(mongodb_uri, serverSelectionTimeoutMS=10000)
        
        # Test connection
        print("⏳ Testing connection...")
        await client.admin.command('ping')
        print("✅ MongoDB connection successful!")
        
        # Get database
        db = client[database_name]
        
        # Test database access
        print(f"📊 Testing database access to '{database_name}'...")
        collections = await db.list_collection_names()
        print(f"✅ Database accessible! Found {len(collections)} collections:")
        for collection in collections:
            print(f"   📁 {collection}")
        
        # Test basic operations
        print("\n🧪 Testing basic operations...")
        
        # Test collection access
        test_collection = db.test_connectivity
        
        # Insert test document
        test_doc = {
            "test_id": "connectivity_test",
            "timestamp": datetime.now(timezone.utc),
            "status": "testing"
        }
        
        print("📝 Inserting test document...")
        result = await test_collection.insert_one(test_doc)
        print(f"✅ Document inserted with ID: {result.inserted_id}")
        
        # Read test document
        print("📖 Reading test document...")
        found_doc = await test_collection.find_one({"test_id": "connectivity_test"})
        if found_doc:
            print(f"✅ Document found: {found_doc['test_id']}")
        else:
            print("❌ Document not found")
            
        # Update test document
        print("✏️ Updating test document...")
        update_result = await test_collection.update_one(
            {"test_id": "connectivity_test"},
            {"$set": {"status": "updated", "updated_at": datetime.now(timezone.utc)}}
        )
        print(f"✅ Document updated: {update_result.modified_count} documents modified")
        
        # Delete test document
        print("🗑️ Cleaning up test document...")
        delete_result = await test_collection.delete_one({"test_id": "connectivity_test"})
        print(f"✅ Document deleted: {delete_result.deleted_count} documents removed")
        
        # Test user collections
        print("\n👥 Testing user management collections...")
        users_collection = db.users
        user_count = await users_collection.count_documents({})
        print(f"📊 Users collection: {user_count} users")
        
        # Test missions collection
        missions_collection = db.missions
        mission_count = await missions_collection.count_documents({})
        print(f"📊 Missions collection: {mission_count} missions")
        
        # Test tasks collection
        tasks_collection = db.tasks
        task_count = await tasks_collection.count_documents({})
        print(f"📊 Tasks collection: {task_count} tasks")
        
        # Close connection
        client.close()
        
        print("\n🎉 MongoDB connectivity test PASSED!")
        print("✅ All operations completed successfully")
        return True
        
    except ConnectionFailure as e:
        print(f"❌ MongoDB connection failed: {e}")
        return False
    except ServerSelectionTimeoutError as e:
        print(f"❌ MongoDB server selection timeout: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        print(f"🔍 Traceback: {traceback.format_exc()}")
        return False

async def test_backend_mongodb_integration():
    """Test Backend API MongoDB integration"""
    print("\n🌐 TESTING BACKEND API MONGODB INTEGRATION")
    print("=" * 60)

    try:
        # Import backend database module
        from backend.app.services.database import DatabaseService

        print("📦 Testing backend database service...")

        # Create database service instance
        print("🔌 Creating database service instance...")
        db_service = DatabaseService()

        # Test database connection
        print("⏳ Testing database connection...")
        await db_service.connect()
        print("✅ Backend database connection successful!")

        # Test basic database operations
        print("🧪 Testing basic database operations...")

        # Test collections access
        if hasattr(db_service, 'missions_collection') and db_service.missions_collection is not None:
            print("✅ Missions collection accessible!")
        else:
            print("⚠️ Missions collection not accessible")

        if hasattr(db_service, 'tasks_collection') and db_service.tasks_collection is not None:
            print("✅ Tasks collection accessible!")
        else:
            print("⚠️ Tasks collection not accessible")

        # Clean up
        await db_service.disconnect()
        print("✅ Backend database service test completed!")

        return True

    except ImportError as e:
        print(f"❌ Cannot import backend database module: {e}")
        return False
    except Exception as e:
        print(f"❌ Backend MongoDB integration test failed: {e}")
        print(f"🔍 Traceback: {traceback.format_exc()}")
        return False

async def main():
    """Main test function"""
    print("🔍 DEEPLICA MONGODB CONNECTIVITY TEST")
    print("=" * 80)
    
    # Test direct MongoDB connection
    mongodb_test = await test_mongodb_connection()
    
    # Test backend integration
    backend_test = await test_backend_mongodb_integration()
    
    # Summary
    print("\n📊 TEST SUMMARY")
    print("=" * 60)
    print(f"MongoDB Direct Connection: {'✅ PASS' if mongodb_test else '❌ FAIL'}")
    print(f"Backend API Integration:   {'✅ PASS' if backend_test else '❌ FAIL'}")
    
    if mongodb_test and backend_test:
        print("\n🎉 ALL MONGODB TESTS PASSED!")
        return True
    else:
        print("\n❌ SOME MONGODB TESTS FAILED!")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
