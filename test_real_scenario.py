#!/usr/bin/env python3
"""
Test the real scenario with DEEPLICA system to identify where placeholders fail.
This will create a mission and inspect the actual task creation and execution.
"""

import asyncio
import sys
import os
import json
import httpx
from typing import Dict, Any
from shared.port_manager import get_service_port

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)


async def test_real_scenario():
    """Test with the real DEEPLICA system"""
    
    print("🚀 Testing real scenario with DEEPLICA system...")
    
    # Check if backend is running
    from shared.port_manager import get_service_port, get_service_host
    backend_url = f"http://{get_service_host('backend')}:{get_service_port('backend')}"
    dispatcher_url = f"http://localhost:{get_service_port('dispatcher')}"
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            # Check backend health
            response = await client.get(f"{backend_url}/health")
            if response.status_code != 200:
                print(f"❌ Backend not running at {backend_url}")
                return False
            print("✅ Backend is running")
            
            # Check dispatcher health
            response = await client.get(f"{dispatcher_url}/health")
            if response.status_code != 200:
                print(f"❌ Dispatcher not running at {dispatcher_url}")
                return False
            print("✅ Dispatcher is running")
            
        except Exception as e:
            print(f"❌ Failed to connect to services: {e}")
            print("Please make sure DEEPLICA services are running")
            return False
    
    # Create a mission with the test scenario
    print("\n📋 Creating mission...")
    
    # Use the mission that already completed planning
    mission_id = "abf57a3b-1b9d-4247-96f9-6ae080460d70"
    print(f"✅ Using existing mission: {mission_id}")

    # Skip mission creation and go directly to checking tasks
    # mission_data = {
    #     "user_input": "call +972547000430 and ask for a number, then call +972547000430 to tell that number",
    #     "description": "Test mission for placeholder resolution",
    #     "priority": "normal"
    # }
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            # Skip mission creation - using existing mission
            # response = await client.post(f"{backend_url}/api/v1/missions", json=mission_data)
            # response.raise_for_status()
            # mission_result = response.json()
            # mission_id = mission_result["mission_id"]
            # print(f"✅ Mission created: {mission_id}")

            # No need to wait - mission already completed planning
            # await asyncio.sleep(5)

            # Get mission details from backend
            response = await client.get(f"{backend_url}/api/v1/missions/{mission_id}")
            response.raise_for_status()
            mission_details = response.json()

            print(f"📊 Mission status: {mission_details.get('status')}")
            print(f"📊 Mission progress: {mission_details.get('progress', {})}")

            # Get mission tasks from backend (internal endpoint)
            response = await client.get(f"{backend_url}/api/v1/internal/missions/{mission_id}/tasks")
            response.raise_for_status()
            tasks = response.json()
            
            print(f"📋 Found {len(tasks)} tasks:")
            
            for i, task in enumerate(tasks):
                print(f"  {i+1}. {task['task_id']} - {task['description']}")
                print(f"     Type: {task['task_type']}, Status: {task['status']}")
                
                # Check task context for placeholders
                context = task.get('context', {})
                if isinstance(context, dict):
                    for key, value in context.items():
                        if isinstance(value, str) and "{{task:" in value:
                            print(f"     🔍 Found placeholder in {key}: {value}")
                
                print()
            
            # Look for the second task that should have placeholders
            second_task = None
            for task in tasks:
                if "tell" in task['description'].lower() or len(tasks) > 1 and task == tasks[1]:
                    second_task = task
                    break
            
            if second_task:
                print(f"🔍 Analyzing second task: {second_task['task_id']}")
                print(f"📋 Task context: {json.dumps(second_task.get('context', {}), indent=2)}")
                
                # Check if it has placeholders
                context = second_task.get('context', {})
                has_placeholders = False
                
                def check_for_placeholders(data, path=""):
                    nonlocal has_placeholders
                    if isinstance(data, dict):
                        for key, value in data.items():
                            check_for_placeholders(value, f"{path}.{key}" if path else key)
                    elif isinstance(data, str) and "{{task:" in data:
                        print(f"     🎯 Found placeholder at {path}: {data}")
                        has_placeholders = True
                
                check_for_placeholders(context)
                
                if has_placeholders:
                    print("✅ Second task has placeholders - this is correct!")
                else:
                    print("❌ Second task has NO placeholders - this is the problem!")
                    
                    # Check if the planner created the task correctly
                    print("\n🔍 Checking if planner created task correctly...")
                    
                    # Look for question field specifically
                    question = context.get('question', '')
                    if question:
                        print(f"📞 Question field: '{question}'")
                        if "{{task:" in question:
                            print("✅ Question has placeholder")
                        else:
                            print("❌ Question has NO placeholder - planner issue!")
                    else:
                        print("❌ No question field found in context")
            
            else:
                print("❌ Could not find second task")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to test with real system: {e}")
            import traceback
            traceback.print_exc()
            return False


async def main():
    """Run the test"""
    print("🧪 Starting real scenario test...")
    
    success = await test_real_scenario()
    
    if success:
        print("\n✅ Real scenario test completed!")
        return 0
    else:
        print("\n❌ Real scenario test failed!")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
