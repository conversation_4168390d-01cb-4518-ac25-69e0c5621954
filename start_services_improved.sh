#!/bin/bash
# =============================================================================
# DEEPLICA SERVICE SCRIPT - ALL PORTS MANAGED DYNAMICALLY
# =============================================================================
# ALL ports are assigned by shared/port_manager.py - NO HARDCODED PORTS
# External services adapt to assigned ports through configuration
# =============================================================================

# Deeplica v0 - Improved Service Startup Script
# This script starts all microservices with proper dependency management and resilience

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')] ✅${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[$(date '+%H:%M:%S')] ⚠️${NC} $1"
}

print_error() {
    echo -e "${RED}[$(date '+%H:%M:%S')] ❌${NC} $1"
}

# Function to check if a service is running
check_service() {
    local port=$1
    local service_name=$2
    
    if curl -s "http://localhost:${port}/health" > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to wait for a service to be ready
wait_for_service() {
    local port=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    print_status "Waiting for ${service_name} to be ready on port ${port}..."
    
    while [ $attempt -le $max_attempts ]; do
        if check_service $port "$service_name"; then
            print_success "${service_name} is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "${service_name} failed to start within $((max_attempts * 2)) seconds"
    return 1
}

# Function to start a service in background
start_service() {
    local service_dir=$1
    local service_name=$2
    local port=$3

    print_status "Starting ${service_name}..."

    # Create logs directory if it doesn't exist
    mkdir -p logs

    # Get absolute paths
    local current_dir=$(pwd)
    local log_name=$(echo "${service_name}" | tr '[:upper:]' '[:lower:]')
    local log_file="${current_dir}/logs/${log_name}.log"
    local pid_file="${current_dir}/logs/${log_name}.pid"

    # Change to service directory and start in background
    cd "${service_dir}"
    nohup python3 -m app.main > "${log_file}" 2>&1 &
    local pid=$!

    # Store PID for later cleanup
    echo $pid > "${pid_file}"

    print_status "${service_name} started with PID ${pid}"
    cd - > /dev/null
}

# Function to check if all services are running
check_all_services() {
    local services=(
        "8000:Backend API"
        "8001:Dispatcher"
        "8002:Dialogue Agent"
        "8003:Planner Agent"
        "8004:Phone Agent"
    )
    
    local all_running=true
    for service in "${services[@]}"; do
        local port=$(echo $service | cut -d: -f1)
        local name=$(echo $service | cut -d: -f2)
        
        if check_service $port "$name"; then
            print_success "${name} is running on port ${port}"
        else
            print_error "${name} is NOT running on port ${port}"
            all_running=false
        fi
    done
    
    if $all_running; then
        return 0
    else
        return 1
    fi
}

# Main execution
main() {
    print_status "🚀 Starting Deeplica v0 Microservices..."
    
    # Create logs directory if it doesn't exist
    mkdir -p logs
    
    # Step 1: Start Backend API (foundation service)
    print_status "📡 Step 1: Starting Backend API (foundation service)..."
    if check_service 8000 "Backend API"; then
        print_warning "Backend API is already running on port 8000"
    else
        start_service "backend" "Backend-API" 8000
        wait_for_service 8000 "Backend API"
    fi
    
    # Step 2: Start Dispatcher (orchestration service)
    print_status "🎯 Step 2: Starting Dispatcher (orchestration service)..."
    if check_service 8001 "Dispatcher"; then
        print_warning "Dispatcher is already running on port 8001"
    else
        start_service "dispatcher" "Dispatcher" 8001
        wait_for_service 8001 "Dispatcher"
    fi
    
    # Step 3: Start Agent Services (they will wait for backend automatically)
    print_status "🤖 Step 3: Starting Agent Services..."
    
    # Start Planner Agent
    if check_service 8003 "Planner Agent"; then
        print_warning "Planner Agent is already running on port 8003"
    else
        start_service "agents/planner" "Planner-Agent" 8003
        wait_for_service 8003 "Planner Agent"
    fi
    
    # Start Dialogue Agent
    if check_service 8002 "Dialogue Agent"; then
        print_warning "Dialogue Agent is already running on port 8002"
    else
        start_service "agents/dialogue" "Dialogue-Agent" 8002
        wait_for_service 8002 "Dialogue Agent"
    fi
    
    # Start Phone Agent
    if check_service 8004 "Phone Agent"; then
        print_warning "Phone Agent is already running on port 8004"
    else
        start_service "agents/phone" "Phone-Agent" 8004
        wait_for_service 8004 "Phone Agent"
    fi
    
    # Step 4: Start CLI Terminal UI (optional)
    print_status "🖥️  Step 4: Starting CLI Terminal UI (optional)..."
    print_status "💡 Note: CLI Terminal UI will wait for backend and never crash"
    print_status "🚀 To start CLI manually: cd cli && python3 main.py"

    # Final status check
    print_status "🔍 Final system status check..."

    if check_all_services; then
        print_success "🎉 All microservices are running successfully!"
        print_status ""
        print_status "📋 Service URLs:"
        print_status "   • Backend API:     http://localhost:8000"
        print_status "   • Dispatcher:      http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('dispatcher')")"
        print_status "   • Dialogue Agent:  http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('dialogue')")"
        print_status "   • Planner Agent:   http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('planner')")"
        print_status "   • Phone Agent:     http://localhost:$(python3 -c "from shared.port_manager import get_service_port; print(get_service_port('phone')")"
        print_status ""
        print_status "🖥️  CLI Terminal UI:"
        print_status "   • Start manually:  cd cli && python3 main.py"
        print_status "   • Features:        Chat interface, mission management"
        print_status "   • Resilience:      Waits for backend, never crashes"
        print_status ""
        print_status "📊 Health check: curl http://localhost:8000/health"
        print_status "🎯 Create mission: curl -X POST http://localhost:8000/api/v1/missions -H 'Content-Type: application/json' -d '{\"user_input\": \"your request here\"}'"
        print_status "🛑 To stop all services: ./stop_all_services.sh"
        print_status "📝 Logs are available in the logs/ directory"
    else
        print_error "❌ Some services failed to start. Check the logs in the logs/ directory."
        exit 1
    fi
}

# Run main function
main "$@"
