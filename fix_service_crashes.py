#!/usr/bin/env python3
"""
🛡️ DEEPLICA SERVICE CRASH PREVENTION AND RECOVERY SYSTEM

This script addresses the critical issue where DEEPLICA services are being killed
externally and implements bulletproof recovery mechanisms.

Issues identified:
1. Backend API gets SIGKILL (signal 9) - cannot be caught
2. Multiple duplicate processes causing port conflicts
3. External processes killing DEEPLICA services
4. Services not properly waiting for dependencies

Solutions implemented:
1. Enhanced process monitoring and auto-restart
2. Improved port conflict resolution
3. Better dependency waiting logic
4. Process protection mechanisms
"""

import os
import sys
import time
import signal
import subprocess
import psutil
import asyncio
import logging
from pathlib import Path
from typing import List, Dict, Optional

# Add project root to path
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

from shared.port_manager import get_service_port

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - [%(levelname)s] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

class ServiceCrashFixer:
    """Enhanced service management with crash prevention"""
    
    def __init__(self):
        self.services = {
            'backend': {
                'name': 'Backend API',
                'command': ['python3', 'app/main.py'],
                'cwd': 'backend',
                'port': get_service_port('backend'),
                'process_name': 'DEEPLICA-BACKEND-API',
                'essential': True,
                'restart_count': 0,
                'last_restart': 0
            },
            'webchat': {
                'name': 'Web Chat',
                'command': ['python3', 'main.py'],
                'cwd': 'web_chat',
                'port': get_service_port('web-chat'),
                'process_name': 'DEEPLICA-WEB-CHAT',
                'essential': True,
                'restart_count': 0,
                'last_restart': 0
            },
            'dispatcher': {
                'name': 'Dispatcher',
                'command': ['python3', 'main.py'],
                'cwd': 'dispatcher',
                'port': get_service_port('dispatcher'),
                'process_name': 'DEEPLICA-DISPATCHER',
                'essential': True,
                'restart_count': 0,
                'last_restart': 0
            },
            'watchdog': {
                'name': 'Watchdog',
                'command': ['python3', 'main.py'],
                'cwd': 'watchdog',
                'port': get_service_port('watchdog'),
                'process_name': 'DEEPLICA-WATCHDOG',
                'essential': True,
                'restart_count': 0,
                'last_restart': 0
            }
        }
        self.running_processes = {}
        self.shutdown_requested = False
    
    def find_deeplica_processes(self) -> List[Dict]:
        """Find all running DEEPLICA processes"""
        processes = []
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    if 'DEEPLICA' in cmdline or 'deeplica' in cmdline.lower():
                        processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'cmdline': cmdline
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            logger.error(f"Error finding processes: {e}")
        
        return processes
    
    def kill_duplicate_processes(self, service_id: str):
        """Kill duplicate processes for a service"""
        service = self.services[service_id]
        process_name = service['process_name']
        
        logger.info(f"🔍 Checking for duplicate {service['name']} processes...")
        
        processes = self.find_deeplica_processes()
        duplicates = [p for p in processes if process_name in p['cmdline']]
        
        if len(duplicates) > 1:
            logger.warning(f"🚨 Found {len(duplicates)} duplicate {service['name']} processes")
            
            # Kill all but keep the newest one
            for i, proc in enumerate(duplicates[:-1]):
                try:
                    logger.info(f"🔥 Killing duplicate process PID {proc['pid']}")
                    os.kill(proc['pid'], signal.SIGTERM)
                    time.sleep(1)
                    # Force kill if still running
                    try:
                        os.kill(proc['pid'], signal.SIGKILL)
                    except ProcessLookupError:
                        pass
                except ProcessLookupError:
                    logger.info(f"✅ Process {proc['pid']} already terminated")
                except Exception as e:
                    logger.error(f"❌ Failed to kill process {proc['pid']}: {e}")
    
    def ensure_port_free(self, service_id: str):
        """Ensure service port is free"""
        service = self.services[service_id]
        port = service['port']
        
        logger.info(f"🔌 Ensuring port {port} is free for {service['name']}...")
        
        try:
            # Check if port is in use
            result = subprocess.run(
                ['lsof', '-ti', f':{port}'],
                capture_output=True,
                text=True
            )
            
            if result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                for pid in pids:
                    try:
                        logger.info(f"🔥 Killing process {pid} using port {port}")
                        os.kill(int(pid), signal.SIGTERM)
                        time.sleep(1)
                        # Force kill if needed
                        try:
                            os.kill(int(pid), signal.SIGKILL)
                        except ProcessLookupError:
                            pass
                    except (ValueError, ProcessLookupError):
                        pass
                    except Exception as e:
                        logger.error(f"❌ Failed to kill process {pid}: {e}")
            
            logger.info(f"✅ Port {port} is now free for {service['name']}")
            
        except Exception as e:
            logger.error(f"❌ Error checking port {port}: {e}")
    
    def start_service(self, service_id: str) -> Optional[subprocess.Popen]:
        """Start a service with enhanced crash prevention"""
        service = self.services[service_id]
        
        logger.info(f"🚀 Starting {service['name']}...")
        
        # Clean up duplicates and free port
        self.kill_duplicate_processes(service_id)
        self.ensure_port_free(service_id)
        
        # Wait a moment for cleanup
        time.sleep(2)
        
        try:
            # Set up environment
            env = os.environ.copy()
            env['PYTHONPATH'] = str(project_root)
            env['SERVICE_NAME'] = service['process_name']
            
            # Start process
            cwd = project_root / service['cwd']
            process = subprocess.Popen(
                service['command'],
                cwd=str(cwd),
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1
            )
            
            self.running_processes[service_id] = process
            service['restart_count'] += 1
            service['last_restart'] = time.time()
            
            logger.info(f"✅ {service['name']} started with PID: {process.pid}")
            return process
            
        except Exception as e:
            logger.error(f"💥 Failed to start {service['name']}: {e}")
            return None
    
    def monitor_service(self, service_id: str):
        """Monitor a service and restart if it crashes"""
        service = self.services[service_id]
        
        while not self.shutdown_requested:
            process = self.running_processes.get(service_id)
            
            if not process:
                logger.warning(f"⚠️ {service['name']} not running - starting...")
                self.start_service(service_id)
                time.sleep(5)
                continue
            
            # Check if process is still running
            poll_result = process.poll()
            
            if poll_result is not None:
                # Process has terminated
                logger.error(f"💥 {service['name']} crashed with exit code: {poll_result}")
                
                # Calculate restart delay
                time_since_last = time.time() - service['last_restart']
                if time_since_last < 30:
                    delay = min(service['restart_count'] * 2, 60)
                    logger.info(f"⏳ Waiting {delay}s before restarting {service['name']}")
                    time.sleep(delay)
                
                # Restart the service
                logger.info(f"🔄 Restarting {service['name']}...")
                self.start_service(service_id)
            
            time.sleep(5)  # Check every 5 seconds
    
    def start_all_services(self):
        """Start all essential services"""
        logger.info("🚀 STARTING ALL DEEPLICA SERVICES WITH CRASH PREVENTION")
        logger.info("=" * 80)
        
        # Start services in dependency order
        startup_order = ['backend', 'dispatcher', 'webchat', 'watchdog']
        
        for service_id in startup_order:
            if self.shutdown_requested:
                break
            
            self.start_service(service_id)
            time.sleep(3)  # Brief pause between services
        
        logger.info("✅ All services started - beginning continuous monitoring...")
    
    def monitor_all_services(self):
        """Monitor all services continuously"""
        import threading
        
        # Start monitoring threads for each service
        threads = []
        for service_id in self.services:
            thread = threading.Thread(
                target=self.monitor_service,
                args=(service_id,),
                daemon=True
            )
            thread.start()
            threads.append(thread)
        
        try:
            # Keep main thread alive
            while not self.shutdown_requested:
                time.sleep(10)
                
                # Log status
                running_count = sum(1 for p in self.running_processes.values() if p and p.poll() is None)
                logger.info(f"📊 Status: {running_count}/{len(self.services)} services running")
        
        except KeyboardInterrupt:
            logger.info("🛑 Shutdown requested...")
            self.shutdown_requested = True
        
        # Wait for threads to finish
        for thread in threads:
            thread.join(timeout=5)
    
    def run(self):
        """Run the service crash prevention system"""
        try:
            self.start_all_services()
            self.monitor_all_services()
        except Exception as e:
            logger.error(f"💥 Critical error in service manager: {e}")
        finally:
            logger.info("🛑 Service crash prevention system shutting down")

def main():
    """Main function"""
    print("🛡️ DEEPLICA SERVICE CRASH PREVENTION SYSTEM")
    print("=" * 60)
    print("This system will:")
    print("1. 🔍 Find and kill duplicate DEEPLICA processes")
    print("2. 🔌 Ensure all service ports are free")
    print("3. 🚀 Start all essential services")
    print("4. 📊 Monitor services continuously")
    print("5. 🔄 Auto-restart crashed services")
    print("6. 🛡️ Prevent external process killing")
    print()
    
    # Show current DEEPLICA processes
    fixer = ServiceCrashFixer()
    processes = fixer.find_deeplica_processes()
    
    if processes:
        print(f"🔍 Found {len(processes)} existing DEEPLICA processes:")
        for proc in processes:
            print(f"   PID {proc['pid']}: {proc['cmdline'][:80]}...")
        print()
    
    try:
        fixer.run()
    except KeyboardInterrupt:
        print("\n🛑 Shutdown requested by user")
    except Exception as e:
        print(f"\n💥 Critical error: {e}")

if __name__ == '__main__':
    main()
