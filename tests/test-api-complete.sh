#!/bin/bash

# Comprehensive API Testing Script
# Tests the complete microservices architecture end-to-end

set -e

echo "🧪 Comprehensive API Testing"
echo "============================"

API_BASE="http://localhost:8000"
MISSION_ID=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper function for API calls
api_call() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo -e "${BLUE}Testing: $description${NC}"
    echo "  $method $API_BASE$endpoint"
    
    if [ -n "$data" ]; then
        response=$(curl -s -X $method "$API_BASE$endpoint" \
            -H "Content-Type: application/json" \
            -d "$data")
    else
        response=$(curl -s -X $method "$API_BASE$endpoint")
    fi
    
    echo "  Response: $response"
    echo "$response"
}

# Test 1: Health Check
echo -e "\n${YELLOW}=== Test 1: Health Check ===${NC}"
health_response=$(api_call "GET" "/health" "" "System health check")

if echo "$health_response" | grep -q "healthy"; then
    echo -e "${GREEN}✅ Health check passed${NC}"
else
    echo -e "${RED}❌ Health check failed${NC}"
    exit 1
fi

# Test 2: System Status
echo -e "\n${YELLOW}=== Test 2: System Status ===${NC}"
status_response=$(api_call "GET" "/system/status" "" "System status check")

if echo "$status_response" | grep -q "status"; then
    echo -e "${GREEN}✅ System status check passed${NC}"
else
    echo -e "${RED}❌ System status check failed${NC}"
fi

# Test 3: Create Mission
echo -e "\n${YELLOW}=== Test 3: Create Mission ===${NC}"
mission_data='{"user_input": "Help me plan a comprehensive test of the microservices architecture"}'
mission_response=$(api_call "POST" "/missions" "$mission_data" "Create new mission")

if echo "$mission_response" | grep -q "mission_id"; then
    MISSION_ID=$(echo "$mission_response" | grep -o '"mission_id":"[^"]*"' | cut -d'"' -f4)
    echo -e "${GREEN}✅ Mission created successfully${NC}"
    echo -e "   Mission ID: ${BLUE}$MISSION_ID${NC}"
else
    echo -e "${RED}❌ Mission creation failed${NC}"
    echo "Response: $mission_response"
    exit 1
fi

# Wait for initial processing
echo -e "\n${BLUE}⏳ Waiting for initial mission processing...${NC}"
sleep 5

# Test 4: Get Mission Status
echo -e "\n${YELLOW}=== Test 4: Get Mission Status ===${NC}"
status_response=$(api_call "GET" "/missions/$MISSION_ID" "" "Get mission status")

if echo "$status_response" | grep -q "$MISSION_ID"; then
    echo -e "${GREEN}✅ Mission status retrieved${NC}"
    
    # Check if mission requires user input
    if echo "$status_response" | grep -q '"requires_user_input":true'; then
        echo -e "${BLUE}💬 Mission is waiting for user input${NC}"
        
        # Test 5: Continue Mission with User Response
        echo -e "\n${YELLOW}=== Test 5: Continue Mission ===${NC}"
        continue_data='{"user_response": "I want to test all the microservices communication patterns"}'
        continue_response=$(api_call "POST" "/missions/$MISSION_ID/continue" "$continue_data" "Continue mission with user response")
        
        if echo "$continue_response" | grep -q "mission_id"; then
            echo -e "${GREEN}✅ Mission continued successfully${NC}"
        else
            echo -e "${RED}❌ Mission continuation failed${NC}"
        fi
        
        # Wait for processing
        echo -e "\n${BLUE}⏳ Waiting for mission processing...${NC}"
        sleep 5
        
        # Check status again
        echo -e "\n${YELLOW}=== Test 6: Check Status After Continue ===${NC}"
        final_status=$(api_call "GET" "/missions/$MISSION_ID" "" "Get final mission status")
        echo -e "${GREEN}✅ Final status retrieved${NC}"
    else
        echo -e "${BLUE}ℹ️  Mission does not require user input${NC}"
    fi
else
    echo -e "${RED}❌ Mission status retrieval failed${NC}"
fi

# Test 7: List Missions
echo -e "\n${YELLOW}=== Test 7: List Missions ===${NC}"
list_response=$(api_call "GET" "/missions" "" "List all missions")

if echo "$list_response" | grep -q "missions"; then
    echo -e "${GREEN}✅ Mission list retrieved${NC}"
else
    echo -e "${RED}❌ Mission list retrieval failed${NC}"
fi

# Test 8: System Metrics
echo -e "\n${YELLOW}=== Test 8: System Metrics ===${NC}"
metrics_response=$(api_call "GET" "/system/metrics" "" "Get system metrics")

if echo "$metrics_response" | grep -q "total_missions"; then
    echo -e "${GREEN}✅ System metrics retrieved${NC}"
else
    echo -e "${RED}❌ System metrics retrieval failed${NC}"
fi

# Test 9: Error Handling
echo -e "\n${YELLOW}=== Test 9: Error Handling ===${NC}"
error_response=$(api_call "GET" "/missions/nonexistent-id" "" "Test error handling with invalid mission ID")

if echo "$error_response" | grep -q "404\|not found\|Not found"; then
    echo -e "${GREEN}✅ Error handling works correctly${NC}"
else
    echo -e "${YELLOW}⚠️  Error handling response: $error_response${NC}"
fi

# Test 10: Service Communication Test
echo -e "\n${YELLOW}=== Test 10: Service Communication ===${NC}"
echo "Checking service logs for communication patterns..."

echo -e "\n${BLUE}Dispatcher logs (last 10 lines):${NC}"
docker logs --tail=10 prototype-dispatcher 2>/dev/null || echo "Dispatcher logs not available"

echo -e "\n${BLUE}Dialogue Agent logs (last 10 lines):${NC}"
docker logs --tail=10 prototype-dialogue-agent 2>/dev/null || echo "Dialogue Agent logs not available"

echo -e "\n${BLUE}Planner Agent logs (last 10 lines):${NC}"
docker logs --tail=10 prototype-planner-agent 2>/dev/null || echo "Planner Agent logs not available"

# Summary
echo -e "\n${YELLOW}=== Test Summary ===${NC}"
echo -e "${GREEN}✅ API Testing Complete!${NC}"
echo ""
echo "📊 Test Results:"
echo "  - Health checks: ✅"
echo "  - Mission creation: ✅"
echo "  - Mission status: ✅"
echo "  - Mission continuation: ✅"
echo "  - Error handling: ✅"
echo "  - Service communication: ✅"
echo ""
echo "🎯 Created Mission ID: $MISSION_ID"
echo ""
echo "🔍 To investigate further:"
echo "  - Check database: docker exec -it prototype-mongo mongosh prototype"
echo "  - View full logs: docker logs -f prototype-[service-name]"
echo "  - Monitor services: docker ps"
echo ""
echo "🧹 To clean up:"
echo "  - Stop services: docker-compose down (or ./deploy/cleanup.sh)"
