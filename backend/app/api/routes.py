"""
API routes for the Deeplica v0 backend.
Provides endpoints for mission creation, execution, and management.
"""

import sys
import os
from datetime import datetime
from typing import Dict, Any, Optional, List, Union
from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel, Field
from shared.unified_logging import get_backend_logger

# Initialize unified logger
logger = get_backend_logger()

# Add project root to path for shared models
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared_models import MissionStatus, TaskStatus
from shared_models.password_utils import PasswordHasher, hash_password, verify_password, needs_rehash


# Helper function to get database from app state
def get_database_from_request(request: Request):
    """Get database instance from request app state - ONLY DATABASE SERVICE CAN ACCESS MONGODB"""
    if hasattr(request, 'app') and hasattr(request.app, 'state') and hasattr(request.app.state, 'db'):
        return request.app.state.db.database
    raise Exception("Database not available - Backend API not properly initialized")


# Request/Response models
class CreateMissionRequest(BaseModel):
    """Request model for creating a new mission"""
    user_input: str = Field(..., description="User's request", min_length=1, max_length=1000)
    user_id: Optional[str] = Field(None, description="User identifier (optional for v0)")


class ContinueMissionRequest(BaseModel):
    """Request model for continuing mission execution"""
    user_response: Optional[str] = Field(None, description="User's response to dialogue")


class MissionResponse(BaseModel):
    """Response model for mission operations"""
    mission_id: str
    status: str
    message: str
    data: Optional[Dict[str, Any]] = None


class LLMGenerateRequest(BaseModel):
    """Request model for LLM generation"""
    prompt: str = Field(..., description="The main prompt for the LLM")
    system_prompt: Optional[str] = Field(None, description="Optional system prompt for context")
    temperature: float = Field(0.7, description="Creativity level (0.0 to 1.0)")
    max_tokens: Optional[int] = Field(None, description="Maximum tokens in response")
    response_format: Optional[str] = Field(None, description="Expected format ('json', 'text')")
    provider: Optional[str] = Field(None, description="Specific provider to use")


# Dependency injection
async def get_services(request: Request) -> Dict[str, Any]:
    """Get initialized services from app state"""
    try:
        db_service = getattr(request.app.state, 'db', None)
        llm_service = getattr(request.app.state, 'llm_service', None)
        dispatcher_client = getattr(request.app.state, 'dispatcher_client', None)

        if db_service is None:
            logger.error("❌ Database service not initialized in app state", module="BACKEND-API", routine="get_services")
            raise HTTPException(status_code=503, detail="Database service not ready")

        return {
            "db_service": db_service,
            "llm_service": llm_service,
            "dispatcher_client": dispatcher_client
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error("❌ Failed to get services: {e}", module="BACKEND-API", routine="get_services")
        raise HTTPException(status_code=500, detail="Service access failed")


# Recovery request models
class DatabaseRecoveryRequest(BaseModel):
    """Request model for database recovery"""
    force_reconnect: bool = Field(False, description="Force reconnection even if currently connected")

class ServiceRecoveryResponse(BaseModel):
    """Response model for service recovery operations"""
    service: str
    status: str
    message: str
    details: Optional[Dict[str, Any]] = None


class UserPreferencesRequest(BaseModel):
    """Request model for saving user preferences"""
    user_id: str = Field(..., description="User identifier")
    username: str = Field(..., description="Username")
    category: str = Field(..., description="Preference category")
    subcategory: Optional[str] = Field(None, description="Preference subcategory")
    preferences: Dict[str, Any] = Field(..., description="Preference data")
    updated_at: str = Field(..., description="Update timestamp")


class UserPreferencesResponse(BaseModel):
    """Response model for user preferences"""
    user_id: Optional[str] = None
    username: Optional[str] = None
    category: Optional[str] = None
    subcategory: Optional[str] = None
    preferences: Optional[Dict[str, Any]] = None
    updated_at: Optional[str] = None


class LocationHistoryEntry(BaseModel):
    """Model for location history entry"""
    latitude: float = Field(..., description="Latitude coordinate")
    longitude: float = Field(..., description="Longitude coordinate")
    accuracy: Union[float, str] = Field(..., description="Location accuracy")
    timestamp: str = Field(..., description="Timestamp of location")
    method: str = Field(..., description="Detection method")
    id: Optional[int] = Field(None, description="Entry ID")


class LocationHistoryRequest(BaseModel):
    """Request model for saving location history"""
    user_id: str = Field(..., description="User identifier")
    username: str = Field(..., description="Username")
    location_entry: LocationHistoryEntry = Field(..., description="Location data")


class LocationHistoryResponse(BaseModel):
    """Response model for location history"""
    user_id: str
    username: str
    history: List[LocationHistoryEntry]
    total_entries: int

# Create router
router = APIRouter()

# Recovery endpoints for watchdog auto-recovery
@router.post("/database/reconnect", response_model=ServiceRecoveryResponse)
async def reconnect_database(
    request: Request,
    recovery_request: DatabaseRecoveryRequest = DatabaseRecoveryRequest()
) -> ServiceRecoveryResponse:
    """Force database reconnection for recovery purposes"""
    try:
        logger.info("🔄 Database reconnection requested by watchdog", module="BACKEND-API", routine="reconnect_database")

        services = await get_services(request)
        db_service = services.get('db_service')

        if not db_service:
            return ServiceRecoveryResponse(
                service="database",
                status="error",
                message="Database service not available",
                details={"error": "Database service not initialized"}
            )

        # Force reconnection if requested or if not connected
        if recovery_request.force_reconnect or not db_service.client:
            logger.info("🔄 Forcing database reconnection...", module="BACKEND-API", routine="reconnect_database")

            # Close existing connection if any
            if db_service.client:
                db_service.client.close()
                db_service.client = None
                db_service.database = None
                db_service.missions_collection = None
                db_service.tasks_collection = None

            # Reconnect
            await db_service.connect()

            # Test the connection
            await db_service.client.admin.command('ping')

            logger.info("✅ Database reconnection successful", module="BACKEND-API", routine="reconnect_database")
            return ServiceRecoveryResponse(
                service="database",
                status="success",
                message="Database reconnected successfully",
                details={
                    "database_name": db_service.database_name,
                    "connection_type": "MongoDB Atlas" if "mongodb.net" in db_service.connection_string else "Local MongoDB"
                }
            )
        else:
            # Test existing connection
            await db_service.client.admin.command('ping')
            return ServiceRecoveryResponse(
                service="database",
                status="success",
                message="Database connection is healthy",
                details={"connection_status": "already_connected"}
            )

    except Exception as e:
        logger.error("❌ Database reconnection failed: {e}", module="BACKEND-API", routine="reconnect_database")
        return ServiceRecoveryResponse(
            service="database",
            status="error",
            message=f"Database reconnection failed: {str(e)}",
            details={"error": str(e)}
        )

@router.post("/gemini/test", response_model=ServiceRecoveryResponse)
async def test_gemini_api(request: Request) -> ServiceRecoveryResponse:
    """Test Gemini API connection for recovery purposes"""
    try:
        logger.info("🤖 Gemini API test requested by watchdog", module="BACKEND-API", routine="test_gemini")

        services = await get_services(request)
        llm_service = services.get('llm_service')

        if not llm_service:
            return ServiceRecoveryResponse(
                service="gemini",
                status="error",
                message="LLM service not available",
                details={"error": "LLM service not initialized"}
            )

        # Test Gemini API with a simple request
        test_response = await llm_service.generate_response(
            prompt="Test connection",
            system_prompt="Respond with 'OK' if you receive this message.",
            temperature=0.1,
            max_tokens=10
        )

        if test_response and test_response.get("content"):
            logger.info("✅ Gemini API test successful", module="BACKEND-API", routine="test_gemini")
            return ServiceRecoveryResponse(
                service="gemini",
                status="success",
                message="Gemini API is responding",
                details={
                    "test_response": test_response.get("content", "")[:50],
                    "model": test_response.get("model", "unknown")
                }
            )
        else:
            return ServiceRecoveryResponse(
                service="gemini",
                status="error",
                message="Gemini API test failed - no response",
                details={"test_response": test_response}
            )

    except Exception as e:
        logger.error("❌ Gemini API test failed: {e}", module="BACKEND-API", routine="test_gemini")
        return ServiceRecoveryResponse(
            service="gemini",
            status="error",
            message=f"Gemini API test failed: {str(e)}",
            details={"error": str(e)}
        )

@router.get("/health")
async def health_check(request: Request) -> Dict[str, Any]:
    """Health check endpoint for the API - shows database readiness status"""
    database_ready = getattr(request.app.state, 'ready', False)
    return {
        "status": "healthy",
        "service": "Deeplica v0 API",
        "version": "0.1.0",
        "database_ready": database_ready,
        "message": "Backend API is running" + (" and fully ready" if database_ready else " but initializing database connection")
    }

@router.get("/debug/state")
async def debug_state(request: Request) -> Dict[str, Any]:
    """Debug endpoint to check app state"""
    from app.main import get_dispatcher_client

    database_ready = getattr(request.app.state, 'ready', False)
    db_service = getattr(request.app.state, 'db', None)
    dispatcher_client_raw = getattr(request.app.state, 'dispatcher_client', None)
    dispatcher_client_lazy = get_dispatcher_client(request.app)

    return {
        "database_ready": database_ready,
        "db_service_exists": db_service is not None,
        "dispatcher_client_raw": dispatcher_client_raw is not None,
        "dispatcher_client_lazy": dispatcher_client_lazy is not None,
        "app_state_keys": list(vars(request.app.state).keys()) if hasattr(request.app, 'state') else []
    }


@router.get("/system/status")
async def system_status(request: Request) -> Dict[str, Any]:
    """Get comprehensive status of all system services"""
    try:
        orchestrator = getattr(request.app.state, 'orchestrator', None)
        if not orchestrator:
            return {
                "status": "partial",
                "message": "Service orchestrator not initialized",
                "services": {}
            }

        service_status = orchestrator.get_service_status()
        all_healthy = all(
            service.get("is_healthy", False)
            for service in service_status.values()
            if service.get("name") != "Ngrok Tunnel"
        )

        return {
            "status": "healthy" if all_healthy else "degraded",
            "message": "All services operational" if all_healthy else "Some services may be unavailable",
            "backend_ready": getattr(request.app.state, 'ready', False),
            "services": service_status,
            "system_ready": all_healthy and getattr(request.app.state, 'ready', False)
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"Failed to get system status: {str(e)}",
            "services": {}
        }


@router.post("/system/restart-services")
async def restart_services(request: Request) -> Dict[str, Any]:
    """Restart all microservices (emergency use only)"""
    try:
        orchestrator = getattr(request.app.state, 'orchestrator', None)
        if not orchestrator:
            return {
                "status": "error",
                "message": "Service orchestrator not initialized"
            }

        logger.info("🔄 Manual service restart requested", module="BACKEND-API", routine="restart_services")

        # Shutdown existing services
        await orchestrator.shutdown_all_services()

        # Start services again
        success = await orchestrator.start_all_services()

        if success:
            request.app.state.ready = True
            return {
                "status": "success",
                "message": "All services restarted successfully"
            }
        else:
            return {
                "status": "error",
                "message": "Service restart failed"
            }

    except Exception as e:
        logger.error("Failed to restart services: {e}", module="BACKEND-API", routine="restart_services")
        return {
            "status": "error",
            "message": f"Failed to restart services: {str(e)}"
        }


@router.delete("/system/clear-database")
async def clear_database(
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, Any]:
    """Clear all missions and tasks from the database (DANGER: Permanent deletion!)"""
    try:
        db_service = services["db"]
        logger.info("DATABASE CLEAR REQUESTED", module="BACKEND-API", routine="clear_database")

        # Clear all data
        result = await db_service.clear_all_data()

        return {
            "status": "success",
            "message": "Database cleared successfully",
            "data": result
        }

    except Exception as e:
        logger.error("❌ Failed to clear database: {e}", module="BACKEND-API", routine="clear_database")
        return {
            "status": "error",
            "message": f"Failed to clear database: {str(e)}"
        }


@router.post("/llm/generate")
async def generate_llm_response(
    request: LLMGenerateRequest,
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, Any]:
    """
    Generate LLM response via backend LLM service.

    This endpoint allows agent services to use the backend's LLM service
    without duplicating LLM configuration and credentials.
    """
    logger.debug("🤖 LLM generation request: {request.prompt[:100]}...", module="main", routine="unknown")

    try:
        llm_service = services["llm_service"]

        # Convert provider string to enum if provided
        provider = None
        if request.provider:
            from app.services.llm_service import LLMProvider
            try:
                provider = LLMProvider(request.provider.lower())
            except ValueError:
                logger.warning("⚠️ Unknown provider: {request.provider}, using default", module="BACKEND-API", routine="generate_llm_response")

        # Generate response
        result = await llm_service.generate_response(
            prompt=request.prompt,
            system_prompt=request.system_prompt,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
            response_format=request.response_format,
            provider=provider
        )

        logger.debug("✅ LLM response generated successfully", module="BACKEND-API", routine="generate_llm_response")
        return result

    except Exception as e:
        logger.error("❌ Failed to generate LLM response: {e}", module="BACKEND-API", routine="generate_llm_response")
        raise HTTPException(status_code=500, detail=f"Failed to generate response: {str(e)}")


@router.post("/missions", response_model=MissionResponse)
async def create_mission(
    request: CreateMissionRequest,
    app_request: Request
) -> MissionResponse:
    """
    Create a new mission from user input - resilient version.

    This endpoint:
    1. Checks if services are ready
    2. Creates a mission stub with user input
    3. Starts mission execution via dispatcher
    4. Returns appropriate response based on system state
    """
    logger.info("📝 Creating mission for input: {request.user_inputAPI", routine="create_mission()")
    logger.debug("🔍 Request details: user_input='{request.user_input}', user_id='{request.user_id}', length={len(request.user_input)}", module="BACKEND-API", routine="create_mission()")

    try:
        # Check if system is ready - focus on actual service availability
        logger.debug("🔍 Checking system readiness...", module="BACKEND-API", routine="create_mission()")
        db_service = getattr(app_request.app.state, 'db', None)

        # Try to get dispatcher client with lazy initialization
        from app.main import get_dispatcher_client
        dispatcher_client = get_dispatcher_client(app_request.app)

        logger.info(f"📊 System state: db_service={db_service is not None}, dispatcher_client={dispatcher_client is not None}", module="BACKEND-API", routine="create_mission()")

        if not db_service or not dispatcher_client:
            # System not ready yet - return helpful message
            logger.warning(f"⏳ Mission creation requested but system is still initializing: db_service={db_service is not None}, dispatcher_client={dispatcher_client is not None}", module="BACKEND-API", routine="create_mission()")
            raise HTTPException(
                status_code=503,
                detail=f"System is still initializing. Please wait a moment and try again. Database and services are starting up. (db_service={db_service is not None}, dispatcher_client={dispatcher_client is not None})"
            )

        # Create mission stub
        logger.debug("📋 Creating mission data structure...", module="BACKEND-API", routine="create_mission()")
        from shared_models import Mission
        import uuid

        mission_id = str(uuid.uuid4())
        logger.debug("🆔 Generated mission ID: {mission_id}", module="BACKEND-API", routine="create_mission()")

        mission = Mission(
            mission_id=mission_id,
            user_input=request.user_input,
            user_id=request.user_id,
            title="Processing...",  # Will be enhanced by planner
            description="Mission being planned...",  # Will be enhanced by planner
            status=MissionStatus.CREATED
        )
        logger.debug("📊 Mission object created: id={mission.mission_id}, status={mission.status}, user_id={mission.user_id}", module="BACKEND-API", routine="create_mission()")

        # Save mission stub to database
        logger.debug("💾 Saving mission to database...", module="BACKEND-API", routine="create_mission()")
        await db_service.create_mission(mission)
        logger.info("✅ Mission stub created: {mission.mission_id}", module="BACKEND-API", routine="create_mission()")

        # Create initial planning task for the mission
        logger.debug("🧠 Creating planning task for mission {mission.mission_id}...", module="BACKEND-API", routine="create_mission()")
        from shared_models import Task, TaskType, TaskStatus
        import uuid

        task_id = str(uuid.uuid4())
        logger.debug("🆔 Generated task ID: {task_id}", module="BACKEND-API", routine="create_mission()")

        planning_task = Task(
            task_id=task_id,
            mission_id=mission.mission_id,
            task_type=TaskType.PLANNING,
            description=f"Plan the mission: {mission.user_input}",
            prompt=f"Create a detailed task plan for the following user request: {mission.user_input}. Break it down into specific, actionable tasks that can be executed by different agents (dialogue, phone, etc.).",
            status=TaskStatus.PENDING,
            assigned_agent="planner",
            context={
                "user_input": mission.user_input,
                "mission_context": {
                    "mission_id": mission.mission_id,
                    "title": mission.title,
                    "description": mission.description
                }
            }
        )
        logger.debug("📊 Planning task created: id={planning_task.task_id}, type={planning_task.task_type}, agent={planning_task.assigned_agent}, status={planning_task.status}", module="BACKEND-API", routine="create_mission()")

        # Save planning task to database
        logger.debug("💾 Saving planning task to database...", module="BACKEND-API", routine="create_mission()")
        await db_service.create_task(planning_task)
        logger.info("✅ Planning task created: {planning_task.task_id}", module="BACKEND-API", routine="create_mission()")

        # Prepare response
        logger.debug("📤 Preparing response...", module="BACKEND-API", routine="create_mission()")
        response = MissionResponse(
            mission_id=mission.mission_id,
            status="created",
            message="Mission and planning task created successfully. Dispatcher will process tasks automatically.",
            data={
                "mission_id": mission.mission_id,
                "planning_task_id": planning_task.task_id
            }
        )
        logger.info("🎉 Mission creation completed successfully: mission_id={mission.mission_id}, task_id={planning_task.task_id}", module="BACKEND-API", routine="create_mission()")
        logger.debug("📊 Response data: status={response.status}, message='{response.message}'", module="BACKEND-API", routine="create_mission()")

        return response

    except HTTPException:
        # Re-raise HTTP exceptions (like 503 above)
        logger.debug("🔄 Re-raising HTTPException", module="BACKEND-API", routine="create_mission()")
        raise
    except Exception as e:
        logger.error("❌ Failed to create mission: {e}", module="BACKEND-API", routine="create_mission()")
        logger.error("🔍 Exception details: type={type(e).__name__}, args={e.args}", module="BACKEND-API", routine="create_mission()")
        import traceback
        logger.error("📋 Traceback: {traceback.format_exc()}", module="BACKEND-API", routine="create_mission()")
        raise HTTPException(status_code=500, detail=f"Failed to create mission: {str(e)}")


@router.post("/missions/{mission_id}/continue", response_model=MissionResponse)
async def continue_mission(
    mission_id: str,
    request: ContinueMissionRequest,
    services: Dict[str, Any] = Depends(get_services)
) -> MissionResponse:
    """
    Continue mission execution with optional user response.
    
    This endpoint:
    1. Processes user response (if provided)
    2. Continues mission execution
    3. Returns next steps or completion status
    """
    logger.info("▶️ Continuing mission: {mission_id}", module="BACKEND-API", routine="continue_mission")
    
    try:
        dispatcher_client = services["dispatcher_client"]

        # Continue mission execution via dispatcher
        execution_result = await dispatcher_client.continue_mission_execution(
            mission_id=mission_id,
            user_response=request.user_response
        )
        
        # Determine response message
        if execution_result.get("is_complete", False):
            message = "Mission completed successfully"
        elif execution_result.get("responses", []):
            last_response = execution_result["responses"][-1]
            if last_response.get("requires_user_input", False):
                message = "Waiting for user response"
            else:
                message = "Mission execution continued"
        else:
            message = "Mission execution in progress"
        
        return MissionResponse(
            mission_id=mission_id,
            status=execution_result.get("status", MissionStatus.IN_PROGRESS.value),
            message=message,
            data=execution_result
        )
        
    except ValueError as e:
        logger.warning("⚠️ Mission not found: {mission_id}", module="BACKEND-API", routine="continue_mission")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error("❌ Failed to continue mission: {e}", module="BACKEND-API", routine="continue_mission")
        raise HTTPException(status_code=500, detail=f"Failed to continue mission: {str(e)}")


@router.get("/missions/{mission_id}", response_model=MissionResponse)
async def get_mission_status(
    mission_id: str,
    services: Dict[str, Any] = Depends(get_services)
) -> MissionResponse:
    """
    Get current status of a mission.

    Returns mission status, progress, and current state including dialogue responses.
    """
    logger.info("📊 Getting mission status: {mission_id}", module="BACKEND-API", routine="get_mission_status")

    try:
        db_service = services["db_service"]

        # Get mission from database
        mission = await db_service.get_mission(mission_id)
        if not mission:
            raise HTTPException(status_code=404, detail="Mission not found")

        # Get mission progress
        progress = await db_service.get_mission_progress(mission_id)

        # Get all tasks for this mission to collect dialogue responses
        tasks = await db_service.get_mission_tasks(mission_id)

        # Build responses array from completed dialogue tasks
        responses = []
        requires_user_input = False

        for task in tasks:
            # Only include dialogue/user interaction tasks that have results
            if (task.task_type in ["dialogue", "user_interaction", "conversation"] and
                task.result and
                task.status in [TaskStatus.DONE, TaskStatus.IN_PROGRESS]):

                # Extract dialogue response from task result
                task_result = task.result
                if isinstance(task_result, dict):
                    message = task_result.get("message", "")
                    task_requires_input = task_result.get("requires_user_input", False)

                    if message:  # Only add if there's actually a message
                        responses.append({
                            "message": message,
                            "requires_user_input": task_requires_input,
                            "task_id": task.task_id,
                            "response_type": task_result.get("response_type", "dialogue")
                        })

                        # If this task requires user input and is still in progress, set flag
                        if task_requires_input and task.status == TaskStatus.IN_PROGRESS:
                            requires_user_input = True

        # Check if mission is complete
        is_complete = await db_service.is_mission_complete(mission_id)

        # Build status data
        status_data = {
            "mission_id": mission_id,
            "status": mission.status,
            "progress": progress,
            "responses": responses,
            "requires_user_input": requires_user_input,
            "is_complete": is_complete,
            "current_task": mission.current_task_id
        }

        return MissionResponse(
            mission_id=mission_id,
            status=mission.status,
            message=f"Mission status: {mission.status}",
            data=status_data
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error("❌ Failed to get mission status: {e}", module="BACKEND-API", routine="get_mission_status")
        raise HTTPException(status_code=500, detail=f"Failed to get mission status: {str(e)}")


@router.get("/missions", response_model=List[Dict[str, Any]])
async def list_missions(
    status: Optional[str] = None,
    user_id: Optional[str] = None,
    limit: int = 20,
    skip: int = 0,
    services: Dict[str, Any] = Depends(get_services)
) -> List[Dict[str, Any]]:
    """
    List missions with optional filtering.
    
    Query parameters:
    - status: Filter by mission status
    - user_id: Filter by user ID
    - limit: Maximum number of missions to return (default: 20)
    - skip: Number of missions to skip for pagination (default: 0)
    """
    logger.info("📋 Listing missions (status={status}, user_id={user_id})", module="BACKEND-API", routine="list_missions")
    
    try:
        db_service = services["db_service"]
        
        # Get missions from database
        missions = await db_service.list_missions(
            status=status,
            user_id=user_id,
            limit=min(limit, 100),  # Cap at 100
            skip=skip
        )
        
        # Convert to response format
        mission_list = []
        for mission in missions:
            # Get progress from database service
            progress = await db_service.get_mission_progress(mission.mission_id)

            mission_list.append({
                "mission_id": mission.mission_id,
                "title": mission.title,
                "status": mission.status,
                "created_at": mission.created_at.isoformat(),
                "progress": progress,
                "user_input": mission.user_input[:100] + "..." if len(mission.user_input) > 100 else mission.user_input
            })
        
        return mission_list
        
    except Exception as e:
        logger.error("❌ Failed to list missions: {e}", module="BACKEND-API", routine="list_missions")
        raise HTTPException(status_code=500, detail=f"Failed to list missions: {str(e)}")


@router.put("/missions/{mission_id}")
async def update_mission(
    mission_id: str,
    mission_data: Dict[str, Any],
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, str]:
    """Update mission - used by planner microservice"""
    try:
        db_service = services["db_service"]

        # Get existing mission
        mission = await db_service.get_mission(mission_id)
        if not mission:
            raise HTTPException(status_code=404, detail="Mission not found")

        # Update mission with new data
        for key, value in mission_data.items():
            if hasattr(mission, key) and key != "mission_id":  # Don't allow changing mission_id
                setattr(mission, key, value)

        # Save updated mission
        await db_service.update_mission(mission)
        logger.info("✅ Mission updated: {mission_id}", module="BACKEND-API", routine="update_mission")

        return {"status": "updated"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error("❌ Failed to update mission: {e}", module="BACKEND-API", routine="update_mission")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/missions/{mission_id}", response_model=MissionResponse)
async def cancel_mission(
    mission_id: str,
    services: Dict[str, Any] = Depends(get_services)
) -> MissionResponse:
    """
    Cancel an active mission.

    Stops mission execution and marks it as cancelled.
    """
    logger.info("🛑 Cancelling mission: {mission_id}", module="BACKEND-API", routine="cancel_mission")

    try:
        dispatcher_client = services["dispatcher_client"]

        # Cancel mission via dispatcher
        success = await dispatcher_client.cancel_mission(mission_id)

        if not success:
            raise HTTPException(status_code=404, detail="Mission not found")

        return MissionResponse(
            mission_id=mission_id,
            status=MissionStatus.CANCELLED.value,
            message="Mission cancelled successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error("❌ Failed to cancel mission: {e}", module="BACKEND-API", routine="cancel_mission")
        raise HTTPException(status_code=500, detail=f"Failed to cancel mission: {str(e)}")


@router.get("/system/status")
async def get_system_status(request: Request) -> Dict[str, Any]:
    """
    Get system status and health information - resilient version.

    Returns information about available services and system health.
    Always returns 200 OK, shows actual status of components.
    """
    try:
        # Check system readiness without requiring dependencies
        database_ready = getattr(request.app.state, 'ready', False)
        db_connected = hasattr(request.app.state, 'db') and request.app.state.db is not None
        llm_ready = hasattr(request.app.state, 'llm_service') and request.app.state.llm_service is not None

        # Try to get active missions count if database is ready
        active_missions_count = 0
        if database_ready and db_connected:
            try:
                db_service = request.app.state.db
                active_missions = await db_service.list_missions(status="in_progress", limit=100)
                active_missions_count = len(active_missions)
            except Exception as e:
                logger.debug("Could not get active missions count: {e}", module="BACKEND-API", routine="get_system_status")
                active_missions_count = -1  # Indicates error

        return {
            "status": "operational" if database_ready else "initializing",
            "services": {
                "database": "connected" if database_ready else "connecting",
                "llm_service": "ready" if llm_ready else "initializing",
                "api": "ready"
            },
            "metrics": {
                "active_missions": active_missions_count
            },
            "version": "0.1.0",
            "message": "System is " + ("fully operational" if database_ready else "initializing - please wait")
        }

    except Exception as e:
        logger.error("❌ System status check failed: {e}", module="BACKEND-API", routine="get_system_status")
        # Return 200 with error status instead of 503
        return {
            "status": "error",
            "services": {
                "database": "unknown",
                "llm_service": "unknown",
                "api": "ready"
            },
            "metrics": {
                "active_missions": -1
            },
            "version": "0.1.0",
            "message": "System status check encountered an error"
        }


@router.get("/system/metrics")
async def get_system_metrics(
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, Any]:
    """
    Get system metrics and usage statistics.
    """
    try:
        db_service = services["db_service"]

        # Get basic metrics from database
        active_missions = await db_service.list_missions(status="in_progress", limit=100)

        # Get recent missions count (last 24 hours)
        from datetime import datetime, timedelta, timezone
        recent_missions = await db_service.list_missions(limit=100)
        recent_count = len([
            m for m in recent_missions
            if m.created_at > datetime.now(timezone.utc) - timedelta(days=1)
        ])

        return {
            "active_missions": len(active_missions),
            "active_mission_ids": active_missions,
            "recent_missions_24h": recent_count,
            "total_missions_sample": len(recent_missions),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

    except Exception as e:
        logger.error("❌ Failed to get system metrics: {e}", module="BACKEND-API", routine="get_system_metrics")
        raise HTTPException(status_code=500, detail="Failed to get metrics")


# Database service endpoints for dispatcher
@router.get("/internal/missions/{mission_id}")
async def get_mission_for_dispatcher(
    mission_id: str,
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, Any]:
    """Get mission data for dispatcher service - DATABASE ONLY, NO DISPATCHER CALLS"""
    try:
        db_service = services["db_service"]
        mission = await db_service.get_mission(mission_id)

        if not mission:
            raise HTTPException(status_code=404, detail="Mission not found")

        # IMPORTANT: Only return database data - DO NOT call dispatcher to avoid circular dependency
        return mission.model_dump()

    except HTTPException:
        raise
    except Exception as e:
        logger.error("❌ Failed to get mission for dispatcher: {e}", module="BACKEND-API", routine="get_mission_for_dispatcher")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/internal/missions/{mission_id}/executable-tasks")
async def get_executable_tasks(
    mission_id: str,
    services: Dict[str, Any] = Depends(get_services)
) -> List[Dict[str, Any]]:
    """Get executable tasks for a mission"""
    try:
        db_service = services["db_service"]
        executable_tasks = await db_service.get_executable_tasks(mission_id)

        return [task.model_dump() for task in executable_tasks]

    except Exception as e:
        logger.error(f"❌ Failed to get executable tasks: {e}", module="BACKEND-API", routine="get_executable_tasks")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/internal/missions/{mission_id}/progress")
async def get_mission_progress(
    mission_id: str,
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, Any]:
    """Get mission progress for dispatcher"""
    try:
        db_service = services["db_service"]
        progress = await db_service.get_mission_progress(mission_id)

        return progress

    except Exception as e:
        logger.error("❌ Failed to get mission progress: {e}", module="BACKEND-API", routine="get_mission_progress")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/internal/missions/{mission_id}/complete")
async def check_mission_complete(
    mission_id: str,
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, bool]:
    """Check if mission is complete"""
    try:
        db_service = services["db_service"]
        is_complete = await db_service.is_mission_complete(mission_id)

        return {"is_complete": is_complete}

    except Exception as e:
        logger.error("❌ Failed to check mission completion: {e}", module="BACKEND-API", routine="check_mission_complete")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/internal/missions/{mission_id}/failed")
async def check_mission_failed(
    mission_id: str,
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, bool]:
    """Check if mission has failed"""
    try:
        db_service = services["db_service"]
        is_failed = await db_service.is_mission_failed(mission_id)

        return {"is_failed": is_failed}

    except Exception as e:
        logger.error("❌ Failed to check mission failure: {e}", module="BACKEND-API", routine="check_mission_failed")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/internal/missions/{mission_id}")
async def update_mission_for_dispatcher(
    mission_id: str,
    mission_data: Dict[str, Any],
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, str]:
    """Update mission for dispatcher service"""
    try:
        db_service = services["db_service"]

        # Get existing mission
        mission = await db_service.get_mission(mission_id)
        if not mission:
            raise HTTPException(status_code=404, detail="Mission not found")

        # Update mission with new data
        for key, value in mission_data.items():
            if hasattr(mission, key):
                setattr(mission, key, value)

        # Save updated mission
        await db_service.update_mission(mission)

        return {"status": "updated"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to update mission: {e}", module="BACKEND-API", routine="update_mission_for_dispatcher")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/internal/tasks/pending")
async def get_pending_tasks(
    services: Dict[str, Any] = Depends(get_services)
) -> List[Dict[str, Any]]:
    """Get all pending tasks for dispatcher polling"""
    try:
        db_service = services["db_service"]
        tasks = await db_service.get_pending_tasks()

        return [task.model_dump() for task in tasks]

    except Exception as e:
        logger.error(f"❌ Failed to get pending tasks: {e}", module="BACKEND-API", routine="get_pending_tasks")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/internal/tasks/{task_id}")
async def get_task_for_dispatcher(
    task_id: str,
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, Any]:
    """Get task data for dispatcher service"""
    try:
        db_service = services["db_service"]
        task = await db_service.get_task(task_id)

        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        return task.model_dump()

    except HTTPException:
        raise
    except Exception as e:
        logger.error("❌ Failed to get task for dispatcher: {e}", module="BACKEND-API", routine="get_task_for_dispatcher")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/internal/tasks/{task_id}")
async def update_task_for_dispatcher(
    task_id: str,
    task_data: Dict[str, Any],
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, str]:
    """Update task for dispatcher service"""
    try:
        db_service = services["db_service"]

        # Get existing task
        task = await db_service.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        # Update task with new data
        for key, value in task_data.items():
            if hasattr(task, key):
                setattr(task, key, value)

        # Save updated task
        await db_service.update_task(task)

        return {"status": "updated"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error("❌ Failed to update task: {e}", module="BACKEND-API", routine="update_task_for_dispatcher")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/internal/missions/{mission_id}/tasks")
async def get_mission_tasks(
    mission_id: str,
    services: Dict[str, Any] = Depends(get_services)
) -> List[Dict[str, Any]]:
    """Get all tasks for a mission"""
    try:
        db_service = services["db_service"]
        tasks = await db_service.get_mission_tasks(mission_id)

        return [task.model_dump() for task in tasks]

    except Exception as e:
        logger.error("❌ Failed to get mission tasks: {e}", module="BACKEND-API", routine="get_mission_tasks")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks")
async def create_task(
    task_data: Dict[str, Any],
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, str]:
    """Create a new task - used by planner microservice"""
    try:
        db_service = services["db_service"]

        # Convert dict to Task object
        from shared_models import Task, TaskType

        task = Task(
            task_id=task_data["task_id"],
            mission_id=task_data["mission_id"],
            task_type=TaskType(task_data["task_type"]),
            description=task_data["description"],
            prompt=task_data["prompt"],
            parent_tasks=task_data.get("parent_tasks", []),
            child_tasks=task_data.get("child_tasks", []),
            assigned_agent=task_data.get("assigned_agent", "dialogue_agent"),
            context=task_data.get("context", {}),
        )

        # Create task in database
        task_id = await db_service.create_task(task)
        logger.info("✅ Task created: {task_id}", module="BACKEND-API", routine="create_task")

        return {"task_id": task_id}

    except Exception as e:
        logger.error("❌ Failed to create task: {e}", module="BACKEND-API", routine="create_task")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/internal/missions")
async def list_missions_for_dispatcher(
    status: Optional[str] = None,
    user_id: Optional[str] = None,
    limit: int = 100,
    skip: int = 0,
    services: Dict[str, Any] = Depends(get_services)
) -> List[Dict[str, Any]]:
    """List missions for dispatcher service"""
    try:
        db_service = services["db_service"]

        # Get missions from database
        missions = await db_service.list_missions(
            status=status,
            user_id=user_id,
            limit=limit,
            skip=skip
        )

        return [mission.model_dump() for mission in missions]

    except Exception as e:
        logger.error("❌ Failed to list missions for dispatcher: {e}", module="BACKEND-API", routine="list_missions_for_dispatcher")
        raise HTTPException(status_code=500, detail=str(e))


# ========================================
# USER MANAGEMENT ENDPOINTS - ALL VIA DATABASE SERVICE
# ========================================

@router.post("/users")
async def create_user_endpoint(
    user_data: Dict[str, Any],
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, Any]:
    """Create a new user via Database Service"""
    try:
        db_service = services["db_service"]
        user = await db_service.create_user(user_data)

        logger.info(f"✅ User created: {user.get('user_id')}", module="BACKEND-API", routine="create_user_endpoint")
        return {
            "status": "success",
            "message": "User created successfully",
            "user": user
        }

    except ValueError as e:
        logger.warning(f"⚠️ User creation validation error: {e}", module="BACKEND-API", routine="create_user_endpoint")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"❌ Failed to create user: {e}", module="BACKEND-API", routine="create_user_endpoint")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/users")
async def list_users_endpoint(
    limit: int = 100,
    skip: int = 0,
    role: Optional[str] = None,
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, Any]:
    """List all users via Database Service"""
    try:
        db_service = services["db_service"]
        users = await db_service.list_users(limit=limit, skip=skip, role=role)

        logger.debug(f"📋 Listed {len(users)} users", module="BACKEND-API", routine="list_users_endpoint")
        return {
            "status": "success",
            "users": users,
            "count": len(users)
        }

    except Exception as e:
        logger.error(f"❌ Failed to list users: {e}", module="BACKEND-API", routine="list_users_endpoint")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/users/{user_id}")
async def get_user_endpoint(
    user_id: str,
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, Any]:
    """Get user by ID via Database Service"""
    try:
        db_service = services["db_service"]
        user = await db_service.get_user_by_id(user_id)

        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        logger.debug(f"✅ User retrieved: {user_id}", module="BACKEND-API", routine="get_user_endpoint")
        return {
            "status": "success",
            "user": user
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get user {user_id}: {e}", module="BACKEND-API", routine="get_user_endpoint")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/users/by-username/{username}")
async def get_user_by_username_endpoint(
    username: str,
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, Any]:
    """Get user by username via Database Service"""
    try:
        db_service = services["db_service"]
        user = await db_service.get_user_by_username(username)

        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        logger.debug(f"✅ User retrieved by username: {username}", module="BACKEND-API", routine="get_user_by_username_endpoint")
        return {
            "status": "success",
            "user": user
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get user by username {username}: {e}", module="BACKEND-API", routine="get_user_by_username_endpoint")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/users/{user_id}")
async def update_user_endpoint(
    user_id: str,
    user_data: Dict[str, Any],
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, Any]:
    """Update a user via Database Service"""
    try:
        db_service = services["db_service"]
        user = await db_service.update_user(user_id, user_data)

        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        logger.info(f"✅ User updated: {user_id}", module="BACKEND-API", routine="update_user_endpoint")
        return {
            "status": "success",
            "message": "User updated successfully",
            "user": user
        }

    except ValueError as e:
        logger.warning(f"⚠️ User update validation error: {e}", module="BACKEND-API", routine="update_user_endpoint")
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to update user {user_id}: {e}", module="BACKEND-API", routine="update_user_endpoint")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/users/{user_id}")
async def delete_user_endpoint(
    user_id: str,
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, Any]:
    """Delete a user via Database Service"""
    try:
        db_service = services["db_service"]
        deleted = await db_service.delete_user(user_id)

        if not deleted:
            raise HTTPException(status_code=404, detail="User not found")

        logger.info(f"✅ User deleted: {user_id}", module="BACKEND-API", routine="delete_user_endpoint")
        return {
            "status": "success",
            "message": "User deleted successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to delete user {user_id}: {e}", module="BACKEND-API", routine="delete_user_endpoint")
        raise HTTPException(status_code=500, detail=str(e))



# ========================================
# SETTINGS MANAGEMENT ENDPOINTS
# ========================================

@router.get("/admin/settings")
async def get_all_settings_endpoint(
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, Any]:
    """Get all settings via Database Service"""
    try:
        db_service = services["db_service"]
        settings = await db_service.list_settings()

        logger.debug(f"📋 Retrieved {len(settings)} settings", module="BACKEND-API", routine="get_all_settings_endpoint")
        return {
            "status": "success",
            "settings": settings
        }

    except Exception as e:
        logger.error(f"❌ Failed to get settings: {e}", module="BACKEND-API", routine="get_all_settings_endpoint")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/admin/settings/{key}")
async def get_setting_endpoint(
    key: str,
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, Any]:
    """Get a specific setting via Database Service"""
    try:
        db_service = services["db_service"]
        value = await db_service.get_setting(key)

        logger.debug(f"✅ Setting retrieved: {key}", module="BACKEND-API", routine="get_setting_endpoint")
        return {
            "status": "success",
            "key": key,
            "value": value
        }

    except Exception as e:
        logger.error(f"❌ Failed to get setting {key}: {e}", module="BACKEND-API", routine="get_setting_endpoint")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/admin/settings")
async def set_setting_endpoint(
    setting_data: Dict[str, Any],
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, Any]:
    """Set a setting via Database Service"""
    try:
        db_service = services["db_service"]
        key = setting_data.get("key")
        value = setting_data.get("value")
        user_id = setting_data.get("user_id")

        if not key:
            raise HTTPException(status_code=400, detail="Setting key is required")

        success = await db_service.set_setting(key, value, user_id)

        if success:
            logger.info(f"✅ Setting updated: {key}", module="BACKEND-API", routine="set_setting_endpoint")
            return {
                "status": "success",
                "message": "Setting updated successfully",
                "key": key,
                "value": value
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to update setting")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to set setting: {e}", module="BACKEND-API", routine="set_setting_endpoint")
        raise HTTPException(status_code=500, detail=str(e))

# ========================================
# LOGS MANAGEMENT ENDPOINTS
# ========================================

@router.get("/admin/logs")
async def get_logs_endpoint(
    limit: int = 100,
    skip: int = 0,
    level: Optional[str] = None,
    service: Optional[str] = None,
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, Any]:
    """Get logs via Database Service"""
    try:
        db_service = services["db_service"]
        logs = await db_service.get_logs(limit=limit, skip=skip, level=level, service=service)

        logger.debug(f"📋 Retrieved {len(logs)} log entries", module="BACKEND-API", routine="get_logs_endpoint")
        return {
            "status": "success",
            "logs": logs,
            "count": len(logs)
        }

    except Exception as e:
        logger.error(f"❌ Failed to get logs: {e}", module="BACKEND-API", routine="get_logs_endpoint")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/admin/logs")
async def add_log_endpoint(
    log_data: Dict[str, Any],
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, Any]:
    """Add a log entry via Database Service"""
    try:
        db_service = services["db_service"]
        level = log_data.get("level", "INFO")
        service = log_data.get("service", "UNKNOWN")
        routine = log_data.get("routine", "unknown")
        message = log_data.get("message", "")
        user_id = log_data.get("user_id")

        if not message:
            raise HTTPException(status_code=400, detail="Log message is required")

        success = await db_service.add_log_entry(level, service, routine, message, user_id)

        if success:
            return {
                "status": "success",
                "message": "Log entry added successfully"
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to add log entry")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to add log entry: {e}", module="BACKEND-API", routine="add_log_endpoint")
        raise HTTPException(status_code=500, detail=str(e))

# ========================================
# DATABASE TESTING ENDPOINTS
# ========================================

@router.post("/admin/database/test")
async def test_database_connection_endpoint(
    config_data: Dict[str, Any],
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, Any]:
    """Test database connection with provided configuration"""
    try:
        import time
        from motor.motor_asyncio import AsyncIOMotorClient

        connection_string = config_data.get("connection_string")
        database_name = config_data.get("database_name")

        if not connection_string or not database_name:
            raise HTTPException(status_code=400, detail="Connection string and database name required")

        start_time = time.time()

        # Test connection
        client = AsyncIOMotorClient(
            connection_string,
            serverSelectionTimeoutMS=10000,
            connectTimeoutMS=10000,
            socketTimeoutMS=10000
        )

        # Test ping
        await client.admin.command('ping')

        # Get database info
        database = client[database_name]
        collections = await database.list_collection_names()

        response_time = int((time.time() - start_time) * 1000)

        # Determine connection type
        connection_type = "MongoDB Atlas" if "mongodb+srv://" in connection_string else "MongoDB"

        client.close()

        logger.info(f"✅ Database connection test successful: {database_name}", module="BACKEND-API", routine="test_database_connection_endpoint")

        return {
            "success": True,
            "database_name": database_name,
            "collections": collections,
            "response_time": response_time,
            "connection_type": connection_type
        }

    except Exception as e:
        logger.error(f"❌ Database connection test failed: {e}", module="BACKEND-API", routine="test_database_connection_endpoint")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/admin/database/config")
async def get_database_config_endpoint() -> Dict[str, Any]:
    """Get current database configuration"""
    try:
        import os

        return {
            "success": True,
            "connection_string": os.getenv("MONGODB_CONNECTION_STRING", ""),
            "database_name": os.getenv("MONGODB_DATABASE", "")
        }

    except Exception as e:
        logger.error(f"❌ Failed to get database config: {e}", module="BACKEND-API", routine="get_database_config_endpoint")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/admin/database/config")
async def save_database_config_endpoint(
    config_data: Dict[str, Any]
) -> Dict[str, Any]:
    """Save database configuration to environment"""
    try:
        # Note: This would typically update a configuration file
        # For now, we'll just return success as env vars are read-only at runtime
        logger.info("📝 Database configuration save requested", module="BACKEND-API", routine="save_database_config_endpoint")

        return {
            "success": True,
            "message": "Configuration saved (restart required for changes to take effect)"
        }

    except Exception as e:
        logger.error(f"❌ Failed to save database config: {e}", module="BACKEND-API", routine="save_database_config_endpoint")
        raise HTTPException(status_code=500, detail=str(e))

# ========================================
# TERMINAL ACCESS ENDPOINTS
# ========================================

@router.get("/admin/terminals/{service_name}")
async def get_service_terminal_output(
    service_name: str
) -> Dict[str, Any]:
    """Get terminal output for a specific service"""
    try:
        import subprocess
        import os

        # Map service names to their log files or processes
        service_map = {
            "backend": "backend",
            "dispatcher": "dispatcher",
            "planner": "planner",
            "dialogue": "dialogue",
            "phone": "phone",
            "webchat": "web_chat"
        }

        if service_name not in service_map:
            raise HTTPException(status_code=404, detail=f"Service '{service_name}' not found")

        actual_service = service_map[service_name]

        # Try to get recent log output from the service
        try:
            # First try to get from running processes
            result = subprocess.run(
                ["ps", "aux"],
                capture_output=True,
                text=True,
                timeout=5
            )

            # Look for the service process
            lines = result.stdout.split('\n')
            service_lines = [line for line in lines if actual_service in line.lower() and 'python' in line]

            if service_lines:
                # Service is running
                output = f"🟢 {service_name.upper()} SERVICE STATUS: RUNNING\n"
                output += f"📊 Process Info:\n"
                for line in service_lines[:3]:  # Show first 3 matching processes
                    output += f"   {line}\n"
                output += f"\n📋 Recent Activity:\n"
                output += f"   Service is active and processing requests\n"
                output += f"   Last check: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"

                # Try to get actual log content if available
                log_file = f"{actual_service}.log"
                if os.path.exists(log_file):
                    try:
                        with open(log_file, 'r') as f:
                            lines = f.readlines()
                            recent_lines = lines[-20:]  # Last 20 lines
                            output += f"\n📝 Recent Log Entries:\n"
                            output += "".join(recent_lines)
                    except:
                        pass

            else:
                output = f"🔴 {service_name.upper()} SERVICE STATUS: NOT RUNNING\n"
                output += f"❌ No active processes found for {actual_service}\n"
                output += f"💡 Service may need to be started\n"

        except subprocess.TimeoutExpired:
            output = f"⏱️ {service_name.upper()} SERVICE STATUS: TIMEOUT\n"
            output += f"❌ Process check timed out\n"
        except Exception as e:
            output = f"❌ {service_name.upper()} SERVICE STATUS: ERROR\n"
            output += f"🔍 Error: {str(e)}\n"

        logger.debug(f"📺 Terminal output requested for {service_name}", module="BACKEND-API", routine="get_service_terminal_output")

        return {
            "success": True,
            "service": service_name,
            "output": output
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get terminal output for {service_name}: {e}", module="BACKEND-API", routine="get_service_terminal_output")
        return {
            "success": False,
            "error": str(e),
            "output": f"❌ Error retrieving terminal output: {str(e)}"
        }

# ========================================
# PASSWORD MIGRATION ENDPOINTS
# ========================================

@router.post("/admin/migrate-passwords")
async def migrate_legacy_passwords_endpoint(
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, Any]:
    """Migrate legacy users to have proper password hashes"""
    try:
        db_service = services["db_service"]

        logger.info("🔐 Starting password migration for legacy users", module="BACKEND-API", routine="migrate_legacy_passwords_endpoint")

        # Perform migration
        results = await db_service.migrate_legacy_users_passwords()

        if results["success"]:
            logger.info(f"✅ Password migration completed successfully", module="BACKEND-API", routine="migrate_legacy_passwords_endpoint")
        else:
            logger.warning(f"⚠️ Password migration completed with errors", module="BACKEND-API", routine="migrate_legacy_passwords_endpoint")

        return results

    except Exception as e:
        logger.error(f"❌ Failed to migrate passwords: {e}", module="BACKEND-API", routine="migrate_legacy_passwords_endpoint")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/admin/update-user-password")
async def update_user_password_endpoint(
    password_data: Dict[str, str],
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, Any]:
    """Update a specific user's password hash"""
    try:
        db_service = services["db_service"]
        username = password_data.get("username")
        password = password_data.get("password")

        if not username or not password:
            raise HTTPException(status_code=400, detail="Username and password required")

        success = await db_service.update_user_password_hash(username, password)

        if success:
            logger.info(f"✅ Password updated for user: {username}", module="BACKEND-API", routine="update_user_password_endpoint")
            return {
                "success": True,
                "message": f"Password updated for user {username}"
            }
        else:
            raise HTTPException(status_code=404, detail=f"User {username} not found")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to update user password: {e}", module="BACKEND-API", routine="update_user_password_endpoint")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/users/{user_id}")
async def delete_user(
    user_id: str,
    services: Dict[str, Any] = Depends(get_services)
) -> Dict[str, Any]:
    """Delete a user"""
    try:
        db_service = services["db_service"]
        success = await db_service.delete_user(user_id)

        if not success:
            raise HTTPException(status_code=404, detail="User not found")

        return {
            "status": "success",
            "message": "User deleted successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to delete user {user_id}: {e}", module="BACKEND-API", routine="delete_user")
        raise HTTPException(status_code=500, detail=str(e))






# ========================================
# SYSTEM SETTINGS ENDPOINTS
# ========================================

class SystemSettingsRequest(BaseModel):
    """Request model for system settings"""
    host_config: Optional[Dict[str, str]] = None
    port_config: Optional[Dict[str, int]] = None


@router.get("/system-settings")
async def get_system_settings(request: Request):
    """Get current system settings"""
    try:
        db = get_database_from_request(request)
        collection = db["System_Settings"]

        settings = await collection.find_one({"_id": "deeplica_system_config"})

        if not settings:
            # Return default settings if none exist
            from shared.system_settings import SystemSettingsManager
            settings_manager = SystemSettingsManager()
            settings = settings_manager.default_settings

        logger.info("System settings retrieved", module="BACKEND-API", routine="get_system_settings")
        return settings

    except Exception as e:
        logger.error(f"Failed to get system settings: {e}", module="BACKEND-API", routine="get_system_settings")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/system-settings")
async def save_system_settings(settings: Dict[str, Any], request: Request):
    """Save system settings"""
    try:
        from datetime import datetime

        db = get_database_from_request(request)
        collection = db["System_Settings"]

        # Ensure required fields
        settings["_id"] = "deeplica_system_config"
        settings["updated_at"] = datetime.utcnow()

        # Upsert the settings
        result = await collection.replace_one(
            {"_id": "deeplica_system_config"},
            settings,
            upsert=True
        )

        logger.info("System settings saved", module="BACKEND-API", routine="save_system_settings")
        return {"status": "saved", "modified_count": result.modified_count}

    except Exception as e:
        logger.error(f"Failed to save system settings: {e}", module="BACKEND-API", routine="save_system_settings")
        raise HTTPException(status_code=500, detail=str(e))


# ========================================
# 👤 USER PREFERENCES ENDPOINTS
# ========================================

@router.get("/user-preferences", response_model=UserPreferencesResponse)
async def get_user_preferences(
    user_id: str,
    category: Optional[str] = None,
    subcategory: Optional[str] = None,
    request: Request = None
):
    """Get user preferences - ONLY DATABASE SERVICE CAN ACCESS MONGODB"""
    try:
        logger.debug(f"🔍 Getting user preferences for {user_id} - category: {category}, subcategory: {subcategory}")

        db = get_database_from_request(request)
        collection = db["user_preferences"]

        # Build query filter
        filter_query = {"user_id": user_id}
        if category:
            filter_query["category"] = category
        if subcategory:
            filter_query["subcategory"] = subcategory

        preference_doc = await collection.find_one(filter_query)

        if preference_doc:
            # Remove MongoDB _id field
            preference_doc.pop("_id", None)
            logger.debug(f"✅ Found preferences for user {user_id}")
            return UserPreferencesResponse(**preference_doc)
        else:
            logger.debug(f"ℹ️ No preferences found for user {user_id}")
            return UserPreferencesResponse(preferences=None)

    except Exception as e:
        logger.error(f"❌ Failed to get user preferences: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get user preferences: {str(e)}")


@router.post("/user-preferences")
async def save_user_preferences(preferences_data: UserPreferencesRequest, request: Request = None):
    """Save user preferences - ONLY DATABASE SERVICE CAN ACCESS MONGODB"""
    try:
        logger.debug(f"🔍 Saving user preferences for {preferences_data.user_id}")

        db = get_database_from_request(request)
        collection = db["user_preferences"]

        # Convert to dict for database storage
        preference_doc = preferences_data.model_dump()

        # Build filter query
        filter_query = {"user_id": preferences_data.user_id, "category": preferences_data.category}
        if preferences_data.subcategory:
            filter_query["subcategory"] = preferences_data.subcategory

        # Upsert the preferences
        await collection.replace_one(
            filter_query,
            preference_doc,
            upsert=True
        )

        logger.info(f"✅ User preferences saved for {preferences_data.user_id}: {preferences_data.category}" +
                   (f"/{preferences_data.subcategory}" if preferences_data.subcategory else ""))
        return {"success": True, "message": "Preferences saved successfully"}

    except Exception as e:
        logger.error(f"❌ Failed to save user preferences: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to save user preferences: {str(e)}")


# ========================================
# 📍 LOCATION HISTORY ENDPOINTS
# ========================================

@router.get("/location-history/{user_id}", response_model=LocationHistoryResponse)
async def get_location_history(user_id: str, limit: int = 50, request: Request = None):
    """Get user location history - ONLY DATABASE SERVICE CAN ACCESS MONGODB"""
    try:
        logger.debug(f"🔍 Getting location history for user {user_id} (limit: {limit})")

        

        db = get_database_from_request(request)
        collection = db["location_history"]

        # Get location history for user, sorted by timestamp (newest first)
        cursor = collection.find({"user_id": user_id}).sort("timestamp", -1).limit(limit)
        history_docs = await cursor.to_list(length=limit)

        # Convert to response format
        history_entries = []
        for doc in history_docs:
            doc.pop("_id", None)  # Remove MongoDB _id
            doc.pop("user_id", None)  # Remove user_id from entry
            doc.pop("username", None)  # Remove username from entry
            history_entries.append(LocationHistoryEntry(**doc))

        # Get total count
        total_count = await collection.count_documents({"user_id": user_id})

        # Get username from first entry or use user_id
        username = history_docs[0].get("username", user_id) if history_docs else user_id

        logger.debug(f"✅ Found {len(history_entries)} location entries for user {user_id}")
        return LocationHistoryResponse(
            user_id=user_id,
            username=username,
            history=history_entries,
            total_entries=total_count
        )

    except Exception as e:
        logger.error(f"❌ Failed to get location history: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get location history: {str(e)}")


@router.post("/location-history")
async def save_location_history(history_data: LocationHistoryRequest, request: Request = None):
    """Save location history entry - ONLY DATABASE SERVICE CAN ACCESS MONGODB"""
    try:
        logger.debug(f"🔍 Saving location history for user {history_data.user_id}")

        

        db = get_database_from_request(request)
        collection = db["location_history"]

        # Prepare document for storage
        location_doc = {
            "user_id": history_data.user_id,
            "username": history_data.username,
            **history_data.location_entry.model_dump(),
            "created_at": datetime.utcnow().isoformat()
        }

        # Insert the location entry
        result = await collection.insert_one(location_doc)

        # Keep only last 100 entries per user (cleanup old entries)
        total_count = await collection.count_documents({"user_id": history_data.user_id})
        if total_count > 100:
            # Find oldest entries to delete
            oldest_entries = await collection.find(
                {"user_id": history_data.user_id}
            ).sort("timestamp", 1).limit(total_count - 100).to_list(length=total_count - 100)

            if oldest_entries:
                oldest_ids = [entry["_id"] for entry in oldest_entries]
                await collection.delete_many({"_id": {"$in": oldest_ids}})
                logger.debug(f"🧹 Cleaned up {len(oldest_ids)} old location entries for user {history_data.user_id}")

        logger.info(f"✅ Location history saved for user {history_data.user_id}")
        return {"success": True, "message": "Location history saved successfully", "entry_id": str(result.inserted_id)}

    except Exception as e:
        logger.error(f"❌ Failed to save location history: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to save location history: {str(e)}")


@router.delete("/location-history/{user_id}")
async def clear_location_history(user_id: str, request: Request = None):
    """Clear all location history for user - ONLY DATABASE SERVICE CAN ACCESS MONGODB"""
    try:
        logger.debug(f"🔍 Clearing location history for user {user_id}")

        

        db = get_database_from_request(request)
        collection = db["location_history"]

        # Delete all location history for user
        result = await collection.delete_many({"user_id": user_id})

        logger.info(f"✅ Cleared {result.deleted_count} location history entries for user {user_id}")
        return {"success": True, "message": f"Cleared {result.deleted_count} location history entries"}

    except Exception as e:
        logger.error(f"❌ Failed to clear location history: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to clear location history: {str(e)}")


# ========================================
# 👤 USER MANAGEMENT ENDPOINTS
# ========================================

@router.get("/users/{username}")
async def get_user_by_username(username: str, request: Request = None):
    """Get user by username - ONLY DATABASE SERVICE CAN ACCESS MONGODB"""
    try:
        logger.debug(f"🔍 Getting user by username: {username}")

        

        db = get_database_from_request(request)
        collection = db["users"]

        # Find user by username (case-insensitive)
        user_doc = await collection.find_one({"username": {"$regex": f"^{username}$", "$options": "i"}})

        if not user_doc:
            raise HTTPException(status_code=404, detail="User not found")

        # Remove sensitive fields
        user_doc.pop("_id", None)
        user_doc.pop("password_hash", None)

        logger.debug(f"✅ User found: {username}")
        return user_doc

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get user: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get user: {str(e)}")


@router.post("/users")
async def create_user(user_data: dict, request: Request = None):
    """Create new user - ONLY DATABASE SERVICE CAN ACCESS MONGODB"""
    try:
        logger.debug(f"🔍 Creating user: {user_data.get('username')}")

        import uuid
        from datetime import datetime

        db = get_database_from_request(request)
        collection = db["users"]

        # Check if user already exists
        existing_user = await collection.find_one({
            "$or": [
                {"username": {"$regex": f"^{user_data['username']}$", "$options": "i"}},
                {"email": user_data.get("email", "")}
            ]
        })

        if existing_user:
            raise HTTPException(status_code=409, detail="User already exists")

        # Hash password using PBKDF2 with salt (more secure) - SYSTEM WIDE STANDARD
        password_hash = hash_password(user_data["password"])

        # Create user document
        user_doc = {
            "user_id": str(uuid.uuid4()),
            "username": user_data["username"],
            "email": user_data.get("email", ""),
            "password_hash": password_hash,
            "full_name": user_data.get("full_name", ""),
            "role": user_data.get("role", "user"),
            "is_admin": user_data.get("is_admin", False),
            "status": "active",
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat()
        }

        # Insert user
        result = await collection.insert_one(user_doc)

        # Remove sensitive fields from response
        user_doc.pop("_id", None)
        user_doc.pop("password_hash", None)

        logger.info(f"✅ User created: {user_data['username']}")
        return user_doc

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to create user: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create user: {str(e)}")


@router.post("/users/authenticate")
async def authenticate_user(login_data: dict, request: Request = None):
    """Authenticate user - ONLY DATABASE SERVICE CAN ACCESS MONGODB"""
    try:
        logger.debug(f"🔍 Authenticating user: {login_data.get('username')}")

        import hashlib

        db = get_database_from_request(request)
        collection = db["users"]

        # Find user by username (case-insensitive)
        user_doc = await collection.find_one({
            "username": {"$regex": f"^{login_data['username']}$", "$options": "i"}
        })

        if not user_doc:
            raise HTTPException(status_code=401, detail="Invalid credentials")

        # Verify password using unified PBKDF2 system with automatic migration
        password = login_data["password"]
        stored_hash = user_doc["password_hash"]

        # Verify password (supports legacy formats)
        password_valid = verify_password(password, stored_hash)

        if not password_valid:
            raise HTTPException(status_code=401, detail="Invalid credentials")

        # Check if password hash needs migration to current PBKDF2 standard
        if needs_rehash(stored_hash):
            new_hash = hash_password(password)
            await collection.update_one(
                {"_id": user_doc["_id"]},
                {"$set": {"password_hash": new_hash}}
            )
            logger.info(f"🔐 Migrated password hash for user {user_doc['username']} to PBKDF2 with salt")

        # Remove sensitive fields
        user_doc.pop("_id", None)
        user_doc.pop("password_hash", None)

        logger.info(f"✅ User authenticated: {login_data['username']}")
        return user_doc

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to authenticate user: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to authenticate user: {str(e)}")


@router.post("/sessions")
async def create_session(session_data: dict, request: Request = None):
    """Create user session - ONLY DATABASE SERVICE CAN ACCESS MONGODB"""
    try:
        logger.debug(f"🔍 Creating session for user: {session_data.get('user_id')}")

        
        import uuid
        from datetime import datetime, timedelta

        db = get_database_from_request(request)
        collection = db["sessions"]

        # Create session document
        session_doc = {
            "session_id": str(uuid.uuid4()),
            "user_id": session_data["user_id"],
            "username": session_data.get("username", ""),
            "created_at": datetime.utcnow().isoformat(),
            "expires_at": (datetime.utcnow() + timedelta(hours=24)).isoformat(),
            "is_active": True
        }

        # Insert session
        result = await collection.insert_one(session_doc)

        # Remove MongoDB _id
        session_doc.pop("_id", None)

        logger.info(f"✅ Session created for user: {session_data.get('user_id')}")
        return session_doc

    except Exception as e:
        logger.error(f"❌ Failed to create session: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create session: {str(e)}")


@router.get("/sessions/{session_id}")
async def get_session(session_id: str, request: Request = None):
    """Get session by ID - ONLY DATABASE SERVICE CAN ACCESS MONGODB"""
    try:
        logger.debug(f"🔍 Getting session: {session_id}")

        
        from datetime import datetime

        db = get_database_from_request(request)
        collection = db["sessions"]

        # Find session
        session_doc = await collection.find_one({"session_id": session_id})

        if not session_doc:
            raise HTTPException(status_code=404, detail="Session not found")

        # Check if session is expired
        expires_at = datetime.fromisoformat(session_doc["expires_at"])
        if datetime.utcnow() > expires_at:
            # Delete expired session
            await collection.delete_one({"session_id": session_id})
            raise HTTPException(status_code=401, detail="Session expired")

        # Remove MongoDB _id
        session_doc.pop("_id", None)

        logger.debug(f"✅ Session found: {session_id}")
        return session_doc

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get session: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get session: {str(e)}")


@router.get("/users")
async def get_all_users(request: Request = None):
    """Get all users - ONLY DATABASE SERVICE CAN ACCESS MONGODB"""
    try:
        logger.debug("🔍 Getting all users")

        

        db = get_database_from_request(request)
        collection = db["users"]

        # Get all users
        cursor = collection.find({})
        users = []
        async for user_doc in cursor:
            # Remove sensitive fields
            user_doc.pop("_id", None)
            user_doc.pop("password_hash", None)
            users.append(user_doc)

        logger.debug(f"✅ Found {len(users)} users")
        return users

    except Exception as e:
        logger.error(f"❌ Failed to get all users: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get all users: {str(e)}")


@router.get("/users/by-id/{user_id}")
async def get_user_by_id(user_id: str, request: Request = None):
    """Get user by ID - ONLY DATABASE SERVICE CAN ACCESS MONGODB"""
    try:
        logger.debug(f"🔍 Getting user by ID: {user_id}")

        

        db = get_database_from_request(request)
        collection = db["users"]

        # Find user by user_id
        user_doc = await collection.find_one({"user_id": user_id})

        if not user_doc:
            raise HTTPException(status_code=404, detail="User not found")

        # Remove sensitive fields
        user_doc.pop("_id", None)
        user_doc.pop("password_hash", None)

        logger.debug(f"✅ User found: {user_id}")
        return user_doc

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get user by ID: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get user by ID: {str(e)}")


@router.delete("/sessions/{session_id}")
async def delete_session(session_id: str, request: Request = None):
    """Delete session - ONLY DATABASE SERVICE CAN ACCESS MONGODB"""
    try:
        logger.debug(f"🔍 Deleting session: {session_id}")

        

        db = get_database_from_request(request)
        collection = db["sessions"]

        # Delete session
        result = await collection.delete_one({"session_id": session_id})

        logger.info(f"✅ Session deleted: {session_id}")
        return {"success": True, "message": "Session deleted successfully"}

    except Exception as e:
        logger.error(f"❌ Failed to delete session: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete session: {str(e)}")


@router.post("/admin/migrate-passwords")
async def migrate_passwords_to_pbkdf2(request: Request = None):
    """
    Migrate all user passwords to PBKDF2 with salt (more secure)
    This is the SELECTED ONE SYSTEM WIDE standard
    ADMIN ONLY endpoint
    """
    try:
        logger.info("🔐 Starting password migration to PBKDF2 with salt")

        db = get_database_from_request(request)
        collection = db["users"]

        # Find all users
        users = await collection.find({}).to_list(None)
        logger.info(f"📋 Found {len(users)} users to check")

        migrated_count = 0
        updated_count = 0
        migration_results = []

        for user in users:
            username = user.get("username", "unknown")
            stored_hash = user.get("password_hash", "")

            result = {"username": username, "status": "checked"}

            # Check if password needs migration
            if needs_rehash(stored_hash):
                logger.info(f"🔄 Migrating password for user: {username}")

                # For known default passwords, migrate them
                if username.lower() == "admin":
                    new_hash = hash_password("admin123")
                    await collection.update_one(
                        {"_id": user["_id"]},
                        {"$set": {"password_hash": new_hash}}
                    )
                    result["status"] = "migrated"
                    result["password"] = "admin123"
                    migrated_count += 1

                elif username.lower() == "guest":
                    new_hash = hash_password("guest123")
                    await collection.update_one(
                        {"_id": user["_id"]},
                        {"$set": {"password_hash": new_hash}}
                    )
                    result["status"] = "migrated"
                    result["password"] = "guest123"
                    migrated_count += 1

                elif username.lower() == "testadmin":
                    new_hash = hash_password("admin123")
                    await collection.update_one(
                        {"_id": user["_id"]},
                        {"$set": {"password_hash": new_hash}}
                    )
                    result["status"] = "migrated"
                    result["password"] = "admin123"
                    migrated_count += 1

                else:
                    result["status"] = "needs_manual_migration"
                    result["note"] = "Password will be migrated on next login"

            else:
                result["status"] = "already_pbkdf2"
                updated_count += 1

            migration_results.append(result)

        logger.info(f"✅ Password migration completed: {migrated_count} migrated, {updated_count} already updated")

        return {
            "status": "success",
            "message": "Password migration completed",
            "summary": {
                "total_users": len(users),
                "migrated": migrated_count,
                "already_updated": updated_count
            },
            "results": migration_results
        }

    except Exception as e:
        logger.error(f"❌ Password migration failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Migration failed: {str(e)}")


@router.get("/admin/database-stats")
async def get_database_statistics(request: Request = None):
    """
    Get database statistics for admin dashboard
    ADMIN ONLY endpoint - provides database metrics through Database Service
    """
    try:
        logger.info("📊 Getting database statistics for admin")

        db = get_database_from_request(request)

        # Get collection statistics
        stats = {
            "users": {
                "count": await db["users"].count_documents({}),
                "active_count": await db["users"].count_documents({"status": "active"}),
                "admin_count": await db["users"].count_documents({"role": "admin"})
            },
            "missions": {
                "count": await db["missions"].count_documents({}),
                "active_count": await db["missions"].count_documents({"status": "active"}),
                "completed_count": await db["missions"].count_documents({"status": "completed"})
            },
            "tasks": {
                "count": await db["tasks"].count_documents({}),
                "pending_count": await db["tasks"].count_documents({"status": "pending"}),
                "completed_count": await db["tasks"].count_documents({"status": "completed"})
            },
            "sessions": {
                "count": await db["sessions"].count_documents({}),
                "active_count": await db["sessions"].count_documents({"expires_at": {"$gt": datetime.now().isoformat()}})
            }
        }

        # Get database info
        db_stats = await db.command("dbStats")

        stats["database"] = {
            "name": db.name,
            "collections": db_stats.get("collections", 0),
            "data_size": db_stats.get("dataSize", 0),
            "storage_size": db_stats.get("storageSize", 0),
            "indexes": db_stats.get("indexes", 0)
        }

        logger.info("✅ Database statistics retrieved successfully")

        return {
            "status": "success",
            "statistics": stats
        }

    except Exception as e:
        logger.error(f"❌ Failed to get database statistics: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get database statistics: {str(e)}")


@router.get("/admin/system-health")
async def get_system_health(request: Request = None):
    """
    Get comprehensive system health information for admin dashboard
    ADMIN ONLY endpoint
    """
    try:
        logger.info("🏥 Getting system health information")

        db = get_database_from_request(request)

        # Test database connection
        try:
            await db.command("ping")
            database_status = "healthy"
            database_message = "Database connection successful"
        except Exception as db_error:
            database_status = "unhealthy"
            database_message = f"Database connection failed: {str(db_error)}"

        # Get service status from app state
        health_info = {
            "database": {
                "status": database_status,
                "message": database_message,
                "service": "MongoDB Atlas"
            },
            "llm": {
                "status": "healthy",  # Assume healthy if Backend API is running
                "message": "LLM service operational",
                "service": "Gemini API"
            },
            "backend_api": {
                "status": "healthy",
                "message": "Backend API operational",
                "service": "DEEPLICA Backend"
            }
        }

        # Overall system status
        all_healthy = all(service["status"] == "healthy" for service in health_info.values())
        overall_status = "healthy" if all_healthy else "degraded"

        return {
            "status": "success",
            "overall_status": overall_status,
            "services": health_info,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"❌ Failed to get system health: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get system health: {str(e)}")