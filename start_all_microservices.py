#!/usr/bin/env python3
"""
🚀 START ALL DEEPLICA MICROSERVICES
Automatically launches each microservice in its own VS Code terminal
"""

import os
import sys
import time
import subprocess
import json
from datetime import datetime

# Add project root to path for shared modules
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.port_manager import get_service_port

class DeepplicaMicroserviceLauncher:
    """Launches each microservice in its own VS Code terminal"""
    
    def __init__(self):
        self.launcher_name = "MICROSERVICE-LAUNCHER"
        self.services = [
            {
                "name": "🐕 Watchdog",
                "launch_config": "🐕 Watchdog",
                "wait_time": 3
            },
            {
                "name": "🌐 Backend API",
                "launch_config": "🌐 Backend API",
                "wait_time": 5
            },
            {
                "name": "🎯 Dispatcher",
                "launch_config": "🎯 Dispatcher",
                "wait_time": 3
            },
            {
                "name": "💬 Dialogue Agent",
                "launch_config": "💬 Dialogue Agent",
                "wait_time": 2
            },
            {
                "name": "🧠 Planner Agent",
                "launch_config": "🧠 Planner Agent",
                "wait_time": 2
            },
            {
                "name": "📞 Phone Agent",
                "launch_config": "📞 Phone Agent",
                "wait_time": 2
            },
            {
                "name": "🖥️ CLI Terminal",
                "launch_config": "🖥️ CLI Terminal",
                "wait_time": 1
            }
        ]

    def launch_service_in_vscode_terminal(self, service_config):
        """Launch a service in a new VS Code integrated terminal"""
        name = service_config["name"]

        print(f"[{self.launcher_name}] 🚀 Starting {name} in VS Code terminal...")

        # Map service names to their actual commands
        service_commands = {
            "🐕 Watchdog": {
                "cmd": "python3 watchdog/main.py",
                "cwd": ".",
                "env": {"SERVICE_NAME": "WATCHDOG"}
            },
            "🌐 Backend API": {
                "cmd": "cd backend && python3 -m app.main",
                "cwd": ".",
                "env": {"PORT": str(get_service_port("backend")), "SERVICE_NAME": "BACKEND-API"}
            },
            "🎯 Dispatcher": {
                "cmd": "cd dispatcher && python3 -m app.main",
                "cwd": ".",
                "env": {"PORT": str(get_service_port("dispatcher")), "SERVICE_NAME": "DISPATCHER"}
            },
            "💬 Dialogue Agent": {
                "cmd": "cd agents/dialogue && python3 -m app.main",
                "cwd": ".",
                "env": {"PORT": str(get_service_port("dialogue")), "SERVICE_NAME": "DIALOGUE-AGENT"}
            },
            "🧠 Planner Agent": {
                "cmd": "cd agents/planner && python3 -m app.main",
                "cwd": ".",
                "env": {"PORT": str(get_service_port("planner")), "SERVICE_NAME": "PLANNER-AGENT"}
            },
            "📞 Phone Agent": {
                "cmd": "cd agents/phone && python3 -m app.main",
                "cwd": ".",
                "env": {"PORT": str(get_service_port("phone")), "SERVICE_NAME": "PHONE-AGENT"}
            },
            "🖥️ CLI Terminal": {
                "cmd": "python3 cli/main.py",
                "cwd": ".",
                "env": {"SERVICE_NAME": "CLI-TERMINAL"}
            }
        }

        if name not in service_commands:
            print(f"[{self.launcher_name}] ❌ Unknown service: {name}")
            return False

        service_info = service_commands[name]

        try:
            # Use VS Code's integrated terminal via AppleScript
            if sys.platform == "darwin":
                # Create environment setup
                env_setup = []
                env_setup.append(f'export PYTHONPATH="{os.path.abspath(".")}"')
                for key, value in service_info["env"].items():
                    env_setup.append(f'export {key}="{value}"')

                # Combine environment and command
                full_command = " && ".join(env_setup) + f" && {service_info['cmd']}"

                # AppleScript to create new VS Code terminal and run command
                vscode_script = f'''
                tell application "Visual Studio Code"
                    activate
                end tell

                delay 0.5

                tell application "System Events"
                    tell process "Visual Studio Code"
                        -- Open new terminal with Cmd+Shift+`
                        keystroke "`" using {{command down, shift down}}
                        delay 1

                        -- Type the command
                        keystroke "{full_command.replace('"', '\\"')}"

                        -- Press Enter to execute
                        key code 36
                    end tell
                end tell
                '''

                try:
                    result = subprocess.run(
                        ["osascript", "-e", vscode_script],
                        capture_output=True,
                        text=True,
                        timeout=15
                    )

                    if result.returncode == 0:
                        print(f"[{self.launcher_name}] ✅ {name} started in VS Code terminal")
                        return True
                    else:
                        print(f"[{self.launcher_name}] ⚠️ VS Code terminal launch failed for {name}: {result.stderr}")
                        return self.launch_service_directly(service_config)

                except subprocess.TimeoutExpired:
                    print(f"[{self.launcher_name}] ⚠️ VS Code terminal launch timed out for {name}")
                    return self.launch_service_directly(service_config)
                except Exception as e:
                    print(f"[{self.launcher_name}] ⚠️ VS Code terminal launch error for {name}: {e}")
                    return self.launch_service_directly(service_config)

            # For non-macOS, fallback to direct launch
            else:
                print(f"[{self.launcher_name}] ⚠️ VS Code terminal automation only supported on macOS")
                return self.launch_service_directly(service_config)

        except Exception as e:
            print(f"[{self.launcher_name}] ❌ Failed to launch {name} in VS Code terminal: {e}")
            return self.launch_service_directly(service_config)

    def launch_service_directly(self, service_config):
        """Fallback: Launch service directly in background"""
        name = service_config["name"]
        
        # Map service names to their actual commands
        service_commands = {
            "🐕 Watchdog": {
                "cmd": ["python3", "watchdog/main.py"],
                "cwd": ".",
                "env": {"SERVICE_NAME": "WATCHDOG"}
            },
            "🌐 Backend API": {
                "cmd": ["python3", "-m", "app.main"],
                "cwd": "backend",
                "env": {"PORT": str(get_service_port("backend")), "SERVICE_NAME": "BACKEND-API"}
            },
            "🎯 Dispatcher": {
                "cmd": ["python3", "-m", "app.main"],
                "cwd": "dispatcher",
                "env": {"PORT": str(get_service_port("dispatcher")), "SERVICE_NAME": "DISPATCHER"}
            },
            "💬 Dialogue Agent": {
                "cmd": ["python3", "-m", "app.main"],
                "cwd": "agents/dialogue",
                "env": {"PORT": str(get_service_port("dialogue")), "SERVICE_NAME": "DIALOGUE-AGENT"}
            },
            "🧠 Planner Agent": {
                "cmd": ["python3", "-m", "app.main"],
                "cwd": "agents/planner",
                "env": {"PORT": str(get_service_port("planner")), "SERVICE_NAME": "PLANNER-AGENT"}
            },
            "📞 Phone Agent": {
                "cmd": ["python3", "-m", "app.main"],
                "cwd": "agents/phone",
                "env": {"PORT": str(get_service_port("phone")), "SERVICE_NAME": "PHONE-AGENT"}
            },
            "🖥️ CLI Terminal": {
                "cmd": ["python3", "cli/main.py"],
                "cwd": ".",
                "env": {"SERVICE_NAME": "CLI-TERMINAL"}
            }
        }
        
        if name not in service_commands:
            print(f"[{self.launcher_name}] ❌ Unknown service: {name}")
            return False
        
        service_info = service_commands[name]
        
        try:
            # Set up environment
            env = os.environ.copy()
            env["PYTHONPATH"] = os.path.abspath(".")
            env.update(service_info["env"])
            
            # Start the process in background
            process = subprocess.Popen(
                service_info["cmd"],
                cwd=service_info["cwd"],
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            print(f"[{self.launcher_name}] ✅ {name} started in background (PID {process.pid})")
            return True
            
        except Exception as e:
            print(f"[{self.launcher_name}] ❌ Failed to start {name}: {e}")
            return False

    def check_vscode_available(self):
        """Check if VS Code CLI is available"""
        try:
            result = subprocess.run(["code", "--version"], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"[{self.launcher_name}] ✅ VS Code CLI available")
                return True
            else:
                print(f"[{self.launcher_name}] ⚠️ VS Code CLI not responding")
                return False
        except Exception as e:
            print(f"[{self.launcher_name}] ⚠️ VS Code CLI not available: {e}")
            return False

    def launch_all_services(self):
        """Show instructions for launching all microservices in VS Code terminals"""
        print("🚀 DEEPLICA MICROSERVICE LAUNCHER")
        print("=" * 70)
        print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("🎯 Each microservice MUST run in its own VS Code integrated terminal")
        print("📋 Use the individual launch configurations for each service:")
        print("=" * 70)

        print("\n🔧 HOW TO START EACH SERVICE IN ITS OWN VS CODE TERMINAL:")
        print("   1. Press F5 in VS Code")
        print("   2. Select the service you want to start")
        print("   3. It will open in its own VS Code terminal tab")
        print("   4. Repeat for all services")

        print("\n📋 AVAILABLE LAUNCH CONFIGURATIONS:")
        for i, service_config in enumerate(self.services, 1):
            name = service_config["name"]
            launch_config = service_config["launch_config"]
            print(f"   {i}. {name}")
            print(f"      → Press F5 → Select '{launch_config}'")

        print("\n🎯 RECOMMENDED STARTUP ORDER:")
        print("   1. 🐕 Watchdog (FIRST - registry service)")
        print("   2. 🌐 Backend API (SECOND - core backend)")
        print("   3. 🎯 Dispatcher (THIRD - data management)")
        print("   4. 💬 Dialogue Agent")
        print("   5. 🧠 Planner Agent")
        print("   6. 📞 Phone Agent")
        print("   7. 🖥️ CLI Terminal (LAST)")

        print("\n✅ EACH SERVICE WILL:")
        print("   • Run in its own VS Code integrated terminal tab")
        print("   • Have proper environment variables set")
        print("   • Show real-time logs and output")
        print("   • Be independently debuggable")

        print("\n🛑 TO STOP ALL SERVICES:")
        print("   • Press F5 → Select '🛑 STOP DEEPLICA'")
        print("   • Or manually stop each terminal (Ctrl+C)")

        print("\n💡 WHY INDIVIDUAL LAUNCH CONFIGURATIONS:")
        print("   • VS Code compound configurations don't work in all versions")
        print("   • Individual launches give you full control")
        print("   • Each service gets its own dedicated terminal")
        print("   • Better debugging and monitoring experience")

        print(f"\n⏰ Instructions displayed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("🚀 Ready to start your Deeplica microservices!")

        return True

def main():
    """Main function"""
    launcher = DeepplicaMicroserviceLauncher()
    
    try:
        success = launcher.launch_all_services()
        
        if success:
            print(f"\n🎯 Microservices launched!")
            print(f"💡 Check VS Code terminal tabs for each service")
        else:
            print(f"\n⚠️ Launch completed with issues")
            
        # Keep the launcher running briefly to show the summary
        print(f"\n⏳ Launcher will close in 10 seconds...")
        time.sleep(10)
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print(f"\n🛑 Launch cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n🚨 Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
