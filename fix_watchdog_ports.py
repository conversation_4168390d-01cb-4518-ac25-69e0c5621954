#!/usr/bin/env python3
"""
Fix all hardcoded ports in watchdog/main.py to use port manager
"""

import re
from pathlib import Path

def fix_watchdog_ports():
    """Fix all hardcoded ports in watchdog/main.py"""
    watchdog_file = Path("watchdog/main.py")
    
    if not watchdog_file.exists():
        print("❌ watchdog/main.py not found")
        return
    
    # Read the file
    with open(watchdog_file, 'r') as f:
        content = f.read()
    
    # Define replacements for hardcoded ports
    replacements = [
        # Phone agent (8004)
        (r'http://localhost:8004/', f'http://localhost:{{get_service_port("phone")}}/'),
        (r'http://\{get_localhost\(\)\}:8004/', f'http://{{get_localhost()}}:{{get_service_port("phone")}}/'),
        (r'ngrok http 8004', f'ngrok http {{get_service_port("phone")}}'),
        (r'localhost:8004', f'localhost:{{get_service_port("phone")}}'),
        
        # Backend (8000 -> 8888, but some already fixed)
        (r'http://localhost:8000/', 'http://localhost:8888/'),
        (r'http://\{get_localhost\(\)\}:8000/', 'http://{get_localhost()}:8888/'),
        
        # Dispatcher (8001)
        (r'http://localhost:8001/', f'http://localhost:{{get_service_port("dispatcher")}}/'),
        
        # Agent ports in arrays
        (r'agent_ports = \[8002, 8003, 8004\]', f'agent_ports = [get_service_port("dialogue"), get_service_port("planner"), get_service_port("phone")]'),
        
        # Stop service references (remove these)
        (r'http://\{get_localhost\(\)\}:8006/', '# REMOVED: stop-deeplica service'),
        (r'http://localhost:8006/', '# REMOVED: stop-deeplica service'),
        
        # Watchdog port fallback
        (r'port=8005', f'port=get_service_port("watchdog")'),
        (r'port 8005', f'port {{get_service_port("watchdog")}}'),
    ]
    
    # Apply replacements
    modified = False
    for pattern, replacement in replacements:
        if re.search(pattern, content):
            content = re.sub(pattern, replacement, content)
            modified = True
            print(f"✅ Replaced: {pattern}")
    
    if modified:
        # Write back the file
        with open(watchdog_file, 'w') as f:
            f.write(content)
        print("✅ watchdog/main.py updated with port manager calls")
    else:
        print("ℹ️ No hardcoded ports found to replace")

if __name__ == "__main__":
    fix_watchdog_ports()
