#!/usr/bin/env python3
"""
Test to identify where task data is lost in the flow from phone call results to subsequent tasks.
This focuses on the specific issue where placeholders are not being resolved correctly.
"""

import asyncio
import sys
import os
import json
from unittest.mock import AsyncMock, MagicMock

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from dispatcher.app.orchestrator import MissionOrchestrator
from dispatcher.app.parameter_resolver import ParameterResolver
from shared_models import Task, TaskStatus


async def test_task_data_flow():
    """Test the task data flow to identify where information is lost"""
    
    print("🔍 Testing task data flow for placeholder resolution...")
    
    # Mock database service
    mock_db_service = AsyncMock()
    
    # Create a completed first task (phone call that got a number)
    first_task = Task(
        task_id="mission_123_call_ask_number",
        mission_id="mission_123",
        task_type="phone_call",
        description="Call to ask for a number",
        prompt="Call to ask for a number",
        status=TaskStatus.DONE,
        result={
            "call_status": "completed",
            "duration_seconds": 30,
            "conversation_transcript": [
                {"timestamp": "2025-01-01T10:00:00", "speaker": "agent", "text": "Hi, can you give me a number?"},
                {"timestamp": "2025-01-01T10:00:15", "speaker": "human", "text": "Sure, the number is 42"}
            ],
            "extracted_answer": "42",
            "question_answered": True,
            "twilio_call_sid": "CA123456789",
            "disconnect_reason": "completed-via-hangup",
            "error_message": None
        }
    )
    
    print(f"✅ First task result: {first_task.result}")
    
    # Create a second task that should use the result from the first task
    second_task = Task(
        task_id="mission_123_call_tell_number",
        mission_id="mission_123",
        task_type="phone_call",
        description="Call to tell the number",
        prompt="Call to tell the number received from previous call",
        status=TaskStatus.PENDING,
        context={
            "phone_number": "+972547000430",
            "contact_name": "Eran",
            "context": "Tell the number from previous call",
            "question": "The number is {{task:mission_123_call_ask_number:extracted_answer}}",
            "language": "en",
            "max_duration_minutes": 5
        }
    )
    
    print(f"📋 Second task context (before resolution): {second_task.context}")
    
    # Mock database to return the first task when requested
    mock_db_service.get_task.return_value = first_task
    
    # Test parameter resolution
    resolver = ParameterResolver(mock_db_service)
    
    # Check if placeholders are detected
    has_placeholders = resolver.has_placeholders(second_task.context)
    print(f"🔍 Has placeholders: {has_placeholders}")
    
    if has_placeholders:
        placeholders = resolver._find_all_placeholders(second_task.context)
        print(f"📍 Found {len(placeholders)} placeholders:")
        for i, placeholder in enumerate(placeholders):
            print(f"  {i+1}. {placeholder}")
    
    # Resolve the placeholders
    try:
        resolved_context = await resolver.resolve_task_parameters(second_task.context, "mission_123")
        print(f"✅ Resolved context: {resolved_context}")
        
        # Check if the question was resolved correctly
        original_question = second_task.context["question"]
        resolved_question = resolved_context["question"]
        
        print(f"📋 Original question: '{original_question}'")
        print(f"📋 Resolved question: '{resolved_question}'")
        
        expected_question = "The number is 42"
        if resolved_question == expected_question:
            print("✅ SUCCESS: Question placeholder resolved correctly!")
        else:
            print(f"❌ FAILED: Expected '{expected_question}', got '{resolved_question}'")
            return False
            
    except Exception as e:
        print(f"❌ Parameter resolution failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Now test how the dispatcher would handle this
    print("\n🔧 Testing dispatcher task preparation...")
    
    # Simulate how dispatcher prepares task data for phone agent
    task_data = {
        "description": second_task.description,
        "prompt": second_task.prompt,
        "context": second_task.context,
        "user_input": ""
    }
    
    # Add phone-specific fields
    phone_fields = ["phone_number", "question", "contact_name", "language", "max_duration_minutes"]
    for field in phone_fields:
        if field in second_task.context:
            task_data[field] = second_task.context[field]
    
    # Handle context field specially - it should override the generic context
    if "context" in second_task.context:
        task_data["context"] = second_task.context["context"]
    
    print(f"📋 Task data prepared for phone agent (before resolution): {task_data}")
    
    # Resolve placeholders in the prepared task data
    if resolver.has_placeholders(task_data):
        try:
            resolved_task_data = await resolver.resolve_task_parameters(task_data, "mission_123")
            print(f"📋 Task data after resolution: {resolved_task_data}")
            
            # Check the final question that would be sent to phone agent
            final_question = resolved_task_data.get("question", "")
            print(f"📞 Final question for phone agent: '{final_question}'")
            
            if final_question == "The number is 42":
                print("✅ SUCCESS: Phone agent would receive correct question!")
                return True
            else:
                print(f"❌ FAILED: Phone agent would receive incorrect question: '{final_question}'")
                return False
                
        except Exception as e:
            print(f"❌ Task data resolution failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    else:
        print("❌ No placeholders detected in task data - this is the problem!")
        return False


async def main():
    """Run the test"""
    print("🧪 Starting task data flow test...")
    
    success = await test_task_data_flow()
    
    if success:
        print("\n✅ Task data flow test passed!")
        return 0
    else:
        print("\n❌ Task data flow test failed!")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
