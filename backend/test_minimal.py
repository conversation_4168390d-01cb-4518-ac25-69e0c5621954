#!/usr/bin/env python3
"""
Minimal Backend API test - to debug startup issues
"""

import os
from fastapi import FastAPI
import uvicorn
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# [TEST-MINIMAL:startup] SYSTEM | Creating minimal FastAPI app...

# Create minimal FastAPI app without lifespan
app = FastAPI(
    title="Minimal Test API",
    description="Test API for debugging",
    version="0.1.0"
)

@app.get("/")
async def root():
    return {"message": "Minimal API is working!"}

@app.get("/health")
async def health():
    return {"status": "healthy", "message": "Minimal API is running"}

if __name__ == "__main__":
    # [BACKEND-API:health] STARTUP | 🔍 Starting minimal uvicorn server...
    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=get_service_port("backend"),
            log_level="info"
        )
    except Exception as e:
        # [BACKEND-API:health] STARTUP | ❌ Error starting server: {e}
        raise
