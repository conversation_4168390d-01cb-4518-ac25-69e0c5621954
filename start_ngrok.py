"""
🌐 Ngrok Tunnel Starter
ALL PORTS MANAGED DYNAMICALLY by shared/port_manager.py
<PERSON><PERSON> will use the port assigned by the port manager.
"""

#!/usr/bin/env python3
"""
🌐 NGROK TUNNEL STARTER
Starts ngrok tunnel for exposing local services to the internet
"""

import os
import sys
import subprocess
import time
import requests
from datetime import datetime

# Add project root to path for shared modules
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.port_manager import get_service_port

def start_ngrok_tunnel():
    """Start ngrok tunnel"""
    # Get the port for the service we want to tunnel (default to phone agent)
    service_to_tunnel = os.getenv('NGROK_SERVICE', 'phone')
    port = str(get_service_port(service_to_tunnel))
    
    print("🌐 NGROK TUNNEL STARTER")
    print("=" * 50)
    print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 Starting ngrok tunnel for port {port}")
    
    try:
        # Check if ngrok is installed
        try:
            result = subprocess.run(['ngrok', 'version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Ngrok installed: {result.stdout.strip()}")
            else:
                raise FileNotFoundError()
        except FileNotFoundError:
            print("❌ Ngrok not found")
            print("💡 Install ngrok from: https://ngrok.com/download")
            print("💡 Or use: brew install ngrok (on macOS)")
            return False
        
        # Check if port is in use
        try:
            response = requests.get(f"http://localhost:{port}", timeout=2)
            print(f"✅ Service is running on port {port}")
        except requests.exceptions.RequestException:
            print(f"⚠️ No service detected on port {port}")
            print(f"💡 Make sure your service is running before starting ngrok")
        
        # Start ngrok tunnel
        print(f"🚀 Starting ngrok tunnel for port {port}...")
        
        # Run ngrok in background
        process = subprocess.Popen(
            ['ngrok', 'http', port],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Wait a moment for ngrok to start
        time.sleep(3)
        
        # Check if ngrok started successfully
        if process.poll() is None:
            print("✅ Ngrok tunnel started successfully")
            
            # Try to get tunnel info
            try:
                api_response = requests.get(f"http://localhost:{get_service_port('ngrok-api')}/api/tunnels", timeout=5)
                if api_response.status_code == 200:
                    tunnels = api_response.json()
                    if tunnels.get('tunnels'):
                        for tunnel in tunnels['tunnels']:
                            public_url = tunnel.get('public_url')
                            if public_url:
                                print(f"🌍 Public URL: {public_url}")
                                print(f"🔗 Tunnel: {public_url} -> localhost:{port}")
                    else:
                        print("⚠️ No active tunnels found")
                else:
                    print("⚠️ Could not retrieve tunnel information")
            except Exception as e:
                print(f"⚠️ Could not get tunnel info: {e}")
            
            print("🎯 Ngrok is running in the background")
            print(f"🌐 Access ngrok dashboard at: http://localhost:{get_service_port('ngrok-api')}")
            print("🛑 Press Ctrl+C to stop the tunnel")
            
            # Keep the process running
            try:
                process.wait()
            except KeyboardInterrupt:
                print("\n🛑 Stopping ngrok tunnel...")
                process.terminate()
                process.wait()
                print("✅ Ngrok tunnel stopped")
            
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"❌ Ngrok failed to start")
            if stderr:
                print(f"Error: {stderr.decode()}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to start ngrok tunnel: {e}")
        return False

if __name__ == "__main__":
    try:
        success = start_ngrok_tunnel()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Ngrok startup cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n🚨 Unexpected error: {e}")
        sys.exit(1)
