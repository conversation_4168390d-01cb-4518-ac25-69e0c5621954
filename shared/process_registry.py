#!/usr/bin/env python3
"""
📋 PROCESS REGISTRY
Helper for Deeplica services to register themselves with Stop Deeplica Service
"""

import asyncio
import httpx
import os
import sys
import time
from datetime import datetime
from typing import Optional
from shared.port_manager import get_service_port

class ProcessRegistry:
    """Helper class for registering processes with Stop Deeplica Service"""
    
    def __init__(self, service_name: str, port: Optional[int] = None):
        self.service_name = service_name
        self.port = port
        self.process_id = os.getpid()
        self.watchdog_registry_url = "f"http://localhost:{get_service_port('watchdog')}""  # Watchdog is now the central registry
        self.registered = False
    
    async def register(self, terminal_name: str = "", command: str = "", mandatory: bool = True, max_retries: int = 5) -> bool:
        """Register this process with Watchdog Registry (MANDATORY)"""
        registration_data = {
            "service_name": self.service_name,
            "process_id": self.process_id,
            "port": self.port,
            "start_time": datetime.now().isoformat(),
            "command": command or " ".join(sys.argv),
            "terminal_name": terminal_name
        }

        for attempt in range(max_retries):
            try:
                async with httpx.AsyncClient(timeout=10.0) as client:
                    # Try normal registration first
                    response = await client.post(
                        f"{self.watchdog_registry_url}/register",
                        json=registration_data
                    )

                    if response.status_code == 200:
                        self.registered = True
                        print(f"[{self.service_name}] ✅ REGISTERED with Stop Deeplica Service (PID {self.process_id}, Port {self.port})")
                        return True
                    else:
                        # Try force registration if normal fails
                        force_response = await client.post(
                            f"{self.watchdog_registry_url}/force_register",
                            json=registration_data
                        )

                        if force_response.status_code == 200:
                            self.registered = True
                            print(f"[{self.service_name}] 🔒 FORCE REGISTERED with Stop Deeplica Service (PID {self.process_id}, Port {self.port})")
                            return True
                        else:
                            print(f"[{self.service_name}] ⚠️ Registration attempt {attempt + 1}/{max_retries} failed: HTTP {response.status_code}")

            except Exception as e:
                print(f"[{self.service_name}] ⚠️ Registration attempt {attempt + 1}/{max_retries} failed: {e}")

            if attempt < max_retries - 1:
                wait_time = 2 ** attempt  # Exponential backoff
                print(f"[{self.service_name}] ⏳ Retrying registration in {wait_time} seconds...")
                await asyncio.sleep(wait_time)

        if mandatory:
            print(f"[{self.service_name}] 🚨 CRITICAL: Failed to register after {max_retries} attempts")
            print(f"[{self.service_name}] 🛑 MANDATORY REGISTRATION FAILED - SERVICE CANNOT START")
            print(f"[{self.service_name}] 💡 REMOVED: stop-deeplica service no longer exists")
            sys.exit(1)  # Force exit if mandatory registration fails
        else:
            print(f"[{self.service_name}] ⚠️ Registration failed but continuing (non-mandatory)")
            return False
    
    async def unregister(self) -> bool:
        """Unregister this process from Stop Deeplica Service"""
        if not self.registered:
            return True

        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.post(
                    f"{self.watchdog_registry_url}/unregister/{self.service_name}"
                )

                if response.status_code == 200:
                    self.registered = False
                    print(f"[{self.service_name}] ✅ Unregistered from Stop Deeplica Service")
                    return True
                else:
                    print(f"[{self.service_name}] ⚠️ Failed to unregister: HTTP {response.status_code}")
                    return False

        except Exception as e:
            print(f"[{self.service_name}] ⚠️ Could not unregister: {e}")
            return False

    def _sync_unregister(self) -> bool:
        """Synchronous version of unregister for signal handlers"""
        if not self.registered:
            return True

        try:
            response = httpx.post(
                f"{self.watchdog_registry_url}/unregister/{self.service_name}",
                timeout=5.0
            )

            if response.status_code == 200:
                self.registered = False
                print(f"[{self.service_name}] ✅ Unregistered from Stop Deeplica Service")
                return True
            else:
                print(f"[{self.service_name}] ⚠️ Failed to unregister: HTTP {response.status_code}")
                return False

        except Exception as e:
            print(f"[{self.service_name}] ⚠️ Could not unregister: {e}")
            return False
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        import signal
        
        def signal_handler(signum, frame):
            print(f"\n[{self.service_name}] 🛑 Received signal {signum}, shutting down gracefully...")

            # Unregister from stop service (synchronous version)
            try:
                self._sync_unregister()
            except Exception as e:
                print(f"[{self.service_name}] ⚠️ Error during unregistration: {e}")

            # Exit gracefully
            sys.exit(0)
        
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)

def _sync_register(registry: ProcessRegistry, terminal_name: str, command: str, mandatory: bool) -> bool:
    """Synchronous wrapper for registration"""
    import httpx
    import time

    registration_data = {
        "service_name": registry.service_name,
        "process_id": registry.process_id,
        "port": registry.port,
        "start_time": datetime.now().isoformat(),
        "command": command or " ".join(sys.argv),
        "terminal_name": terminal_name
    }

    max_retries = 5
    for attempt in range(max_retries):
        try:
            # Try normal registration first
            response = httpx.post(
                f"{registry.watchdog_registry_url}/register",
                json=registration_data,
                timeout=10.0
            )

            if response.status_code == 200:
                registry.registered = True
                print(f"[{registry.service_name}] ✅ REGISTERED with Stop Deeplica Service (PID {registry.process_id}, Port {registry.port})")
                return True
            else:
                # Try force registration if normal fails
                force_response = httpx.post(
                    f"{registry.watchdog_registry_url}/force_register",
                    json=registration_data,
                    timeout=10.0
                )

                if force_response.status_code == 200:
                    registry.registered = True
                    print(f"[{registry.service_name}] 🔒 FORCE REGISTERED with Stop Deeplica Service (PID {registry.process_id}, Port {registry.port})")
                    return True
                else:
                    print(f"[{registry.service_name}] ⚠️ Registration attempt {attempt + 1}/{max_retries} failed: HTTP {response.status_code}")

        except Exception as e:
            print(f"[{registry.service_name}] ⚠️ Registration attempt {attempt + 1}/{max_retries} failed: {e}")

        if attempt < max_retries - 1:
            wait_time = 2 ** attempt  # Exponential backoff
            print(f"[{registry.service_name}] ⏳ Retrying registration in {wait_time} seconds...")
            time.sleep(wait_time)

    if mandatory:
        print(f"[{registry.service_name}] 🚨 CRITICAL: Failed to register after {max_retries} attempts")
        print(f"[{registry.service_name}] 🛑 MANDATORY REGISTRATION FAILED - SERVICE CANNOT START")
        print(f"[{registry.service_name}] 💡 REMOVED: stop-deeplica service no longer exists")
        sys.exit(1)  # Force exit if mandatory registration fails
    else:
        print(f"[{registry.service_name}] ⚠️ Registration failed but continuing (non-mandatory)")
        return False

def register_service(service_name: str, port: Optional[int] = None,
                    terminal_name: str = "", command: str = "", mandatory: bool = True) -> ProcessRegistry:
    """Convenience function to register a service (MANDATORY by default)"""
    print(f"[{service_name}] 🔒 MANDATORY REGISTRATION: Starting registration process...")

    registry = ProcessRegistry(service_name, port)

    # Setup signal handlers for graceful shutdown
    registry.setup_signal_handlers()

    # MANDATORY registration with stop service (synchronous)
    try:
        success = _sync_register(registry, terminal_name, command, mandatory)
        if success:
            print(f"[{service_name}] ✅ REGISTRATION SUCCESSFUL: Service is now tracked by Stop Deeplica Service")
        else:
            print(f"[{service_name}] ❌ REGISTRATION FAILED: Service may not be properly managed")
    except Exception as e:
        print(f"[{service_name}] 🚨 CRITICAL REGISTRATION ERROR: {e}")
        if mandatory:
            print(f"[{service_name}] 🛑 EXITING: Cannot start without registration")
            sys.exit(1)

    return registry

def register_service_non_mandatory(service_name: str, port: Optional[int] = None,
                                  terminal_name: str = "", command: str = "") -> ProcessRegistry:
    """Register a service without mandatory requirement (for special cases)"""
    return register_service(service_name, port, terminal_name, command, mandatory=False)

async def unregister_service(registry: ProcessRegistry):
    """Convenience function to unregister a service"""
    if registry:
        await registry.unregister()
