"""
Planner Agent Service - Handles mission planning tasks.
Microservice that processes planning and task generation tasks.
"""

import os
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

import sys

# Add project root to path for shared modules
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.unified_logging import get_planner_logger, should_log_status_change
from shared.port_manager import get_service_port, get_service_host, ensure_service_port_free
from shared.resilience_utils import bulletproof_retry, wait_for_service, bulletproof_service_wrapper

# Initialize unified logger
logger = get_planner_logger()

from .agent import PlannerAgent
from .models import TaskRequest
from .llm_service import BackendLLMClient
from shared.backend_readiness import ensure_backend_ready_before_startup


def cleanup_port(port: int):
    """Kill any processes using the specified port"""
    import subprocess
    import signal
    import time

    try:
        # Find processes using the port
        result = subprocess.run(
            ["lsof", "-ti", f": {port}"],
            capture_output=True,
            text=True,
            timeout=5
        )

        if result.returncode == 0 and result.stdout.strip():
            pids = result.stdout.strip().split('\n')
            for pid in pids:
                if pid.strip():
                    try:
                        pid_int = int(pid.strip())
                        logger.info("🔪 Killing process {pid_int} on port {port}", module="PLANNER-AGENT", routine="cleanup_port({port})")
                        os.kill(pid_int, signal.SIGKILL)
                        time.sleep(0.1)  # Brief pause to let process die
                    except (ProcessLookupError, ValueError, PermissionError):
                        pass  # Process already dead, invalid PID, or permission denied
    except subprocess.TimeoutExpired:
        logger.warning("Timeout checking port {port}", module="PLANNER-AGENT", routine="cleanup_port({port})")
    except Exception as e:
        logger.debug("Error cleaning port {port}: {e}", module="PLANNER-AGENT", routine="cleanup_port({port})")


async def wait_for_backend_and_initialize(app: FastAPI):
    """Wait for Backend API and initialize services - runs in background, NEVER crashes the main service"""
    import httpx
    import asyncio

    backend_url = os.getenv("BACKEND_URL", f"http://localhost:{get_service_port('backend')}")
    logger.info(f"⏳ Waiting for Backend API at {backend_url}", module="main", routine="wait_for_backend_and_initialize")
    logger.info("🔄 Will keep trying INDEFINITELY until Backend API is ready...", module="main", routine="wait_for_backend_and_initialize")
    logger.info("💪 This service will NEVER crash - it will wait as long as needed!", module="main", routine="wait_for_backend_and_initialize")

    attempt = 1
    while True:  # Keep trying forever until successful
        try:
            # Use bulletproof timeout and connection handling
            try:
                timeout = httpx.Timeout(30.0, connect=5.0, read=10.0, write=10.0, pool=5.0)
                limits = httpx.Limits(max_connections=1, max_keepalive_connections=0)

                async with httpx.AsyncClient(timeout=timeout, limits=limits) as client:
                    try:
                        # Check if Backend API is COMPLETELY ready (not just healthy)
                        response = await client.get(f"{backend_url}/ready")

                        if response.status_code == 200:
                            # Backend API is fully initialized and ready
                            try:
                                ready_data = response.json()
                                if isinstance(ready_data, dict) and ready_data.get("status") == "ready":
                                    logger.info("✅ Backend API is FULLY READY and initialized! (attempt {attempt})", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")
                                    break
                                else:
                                    logger.debug("Backend API responded but not fully ready: {ready_data}", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")
                            except (ValueError, TypeError, KeyError, AttributeError) as json_error:
                                logger.debug("Backend API responded but invalid JSON: {json_error}", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")
                        elif response.status_code == 503:
                            # Backend API is running but not fully ready yet
                            try:
                                error_data = response.json()
                                if isinstance(error_data, dict):
                                    logger.debug("Backend API still initializing: {error_data.get('detail', 'Unknown')}", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")
                                else:
                                    logger.debug("Backend API still initializing (503 response)", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")
                            except (ValueError, TypeError, KeyError, AttributeError):
                                logger.debug("Backend API still initializing (503 response)", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")
                        else:
                            logger.debug("Backend API returned status {response.status_code}", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")

                    except httpx.ConnectError as e:
                        logger.debug("Backend API connection failed (port not open yet): {e}", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")
                    except httpx.TimeoutException as e:
                        logger.debug("Backend API timeout (still starting up): {e}", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")
                    except httpx.ReadTimeout as e:
                        logger.debug("Backend API read timeout (still initializing): {e}", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")
                    except httpx.WriteTimeout as e:
                        logger.debug("Backend API write timeout: {e}", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")
                    except httpx.PoolTimeout as e:
                        logger.debug("Backend API pool timeout: {e}", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")
                    except httpx.HTTPStatusError as e:
                        logger.debug("Backend API HTTP error: {e}", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")
                    except httpx.RequestError as e:
                        logger.debug("Backend API request error: {e}", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")
                    except OSError as e:
                        logger.debug("Backend API OS error (network/socket): {e}", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")
                    except Exception as e:
                        logger.debug("Backend API unexpected response error: {e}", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")

            except ImportError as e:
                logger.debug("Failed to import httpx: {e}", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")
            except Exception as e:
                logger.debug("Failed to create HTTP client: {e}", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")

        except Exception as e:
            logger.debug("Unexpected error in connection attempt: {e}", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")

        # Log progress every 15 attempts (30 seconds) - only if status changes
        if attempt % 15 == 0:
            status = f"waiting_attempt_{attempt // 15}"
            if should_log_status_change("PLANNER-AGENT", "backend_wait", status):
                logger.info(f"⏳ Still waiting for Backend API... (attempt {attempt}, {attempt * 2 // 60} minutes elapsed)", module="main", routine="wait_for_backend_and_initialize")
                logger.info("💪 Planner Agent will NEVER give up - continuing to wait...", module="main", routine="wait_for_backend_and_initialize")

        try:
            logger.debug("⏳ Retrying in 2 seconds... (attempt {attempt})", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")
            await asyncio.sleep(2)
        except Exception as e:
            logger.debug("Sleep interrupted: {e}", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")
            # Use time.sleep as fallback
            try:
                import time
                time.sleep(2)
            except Exception as sleep_error:
                logger.debug("Fallback sleep failed: {sleep_error}", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")
                pass  # Continue anyway

        attempt += 1

    logger.info("proceeding with Planner Agent initialization!", module="PLANNER-AGENT:wait_for_backend_and_initialize", routine="HEALTH | ✅ Backend API is healthy and ready")

    # Initialize services with retry loop (avoid recursion)
    service_retry_attempt = 1
    while True:
        try:
            llm_service = BackendLLMClient()
            planner_agent = PlannerAgent(llm_service)
            app.state.agent = planner_agent
            app.state.ready = True
            logger.info("✅ Planner Agent initialized successfully", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")

            # 🛡️ BULLETPROOF: Keep the background task alive indefinitely
            logger.info("🔄 Background task will now run indefinitely to keep service alive", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")

            # Keep the background task alive by waiting indefinitely
            try:
                while True:
                    await asyncio.sleep(60)  # Sleep for 1 minute intervals
                    # Optional: Add health checks or maintenance tasks here
                    logger.debug("🔄 Background task heartbeat - service running", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")
            except asyncio.CancelledError:
                logger.info("🛑 Background task cancelled - service shutting down", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")
                raise  # Re-raise to properly handle cancellation
        except Exception as e:
            logger.error("❌ Failed to initialize Planner Agent (attempt {service_retry_attempt}): {e}", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")
            logger.info("🔄 Will retry initialization...", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")

            try:
                await asyncio.sleep(5)
            except Exception as sleep_error:
                logger.debug("Sleep interrupted during service retry: {sleep_error}", module="PLANNER-AGENT", routine="wait_for_backend_and_initialize")
                try:
                    import time
                    time.sleep(5)
                except Exception:
                    pass

            service_retry_attempt += 1

            # Log progress every 10 attempts - only if status changes
            if service_retry_attempt % 10 == 0:
                status = f"service_retry_attempt_{service_retry_attempt // 10}"
                if should_log_status_change("PLANNER-AGENT", "service_initialization", status):
                    logger.info(f"⏳ Still trying to initialize Planner Agent... (attempt {service_retry_attempt})", module="main", routine="wait_for_backend_and_initialize")
                    logger.info("💪 Will NEVER give up on service initialization!", module="main", routine="wait_for_backend_and_initialize")

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager - starts immediately, handles dependencies in background"""
    import asyncio

    # Startup
    logger.info("🚀 Starting Planner Agent Service", module="main", routine="lifespan")
    logger.info("💪 Service will start immediately and handle dependencies in background - NEVER crashes!", module="main", routine="lifespan")

    # Initialize basic state
    app.state.ready = False
    app.state.agent = None
    app.state.background_task = None

    # Start background task to wait for dependencies - this will NEVER crash the service
    try:
        background_task = asyncio.create_task(wait_for_backend_and_initialize(app))
        app.state.background_task = background_task
        logger.info("waiting for dependencies in background", module="PLANNER-AGENT:lifespan", routine="SYSTEM | ✅ Planner Agent service started")

        # Add exception handler for background task
        def handle_background_task_exception(task):
            if task.cancelled():
                logger.info("Background task was cancelled", module="PLANNER-AGENT", routine="lifespan()")
            elif task.exception():
                logger.error("Background task failed: {task.exception()}", module="PLANNER-AGENT", routine="lifespan()")
                logger.info("💪 Service will continue running despite background task failure", module="PLANNER-AGENT", routine="lifespan()")
            else:
                logger.error("💥 CRITICAL: Background task completed unexpectedly - this should NEVER happen!", module="PLANNER-AGENT", routine="lifespan()")
                logger.error("💥 CRITICAL: Background tasks should run indefinitely to keep service alive", module="PLANNER-AGENT", routine="lifespan()")

        background_task.add_done_callback(handle_background_task_exception)

    except Exception as e:
        logger.error("❌ Failed to start background task: {e}", module="PLANNER-AGENT", routine="handle_background_task_exception")
        # Even if background task fails, don't crash the service
        app.state.ready = False
        app.state.background_task = None
        logger.info("⚠️ Background task failed but service will continue running", module="PLANNER-AGENT", routine="handle_background_task_exception")

    yield

    # Shutdown
    logger.info("🛑 Shutting down Planner Agent Service", module="PLANNER-AGENT", routine="handle_background_task_exception")

    # Cancel background task safely
    if hasattr(app.state, 'background_task') and app.state.background_task:
        try:
            app.state.background_task.cancel()
            try:
                await app.state.background_task
            except asyncio.CancelledError:
                logger.info("Background task cancelled successfully", module="PLANNER-AGENT", routine="lifespan()")
        except Exception as e:
            logger.error("Error cancelling background task: {e}", module="PLANNER-AGENT", routine="lifespan()")

    logger.info("🛑 Planner Agent Service shutdown complete", module="PLANNER-AGENT", routine="lifespan()")


# Create FastAPI app
app = FastAPI(
    title="Planner Agent Service",
    description="Mission Planning and Task Generation Agent",
    version="0.1.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/health")
async def health_check():
    """Health check endpoint - always responds, shows dependency status"""
    backend_ready = getattr(app.state, 'ready', False)
    return {
        "status": "healthy",
        "service": "planner-agent",
        "version": "0.1.0",
        "backend_ready": backend_ready,
        "message": "Service is running" + (" and fully ready" if backend_ready else " but waiting for Backend API")
    }


@app.post("/execute")
async def execute_task(request: TaskRequest):
    """Execute a planning task"""
    logger.info("📥 Received task: {request.task_id} (type: {request.task_type})", module="PLANNER-AGENT", routine="execute_task()")
    
    try:
        agent = app.state.agent
        
        # Execute the task asynchronously and callback to dispatcher
        # Return immediate response to avoid timeout
        import asyncio
        asyncio.create_task(agent.execute_task_async(request))
        
        return {"status": "accepted", "task_id": request.task_id}
        
    except Exception as e:
        logger.error("❌ Failed to execute task: {e}", module="PLANNER-AGENT", routine="execute_task")
        raise HTTPException(status_code=500, detail=str(e))


async def main():
    """🛡️ BULLETPROOF Main startup function - GUARANTEED 100% UPTIME"""
    # Logger is already configured via unified_logging system

    # 🛡️ BULLETPROOF: Infinite retry loop - service NEVER exits
    retry_count = 0
    while True:
        try:
            retry_count += 1
            if retry_count > 1:
                logger.info(f"🔄 BULLETPROOF: Planner Agent startup attempt #{retry_count}", module="PLANNER-AGENT", routine="main")

            # Get configuration from environment
            server_host = os.getenv("PLANNER_AGENT_HOST", get_service_host("planner"))

            # Ensure port is free before starting
            logger.info("🔌 Ensuring planner agent port is free...", module="PLANNER-AGENT", routine="main")
            server_port = ensure_service_port_free("planner", force=True)
            logger.info(f"✅ Planner agent will use port {server_port}", module="PLANNER-AGENT", routine="main")

            debug = os.getenv("DEBUG", "false").lower() == "true"

            # Check if we should wait for backend readiness
            backend_ready = await ensure_backend_ready_before_startup("planner-agent")
            if not backend_ready:
                logger.warning("starting anyway to avoid hanging", module="PLANNER-AGENT", routine="⚠️ Backend readiness timeout")

            # Clean up port before starting
            logger.info(f"🧹 Cleaning up port {server_port} before starting...", module="PLANNER-AGENT", routine="main")
            cleanup_port(server_port)

            logger.info(f"🌟 Starting Planner Agent on {server_host}:{server_port} (debug={debug})", module="PLANNER-AGENT", routine="main")

            # AGGRESSIVELY suppress ALL HTTP request logging
            import logging

            # Disable ALL uvicorn logging completely
            for logger_name in ["uvicorn", "uvicorn.access", "uvicorn.error", "uvicorn.asgi"]:
                logger_obj = logging.getLogger(logger_name)
                logger_obj.setLevel(logging.CRITICAL)
                logger_obj.disabled = True
                logger_obj.propagate = False

            uvicorn.run(
                "agents.planner.app.main:app",
                host=server_host,
                port=server_port,
                reload=debug,
                log_level="error",    # Only show errors
                access_log=False,     # Disable access logging
                use_colors=False      # Disable colors for cleaner output
            )

            # If we reach here, uvicorn completed - THIS SHOULD NEVER HAPPEN!
            logger.error("💥 CRITICAL: Planner Agent uvicorn completed unexpectedly - servers should NEVER complete!", module="PLANNER-AGENT", routine="main")
            logger.error("💥 CRITICAL: This indicates a serious problem - restarting immediately", module="PLANNER-AGENT", routine="main")
            import time
            time.sleep(2)
            continue  # Restart the service immediately
        except OSError as e:
            if "Address already in use" in str(e):
                logger.error(f"❌ Port {server_port} is already in use. Another Planner Agent may be running.", module="PLANNER-AGENT", routine="main")
                logger.info("💡 Try stopping other DEEPLICA services first with '🛑 STOP DEEPLICA'", module="PLANNER-AGENT", routine="main")
                # 🛡️ BULLETPROOF: Never exit - try alternative port
                logger.info("🔄 BULLETPROOF: Will try alternative port - service NEVER crashes", module="PLANNER-AGENT", routine="main")
                try:
                    from shared.port_manager import DeepLicaPortManager
                    port_mgr = DeepLicaPortManager("planner-alt")
                    server_port = port_mgr.allocate_port("planner-alt")
                    logger.info(f"🔄 Using alternative port: {server_port}", module="PLANNER-AGENT", routine="main")
                except:
                    import time
                    time.sleep(10)
                    logger.info("🔄 Retrying after delay", module="PLANNER-AGENT", routine="main")
            else:
                logger.error(f"❌ Network error starting Planner Agent: {e}", module="PLANNER-AGENT", routine="main")
                # 🛡️ BULLETPROOF: Never exit - wait and retry
                logger.info("🔄 BULLETPROOF: Will retry after network error - service NEVER crashes", module="PLANNER-AGENT", routine="main")
                import time
                time.sleep(10)
            continue  # Retry the entire main loop
        except Exception as e:
            logger.error(f"❌ Unexpected error starting Planner Agent: {e}", module="PLANNER-AGENT", routine="main")
            # 🛡️ BULLETPROOF: Never exit - wait and retry
            logger.info("🔄 BULLETPROOF: Will retry after unexpected error - service NEVER crashes", module="PLANNER-AGENT", routine="main")
            import time
            time.sleep(10)
            continue  # Retry the entire main loop

    # 🛡️ BULLETPROOF: This line should NEVER be reached!
    logger.error("💥 CRITICAL: Planner Agent main function exited infinite loop - THIS SHOULD NEVER HAPPEN!", module="PLANNER-AGENT", routine="main")
    # No return - this should never be reached


if __name__ == "__main__":
    # Set distinctive process name for easy identification
    try:
        import setproctitle
        setproctitle.setproctitle("DEEPLICA-PLANNER-AGENT")
        logger.info("PLANNER-AGENT", module="PLANNER-AGENT:startup", routine="PROCESS | Process name set to: DEEPLICA")
    except ImportError:
        logger.warning("process name unchanged", module="PLANNER-AGENT:startup", routine="PROCESS | setproctitle not available")

    # Process detection removed per user request - manual management only

    # Set terminal title and clear identification banner - FORCE RENAME EVEN IF ALREADY NAMED
    service_name = os.getenv("SERVICE_NAME", "PLANNER-AGENT")

    # Multiple methods to ensure terminal gets renamed
    print(f"\033]0;🧠 {service_name}\007", end="")  # xterm title
    print(f"\033]2;🧠 {service_name}\007", end="")  # window title
    print(f"\033]1;🧠 {service_name}\007", end="")  # icon title

    # Also try VS Code specific terminal naming
    import sys
    if hasattr(sys, 'ps1') or hasattr(sys, 'ps2'):
        try:
            import os
            os.system(f'echo -ne "\\033]0;🧠 {service_name}\\007"')
        except:
            pass

    print("\n" + "="*80)
    print(f"🧠 {service_name} TERMINAL")
    print("="*80 + "\n")

    # Wrap main function with bulletproof wrapper to ensure it never crashes
    bulletproof_main = bulletproof_service_wrapper(main, "PLANNER-AGENT")
    exit_code = asyncio.run(bulletproof_main())

    # Check if running in debugger (VS Code)
    import sys
    if hasattr(sys, 'gettrace') and sys.gettrace() is not None:
        print(f"\n🔍 Running in debugger mode - exit code would be: {exit_code}")
        if exit_code != 0:
            print("💡 In production, this would exit with error code")
    # Don't exit in production - let the service restart itself
    # else:
    #     sys.exit(exit_code)  # REMOVED - services should never exit themselves
