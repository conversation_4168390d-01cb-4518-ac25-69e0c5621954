#!/usr/bin/env python3
"""
🖥️ DEEPLICA TERMINAL MANAGEMENT SYSTEM
Ensures each service has its own dedicated terminal/console with proper separation.
CLI service messages are redirected to Watchdog terminal as required.
"""

import os
import sys
import time
import threading
import requests
import logging
from datetime import datetime
from typing import Optional, Dict, Any
from pathlib import Path

# Add project root to path
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

from shared.port_manager import get_service_port


class TerminalManager:
    """
    🖥️ DEEPLICA Terminal Management System
    
    Ensures proper terminal separation:
    - Each service has its own terminal (handled by VS Code integratedTerminal)
    - CLI service messages go to Watchdog terminal
    - Proper terminal naming and identification
    - Terminal health monitoring
    """
    
    def __init__(self, service_name: str):
        self.service_name = service_name
        self.terminal_id = f"DEEPLICA-{service_name}"
        self.start_time = time.time()
        self.message_count = 0

        # Initialize terminal-specific logging first
        self._setup_terminal_logging()

        # Set process title for easy identification
        self._set_process_title()

        # Register terminal with system
        self._register_terminal()
    
    def _set_process_title(self):
        """Set distinctive process title for easy identification"""
        try:
            import setproctitle
            setproctitle.setproctitle(self.terminal_id)
            self.log_terminal_message("PROCESS", f"Process name set to: {self.terminal_id}")
        except ImportError:
            self.log_terminal_message("PROCESS", "setproctitle not available - process name unchanged")
    
    def _setup_terminal_logging(self):
        """Setup terminal-specific logging configuration"""
        # Configure logging for this terminal
        logging.basicConfig(
            level=logging.INFO,
            format=f'%(asctime)s - [%(levelname)s] - Svc: {self.service_name}, Terminal: {self.terminal_id}, msg: %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        self.logger = logging.getLogger(f"deeplica.terminal.{self.service_name.lower()}")
    
    def _register_terminal(self):
        """Register this terminal with the system"""
        self.log_terminal_message("STARTUP", f"🖥️ {self.service_name} TERMINAL")
        self.log_terminal_message("STARTUP", "=" * 80)
        self.log_terminal_message("SYSTEM", f"Terminal ID: {self.terminal_id}")
        self.log_terminal_message("SYSTEM", f"Service: {self.service_name}")
        self.log_terminal_message("SYSTEM", f"PID: {os.getpid()}")
        self.log_terminal_message("SYSTEM", f"Started: {datetime.fromtimestamp(self.start_time).strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Special handling for CLI service
        if self.service_name == "CLI-TERMINAL":
            self.log_terminal_message("SYSTEM", "🔄 CLI messages will be redirected to Watchdog terminal")
            self.log_terminal_message("SYSTEM", "💡 This terminal shows only CLI startup and critical messages")
    
    def log_terminal_message(self, category: str, message: str, level: str = "INFO"):
        """
        Log message to this terminal's console
        
        Args:
            category: Message category (STARTUP, SYSTEM, ERROR, etc.)
            message: The message to log
            level: Log level (INFO, WARNING, ERROR, DEBUG)
        """
        self.message_count += 1
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Format message with unified logging format
        formatted_message = f"{timestamp} - [{level}] - Svc: {self.service_name}, Mod: {category}, Cod: log_terminal_message, msg: {message}"
        
        # Print to this terminal's console
        print(formatted_message)
        
        # Also log through Python logging system
        log_level = getattr(logging, level.upper(), logging.INFO)
        self.logger.log(log_level, f"[{category}] {message}")
    
    def log_startup_complete(self, port: Optional[int] = None, additional_info: Optional[Dict[str, Any]] = None):
        """Log that service startup is complete"""
        self.log_terminal_message("STARTUP", f"✅ {self.service_name} startup complete")
        
        if port:
            self.log_terminal_message("SYSTEM", f"🌐 Service running on port {port}")
        
        if additional_info:
            for key, value in additional_info.items():
                self.log_terminal_message("SYSTEM", f"📋 {key}: {value}")
        
        uptime = time.time() - self.start_time
        self.log_terminal_message("SYSTEM", f"⏱️ Startup time: {uptime:.2f} seconds")
        self.log_terminal_message("SYSTEM", f"💪 Service is now operational and bulletproof")
    
    def log_health_status(self, status: str, details: Optional[Dict[str, Any]] = None):
        """Log health status information"""
        uptime = time.time() - self.start_time
        self.log_terminal_message("HEALTH", f"Status: {status}, Uptime: {uptime:.1f}s, Messages: {self.message_count}")
        
        if details:
            for key, value in details.items():
                self.log_terminal_message("HEALTH", f"{key}: {value}")
    
    def log_error(self, error_type: str, error_message: str, recovery_action: Optional[str] = None):
        """Log error with recovery information"""
        self.log_terminal_message("ERROR", f"💥 {error_type}: {error_message}", "ERROR")
        
        if recovery_action:
            self.log_terminal_message("RECOVERY", f"🔧 Recovery action: {recovery_action}", "WARNING")
    
    def log_bulletproof_action(self, action: str, reason: str):
        """Log bulletproof recovery actions"""
        self.log_terminal_message("BULLETPROOF", f"🛡️ {action} - Reason: {reason}", "WARNING")
    
    def get_terminal_stats(self) -> Dict[str, Any]:
        """Get terminal statistics"""
        uptime = time.time() - self.start_time
        return {
            "terminal_id": self.terminal_id,
            "service_name": self.service_name,
            "pid": os.getpid(),
            "uptime_seconds": uptime,
            "message_count": self.message_count,
            "start_time": self.start_time
        }


class CLIWatchdogRedirector:
    """
    🔄 CLI to Watchdog Message Redirector
    
    Handles redirecting CLI service messages to the Watchdog terminal
    as required by the user specifications.
    """
    
    def __init__(self):
        self.watchdog_url = f"http://localhost:{get_service_port('watchdog')}"
        self.service_name = "CLI-TERMINAL"
        self.watchdog_available = False
        self.message_buffer = []
        self.max_buffer_size = 1000
        self.buffer_lock = threading.Lock()
        
        # Start watchdog monitoring
        self._start_watchdog_monitor()
    
    def _start_watchdog_monitor(self):
        """Monitor watchdog availability"""
        def monitor():
            while True:
                try:
                    self._check_watchdog_availability()
                    time.sleep(10)  # Check every 10 seconds
                except:
                    time.sleep(30)  # Wait longer on error
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
    
    def _check_watchdog_availability(self):
        """Check if watchdog is available"""
        try:
            response = requests.get(f"{self.watchdog_url}/health", timeout=2)
            was_available = self.watchdog_available
            self.watchdog_available = response.status_code == 200
            
            if self.watchdog_available and not was_available:
                self._flush_message_buffer()
        except:
            self.watchdog_available = False
    
    def _flush_message_buffer(self):
        """Send buffered messages to watchdog"""
        try:
            with self.buffer_lock:
                for message in self.message_buffer:
                    self._send_to_watchdog_direct(message)
                self.message_buffer.clear()
        except:
            pass
    
    def _send_to_watchdog_direct(self, message_data: Dict[str, Any]):
        """Send message directly to watchdog"""
        try:
            requests.post(f"{self.watchdog_url}/log", json=message_data, timeout=1)
        except:
            pass
    
    def send_cli_message_to_watchdog(self, level: str, category: str, message: str, data: Optional[Dict[str, Any]] = None):
        """
        Send CLI message to Watchdog terminal instead of CLI terminal
        
        Args:
            level: Log level (INFO, WARNING, ERROR, DEBUG)
            category: Message category
            message: The message content
            data: Additional data
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        message_data = {
            "service": self.service_name,
            "level": level,
            "category": category,
            "message": message,
            "timestamp": timestamp,
            "data": data or {},
            "unified_message": f"{timestamp} - [{level}] - Svc: {self.service_name}, Mod: {category}, Cod: CLI-REDIRECT, msg: {message}"
        }
        
        if self.watchdog_available:
            self._send_to_watchdog_direct(message_data)
        else:
            # Buffer message for later delivery
            with self.buffer_lock:
                self.message_buffer.append(message_data)
                if len(self.message_buffer) > self.max_buffer_size:
                    self.message_buffer = self.message_buffer[-self.max_buffer_size//2:]


# Global instances
_terminal_managers: Dict[str, TerminalManager] = {}
_cli_redirector: Optional[CLIWatchdogRedirector] = None


def get_terminal_manager(service_name: str) -> TerminalManager:
    """Get or create terminal manager for a service"""
    if service_name not in _terminal_managers:
        _terminal_managers[service_name] = TerminalManager(service_name)
    return _terminal_managers[service_name]


def get_cli_redirector() -> CLIWatchdogRedirector:
    """Get CLI to Watchdog redirector"""
    global _cli_redirector
    if _cli_redirector is None:
        _cli_redirector = CLIWatchdogRedirector()
    return _cli_redirector


def log_to_service_terminal(service_name: str, category: str, message: str, level: str = "INFO"):
    """Log message to specific service terminal"""
    terminal_manager = get_terminal_manager(service_name)
    terminal_manager.log_terminal_message(category, message, level)


def log_cli_to_watchdog(category: str, message: str, level: str = "INFO", data: Optional[Dict[str, Any]] = None):
    """Log CLI message to Watchdog terminal (as required)"""
    cli_redirector = get_cli_redirector()
    cli_redirector.send_cli_message_to_watchdog(level, category, message, data)


def get_all_terminal_stats() -> Dict[str, Dict[str, Any]]:
    """Get statistics for all active terminals"""
    return {name: manager.get_terminal_stats() for name, manager in _terminal_managers.items()}
