<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>🔐 Secure Login - Strict Tab Session</title>
    
    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.1);
            -webkit-backdrop-filter: blur(10px); /* Safari support */
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 40px;
            max-width: 450px;
            width: 100%;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }
        
        .security-badge {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .security-badge h1 {
            color: #00d4ff;
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .security-badge p {
            color: #888;
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #00d4ff;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 15px;
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.3);
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #00d4ff;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
        }
        
        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            border: none;
            border-radius: 10px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 212, 255, 0.4);
        }
        
        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .security-info {
            margin-top: 30px;
            padding: 20px;
            background: rgba(255, 71, 87, 0.1);
            border: 1px solid rgba(255, 71, 87, 0.3);
            border-radius: 10px;
        }
        
        .security-info h3 {
            color: #ff4757;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .security-info ul {
            list-style: none;
            font-size: 14px;
            color: #ccc;
        }
        
        .security-info li {
            margin-bottom: 5px;
            padding-left: 20px;
            position: relative;
        }
        
        .security-info li:before {
            content: "🔒";
            position: absolute;
            left: 0;
        }
        
        .tab-id-display {
            margin-top: 20px;
            padding: 15px;
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #00d4ff;
            word-break: break-all;
        }
        
        .error-message {
            background: rgba(255, 71, 87, 0.2);
            border: 1px solid #ff4757;
            color: #ff4757;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: none;
        }
        
        .success-message {
            background: rgba(46, 204, 113, 0.2);
            border: 1px solid #2ecc71;
            color: #2ecc71;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: none;
        }
        
        .demo-credentials {
            margin-top: 20px;
            padding: 15px;
            background: rgba(243, 156, 18, 0.1);
            border: 1px solid rgba(243, 156, 18, 0.3);
            border-radius: 10px;
            font-size: 14px;
            color: #f39c12;
        }
        
        .demo-credentials h4 {
            margin-bottom: 10px;
        }
        
        .demo-credentials code {
            background: rgba(0, 0, 0, 0.3);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="security-badge">
            <h1>🔐 Secure Login</h1>
            <p>Strict Single-Tab Session Enforcement</p>
        </div>
        
        <div id="errorMessage" class="error-message"></div>
        <div id="successMessage" class="success-message"></div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" required autocomplete="username">
            </div>
            
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required autocomplete="current-password">
            </div>
            
            <input type="hidden" id="csrfToken" name="csrf_token" value="{{ csrf_token }}">
            
            <button type="submit" class="login-btn" id="loginBtn">
                🔐 Secure Login
            </button>
        </form>
        
        <div class="demo-credentials">
            <h4>📋 Demo Credentials:</h4>
            <p><strong>Admin:</strong> <code>admin</code> / <code>admin123</code></p>
            <p><strong>User:</strong> <code>demo</code> / <code>demo123</code></p>
        </div>
        
        <div class="tab-id-display">
            <strong>🔑 Tab ID:</strong> <span id="tabIdDisplay">Generating...</span>
        </div>
        
        <div class="security-info">
            <h3>🛡️ Security Features</h3>
            <ul>
                <li>Cryptographically secure tab identification</li>
                <li>Single-tab session enforcement</li>
                <li>Automatic multi-tab detection</li>
                <li>CSRF protection enabled</li>
                <li>Rate limiting active</li>
                <li>Secure cookie handling</li>
            </ul>
        </div>
    </div>
    
    <!-- Load Tab Security Manager -->
    <script src="/static/tab_security.js"></script>
    
    <script>
        // Wait for tab security manager to initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Wait a moment for the tab security manager to initialize
            setTimeout(function() {
                const tabManager = window.getStrictTabSessionManager();
                if (tabManager && tabManager.isInitialized) {
                    document.getElementById('tabIdDisplay').textContent = 
                        tabManager.getTabId().substring(0, 16) + '...';
                } else {
                    document.getElementById('tabIdDisplay').textContent = 'Initialization failed';
                }
            }, 500);
            
            // Handle login form submission
            document.getElementById('loginForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const loginBtn = document.getElementById('loginBtn');
                const errorDiv = document.getElementById('errorMessage');
                const successDiv = document.getElementById('successMessage');
                
                // Hide previous messages
                errorDiv.style.display = 'none';
                successDiv.style.display = 'none';
                
                // Disable button
                loginBtn.disabled = true;
                loginBtn.textContent = '🔄 Authenticating...';
                
                try {
                    const tabManager = window.getStrictTabSessionManager();
                    if (!tabManager || !tabManager.isInitialized) {
                        throw new Error('Tab security manager not initialized');
                    }
                    
                    const formData = new FormData(this);
                    
                    const response = await fetch('/login', {
                        method: 'POST',
                        headers: {
                            'X-Tab-ID': tabManager.getTabId(),
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: formData,
                        credentials: 'same-origin'
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        // Set session token in tab manager
                        tabManager.setSessionToken(result.session_token);
                        
                        // Show success message
                        successDiv.textContent = '✅ Login successful! Redirecting...';
                        successDiv.style.display = 'block';
                        
                        // Redirect to dashboard
                        setTimeout(() => {
                            window.location.href = result.redirect || '/dashboard';
                        }, 1000);
                        
                    } else {
                        // Show error message
                        errorDiv.textContent = '❌ ' + (result.message || 'Login failed');
                        errorDiv.style.display = 'block';
                        
                        if (result.security_violation) {
                            errorDiv.innerHTML = '🚨 <strong>SECURITY VIOLATION:</strong> ' + result.message;
                        }
                    }
                    
                } catch (error) {
                    console.error('Login error:', error);
                    errorDiv.textContent = '❌ Login failed: ' + error.message;
                    errorDiv.style.display = 'block';
                } finally {
                    // Re-enable button
                    loginBtn.disabled = false;
                    loginBtn.textContent = '🔐 Secure Login';
                }
            });
        });
        
        // Prevent form submission if tab manager not ready
        window.addEventListener('beforeunload', function() {
            const tabManager = window.getStrictTabSessionManager();
            if (tabManager) {
                tabManager.destroy();
            }
        });
    </script>
</body>
</html>
