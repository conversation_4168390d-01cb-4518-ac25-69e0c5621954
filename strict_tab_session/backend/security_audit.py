"""
🔍 SECURITY AUDIT LOGGING SYSTEM - Comprehensive Security Event Tracking

This module implements comprehensive security audit logging for the strict
single-tab session enforcement system with detailed event tracking and analysis.

Security Features:
- Comprehensive event logging
- Security violation tracking
- Audit trail maintenance
- Real-time security monitoring
- Threat pattern analysis
- Compliance reporting

<AUTHOR> Security Team
@version 1.0.0
@date 2025-07-11
"""

import json
import time
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import logging

# Configure audit logger
audit_logger = logging.getLogger('SecurityAudit')
audit_logger.setLevel(logging.INFO)

# Create file handler for audit logs
audit_handler = logging.FileHandler('security_audit.log')
audit_formatter = logging.Formatter(
    '%(asctime)s - [AUDIT] - %(levelname)s - %(message)s'
)
audit_handler.setFormatter(audit_formatter)
audit_logger.addHandler(audit_handler)

class SecurityEventType(Enum):
    """Security event types for categorization"""
    SESSION_CREATED = "session_created"
    SESSION_VALIDATED = "session_validated"
    SESSION_INVALIDATED = "session_invalidated"
    MULTI_TAB_ATTEMPT = "multi_tab_attempt"
    TAB_MISMATCH = "tab_mismatch"
    INVALID_TAB_ID = "invalid_tab_id"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    CSRF_VIOLATION = "csrf_violation"
    AUTHENTICATION_FAILURE = "authentication_failure"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    CHALLENGE_FAILED = "challenge_failed"
    BROADCAST_VIOLATION = "broadcast_violation"
    SESSION_HIJACK_ATTEMPT = "session_hijack_attempt"

class SecuritySeverity(Enum):
    """Security event severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class SecurityEvent:
    """Represents a security event for audit logging"""
    event_id: str
    event_type: SecurityEventType
    severity: SecuritySeverity
    timestamp: datetime
    user_id: Optional[str]
    username: Optional[str]
    tab_id: Optional[str]
    session_token: Optional[str]
    ip_address: str
    user_agent: str
    details: Dict[str, Any]
    risk_score: int
    action_taken: str
    
class SecurityAuditLogger:
    """
    🔍 Comprehensive Security Audit Logging System
    
    Tracks all security events, violations, and suspicious activities
    with detailed analysis and threat pattern detection.
    """
    
    def __init__(self, storage_backend=None):
        """
        Initialize the security audit logger
        
        Args:
            storage_backend: Optional storage backend (Redis, database, etc.)
        """
        self.storage_backend = storage_backend
        self.event_cache = []
        self.threat_patterns = {}
        self.risk_thresholds = {
            SecuritySeverity.LOW: 10,
            SecuritySeverity.MEDIUM: 25,
            SecuritySeverity.HIGH: 50,
            SecuritySeverity.CRITICAL: 100
        }
        
        audit_logger.info("🔍 Security Audit Logger initialized")

    def log_security_event(self, event_type: SecurityEventType, severity: SecuritySeverity,
                          user_id: str = None, username: str = None, tab_id: str = None,
                          session_token: str = None, ip_address: str = None,
                          user_agent: str = None, details: Dict[str, Any] = None,
                          action_taken: str = None) -> str:
        """
        Log a security event with comprehensive details
        
        Returns:
            Event ID for tracking
        """
        event_id = self._generate_event_id()
        risk_score = self._calculate_risk_score(event_type, severity, details or {})
        
        # Sanitize sensitive data
        safe_session_token = session_token[:8] + '...' if session_token else None
        safe_tab_id = tab_id[:8] + '...' if tab_id else None
        
        event = SecurityEvent(
            event_id=event_id,
            event_type=event_type,
            severity=severity,
            timestamp=datetime.now(),
            user_id=user_id,
            username=username,
            tab_id=safe_tab_id,
            session_token=safe_session_token,
            ip_address=ip_address or 'unknown',
            user_agent=user_agent or 'unknown',
            details=details or {},
            risk_score=risk_score,
            action_taken=action_taken or 'logged'
        )
        
        # Store event
        self._store_event(event)
        
        # Log to file
        audit_logger.info(f"SECURITY_EVENT: {json.dumps(asdict(event), default=str)}")
        
        # Check for threat patterns
        self._analyze_threat_patterns(event)
        
        # Alert on high-risk events
        if severity in [SecuritySeverity.HIGH, SecuritySeverity.CRITICAL]:
            self._send_security_alert(event)
        
        return event_id

    def log_multi_tab_attempt(self, user_id: str, username: str, tab_id: str,
                             ip_address: str, user_agent: str, existing_sessions: int):
        """Log multi-tab session attempt"""
        return self.log_security_event(
            event_type=SecurityEventType.MULTI_TAB_ATTEMPT,
            severity=SecuritySeverity.HIGH,
            user_id=user_id,
            username=username,
            tab_id=tab_id,
            ip_address=ip_address,
            user_agent=user_agent,
            details={
                'existing_sessions': existing_sessions,
                'violation_type': 'multi_tab_session'
            },
            action_taken='session_rejected'
        )

    def log_tab_mismatch(self, expected_tab_id: str, received_tab_id: str,
                        session_token: str, ip_address: str, user_agent: str):
        """Log tab ID mismatch violation"""
        return self.log_security_event(
            event_type=SecurityEventType.TAB_MISMATCH,
            severity=SecuritySeverity.CRITICAL,
            tab_id=received_tab_id,
            session_token=session_token,
            ip_address=ip_address,
            user_agent=user_agent,
            details={
                'expected_tab_id': expected_tab_id[:8] + '...',
                'received_tab_id': received_tab_id[:8] + '...',
                'violation_type': 'tab_id_mismatch'
            },
            action_taken='session_invalidated'
        )

    def log_invalid_tab_id(self, invalid_tab_id: str, ip_address: str, user_agent: str):
        """Log invalid tab ID format"""
        return self.log_security_event(
            event_type=SecurityEventType.INVALID_TAB_ID,
            severity=SecuritySeverity.MEDIUM,
            ip_address=ip_address,
            user_agent=user_agent,
            details={
                'invalid_tab_id': invalid_tab_id[:16] + '...' if len(invalid_tab_id) > 16 else invalid_tab_id,
                'violation_type': 'invalid_format'
            },
            action_taken='request_rejected'
        )

    def log_rate_limit_exceeded(self, identifier: str, request_count: int,
                               window_seconds: int, ip_address: str):
        """Log rate limit violation"""
        return self.log_security_event(
            event_type=SecurityEventType.RATE_LIMIT_EXCEEDED,
            severity=SecuritySeverity.MEDIUM,
            ip_address=ip_address,
            details={
                'identifier': identifier,
                'request_count': request_count,
                'window_seconds': window_seconds,
                'violation_type': 'rate_limit'
            },
            action_taken='requests_blocked'
        )

    def log_session_created(self, user_id: str, username: str, tab_id: str,
                           session_token: str, ip_address: str, user_agent: str):
        """Log successful session creation"""
        return self.log_security_event(
            event_type=SecurityEventType.SESSION_CREATED,
            severity=SecuritySeverity.LOW,
            user_id=user_id,
            username=username,
            tab_id=tab_id,
            session_token=session_token,
            ip_address=ip_address,
            user_agent=user_agent,
            details={
                'event_type': 'session_creation',
                'success': True
            },
            action_taken='session_created'
        )

    def log_session_invalidated(self, user_id: str, username: str, tab_id: str,
                               session_token: str, reason: str):
        """Log session invalidation"""
        severity = SecuritySeverity.HIGH if 'violation' in reason.lower() else SecuritySeverity.LOW
        
        return self.log_security_event(
            event_type=SecurityEventType.SESSION_INVALIDATED,
            severity=severity,
            user_id=user_id,
            username=username,
            tab_id=tab_id,
            session_token=session_token,
            details={
                'invalidation_reason': reason,
                'event_type': 'session_invalidation'
            },
            action_taken='session_invalidated'
        )

    def get_security_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get security summary for the specified time period"""
        since = datetime.now() - timedelta(hours=hours)
        events = self._get_events_since(since)
        
        summary = {
            'time_period_hours': hours,
            'total_events': len(events),
            'events_by_type': {},
            'events_by_severity': {},
            'high_risk_events': [],
            'threat_patterns': self.threat_patterns,
            'top_violating_ips': {},
            'security_score': 100  # Start with perfect score
        }
        
        # Analyze events
        for event in events:
            # Count by type
            event_type = event.event_type.value
            summary['events_by_type'][event_type] = summary['events_by_type'].get(event_type, 0) + 1
            
            # Count by severity
            severity = event.severity.value
            summary['events_by_severity'][severity] = summary['events_by_severity'].get(severity, 0) + 1
            
            # Track high-risk events
            if event.severity in [SecuritySeverity.HIGH, SecuritySeverity.CRITICAL]:
                summary['high_risk_events'].append({
                    'event_id': event.event_id,
                    'type': event_type,
                    'severity': severity,
                    'timestamp': event.timestamp.isoformat(),
                    'risk_score': event.risk_score
                })
            
            # Track violating IPs
            if event.ip_address:
                summary['top_violating_ips'][event.ip_address] = \
                    summary['top_violating_ips'].get(event.ip_address, 0) + event.risk_score
            
            # Reduce security score based on risk
            summary['security_score'] -= event.risk_score * 0.1
        
        # Ensure security score doesn't go below 0
        summary['security_score'] = max(0, summary['security_score'])
        
        return summary

    def _generate_event_id(self) -> str:
        """Generate unique event ID"""
        timestamp = str(int(time.time() * 1000000))
        random_data = str(hash(timestamp + str(time.time())))
        return hashlib.sha256((timestamp + random_data).encode()).hexdigest()[:16]

    def _calculate_risk_score(self, event_type: SecurityEventType, 
                             severity: SecuritySeverity, details: Dict[str, Any]) -> int:
        """Calculate risk score for an event"""
        base_score = self.risk_thresholds[severity]
        
        # Adjust based on event type
        if event_type in [SecurityEventType.MULTI_TAB_ATTEMPT, SecurityEventType.TAB_MISMATCH]:
            base_score *= 1.5
        elif event_type == SecurityEventType.SESSION_HIJACK_ATTEMPT:
            base_score *= 2.0
        
        return int(base_score)

    def _store_event(self, event: SecurityEvent):
        """Store event in cache and backend"""
        self.event_cache.append(event)
        
        # Keep cache size manageable
        if len(self.event_cache) > 1000:
            self.event_cache = self.event_cache[-500:]
        
        # Store in backend if available
        if self.storage_backend:
            try:
                self.storage_backend.store_audit_event(asdict(event))
            except Exception as e:
                audit_logger.error(f"Failed to store event in backend: {e}")

    def _get_events_since(self, since: datetime) -> List[SecurityEvent]:
        """Get events since specified time"""
        return [event for event in self.event_cache if event.timestamp >= since]

    def _analyze_threat_patterns(self, event: SecurityEvent):
        """Analyze for threat patterns"""
        # Track patterns by IP address
        ip_key = f"ip_{event.ip_address}"
        if ip_key not in self.threat_patterns:
            self.threat_patterns[ip_key] = {'count': 0, 'risk_score': 0, 'last_seen': None}
        
        self.threat_patterns[ip_key]['count'] += 1
        self.threat_patterns[ip_key]['risk_score'] += event.risk_score
        self.threat_patterns[ip_key]['last_seen'] = event.timestamp.isoformat()

    def _send_security_alert(self, event: SecurityEvent):
        """Send security alert for high-risk events"""
        alert_message = f"🚨 HIGH-RISK SECURITY EVENT: {event.event_type.value}"
        audit_logger.critical(f"SECURITY_ALERT: {alert_message} - Event ID: {event.event_id}")
        
        # In production, integrate with alerting systems (email, Slack, etc.)
        print(f"🚨 SECURITY ALERT: {alert_message}")

# Global audit logger instance
security_audit = SecurityAuditLogger()
