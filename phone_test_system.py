#!/usr/bin/env python3
"""
🔧 COMPREHENSIVE PHONE CALL TEST SYSTEM
Tests phone calls until they work perfectly without error messages.
"""

import asyncio
import httpx
import json
import time
import subprocess
import os
from datetime import datetime
from shared.port_manager import get_service_port

class PhoneCallTester:
    """Comprehensive phone call testing system"""
    
    def __init__(self):
        self.phone_number = "+972547000430"
        self.test_message = "Hello Eran, how are you?"
        from shared.port_manager import get_service_port, get_service_host
        self.backend_url = f"http://{get_service_host('backend')}:{get_service_port('backend')}"
        self.phone_url = f"http://localhost:{get_service_port('phone')}"
        self.ngrok_api = f"http://localhost:{get_service_port('ngrok-api')}/api/tunnels"
        self.test_count = 0
        
    async def run_comprehensive_test(self):
        """Run comprehensive phone call test until it works"""
        print("🔧 COMPREHENSIVE PHONE CALL TEST SYSTEM")
        print("=" * 60)
        print(f"📞 Target: {self.phone_number}")
        print(f"💬 Message: {self.test_message}")
        print("=" * 60)
        
        while True:
            self.test_count += 1
            print(f"\n🧪 TEST #{self.test_count} - {datetime.now().strftime('%H:%M:%S')}")
            
            # Step 1: Check all services
            if not await self.check_all_services():
                print("❌ Services not ready - fixing...")
                await self.fix_services()
                continue
                
            # Step 2: Check ngrok tunnel
            tunnel_url = await self.check_ngrok_tunnel()
            if not tunnel_url:
                print("❌ ngrok tunnel not ready - fixing...")
                await self.fix_ngrok()
                continue
                
            # Step 3: Update webhook URL
            if not await self.update_webhook_url(tunnel_url):
                print("❌ Webhook URL update failed - fixing...")
                continue
                
            # Step 4: Test webhook accessibility
            if not await self.test_webhook_accessibility(tunnel_url):
                print("❌ Webhook not accessible - fixing...")
                continue
                
            # Step 5: Clear any old call states
            await self.clear_old_calls()
                
            # Step 6: Make test call
            call_result = await self.make_test_call()
            if call_result:
                print("✅ Test call initiated successfully!")
                print(f"📞 CALLING {self.phone_number} NOW...")
                print(f"💬 Expected message: '{self.test_message}'")
                print("\n🎯 PLEASE ANSWER THE PHONE AND CONFIRM:")
                print("   1. You hear the message clearly")
                print("   2. NO error messages are spoken")
                print("   3. The call works properly")
                print("\n⌨️  Type 'OK' when the call works correctly, or 'FAIL' to try again:")
                
                # Wait for user confirmation
                user_input = input().strip().upper()
                if user_input == 'OK':
                    print("🎉 SUCCESS! Phone call system is working correctly!")
                    return True
                else:
                    print("🔄 User reported failure - analyzing and fixing...")
                    await self.analyze_failure()
            else:
                print("❌ Test call failed - analyzing...")
                await self.analyze_failure()
                
            print("⏳ Waiting 10 seconds before next test...")
            await asyncio.sleep(10)
    
    async def check_all_services(self):
        """Check if all required services are running"""
        services = [
            ("Backend API", f"http://{get_service_host('backend')}:{get_service_port('backend')}/health"),
            ("Phone Agent", f"http://localhost:{get_service_port('phone')}/health"),
            ("Dispatcher", f"http://localhost:{get_service_port('dispatcher')}/health")
        ]
        
        all_good = True
        for name, url in services:
            try:
                async with httpx.AsyncClient(timeout=5.0) as client:
                    response = await client.get(url)
                    if response.status_code == 200:
                        print(f"✅ {name}: Ready")
                    else:
                        print(f"❌ {name}: HTTP {response.status_code}")
                        all_good = False
            except Exception as e:
                print(f"❌ {name}: {e}")
                all_good = False
                
        return all_good
    
    async def check_ngrok_tunnel(self):
        """Check ngrok tunnel and return URL"""
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(self.ngrok_api)
                if response.status_code == 200:
                    data = response.json()
                    tunnels = data.get('tunnels', [])
                    for tunnel in tunnels:
                        if tunnel.get('proto') == 'https':
                            url = tunnel.get('public_url')
                            print(f"✅ ngrok tunnel: {url}")
                            return url
                    print("❌ No HTTPS tunnel found")
                    return None
                else:
                    print(f"❌ ngrok API: HTTP {response.status_code}")
                    return None
        except Exception as e:
            print(f"❌ ngrok check failed: {e}")
            return None
    
    async def update_webhook_url(self, tunnel_url):
        """Update webhook URL in phone service"""
        try:
            # Update .env file
            env_path = ".env"
            if os.path.exists(env_path):
                with open(env_path, 'r') as f:
                    lines = f.readlines()
                
                updated = False
                for i, line in enumerate(lines):
                    if line.startswith('TWILIO_WEBHOOK_URL='):
                        lines[i] = f'TWILIO_WEBHOOK_URL={tunnel_url}\n'
                        updated = True
                        break
                
                if not updated:
                    lines.append(f'TWILIO_WEBHOOK_URL={tunnel_url}\n')
                
                with open(env_path, 'w') as f:
                    f.writelines(lines)
                
                print(f"✅ Updated .env with webhook URL")
            
            # Reload phone service config
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.post(f"http://localhost:{get_service_port('phone')}/reload_config")
                if response.status_code == 200:
                    print("✅ Phone service config reloaded")
                    return True
                else:
                    print(f"❌ Config reload failed: HTTP {response.status_code}")
                    return False
                    
        except Exception as e:
            print(f"❌ Webhook URL update failed: {e}")
            return False
    
    async def test_webhook_accessibility(self, tunnel_url):
        """Test if webhook is accessible through tunnel"""
        try:
            test_url = f"{tunnel_url}/health"
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(test_url)
                if response.status_code == 200:
                    print("✅ Webhook accessible through tunnel")
                    return True
                else:
                    print(f"❌ Webhook test failed: HTTP {response.status_code}")
                    return False
        except Exception as e:
            print(f"❌ Webhook accessibility test failed: {e}")
            return False
    
    async def clear_old_calls(self):
        """Clear any old call states"""
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.post(f"http://localhost:{get_service_port('phone')}/emergency_stop", json={})
                if response.status_code == 200:
                    print("✅ Cleared old call states")
                else:
                    print(f"⚠️ Emergency stop returned: {response.status_code}")
        except Exception as e:
            print(f"⚠️ Could not clear old calls: {e}")
    
    async def make_test_call(self):
        """Make the actual test call"""
        try:
            task_data = {
                "task_id": f"test_call_{int(time.time())}",
                "mission_id": f"test_mission_{int(time.time())}",
                "task_type": "phone_call",
                "task_data": {
                    "phone_number": self.phone_number,
                    "contact_name": "Eran",
                    "context": "This is a test call to verify the system works without error messages",
                    "question": self.test_message
                },
                "mission_context": {
                    "mission_id": f"test_mission_{int(time.time())}",
                    "user_input": "Test phone call",
                    "title": "Phone Test",
                    "description": "Testing phone system",
                    "priority": "normal",
                    "status": "in_progress",
                    "created_at": datetime.now().isoformat() + "Z"
                },
                "callback_url": f"http://localhost:{get_service_port('dispatcher')}/task_callback"
            }
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.post(
                    f"http://localhost:{get_service_port('phone')}/execute",
                    json=task_data,
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ Call request accepted: {result}")
                    return True
                else:
                    print(f"❌ Call request failed: HTTP {response.status_code}")
                    print(f"Response: {response.text}")
                    return False
                    
        except Exception as e:
            print(f"❌ Test call failed: {e}")
            return False
    
    async def fix_services(self):
        """Attempt to fix service issues"""
        print("🔧 Attempting to fix services...")
        await asyncio.sleep(5)
    
    async def fix_ngrok(self):
        """Attempt to fix ngrok tunnel"""
        print("🔧 Attempting to fix ngrok tunnel...")
        try:
            # Kill existing ngrok
            subprocess.run(["pkill", "-f", "ngrok"], capture_output=True)
            await asyncio.sleep(2)
            
            # Start new ngrok
            subprocess.Popen(["ngrok", "http", str(get_service_port("phone"))], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            await asyncio.sleep(8)
            print("✅ ngrok restart attempted")
        except Exception as e:
            print(f"❌ ngrok fix failed: {e}")
    
    async def analyze_failure(self):
        """Analyze what went wrong"""
        print("🔍 Analyzing failure...")
        await asyncio.sleep(2)

async def main():
    """Main test function"""
    tester = PhoneCallTester()
    await tester.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
