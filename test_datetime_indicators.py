#!/usr/bin/env python3
"""
🕒 DATE/TIME INDICATORS TEST

Tests that the date/time and connection status indicators work properly.
"""

import requests
import time
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

from shared.port_manager import get_service_port

def test_datetime_elements_present():
    """Test that date/time elements are present in the HTML"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🕒 Testing date/time elements on port {web_chat_port}...")
        
        # Login and get chat page
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        
        print("📝 Logging in...")
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
        
        print("💬 Getting chat page...")
        chat_response = session.get(
            f"http://localhost:{web_chat_port}/chat",
            allow_redirects=True,
            timeout=10
        )
        
        if chat_response.status_code != 200:
            print(f"❌ Chat access failed: {chat_response.status_code}")
            return False
        
        content = chat_response.text
        
        # Check for date/time elements
        datetime_elements = [
            'id="chatDate"',
            'id="chatTime"',
            'id="connectionStatus"',
            'id="chatDateTime"',
            'chat-datetime-display',
            'initializeChatDateTime',
            'updateChatDateTime'
        ]
        
        missing_elements = []
        for element in datetime_elements:
            if element not in content:
                missing_elements.append(element)
        
        if missing_elements:
            print(f"❌ Missing date/time elements: {missing_elements}")
            return False
        
        print(f"✅ All date/time elements found in HTML")
        
        # Check for connection status elements
        connection_elements = [
            'connection-status',
            'updateConnectionStatus',
            'Connecting...',
            'Connected',
            'Disconnected'
        ]
        
        missing_connection = []
        for element in connection_elements:
            if element not in content:
                missing_connection.append(element)
        
        if missing_connection:
            print(f"❌ Missing connection elements: {missing_connection}")
            return False
        
        print(f"✅ All connection status elements found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing date/time elements: {e}")
        return False

def test_datetime_javascript_functions():
    """Test that date/time JavaScript functions are present"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔧 Testing date/time JavaScript functions on port {web_chat_port}...")
        
        # Login and get chat page
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login failed for JS test")
            return False
        
        chat_response = session.get(
            f"http://localhost:{web_chat_port}/chat",
            allow_redirects=True,
            timeout=10
        )
        
        if chat_response.status_code != 200:
            print(f"❌ Chat access failed for JS test")
            return False
        
        content = chat_response.text
        
        # Check for JavaScript functions
        js_functions = [
            'function initializeChatDateTime()',
            'function updateChatDateTime()',
            'function updateConnectionStatus(',
            'setInterval(updateChatDateTime, 1000)',
            'getElementById(\'chatDate\')',
            'getElementById(\'chatTime\')',
            'getElementById(\'connectionStatus\')',
            'toLocaleDateString',
            'toLocaleTimeString'
        ]
        
        missing_functions = []
        for func in js_functions:
            if func not in content:
                missing_functions.append(func)
        
        if missing_functions:
            print(f"❌ Missing JavaScript functions: {missing_functions}")
            return False
        
        print(f"✅ All date/time JavaScript functions found")
        
        # Check for debugging code
        debug_elements = [
            'console.log(\'🕒',
            'Date/time elements found',
            'Date/time updated:',
            'Updating connection status'
        ]
        
        found_debug = []
        for debug in debug_elements:
            if debug in content:
                found_debug.append(debug)
        
        if len(found_debug) >= 2:
            print(f"✅ Debugging code found: {found_debug}")
        else:
            print(f"⚠️ Limited debugging code found: {found_debug}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing JavaScript functions: {e}")
        return False

def test_chat_initialization_order():
    """Test that chat initialization includes date/time setup"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🚀 Testing chat initialization order on port {web_chat_port}...")
        
        # Login and get chat page
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login failed for initialization test")
            return False
        
        chat_response = session.get(
            f"http://localhost:{web_chat_port}/chat",
            allow_redirects=True,
            timeout=10
        )
        
        if chat_response.status_code != 200:
            print(f"❌ Chat access failed for initialization test")
            return False
        
        content = chat_response.text
        
        # Check initialization order
        init_functions = [
            'initializeHeaderVideo();',
            'initializeChat();',
            'initializeChatDateTime();',
            'loadFontPreference();'
        ]
        
        # Find positions of each function call
        positions = {}
        for func in init_functions:
            pos = content.find(func)
            if pos != -1:
                positions[func] = pos
            else:
                print(f"❌ Missing initialization function: {func}")
                return False
        
        # Check if they're in the right order
        sorted_positions = sorted(positions.items(), key=lambda x: x[1])
        expected_order = [
            'initializeHeaderVideo();',
            'initializeChat();',
            'initializeChatDateTime();',
            'loadFontPreference();'
        ]
        
        actual_order = [func for func, pos in sorted_positions]
        
        if actual_order == expected_order:
            print(f"✅ Initialization functions in correct order")
        else:
            print(f"⚠️ Initialization order differs:")
            print(f"  Expected: {expected_order}")
            print(f"  Actual: {actual_order}")
        
        # Check that initializeChatDateTime is called
        if 'initializeChatDateTime();' in content:
            print(f"✅ initializeChatDateTime() is called during initialization")
        else:
            print(f"❌ initializeChatDateTime() not called during initialization")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing initialization order: {e}")
        return False

def test_css_styling():
    """Test that date/time CSS styling is present"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🎨 Testing date/time CSS styling on port {web_chat_port}...")
        
        # Login and get chat page
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login failed for CSS test")
            return False
        
        chat_response = session.get(
            f"http://localhost:{web_chat_port}/chat",
            allow_redirects=True,
            timeout=10
        )
        
        if chat_response.status_code != 200:
            print(f"❌ Chat access failed for CSS test")
            return False
        
        content = chat_response.text
        
        # Check for CSS classes
        css_classes = [
            '.chat-datetime-display',
            '.connection-status',
            '.connection-status.connecting',
            '.connection-status.connected',
            '.connection-status.disconnected',
            '.chat-date-part',
            '.chat-time-part'
        ]
        
        missing_css = []
        for css_class in css_classes:
            if css_class not in content:
                missing_css.append(css_class)
        
        if missing_css:
            print(f"❌ Missing CSS classes: {missing_css}")
            return False
        
        print(f"✅ All date/time CSS classes found")
        
        # Check for styling properties
        style_properties = [
            'font-family: \'Orbitron\'',
            'background: linear-gradient',
            'border-radius:',
            'text-shadow:',
            'box-shadow:'
        ]
        
        found_styles = []
        for style in style_properties:
            if style in content:
                found_styles.append(style)
        
        if len(found_styles) >= 3:
            print(f"✅ Styling properties found: {found_styles}")
        else:
            print(f"⚠️ Limited styling found: {found_styles}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing CSS styling: {e}")
        return False

def main():
    """Run all date/time indicator tests"""
    print("🕒 DATE/TIME INDICATORS TEST")
    print("=" * 50)
    print("Testing date/time functionality:")
    print("- HTML elements present")
    print("- JavaScript functions")
    print("- Initialization order")
    print("- CSS styling")
    print()
    
    tests_passed = 0
    total_tests = 4
    
    # Test 1: Date/time elements present
    print("🧪 TEST 1: Date/Time Elements Present")
    if test_datetime_elements_present():
        tests_passed += 1
    
    # Test 2: JavaScript functions
    print("\n🧪 TEST 2: JavaScript Functions")
    if test_datetime_javascript_functions():
        tests_passed += 1
    
    # Test 3: Initialization order
    print("\n🧪 TEST 3: Initialization Order")
    if test_chat_initialization_order():
        tests_passed += 1
    
    # Test 4: CSS styling
    print("\n🧪 TEST 4: CSS Styling")
    if test_css_styling():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"🕒 TEST RESULTS: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✅ ALL TESTS PASSED - Date/time indicators should work!")
        print("🎉 Features verified:")
        print("   - HTML elements present")
        print("   - JavaScript functions implemented")
        print("   - Proper initialization order")
        print("   - CSS styling applied")
        print("   - Debugging code added")
    else:
        print("❌ SOME TESTS FAILED - Date/time indicators need fixes")
        
        if tests_passed >= 3:
            print("🔧 RECOMMENDATION: Minor date/time issues remain")
        elif tests_passed >= 2:
            print("🔧 RECOMMENDATION: Some date/time features work")
        else:
            print("🔧 RECOMMENDATION: Major date/time fixes needed")
    
    print(f"\n🌐 Manual test:")
    print(f"1. Open http://localhost:8007")
    print(f"2. Login with: admin / admin123")
    print(f"3. Check browser console for date/time logs")
    print(f"4. Look for date/time display in header")
    print(f"5. Check connection status indicator")

if __name__ == '__main__':
    main()
