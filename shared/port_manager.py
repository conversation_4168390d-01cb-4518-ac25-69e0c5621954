#!/usr/bin/env python3
"""
🔌 Deeplica Port Manager
Centralized port management system to prevent port conflicts across all Deeplica services.

IMPORTANT: ALL ports are managed with DHCP-style constant assignments.
All other ports are configurable via admin interface with factory defaults.
"""

import os
import sys
import time
import socket
import subprocess
import psutil
import json
from typing import Dict, List, Optional, Set
from pathlib import Path
from datetime import datetime
# Removed circular import - using direct values instead

# Global message tracker to prevent spam across all instances
_global_message_tracker = {}

class DeepLicaPortManager:
    """Centralized port and host management for all Deeplica services"""

    # RESERVED PORT ASSIGNMENTS - Like DHCP with constant assignments
    # Port Manager assigns the SAME port to the SAME service every time
    # These are the reserved/constant assignments managed by port manager
    RESERVED_PORTS = {
        # Core Services - CONSTANT assignments managed by port manager
        "BACKEND-API": 8888,        # Always 8888 - managed by port manager
        "DISPATCHER": 8001,         # Always 8001 - managed by port manager
        "DIALOGUE-AGENT": 8002,     # Always 8002 - managed by port manager
        "PLANNER-AGENT": 8003,      # Always 8003 - managed by port manager
        "PHONE-AGENT": 8004,        # Always 8004 - managed by port manager

        # System Services - CONSTANT assignments
        "WATCHDOG": 8005,           # Always 8005 - managed by port manager

        # Web Services - CONSTANT assignments
        "WEB-CHAT": 8007,           # Always 8007 - managed by port manager
        "CLI-TERMINAL": 8008,       # Always 8008 - managed by port manager

        # External Services - CONSTANT assignments for compatibility
        "TWILIO-ECHO-BOT": 8009,    # Always 8009 - managed by port manager
        "WEBHOOK-SERVER": 8010,     # Always 8010 - managed by port manager
        "PHONE-WEBHOOK": 8010,      # Always 8010 - same as webhook-server
        "NGROK-API": 4040,          # Always 4040 - managed by port manager
        "NGROK-TUNNEL": 8080,       # Always 8080 - CONSTANT for ngrok
        "NGROK-FORWARDER": 8080,    # Always 8080 - same as ngrok tunnel

        # API Services - CONSTANT assignments for external integrations
        "TWILIO-API": 8021,         # Always 8021 - managed by port manager
        "GEMINI-API": 8022,         # Always 8022 - managed by port manager
        "OPENAI-API": 8023,         # Always 8023 - managed by port manager
        "MONGODB-API": 8024,        # Always 8024 - managed by port manager
        "EXTERNAL-API": 8025,       # Always 8025 - managed by port manager

        # Database Services - CONSTANT assignments
        "MONGODB-PROXY": 8020,      # Always 8020 - managed by port manager

        # Development & Utility Services - CONSTANT assignments
        "TEST-SERVER": 8011,        # Always 8011 - managed by port manager
        "DEV-SERVER": 8012,         # Always 8012 - managed by port manager
        "PROXY-SERVER": 8013,       # Always 8013 - managed by port manager
        "MOCK-SERVER": 8014,        # Always 8014 - managed by port manager
        "DEBUG-SERVER": 8015,       # Always 8015 - managed by port manager

        # Admin & Monitoring Services - CONSTANT assignments
        "ADMIN-PANEL": 8016,        # Always 8016 - managed by port manager
        "METRICS": 8017,            # Always 8017 - managed by port manager
        "LOGS": 8018,               # Always 8018 - managed by port manager
        "HEALTH-CHECK": 8019        # Always 8019 - managed by port manager
    }

    # Factory defaults are the same as reserved ports
    FACTORY_DEFAULTS = RESERVED_PORTS.copy()

    # Local backup file for settings (available before database)
    # Always use the main shared directory, regardless of where this is called from
    SETTINGS_BACKUP_FILE = Path(__file__).parent / "deeplica_port_settings.json"

    def __init__(self, service_name: str = "PORT-MANAGER"):
        self.service_name = service_name
        self.reserved_ports: Set[int] = set()

        # 🔌 DHCP-LIKE PORT ALLOCATION SYSTEM
        self.port_leases: Dict[str, Dict] = {}  # Track active port leases
        self.allocated_ports: Set[int] = set()  # Track all allocated ports
        self.lease_file = Path(__file__).parent / "port_leases.json"
        self.conflict_resolution_enabled = True

        # Anti-spam tracking
        self._last_backup_message = 0
        self._last_factory_message = 0
        self._message_cooldown = 60  # seconds (increased from 30)
        self._last_messages = {}  # Track last messages to prevent spam

        # Load current port configuration
        self.OFFICIAL_PORTS = self._load_port_configuration()

        # 🔌 DHCP-LIKE INITIALIZATION
        self._load_port_leases()

        # Only validate conflicts if explicitly requested, not during initialization
        # This prevents infinite loops during startup
        if hasattr(self, '_validate_on_init') and self._validate_on_init:
            self._validate_and_fix_conflicts()

        # Ensure backup file exists
        self._ensure_backup_file()

    # ============================================================================
    # 🔌 DHCP-LIKE PORT ALLOCATION SYSTEM
    # ============================================================================

    def _load_port_leases(self):
        """Load existing port leases from file"""
        try:
            if self.lease_file.exists():
                with open(self.lease_file, 'r') as f:
                    self.port_leases = json.load(f)

                # Rebuild allocated ports set
                self.allocated_ports = set()
                for service, lease_info in self.port_leases.items():
                    if isinstance(lease_info, dict) and 'port' in lease_info:
                        self.allocated_ports.add(lease_info['port'])

                print(f"[{self.service_name}] 📁 Loaded {len(self.port_leases)} port leases from file")
            else:
                self.port_leases = {}
                self.allocated_ports = set()
                print(f"[{self.service_name}] 📁 Initialized empty port lease system")

        except Exception as e:
            print(f"[{self.service_name}] ⚠️ Error loading port leases: {e}")
            self.port_leases = {}
            self.allocated_ports = set()

    def _save_port_leases(self):
        """Save current port leases to file"""
        try:
            with open(self.lease_file, 'w') as f:
                json.dump(self.port_leases, f, indent=2, default=str)
            print(f"[{self.service_name}] 💾 Saved {len(self.port_leases)} port leases to file")
        except Exception as e:
            print(f"[{self.service_name}] ❌ Error saving port leases: {e}")

    def _validate_and_fix_conflicts(self):
        """Validate port assignments and fix conflicts automatically"""
        if not self.conflict_resolution_enabled:
            return

        # First, clean up duplicate leases for the same service
        self._cleanup_duplicate_leases()

        conflicts_found = []
        port_usage = {}

        # Check for conflicts in current leases
        for service, lease_info in self.port_leases.items():
            if isinstance(lease_info, dict) and 'port' in lease_info:
                port = lease_info['port']
                if port in port_usage:
                    # Only consider it a conflict if the port is actually in use
                    if not self.is_port_free(port):
                        conflicts_found.append({
                            'port': port,
                            'services': [port_usage[port], service],
                            'type': 'lease_conflict'
                        })
                else:
                    port_usage[port] = service

        # Check for conflicts with reserved ports (only if ports are actually in use)
        for service_key, port in self.RESERVED_PORTS.items():
            if port in port_usage:
                # Get the normalized name for the service that has the lease
                leased_service = port_usage[port]
                lease_info = self.port_leases.get(leased_service, {})
                normalized_name = lease_info.get('normalized_name', leased_service.upper())

                # Only consider it a conflict if the normalized names don't match AND the port is actually in use
                if normalized_name != service_key and not self.is_port_free(port):
                    conflicts_found.append({
                        'port': port,
                        'services': [leased_service, service_key],
                        'type': 'reserved_conflict'
                    })

        # Fix conflicts only if there are actual conflicts (ports in use)
        if conflicts_found:
            print(f"[{self.service_name}] 🚨 Found {len(conflicts_found)} actual port conflicts - fixing automatically")
            for conflict in conflicts_found:
                port = conflict['port']
                service = conflict.get('service', 'unknown')
                self._resolve_port_conflict(port, service, force=True)

            # Save updated leases
            self._save_port_leases()
        else:
            # Only print this message once to avoid spam
            if not hasattr(self, '_no_conflicts_printed'):
                print(f"[{self.service_name}] ✅ No actual port conflicts detected")
                self._no_conflicts_printed = True

    def _cleanup_duplicate_leases(self):
        """Clean up duplicate leases for the same service with different names"""
        try:
            # Group leases by port
            port_groups = {}
            for service, lease_info in self.port_leases.items():
                if isinstance(lease_info, dict) and 'port' in lease_info:
                    port = lease_info['port']
                    if port not in port_groups:
                        port_groups[port] = []
                    port_groups[port].append((service, lease_info))

            # Clean up duplicates
            services_to_remove = []
            for port, leases in port_groups.items():
                if len(leases) > 1:
                    # Keep the most recent lease and remove others
                    leases.sort(key=lambda x: x[1].get('assigned_at', ''), reverse=True)
                    keep_service = leases[0][0]

                    for service, _ in leases[1:]:
                        services_to_remove.append(service)

                    if services_to_remove:
                        print(f"[{self.service_name}] 🧹 Cleaning up duplicate leases for port {port}, keeping {keep_service}")

            # Remove duplicate services
            for service in services_to_remove:
                if service in self.port_leases:
                    del self.port_leases[service]

            # Save cleaned leases if we removed any
            if services_to_remove:
                self._save_port_leases()
                print(f"[{self.service_name}] ✅ Cleaned up {len(services_to_remove)} duplicate leases")

        except Exception as e:
            print(f"[{self.service_name}] ⚠️ Error cleaning duplicate leases: {e}")

    def _resolve_port_conflict(self, conflict: Dict):
        """Resolve a specific port conflict"""
        port = conflict['port']
        services = conflict['services']
        conflict_type = conflict['type']

        print(f"[{self.service_name}] 🔧 Resolving {conflict_type} on port {port} between {services}")

        if conflict_type == 'reserved_conflict':
            # Reserved port takes priority - reassign the conflicting service
            conflicting_service = services[0]  # The non-reserved service
            new_port = self._find_available_port(conflicting_service)

            if new_port:
                print(f"[{self.service_name}] 🔄 Reassigning {conflicting_service} from {port} to {new_port}")

                # Update lease
                self.port_leases[conflicting_service] = {
                    'port': new_port,
                    'assigned_at': datetime.now().isoformat(),
                    'reason': f'conflict_resolution_from_{port}'
                }

                # Update allocated ports
                self.allocated_ports.discard(port)
                self.allocated_ports.add(new_port)

                # Update official ports
                self.OFFICIAL_PORTS[conflicting_service] = new_port

    def _find_available_port(self, service_name: str, start_port: int = 8100) -> Optional[int]:
        """Find an available port for a service (DHCP-like allocation)"""
        # Try to find a port in the dynamic range (8100-8999)
        for port in range(start_port, 9000):
            if (port not in self.allocated_ports and
                port not in self.RESERVED_PORTS.values() and
                self.is_port_free(port)):
                return port

        # If no port found in primary range, try extended range
        for port in range(9000, 9999):
            if (port not in self.allocated_ports and
                port not in self.RESERVED_PORTS.values() and
                self.is_port_free(port)):
                return port

        print(f"[{self.service_name}] ❌ No available port found for {service_name}")
        return None

    def allocate_port(self, service_name: str, preferred_port: Optional[int] = None, requester_unique_id: str = None) -> int:
        """🔌 BULLETPROOF PORT ALLOCATION - Allocate a tested, free port to a service"""

        # 🔌 STEP 0: Normalize service name using aliases
        normalized_service_name = service_name
        if service_name.lower() in self.SERVICE_ALIASES:
            normalized_service_name = self.SERVICE_ALIASES[service_name.lower()]
            print(f"[{self.service_name}] 🔄 Mapped {service_name} to {normalized_service_name}")

        service_key = normalized_service_name.upper()  # Keep hyphens for RESERVED_PORTS lookup

        # Create unique requester ID if not provided
        if not requester_unique_id:
            import time
            requester_unique_id = f"{service_name}_{int(time.time())}"

        # 🔌 STEP 1: Check if service already has a lease and test it
        if service_name in self.port_leases:
            existing_lease = self.port_leases[service_name]
            existing_port = existing_lease['port']

            # Test if existing port is still available and working
            if self._test_port_availability(existing_port):
                print(f"[{self.service_name}] 🔄 Service {service_name} reusing tested port {existing_port}")
                # Update lease timestamp and requester ID
                existing_lease['last_verified'] = datetime.now().isoformat()
                existing_lease['requester_id'] = requester_unique_id
                self._save_port_leases()
                return existing_port
            else:
                print(f"[{self.service_name}] ⚠️ Existing port {existing_port} for {service_name} is no longer available")
                # Remove invalid lease
                del self.port_leases[service_name]
                self.allocated_ports.discard(existing_port)

        # 🔌 STEP 2: Try to allocate and test reserved port
        if service_key in self.RESERVED_PORTS:
            reserved_port = self.RESERVED_PORTS[service_key]
            print(f"[{self.service_name}] 🔍 Testing reserved port {reserved_port} for {service_name}")

            if self._test_and_free_port(reserved_port, service_name):
                self.port_leases[service_name] = {
                    'port': reserved_port,
                    'assigned_at': datetime.now().isoformat(),
                    'last_verified': datetime.now().isoformat(),
                    'requester_id': requester_unique_id,
                    'type': 'reserved',
                    'normalized_name': normalized_service_name
                }
                self.allocated_ports.add(reserved_port)
                self._save_port_leases()
                print(f"[{self.service_name}] ✅ Allocated and tested reserved port {reserved_port} to {service_name}")
                return reserved_port
            else:
                print(f"[{self.service_name}] ❌ Failed to secure reserved port {reserved_port} for {service_name}")

        # 🔌 STEP 3: Try preferred port if specified and test it
        if preferred_port and self._test_and_free_port(preferred_port, service_name):
            self.port_leases[service_name] = {
                'port': preferred_port,
                'assigned_at': datetime.now().isoformat(),
                'last_verified': datetime.now().isoformat(),
                'requester_id': requester_unique_id,
                'type': 'preferred'
            }
            self.allocated_ports.add(preferred_port)
            self._save_port_leases()
            print(f"[{self.service_name}] ✅ Allocated and tested preferred port {preferred_port} to {service_name}")
            return preferred_port

        # 🔌 STEP 4: Find and test available port dynamically
        available_port = self._find_and_test_available_port(service_name)
        if available_port:
            self.port_leases[service_name] = {
                'port': available_port,
                'assigned_at': datetime.now().isoformat(),
                'last_verified': datetime.now().isoformat(),
                'requester_id': requester_unique_id,
                'type': 'dynamic'
            }
            self.allocated_ports.add(available_port)
            self._save_port_leases()
            print(f"[{self.service_name}] 🆕 Allocated and tested dynamic port {available_port} to {service_name}")
            return available_port

        # This should never happen with bulletproof design
        raise RuntimeError(f"CRITICAL: Could not allocate any tested port for service: {service_name}")

    def release_port(self, service_name: str):
        """🔌 Release a port lease"""
        if service_name in self.port_leases:
            port = self.port_leases[service_name]['port']
            del self.port_leases[service_name]
            self.allocated_ports.discard(port)
            self._save_port_leases()
            print(f"[{self.service_name}] 🔓 Released port {port} from {service_name}")

    def get_port_lease_info(self, service_name: str) -> Optional[Dict]:
        """Get port lease information for a service"""
        return self.port_leases.get(service_name)

    def list_all_leases(self) -> Dict[str, Dict]:
        """List all current port leases"""
        return self.port_leases.copy()

    def _get_processes_on_port(self, port: int) -> List[Dict]:
        """Get detailed information about processes using a port"""
        processes = []
        try:
            result = subprocess.run(['lsof', '-i', f':{port}'],
                                  capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                for line in lines:
                    parts = line.split()
                    if len(parts) >= 2:
                        processes.append({
                            'name': parts[0],
                            'pid': parts[1],
                            'user': parts[2] if len(parts) > 2 else 'unknown'
                        })
        except Exception as e:
            print(f"[{self.service_name}] ⚠️ Error getting processes on port {port}: {e}")

        return processes

    def _should_print_message(self, message_key: str, message: str) -> bool:
        """Check if we should print this message to avoid spam"""
        current_time = time.time()

        # Always print error messages
        if any(keyword in message.lower() for keyword in ['error', 'failed', 'critical']):
            return True

        # Use global tracker for common messages to prevent spam across instances
        global_key = f"global_{message_key}"
        if global_key in _global_message_tracker:
            last_time, last_msg = _global_message_tracker[global_key]
            if (current_time - last_time < self._message_cooldown and
                last_msg == message):
                return False

        # Check local instance tracker
        if message_key in self._last_messages:
            last_time, last_msg = self._last_messages[message_key]
            if (current_time - last_time < self._message_cooldown and
                last_msg == message):
                return False

        # Update both trackers
        _global_message_tracker[global_key] = (current_time, message)
        self._last_messages[message_key] = (current_time, message)
        return True

    def _log_unified(self, level: str, routine: str, message: str):
        """Log message in unified DEEPLICA format"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"{timestamp} - [{level}] - Svc: {self.service_name}, Mod: PORT-MANAGER, Cod: {routine}, msg: {message}")

    def _print_if_new(self, message_key: str, message: str, routine: str = "port_manager"):
        """Print message only if it's new or after cooldown"""
        if self._should_print_message(message_key, message):
            self._log_unified("INFO", routine, message)

    def _load_port_configuration(self) -> Dict[str, int]:
        """Load port configuration from backup file or factory defaults"""
        try:
            # Try to load from local backup file first
            if self.SETTINGS_BACKUP_FILE.exists():
                with open(self.SETTINGS_BACKUP_FILE, 'r') as f:
                    data = json.load(f)
                    ports = data.get('port_config', {})
                    if ports:
                        # Anti-spam: only print if enough time has passed
                        self._print_if_new("backup_loaded", "📁 Loaded ports from backup file", "_load_port_configuration")
                        return self._convert_to_official_format(ports)

            # Fallback to factory defaults
            self._print_if_new("factory_defaults", "🏭 Using factory default ports", "_load_port_configuration")
            return self.FACTORY_DEFAULTS.copy()

        except Exception as e:
            self._log_unified("ERROR", "_load_port_configuration", f"⚠️ Error loading port config: {e}")
            return self.FACTORY_DEFAULTS.copy()

    def _convert_to_official_format(self, ports: Dict[str, int]) -> Dict[str, int]:
        """Convert database format to official format"""
        official_ports = {}

        for service, port in ports.items():
            # Convert service names to official format
            if service == 'backend':
                official_ports['BACKEND-API'] = port
            elif service == 'dispatcher':
                official_ports['DISPATCHER'] = port
            elif service == 'dialogue':
                official_ports['DIALOGUE-AGENT'] = port
            elif service == 'planner':
                official_ports['PLANNER-AGENT'] = port
            elif service == 'phone':
                official_ports['PHONE-AGENT'] = port
            elif service == 'watchdog':
                official_ports['WATCHDOG'] = port
            elif service == 'stop-deeplica':
                official_ports['STOP-DEEPLICA-SERVICE'] = port
            elif service == 'web-chat':
                official_ports['WEB-CHAT'] = port
            elif service == 'cli':
                official_ports['CLI-TERMINAL'] = port
            elif service == 'twilio-echo-bot':
                official_ports['TWILIO-ECHO-BOT'] = port
            elif service == 'webhook':
                official_ports['WEBHOOK-SERVER'] = port
            elif service == 'ngrok-api':
                official_ports['NGROK-API'] = port
            elif service == 'ngrok-tunnel':
                official_ports['NGROK-TUNNEL'] = port
            elif service == 'mongodb-proxy':
                official_ports['MONGODB-PROXY'] = port
            elif service == 'test-server':
                official_ports['TEST-SERVER'] = port
            elif service == 'dev-server':
                official_ports['DEV-SERVER'] = port
            elif service == 'proxy-server':
                official_ports['PROXY-SERVER'] = port
            elif service == 'mock-server':
                official_ports['MOCK-SERVER'] = port
            elif service == 'debug-server':
                official_ports['DEBUG-SERVER'] = port
            elif service == 'admin-panel':
                official_ports['ADMIN-PANEL'] = port
            elif service == 'metrics':
                official_ports['METRICS'] = port
            elif service == 'logs':
                official_ports['LOGS'] = port
            elif service == 'health-check':
                official_ports['HEALTH-CHECK'] = port
            else:
                official_ports[service.upper()] = port

        # Ensure reserved ports are always assigned correctly (like DHCP reservations)
        for service, reserved_port in self.RESERVED_PORTS.items():
            official_ports[service] = reserved_port

        return official_ports

    def _ensure_backup_file(self):
        """Ensure backup file exists with current settings"""
        try:
            # Create directory if it doesn't exist
            self.SETTINGS_BACKUP_FILE.parent.mkdir(exist_ok=True)

            # Create backup file if it doesn't exist
            if not self.SETTINGS_BACKUP_FILE.exists():
                self._save_to_backup_file()

        except Exception as e:
            self._log_unified("ERROR", "_ensure_backup_file", f"⚠️ Error ensuring backup file: {e}")

    def _save_to_backup_file(self):
        """Save current port configuration to backup file"""
        try:
            # Convert to database format for consistency
            db_format = {}
            for service, port in self.OFFICIAL_PORTS.items():
                if service == 'BACKEND-API':
                    db_format['backend'] = port
                elif service == 'DISPATCHER':
                    db_format['dispatcher'] = port
                elif service == 'DIALOGUE-AGENT':
                    db_format['dialogue'] = port
                elif service == 'PLANNER-AGENT':
                    db_format['planner'] = port
                elif service == 'PHONE-AGENT':
                    db_format['phone'] = port
                elif service == 'WATCHDOG':
                    db_format['watchdog'] = port
                elif service == 'STOP-DEEPLICA-SERVICE':
                    db_format['stop-deeplica'] = port
                elif service == 'WEB-CHAT':
                    db_format['web-chat'] = port
                elif service == 'CLI-TERMINAL':
                    db_format['cli'] = port
                elif service == 'TWILIO-ECHO-BOT':
                    db_format['twilio-echo-bot'] = port
                elif service == 'WEBHOOK-SERVER':
                    db_format['webhook'] = port
                elif service == 'NGROK-API':
                    db_format['ngrok-api'] = port
                elif service == 'NGROK-TUNNEL':
                    db_format['ngrok-tunnel'] = port
                elif service == 'MONGODB-PROXY':
                    db_format['mongodb-proxy'] = port
                elif service == 'TEST-SERVER':
                    db_format['test-server'] = port
                elif service == 'DEV-SERVER':
                    db_format['dev-server'] = port
                elif service == 'PROXY-SERVER':
                    db_format['proxy-server'] = port
                elif service == 'MOCK-SERVER':
                    db_format['mock-server'] = port
                elif service == 'DEBUG-SERVER':
                    db_format['debug-server'] = port
                elif service == 'ADMIN-PANEL':
                    db_format['admin-panel'] = port
                elif service == 'METRICS':
                    db_format['metrics'] = port
                elif service == 'LOGS':
                    db_format['logs'] = port
                elif service == 'HEALTH-CHECK':
                    db_format['health-check'] = port

            backup_data = {
                'port_config': db_format,
                'last_updated': time.time(),
                'version': '5.0',
                'port_management_type': 'DHCP_STYLE_RESERVATIONS',
                'reserved_ports': self.RESERVED_PORTS,
                'factory_defaults': self.FACTORY_DEFAULTS
            }

            with open(self.SETTINGS_BACKUP_FILE, 'w') as f:
                json.dump(backup_data, f, indent=2)

            self._log_unified("INFO", "_save_to_backup_file", "💾 Saved port configuration to backup file")

        except Exception as e:
            self._log_unified("ERROR", "_save_to_backup_file", f"❌ Error saving backup file: {e}")

    def update_port_configuration(self, new_ports: Dict[str, int]):
        """Update port configuration and save to backup file"""
        try:
            # Convert and update ports
            updated_ports = self._convert_to_official_format(new_ports)
            self.OFFICIAL_PORTS.update(updated_ports)

            # Save to backup file
            self._save_to_backup_file()

            print(f"[{self.service_name}] ✅ Port configuration updated")
            print(f"[{self.service_name}] ⚠️ System restart required for changes to take effect")

        except Exception as e:
            print(f"[{self.service_name}] ❌ Error updating port configuration: {e}")

    def reset_to_factory_defaults(self):
        """Reset all ports to factory defaults"""
        try:
            self.OFFICIAL_PORTS = self.FACTORY_DEFAULTS.copy()
            self._save_to_backup_file()

            print(f"[{self.service_name}] 🏭 Reset to factory default ports")
            print(f"[{self.service_name}] ⚠️ System restart required for changes to take effect")

        except Exception as e:
            print(f"[{self.service_name}] ❌ Error resetting to factory defaults: {e}")

    def get_factory_defaults(self) -> Dict[str, int]:
        """Get factory default port configuration"""
        return self.FACTORY_DEFAULTS.copy()

    # OFFICIAL DEEPLICA HOST ASSIGNMENTS - SINGLE SOURCE OF TRUTH
    OFFICIAL_HOSTS = {
        # Default host configuration - can be overridden via environment or admin settings
        "DEFAULT": "0.0.0.0",  # Bind to all interfaces by default
        "LOCALHOST": "127.0.0.1",  # Localhost only
        "EXTERNAL": "0.0.0.0",  # External access
    }

    # Service name mappings for consistency
    SERVICE_ALIASES = {
        # Core Services
        "backend": "BACKEND-API",
        "backend-api": "BACKEND-API",
        "api": "BACKEND-API",

        "dispatcher": "DISPATCHER",

        "dialogue": "DIALOGUE-AGENT",
        "dialogue-agent": "DIALOGUE-AGENT",

        "planner": "PLANNER-AGENT",
        "planner-agent": "PLANNER-AGENT",

        "phone": "PHONE-AGENT",
        "phone-agent": "PHONE-AGENT",

        # System Services
        "watchdog": "WATCHDOG",

        # Web Services
        "webchat": "WEB-CHAT",
        "web-chat": "WEB-CHAT",
        "chat": "WEB-CHAT",

        "cli": "CLI-TERMINAL",
        "cli-terminal": "CLI-TERMINAL",
        "terminal": "CLI-TERMINAL",

        # External Services
        "twilio-echo-bot": "TWILIO-ECHO-BOT",
        "echo-bot": "TWILIO-ECHO-BOT",
        "twilio": "TWILIO-ECHO-BOT",

        "webhook": "WEBHOOK-SERVER",
        "webhook-server": "WEBHOOK-SERVER",
        "webhooks": "WEBHOOK-SERVER",
        "phone-webhook": "PHONE-WEBHOOK",

        "ngrok-api": "NGROK-API",
        "ngrok": "NGROK-API",

        "ngrok-tunnel": "NGROK-TUNNEL",
        "ngrok-forwarder": "NGROK-FORWARDER",
        "tunnel": "NGROK-TUNNEL",

        # API Services - CONSTANT ports for external integrations
        "twilio-api": "TWILIO-API",
        "gemini-api": "GEMINI-API",
        "gemini": "GEMINI-API",
        "openai-api": "OPENAI-API",
        "openai": "OPENAI-API",
        "mongodb-api": "MONGODB-API",
        "external-api": "EXTERNAL-API",

        # Database Services
        "mongodb": "MONGODB-PROXY",
        "mongodb-proxy": "MONGODB-PROXY",
        "mongo": "MONGODB-PROXY",
        "database": "MONGODB-PROXY",

        # Development Services
        "test": "TEST-SERVER",
        "test-server": "TEST-SERVER",
        "dev": "DEV-SERVER",
        "dev-server": "DEV-SERVER",
        "proxy": "PROXY-SERVER",
        "proxy-server": "PROXY-SERVER",
        "mock": "MOCK-SERVER",
        "mock-server": "MOCK-SERVER",
        "debug": "DEBUG-SERVER",
        "debug-server": "DEBUG-SERVER",

        # Admin Services
        "admin": "ADMIN-PANEL",
        "admin-panel": "ADMIN-PANEL",
        "metrics": "METRICS",
        "logs": "LOGS",
        "health": "HEALTH-CHECK",
        "health-check": "HEALTH-CHECK",
    }

    def get_port(self, service_name: str) -> int:
        """Get the official port for a service"""
        # Normalize service name
        normalized_name = service_name.upper().replace("-", "-")

        # Check aliases first
        if service_name.lower() in self.SERVICE_ALIASES:
            normalized_name = self.SERVICE_ALIASES[service_name.lower()]

        # Get port from official assignments
        if normalized_name in self.OFFICIAL_PORTS:
            return self.OFFICIAL_PORTS[normalized_name]

        raise ValueError(f"Unknown service: {service_name}. Available services: {list(self.OFFICIAL_PORTS.keys())}")

    def get_all_configurable_services(self) -> Dict[str, int]:
        """Get all services - ALL ports are now configurable"""
        return self.OFFICIAL_PORTS.copy()

    def is_port_reserved(self, service_name: str) -> bool:
        """Check if a service has a reserved port assignment (like DHCP reservation)"""
        # Normalize service name
        normalized_name = service_name.upper().replace("-", "-")

        # Check aliases first
        if service_name.lower() in self.SERVICE_ALIASES:
            normalized_name = self.SERVICE_ALIASES[service_name.lower()]

        return normalized_name in self.RESERVED_PORTS

    def get_reserved_port(self, service_name: str) -> int:
        """Get the reserved port for a service (like DHCP reservation)"""
        # Normalize service name
        normalized_name = service_name.upper().replace("-", "-")

        # Check aliases first
        if service_name.lower() in self.SERVICE_ALIASES:
            normalized_name = self.SERVICE_ALIASES[service_name.lower()]

        if normalized_name in self.RESERVED_PORTS:
            return self.RESERVED_PORTS[normalized_name]

        raise ValueError(f"No reserved port for service: {service_name}")

    def is_port_free(self, port: int) -> bool:
        """Check if a port is free"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex(('localhost', port))
                return result != 0
        except Exception:
            return False

    def _test_port_availability(self, port: int) -> bool:
        """🔍 BULLETPROOF: Test if a port is truly available and can be bound"""
        try:
            # Test TCP binding
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                sock.settimeout(1.0)  # 1 second timeout
                sock.bind(('0.0.0.0', port))
                sock.listen(1)

            # Test UDP binding as well
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as sock:
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                sock.settimeout(1.0)
                sock.bind(('0.0.0.0', port))

            return True
        except (OSError, socket.error) as e:
            return False

    def _test_and_free_port(self, port: int, service_name: str) -> bool:
        """🔍 BULLETPROOF: Test port and free it if needed"""
        # First test if port is available
        if self._test_port_availability(port):
            return True

        # Port is in use - try to free it
        print(f"[{self.service_name}] 🔧 Port {port} in use, attempting to free for {service_name}")

        if self._resolve_port_conflict(port, service_name, force=True):
            # Wait a moment for port to be freed
            import time
            time.sleep(0.5)

            # Test again
            if self._test_port_availability(port):
                print(f"[{self.service_name}] ✅ Port {port} successfully freed and tested")
                return True

        print(f"[{self.service_name}] ❌ Failed to free port {port} for {service_name}")
        return False

    def _find_and_test_available_port(self, service_name: str, start_port: int = 8001) -> Optional[int]:
        """🔍 BULLETPROOF: Find an available port and test it thoroughly"""
        # Try ports in a reasonable range
        for port in range(start_port, start_port + 100):
            # Skip reserved ports for other services
            if port in [p for p in self.RESERVED_PORTS.values() if p != start_port]:
                continue

            # Skip already allocated ports
            if port in self.allocated_ports:
                continue

            # Test if port is truly available
            if self._test_port_availability(port):
                print(f"[{self.service_name}] 🔍 Found and tested available port {port} for {service_name}")
                return port

        print(f"[{self.service_name}] ❌ No available tested port found for {service_name}")
        return None

    def is_backend_port_constant(self, service_name: str) -> bool:
        """Check if this is the constant Backend API port"""
        normalized_name = service_name.lower()
        if normalized_name in self.SERVICE_ALIASES:
            normalized_name = self.SERVICE_ALIASES[normalized_name]
        return normalized_name == "BACKEND-API"
    
    def get_process_on_port(self, port: int) -> Optional[Dict]:
        """Get information about the process using a port"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    for conn in proc.connections():
                        if conn.laddr.port == port:
                            return {
                                'pid': proc.info['pid'],
                                'name': proc.info['name'],
                                'cmdline': ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else '',
                                'process': proc
                            }
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            print(f"[{self.service_name}] ❌ Error checking port {port}: {e}")
        return None

    def _get_processes_on_port(self, port: int) -> List[Dict]:
        """Get all processes using a specific port"""
        processes = []
        try:
            # Use lsof to get processes on port
            result = subprocess.run(['lsof', '-ti', f':{port}'],
                                  capture_output=True, text=True, timeout=5)

            if result.returncode == 0 and result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                for pid_str in pids:
                    if pid_str.strip():
                        try:
                            pid = int(pid_str.strip())
                            # Get process details
                            proc_result = subprocess.run(['ps', '-p', str(pid), '-o', 'pid,comm,args'],
                                                       capture_output=True, text=True, timeout=5)
                            if proc_result.returncode == 0:
                                lines = proc_result.stdout.strip().split('\n')
                                if len(lines) > 1:  # Skip header
                                    parts = lines[1].strip().split(None, 2)
                                    if len(parts) >= 3:
                                        processes.append({
                                            'pid': pid,
                                            'command': parts[2],
                                            'name': parts[1]
                                        })
                        except (ValueError, subprocess.TimeoutExpired):
                            continue
        except Exception as e:
            print(f"[{self.service_name}] ❌ Error getting processes on port {port}: {e}")

        return processes

    def _resolve_port_conflict(self, port: int, requesting_service: str, force: bool = False) -> bool:
        """Aggressively resolve port conflicts"""
        try:
            # First check if the port is actually free - if so, no conflict to resolve
            if self.is_port_free(port):
                print(f"[{self.service_name}] ✅ Port {port} is already free - no conflict to resolve")
                return True

            print(f"[{self.service_name}] 🔧 Resolving port conflict for port {port} (requesting service: {requesting_service})")

            # Try multiple resolution attempts
            for attempt in range(3):
                print(f"[{self.service_name}] 🔄 Conflict resolution attempt {attempt + 1}/3 for port {port}")

                # Kill processes using the port
                if self.kill_process_on_port(port, force=True):
                    # Wait for port to be released
                    wait_time = 2 + attempt
                    print(f"[{self.service_name}] ⏳ Waiting {wait_time}s for port {port} to be released...")
                    time.sleep(wait_time)

                    # Verify port is now free
                    if self.is_port_free(port):
                        print(f"[{self.service_name}] ✅ Port {port} successfully freed for {requesting_service}")
                        return True
                    else:
                        print(f"[{self.service_name}] ⚠️ Port {port} still in use after attempt {attempt + 1}")

                        # On later attempts, use more aggressive killing
                        if attempt > 0:
                            try:
                                result = subprocess.run(['lsof', '-ti', f':{port}'],
                                                      capture_output=True, text=True, timeout=5)
                                if result.returncode == 0 and result.stdout.strip():
                                    pids = result.stdout.strip().split('\n')
                                    for pid in pids:
                                        if pid.strip():
                                            print(f"[{self.service_name}] 💀 Force killing PID {pid} on port {port}")
                                            subprocess.run(['kill', '-9', pid], timeout=5)
                            except Exception as e:
                                print(f"[{self.service_name}] ⚠️ Error force killing processes on port {port}: {e}")
                else:
                    print(f"[{self.service_name}] ❌ Failed to kill processes on port {port} (attempt {attempt + 1})")

            # Final check
            if self.is_port_free(port):
                print(f"[{self.service_name}] ✅ Port {port} finally freed after aggressive resolution")
                return True
            else:
                print(f"[{self.service_name}] ❌ Failed to free port {port} after all attempts")
                return False

        except Exception as e:
            print(f"[{self.service_name}] ❌ Error resolving port conflict for port {port}: {e}")
            return False
    
    def kill_process_on_port(self, port: int, force: bool = False) -> bool:
        """Kill the process using a port"""
        try:
            print(f"[{self.service_name}] 🔍 Checking port {port}...")
            
            proc_info = self.get_process_on_port(port)
            if not proc_info:
                print(f"[{self.service_name}] ✅ Port {port} is free")
                return True
            
            pid = proc_info['pid']
            name = proc_info['name']
            cmdline = proc_info['cmdline']
            
            print(f"[{self.service_name}] 🎯 Found process on port {port}: PID {pid} ({name})")
            print(f"[{self.service_name}] 📋 Command: {cmdline[:100]}...")
            
            # Try graceful termination first
            try:
                proc_info['process'].terminate()
                proc_info['process'].wait(timeout=5)
                print(f"[{self.service_name}] ✅ Gracefully terminated PID {pid}")
                return True
            except psutil.TimeoutExpired:
                if force:
                    proc_info['process'].kill()
                    print(f"[{self.service_name}] 🔥 Force killed PID {pid}")
                    return True
                else:
                    print(f"[{self.service_name}] ⚠️ Process {pid} did not terminate gracefully")
                    return False
            except psutil.NoSuchProcess:
                print(f"[{self.service_name}] ✅ Process {pid} already terminated")
                return True
                
        except Exception as e:
            print(f"[{self.service_name}] ❌ Error killing process on port {port}: {e}")
            
            # Fallback to system commands
            try:
                result = subprocess.run(['lsof', '-ti', f':{port}'], 
                                      capture_output=True, text=True, timeout=5)
                if result.stdout.strip():
                    pids = result.stdout.strip().split('\n')
                    for pid in pids:
                        if pid.strip():
                            subprocess.run(['kill', '-9' if force else '-15', pid.strip()], 
                                         capture_output=True, timeout=5)
                            print(f"[{self.service_name}] 🔪 Killed PID {pid} on port {port}")
                    return True
            except Exception as fallback_error:
                print(f"[{self.service_name}] ❌ Fallback kill failed: {fallback_error}")
            
            return False
    
    def ensure_port_free(self, service_name: str, force: bool = True) -> int:
        """🔌 DHCP-LIKE PORT ALLOCATION - Ensure a service gets a free port"""

        # 🔌 STEP 1: Try to allocate port using DHCP-like system
        allocated_port = self.allocate_port(service_name)

        self._print_if_new(f"ensure_port_{allocated_port}", f"🔌 Ensuring port {allocated_port} is free for {service_name}...")

        # 🔌 STEP 2: Check if allocated port is actually free
        if self.is_port_free(allocated_port):
            self._print_if_new(f"port_free_{allocated_port}", f"✅ Port {allocated_port} is already free")
            return allocated_port

        # 🔌 STEP 3: Port is in use - check if it's our own service
        processes = self._get_processes_on_port(allocated_port)
        our_process = False

        for proc in processes:
            if service_name.upper() in proc.get('name', '').upper():
                our_process = True
                break

        if our_process:
            self._print_if_new(f"our_process_{allocated_port}", f"🔄 Port {allocated_port} is used by our own {service_name} - allowing")
            return allocated_port

        # 🔌 STEP 4: Port conflict detected - resolve it aggressively
        self._print_if_new(f"port_conflict_{allocated_port}", f"🚨 Port {allocated_port} conflict for {service_name} - resolving aggressively...")

        # Try multiple times with increasing force
        for attempt in range(3):
            if self.kill_process_on_port(allocated_port, force=True):
                # Wait longer for the port to be released
                time.sleep(2 + attempt)

                if self.is_port_free(allocated_port):
                    print(f"[{self.service_name}] ✅ Port {allocated_port} is now free for {service_name} (attempt {attempt + 1})")
                    return allocated_port
                else:
                    print(f"[{self.service_name}] ⚠️ Port {allocated_port} still in use after cleanup attempt {attempt + 1}")

                    # Try killing with SIGKILL on subsequent attempts
                    if attempt > 0:
                        try:
                            result = subprocess.run(['lsof', '-ti', f':{allocated_port}'],
                                                  capture_output=True, text=True, timeout=5)
                            if result.returncode == 0 and result.stdout.strip():
                                pids = result.stdout.strip().split('\n')
                                for pid in pids:
                                    if pid.strip():
                                        print(f"[{self.service_name}] 🔧 Force killing PID {pid} on port {allocated_port}")
                                        subprocess.run(['kill', '-9', pid], timeout=5)
                        except Exception as e:
                            print(f"[{self.service_name}] ⚠️ Error force killing processes on port {allocated_port}: {e}")
            else:
                print(f"[{self.service_name}] ❌ Failed to kill processes on port {allocated_port} (attempt {attempt + 1})")

                # 🔌 STEP 5: Find alternative port
                alternative_port = self._find_available_port(service_name)
                if alternative_port:
                    print(f"[{self.service_name}] 🔄 Using alternative port {alternative_port} for {service_name}")

                    # Update lease to new port
                    self.port_leases[service_name] = {
                        'port': alternative_port,
                        'assigned_at': datetime.now().isoformat(),
                        'reason': f'conflict_resolution_from_{allocated_port}'
                    }
                    self.allocated_ports.add(alternative_port)
                    self._save_port_leases()

                    return alternative_port
                raise RuntimeError(f"Could not free port {port} for {service_name}")
        else:
            raise RuntimeError(f"Failed to kill process on port {port} for {service_name}")
    
    def cleanup_all_deeplica_ports(self, force: bool = True) -> Dict[str, bool]:
        """Clean up all Deeplica ports"""
        print(f"[{self.service_name}] 🧹 Cleaning up all Deeplica ports...")
        
        results = {}
        for service, port in self.OFFICIAL_PORTS.items():
            try:
                if not self.is_port_free(port):
                    print(f"[{self.service_name}] 🔧 Cleaning port {port} ({service})...")
                    success = self.kill_process_on_port(port, force=force)
                    results[service] = success
                else:
                    results[service] = True
            except Exception as e:
                print(f"[{self.service_name}] ❌ Error cleaning {service} port {port}: {e}")
                results[service] = False
        
        successful = sum(1 for success in results.values() if success)
        total = len(results)
        print(f"[{self.service_name}] 📊 Port cleanup: {successful}/{total} ports cleaned successfully")
        
        return results
    
    def get_all_ports(self) -> Dict[str, int]:
        """Get all official port assignments"""
        return self.OFFICIAL_PORTS.copy()
    
    def validate_port_assignment(self, service_name: str, port: int) -> bool:
        """Validate that a service is using its correct port"""
        expected_port = self.get_port(service_name)
        return port == expected_port

# Global instance for easy access
port_manager = DeepLicaPortManager()

# Convenience functions
def get_service_port(service_name: str) -> int:
    """Get the official port for a service"""
    return port_manager.get_port(service_name)

def ensure_service_port_free(service_name: str, force: bool = True) -> int:
    """Ensure a service's port is free"""
    return port_manager.ensure_port_free(service_name, force=force)

def cleanup_all_ports(force: bool = True) -> Dict[str, bool]:
    """Clean up all Deeplica ports"""
    return port_manager.cleanup_all_deeplica_ports(force=force)

def is_port_free(port: int) -> bool:
    """Check if a port is free"""
    return port_manager.is_port_free(port)

def update_port_configuration(new_ports: Dict[str, int]):
    """Update port configuration (admin function)"""
    return port_manager.update_port_configuration(new_ports)

def reset_to_factory_defaults():
    """Reset all ports to factory defaults (admin function)"""
    return port_manager.reset_to_factory_defaults()

def get_factory_defaults() -> Dict[str, int]:
    """Get factory default port configuration"""
    return port_manager.get_factory_defaults()

def is_backend_port_constant(service_name: str) -> bool:
    """Check if this is the constant Backend API port"""
    return port_manager.is_backend_port_constant(service_name)

def is_port_configurable(service_name: str) -> bool:
    """Check if a service port is configurable - ALL ports are now configurable"""
    return port_manager.is_port_configurable(service_name)

def get_all_configurable_services() -> Dict[str, int]:
    """Get all services - ALL ports are now configurable"""
    return port_manager.get_all_configurable_services()

def get_all_ports() -> Dict[str, int]:
    """Get all official port assignments"""
    return port_manager.get_all_ports()

# Service convenience functions - ALL use port manager
def get_backend_port() -> int:
    """Get Backend API port from port manager"""
    return get_service_port("backend")

def get_ngrok_api_port() -> int:
    """Get Ngrok API port from port manager"""
    return get_service_port("ngrok-api")

def get_ngrok_tunnel_port() -> int:
    """Get Ngrok tunnel port from port manager"""
    return get_service_port("ngrok-tunnel")

def get_twilio_port() -> int:
    """Get Twilio echo bot port from port manager"""
    return get_service_port("twilio-echo-bot")

def get_webhook_port() -> int:
    """Get webhook server port from port manager"""
    return get_service_port("webhook")

def get_mongodb_port() -> int:
    """Get MongoDB proxy port from port manager"""
    return get_service_port("mongodb-proxy")

# ========================================
# HOST MANAGEMENT FUNCTIONS
# ========================================

def get_service_host(service_name: str = None) -> str:
    """
    Get the host for a service from centralized configuration.

    Args:
        service_name: Name of the service (optional, defaults to DEFAULT)

    Returns:
        Host string (e.g., "0.0.0.0", "127.0.0.1")

    Environment Variables:
        DEEPLICA_HOST: Override default host for all services
        {SERVICE}_HOST: Override host for specific service (e.g., BACKEND_HOST)
    """
    # Check for service-specific environment override
    if service_name:
        service_env_var = f"{service_name.upper().replace('-', '_')}_HOST"
        if service_env_var in os.environ:
            return os.environ[service_env_var]

    # Check for global environment override
    if "DEEPLICA_HOST" in os.environ:
        return os.environ["DEEPLICA_HOST"]

    # Try to load from database settings
    try:
        from shared.system_settings import get_system_host_config
        import asyncio

        # For synchronous calls, we'll use a simple approach
        # In production, this should be cached or loaded at startup
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If we're in an async context, we can't await here
                # Fall back to default
                pass
            else:
                host_config = loop.run_until_complete(get_system_host_config())
                return host_config.get("default_host", "0.0.0.0")
        except:
            pass
    except:
        pass

    # Return default host
    port_manager = DeepLicaPortManager()
    return port_manager.OFFICIAL_HOSTS["DEFAULT"]


def get_localhost() -> str:
    """Get localhost address for client connections."""
    # Check for environment override
    if "DEEPLICA_LOCALHOST" in os.environ:
        return os.environ["DEEPLICA_LOCALHOST"]

    port_manager = DeepLicaPortManager()
    return port_manager.OFFICIAL_HOSTS["LOCALHOST"]


def get_external_host() -> str:
    """Get external host address for external access."""
    # Check for environment override
    if "DEEPLICA_EXTERNAL_HOST" in os.environ:
        return os.environ["DEEPLICA_EXTERNAL_HOST"]

    port_manager = DeepLicaPortManager()
    return port_manager.OFFICIAL_HOSTS["EXTERNAL"]


def set_default_host(host: str) -> None:
    """
    Set the default host for all services.
    This would typically be called from admin settings.

    Args:
        host: New default host (e.g., "0.0.0.0", "127.0.0.1")
    """
    # For now, set environment variable
    # In the future, this could write to a config file
    os.environ["DEEPLICA_HOST"] = host
    print(f"🌐 Default host set to: {host}")


def get_service_url(service_name: str, include_protocol: bool = True) -> str:
    """
    Get the complete URL for a service.

    Args:
        service_name: Name of the service
        include_protocol: Whether to include http:// prefix

    Returns:
        Complete service URL (e.g., "http://0.0.0.0:8888")
    """
    host = get_service_host(service_name)
    port = get_service_port(service_name)

    if include_protocol:
        return f"http://{host}:{port}"
    else:
        return f"{host}:{port}"


# ========================================
# MAIN FUNCTION FOR CLI USAGE
# ========================================

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Deeplica Port Manager")
    parser.add_argument("--service", help="Get port for specific service")
    parser.add_argument("--list", action="store_true", help="List all port assignments")
    parser.add_argument("--cleanup", action="store_true", help="Cleanup all ports")
    parser.add_argument("--check", type=int, help="Check if specific port is free")

    args = parser.parse_args()

    if args.service:
        try:
            port = get_service_port(args.service)
            print(f"{args.service}: {port}")
        except ValueError as e:
            print(f"Error: {e}")
            sys.exit(1)
    elif args.list:
        port_manager = DeepLicaPortManager()
        print("Deeplica Port Assignments:")
        for service, port in port_manager.get_all_ports().items():
            status = "FREE" if is_port_free(port) else "IN USE"
            print(f"  {service:25} {port:5} ({status})")
    elif args.cleanup:
        cleanup_all_ports()
    elif args.check:
        free = is_port_free(args.check)
        print(f"Port {args.check}: {'FREE' if free else 'IN USE'}")
    else:
        parser.print_help()
