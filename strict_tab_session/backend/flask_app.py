"""
🔐 STRICT SINGLE-TAB SESSION ENFORCEMENT - Flask Application

Complete Flask application implementing strict single-tab session enforcement
with comprehensive security validation, CSRF protection, and audit logging.

Security Features:
- Tab ID validation on every request
- Automatic session invalidation on violations
- CSRF token validation
- Rate limiting
- Security audit logging
- Secure cookie handling

<AUTHOR> Security Team
@version 1.0.0
@date 2025-07-11
"""

import os
import json
import redis
from datetime import datetime, timedelta
from flask import Flask, request, jsonify, render_template, redirect, url_for, session, make_response
from werkzeug.security import check_password_hash, generate_password_hash
from werkzeug.exceptions import BadRequest, Unauthorized, TooManyRequests

from tab_session_manager import <PERSON>rict<PERSON>ab<PERSON>essionManager, SecurityError, require_valid_tab_session, rate_limit

# Initialize Flask app
app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', 'your-super-secret-key-change-in-production')

# Configure secure session cookies
app.config.update(
    SESSION_COOKIE_SECURE=True,  # HTTPS only
    SESSION_COOKIE_HTTPONLY=True,  # No JavaScript access
    SESSION_COOKIE_SAMESITE='Strict',  # CSRF protection
    PERMANENT_SESSION_LIFETIME=timedelta(minutes=30)
)

# Initialize Redis (optional)
try:
    redis_client = redis.Redis(
        host=os.environ.get('REDIS_HOST', 'localhost'),
        port=int(os.environ.get('REDIS_PORT', 6379)),
        db=int(os.environ.get('REDIS_DB', 0)),
        decode_responses=True
    )
    redis_client.ping()  # Test connection
except:
    redis_client = None
    print("⚠️ Redis not available, using in-memory storage")

# Initialize session manager
session_manager = StrictTabSessionManager(redis_client=redis_client)

# Demo user database (replace with real database)
DEMO_USERS = {
    'admin': {
        'password_hash': generate_password_hash('admin123'),
        'user_id': 'user_admin_001',
        'role': 'admin'
    },
    'demo': {
        'password_hash': generate_password_hash('demo123'),
        'user_id': 'user_demo_001',
        'role': 'user'
    }
}

@app.before_request
def before_request():
    """Attach session manager to request context"""
    request.session_manager = session_manager

@app.errorhandler(401)
def unauthorized(error):
    """Handle unauthorized access"""
    return jsonify({
        'error': 'Unauthorized',
        'message': 'For your own safety and privacy, simultaneous sessions are not allowed. Please authenticate again.',
        'redirect': '/login'
    }), 401

@app.errorhandler(429)
def rate_limit_exceeded(error):
    """Handle rate limit exceeded"""
    return jsonify({
        'error': 'Rate Limit Exceeded',
        'message': 'Too many requests. Please try again later.'
    }), 429

@app.route('/')
def index():
    """Home page - redirect to login if not authenticated"""
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
@rate_limit(max_requests=10, window=300)  # 10 login attempts per 5 minutes
def login():
    """Login page with strict tab session enforcement"""
    if request.method == 'GET':
        # Generate CSRF token for the form
        csrf_token = session_manager.generate_csrf_token()
        session['csrf_token'] = csrf_token
        
        return render_template('login.html', csrf_token=csrf_token)
    
    elif request.method == 'POST':
        try:
            # Get form data
            username = request.form.get('username', '').strip()
            password = request.form.get('password', '')
            tab_id = request.headers.get('X-Tab-ID')
            csrf_token = request.form.get('csrf_token')
            
            # Validate required fields
            if not all([username, password, tab_id, csrf_token]):
                return jsonify({
                    'success': False,
                    'message': 'Missing required fields'
                }), 400
            
            # Validate CSRF token
            if csrf_token != session.get('csrf_token'):
                return jsonify({
                    'success': False,
                    'message': 'Invalid CSRF token'
                }), 403
            
            # Validate Tab ID format
            if not session_manager.validate_tab_id(tab_id):
                return jsonify({
                    'success': False,
                    'message': 'Invalid tab identifier'
                }), 400
            
            # Authenticate user
            user = DEMO_USERS.get(username)
            if not user or not check_password_hash(user['password_hash'], password):
                return jsonify({
                    'success': False,
                    'message': 'Invalid username or password'
                }), 401
            
            # Create tab-bound session
            try:
                session_token, new_csrf_token = session_manager.create_session(
                    tab_id=tab_id,
                    user_id=user['user_id'],
                    username=username,
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent', '')
                )
                
                # Set secure session cookie
                response = make_response(jsonify({
                    'success': True,
                    'message': 'Login successful',
                    'session_token': session_token,
                    'csrf_token': new_csrf_token,
                    'redirect': '/dashboard'
                }))
                
                response.set_cookie(
                    'session_token',
                    session_token,
                    max_age=1800,  # 30 minutes
                    secure=True,
                    httponly=True,
                    samesite='Strict'
                )
                
                return response
                
            except SecurityError as e:
                return jsonify({
                    'success': False,
                    'message': str(e),
                    'security_violation': True
                }), 403
            
        except Exception as e:
            app.logger.error(f"Login error: {e}")
            return jsonify({
                'success': False,
                'message': 'Login failed due to server error'
            }), 500

@app.route('/dashboard')
@require_valid_tab_session
@rate_limit(max_requests=100, window=300)
def dashboard():
    """Protected dashboard page"""
    session_token = request.headers.get('X-Session-Token')
    session_info = session_manager.get_session_info(session_token)
    
    return render_template('dashboard.html', session_info=session_info)

@app.route('/api/validate-session', methods=['POST'])
@rate_limit(max_requests=200, window=300)  # Higher limit for validation requests
def validate_session():
    """Validate tab-bound session"""
    try:
        # Get headers
        tab_id = request.headers.get('X-Tab-ID')
        session_token = request.headers.get('X-Session-Token')
        csrf_token = request.headers.get('X-CSRF-Token')
        
        if not all([tab_id, session_token]):
            return jsonify({
                'valid': False,
                'reason': 'Missing required headers'
            }), 400
        
        # Validate session
        is_valid, reason = session_manager.validate_session(
            tab_id=tab_id,
            session_token=session_token,
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        return jsonify({
            'valid': is_valid,
            'reason': reason,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        app.logger.error(f"Session validation error: {e}")
        return jsonify({
            'valid': False,
            'reason': 'Validation error'
        }), 500

@app.route('/api/logout', methods=['POST'])
@require_valid_tab_session
def logout():
    """Logout and invalidate session"""
    try:
        session_token = request.headers.get('X-Session-Token')
        
        # Invalidate session
        success = session_manager.invalidate_session(
            session_token, 
            "User logout"
        )
        
        if success:
            response = make_response(jsonify({
                'success': True,
                'message': 'Logged out successfully'
            }))
            
            # Clear session cookie
            response.set_cookie('session_token', '', expires=0)
            return response
        else:
            return jsonify({
                'success': False,
                'message': 'Logout failed'
            }), 500
            
    except Exception as e:
        app.logger.error(f"Logout error: {e}")
        return jsonify({
            'success': False,
            'message': 'Logout failed due to server error'
        }), 500

@app.route('/api/session-info')
@require_valid_tab_session
def get_session_info():
    """Get current session information"""
    session_token = request.headers.get('X-Session-Token')
    session_info = session_manager.get_session_info(session_token)
    
    return jsonify({
        'session_info': session_info,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/admin/audit-log')
@require_valid_tab_session
def get_audit_log():
    """Get security audit log (admin only)"""
    # Note: In production, add proper admin role checking
    session_token = request.headers.get('X-Session-Token')
    session_info = session_manager.get_session_info(session_token)
    
    if not session_info:
        return jsonify({'error': 'Session not found'}), 404
    
    # For demo purposes, return sample audit data
    # In production, implement proper audit log retrieval
    audit_data = {
        'events': [
            {
                'timestamp': datetime.now().isoformat(),
                'event_type': 'session_created',
                'user': session_info.get('username'),
                'details': 'Session created successfully'
            }
        ],
        'total_events': 1,
        'timestamp': datetime.now().isoformat()
    }
    
    return jsonify(audit_data)

# Security middleware
@app.after_request
def after_request(response):
    """Add security headers to all responses"""
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
    response.headers['Content-Security-Policy'] = "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"
    
    return response

if __name__ == '__main__':
    # Development server (use proper WSGI server in production)
    app.run(
        host='127.0.0.1',
        port=5000,
        debug=False,  # Never use debug=True in production
        ssl_context='adhoc'  # Use proper SSL certificates in production
    )
