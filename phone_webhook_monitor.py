#!/usr/bin/env python3
"""
🎤 DEEPLICA PHONE WEBHOOK MONITOR
Simple webhook server that logs conversations and displays them
Uses dynamic port management - NO HARDCODED PORTS!
"""

import os
import sys
import json
import datetime
from pathlib import Path
from flask import Flask, request, Response

# Add project root to path
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

# Import port management
from shared.port_manager import get_service_port, ensure_service_port_free
import requests
import asyncio
import aiohttp

app = Flask(__name__)

# Global conversation log
conversation_log = []

# User information for chat integration
ADMIN_USER = "Eran"  # From memories - admin user name

def send_to_deepchat(sender, message, call_sid="Unknown"):
    """Send conversation message to DeepChat interface"""
    try:
        web_chat_port = get_service_port('web-chat')

        # Format message for DeepChat
        if sender == 'DEEPLICA':
            chat_message = f"📞 Phone Call: {message}"
            chat_sender = "deeplica"
        elif sender == 'USER':
            chat_message = f"📞 {ADMIN_USER} (Phone): {message}"
            chat_sender = ADMIN_USER.lower()
        else:
            chat_message = f"📞 System: {message}"
            chat_sender = "system"

        # Send to Web Chat API (if available)
        try:
            response = requests.post(
                f"http://localhost:{web_chat_port}/api/inject_message",
                json={
                    "sender": chat_sender,
                    "message": chat_message,
                    "source": "phone_call",
                    "call_sid": call_sid
                },
                timeout=2
            )
            if response.status_code == 200:
                print(f"✅ Sent to DeepChat: {chat_sender} - {chat_message[:50]}...")
            else:
                print(f"⚠️ DeepChat API error: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"⚠️ Could not send to DeepChat: {e}")

    except Exception as e:
        print(f"❌ Error sending to DeepChat: {e}")

def log_message(speaker, message, call_sid="Unknown"):
    """Log a conversation message"""
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    log_entry = {
        'timestamp': timestamp,
        'speaker': speaker,
        'message': message,
        'call_sid': call_sid
    }

    conversation_log.append(log_entry)

    # Print to console for real-time monitoring
    if speaker == 'DEEPLICA':
        print(f"🎤 DEEPLICA SAYS: {message}")
    elif speaker == 'USER':
        print(f"👤 USER SAYS: {message}")
    else:
        print(f"📋 {speaker}: {message}")

    # Send to DeepChat
    send_to_deepchat(speaker, message, call_sid)

@app.route('/webhook/voice', methods=['POST', 'GET'])
def handle_voice_call():
    """Handle incoming voice call from Twilio"""
    print("\n🚨 WEBHOOK CALLED! 🚨")
    print(f"Method: {request.method}")
    print(f"Headers: {dict(request.headers)}")
    print(f"Form data: {dict(request.form)}")

    try:
        # Get call information
        from_number = request.form.get('From', 'Unknown')
        to_number = request.form.get('To', 'Unknown')
        call_sid = request.form.get('CallSid', 'Unknown')

        print(f"\n📞 NEW CALL RECEIVED: {from_number} -> {to_number} (SID: {call_sid})")
        print("🎯 PROCESSING CALL - GENERATING TWIML RESPONSE")
        log_message('SYSTEM', f"Call started: {from_number} -> {to_number}", call_sid)
        
        # Create TwiML response
        twiml_response = '''<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="alice" language="en-US">
        Hello Eran! This is DEEPLICA calling for a test. 
        Please wait 3 seconds, then say your name Eran 5 times clearly. 
        Starting in 3... 2... 1...
    </Say>
    <Pause length="3"/>
    <Say voice="alice" language="en-US">
        Now please say Eran 5 times. Begin now.
    </Say>
    <Record action="/webhook/recording" method="POST" maxLength="15" playBeep="false" transcribe="true" transcribeCallback="/webhook/transcription"/>
    <Say voice="alice" language="en-US">
        Thank you Eran! DEEPLICA test call completed successfully. Goodbye!
    </Say>
</Response>'''
        
        # Log what DEEPLICA will say - EXACTLY what the caller hears
        print("\n" + "="*80)
        print("🎤 PHONE CALL AUDIO - WHAT ERAN HEARS:")
        print("="*80)

        message1 = "Hello Eran! This is DEEPLICA calling for a test. Please wait 3 seconds, then say your name Eran 5 times clearly. Starting in 3... 2... 1..."
        message2 = "Now please say Eran 5 times. Begin now."
        message3 = "Thank you Eran! DEEPLICA test call completed successfully. Goodbye!"

        print(f"🔊 DEEPLICA SAYS: {message1}")
        print(f"⏸️  [3 SECOND PAUSE]")
        print(f"🔊 DEEPLICA SAYS: {message2}")
        print(f"🎙️  [RECORDING USER FOR 15 SECONDS]")
        print(f"🔊 DEEPLICA SAYS: {message3}")
        print("="*80)

        log_message('DEEPLICA', message1, call_sid)
        log_message('DEEPLICA', "[3 second pause]", call_sid)
        log_message('DEEPLICA', message2, call_sid)
        log_message('SYSTEM', "[Recording user for 15 seconds]", call_sid)
        log_message('DEEPLICA', message3, call_sid)
        
        print(f"✅ TwiML response sent for call {call_sid}")
        
        return Response(twiml_response, mimetype='text/xml')
        
    except Exception as e:
        print(f"❌ Error handling voice call: {e}")
        log_message('ERROR', f"Error handling voice call: {e}", call_sid)
        
        # Fallback response
        fallback_twiml = '''<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="alice" language="en-US">
        Hello Eran! This is DEEPLICA. Please wait 3 seconds then say Eran 5 times. Thank you!
    </Say>
    <Pause length="18"/>
    <Say voice="alice" language="en-US">Goodbye!</Say>
</Response>'''
        
        return Response(fallback_twiml, mimetype='text/xml')

@app.route('/webhook/recording', methods=['POST'])
def handle_recording():
    """Handle recording completion"""
    try:
        recording_url = request.form.get('RecordingUrl', 'No URL')
        recording_duration = request.form.get('RecordingDuration', '0')
        call_sid = request.form.get('CallSid', 'Unknown')
        
        log_message('SYSTEM', f"Recording completed: {recording_duration}s - URL: {recording_url}", call_sid)
        print(f"🎵 Recording completed: {recording_duration}s")
        
        # Continue with call flow
        return Response('<?xml version="1.0" encoding="UTF-8"?><Response></Response>', mimetype='text/xml')
        
    except Exception as e:
        print(f"❌ Error handling recording: {e}")
        return Response('<?xml version="1.0" encoding="UTF-8"?><Response></Response>', mimetype='text/xml')

@app.route('/webhook/transcription', methods=['POST'])
def handle_transcription():
    """Handle speech transcription"""
    try:
        transcription_text = request.form.get('TranscriptionText', 'No transcription')
        transcription_status = request.form.get('TranscriptionStatus', 'Unknown')
        call_sid = request.form.get('CallSid', 'Unknown')
        
        if transcription_status == 'completed' and transcription_text and transcription_text != 'No transcription':
            log_message('USER', transcription_text, call_sid)
            
            # Count how many times "Eran" was said
            eran_count = transcription_text.lower().count('eran')
            log_message('ANALYSIS', f"User said 'Eran' {eran_count} times out of requested 5", call_sid)
            
            print(f"👤 USER SAID: {transcription_text}")
            print(f"📊 ANALYSIS: Found 'Eran' {eran_count} times")
        else:
            log_message('USER', f"[Transcription failed: {transcription_status}]", call_sid)
            print(f"❌ Transcription failed: {transcription_status}")
        
        # Final message from DEEPLICA
        log_message('DEEPLICA', "Thank you Eran! DEEPLICA test call completed successfully. Goodbye!", call_sid)
        log_message('SYSTEM', "Call ended", call_sid)
        
        return Response('OK', mimetype='text/plain')
        
    except Exception as e:
        print(f"❌ Error handling transcription: {e}")
        return Response('Error', mimetype='text/plain')

@app.route('/conversation', methods=['GET'])
def view_conversation():
    """View conversation log"""
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>🎤 DEEPLICA Phone Conversation</title>
        <meta http-equiv="refresh" content="3">
        <style>
            body {{ font-family: monospace; background: #000; color: #0f0; padding: 20px; }}
            .header {{ color: #0ff; font-size: 1.5em; margin-bottom: 20px; }}
            .entry {{ margin: 10px 0; padding: 10px; border-left: 3px solid #333; }}
            .deeplica {{ border-left-color: #0f0; background: #001100; }}
            .user {{ border-left-color: #f80; background: #110800; }}
            .system {{ border-left-color: #08f; background: #000811; }}
            .analysis {{ border-left-color: #fa0; background: #111100; }}
            .error {{ border-left-color: #f00; background: #110000; }}
            .timestamp {{ color: #888; font-size: 0.8em; }}
        </style>
    </head>
    <body>
        <div class="header">🎤 DEEPLICA Phone Conversation Monitor</div>
        <div>Total Messages: {len(conversation_log)}</div>
        <div>Last Updated: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</div>
        <hr>
    """
    
    for entry in conversation_log:
        speaker_class = entry['speaker'].lower()
        html += f"""
        <div class="entry {speaker_class}">
            <div class="timestamp">{entry['timestamp']} - {entry['call_sid']}</div>
            <strong>{entry['speaker']}:</strong> {entry['message']}
        </div>
        """
    
    if not conversation_log:
        html += "<div class='entry system'>No conversation data yet. Waiting for phone calls...</div>"
    
    html += "</body></html>"
    return html

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "phone-webhook-monitor", "messages": len(conversation_log)}, 200

@app.route('/', methods=['GET'])
def root():
    """Root endpoint"""
    return view_conversation()

if __name__ == '__main__':
    # Get dynamic port from port manager
    service_port = get_service_port('webhook-server')

    print("🎤 DEEPLICA PHONE WEBHOOK MONITOR")
    print("=" * 50)
    print(f"🚀 Starting conversation monitor on port {service_port} (from port manager)...")
    print("📞 Webhook: /webhook/voice")
    print(f"🌐 Monitor: http://localhost:{service_port}/conversation")
    print(f"📊 Health: http://localhost:{service_port}/health")
    print()
    print("🎯 CONVERSATION TRACKING:")
    print("   • DEEPLICA messages (what system says)")
    print("   • USER responses (transcribed speech)")
    print("   • SYSTEM events (call status, recordings)")
    print("   • ANALYSIS (speech analysis results)")
    print()
    print("🔥 SERVER STARTING - WILL NOT TERMINATE")
    print("🔥 ALL PHONE CALL AUDIO WILL BE LOGGED TO CONSOLE")
    print("🔥 WEBHOOK READY FOR TWILIO CALLS")
    print(f"🔌 Using dynamic port {service_port} from port manager")
    print()

    # Ensure port is free before starting
    try:
        ensure_service_port_free('webhook-server', force=True)
        print(f"✅ Port {service_port} is ready for phone webhook")
    except Exception as e:
        print(f"⚠️ Port cleanup warning: {e}")

    try:
        app.run(
            host='0.0.0.0',
            port=service_port,
            debug=False,
            threaded=True,
            use_reloader=False
        )
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        print("🔄 Restarting server...")
        # Keep trying to restart
        import time
        time.sleep(2)
        os.system(f"python3 {__file__}")
