#!/usr/bin/env python3
"""
🔍 Comprehensive Port Verification Script
Scans the entire codebase to ensure no hardcoded ports remain and all services use port manager.
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Dict, <PERSON><PERSON>

def scan_for_hardcoded_ports():
    """Scan all files for hardcoded port references"""
    print("🔍 Scanning for hardcoded ports in the codebase...")
    
    # Directories to scan
    scan_dirs = [
        "backend", "dispatcher", "agents", "watchdog", "web_chat", "cli", 
        "orchestrator", "shared", "scripts", "tests", "twilio-echo-bot"
    ]
    
    # Files to scan in root
    root_files = [
        "start_all_services.py", "startup_orchestrator.py", "start_ngrok.py",
        "webhook_server.py", "validate_ports.py"
    ]
    
    # Port patterns to look for (excluding comments and documentation)
    port_patterns = [
        r'(?<!#.*):80[0-9][0-9](?!\s*#)',  # :8xxx not in comments
        r'(?<!#.*)localhost:80[0-9][0-9](?!\s*#)',  # localhost:8xxx not in comments
        r'(?<!#.*)PORT\s*=\s*80[0-9][0-9](?!\s*#)',  # PORT=8xxx not in comments
        r'(?<!#.*)port\s*=\s*80[0-9][0-9](?!\s*#)',  # port=8xxx not in comments
        r'(?<!#.*)"80[0-9][0-9]"(?!\s*#)',  # "8xxx" not in comments
        r'(?<!#.*)ngrok http 80[0-9][0-9](?!\s*#)',  # ngrok http 8xxx not in comments
    ]
    
    # Allowed hardcoded ports (these are acceptable)
    allowed_hardcoded = {
        '8888',  # Backend API constant port
        'get_service_port("twilio-echo-bot")',  # Twilio echo bot constant port
        'get_service_port("webhook-server")',  # Webhook server constant port
        'get_service_port("ngrok-api")',  # Ngrok API constant port
        'get_service_port("ngrok-tunnel")',  # Ngrok tunnel constant port
    }
    
    issues = []
    
    # Scan directories
    for scan_dir in scan_dirs:
        if Path(scan_dir).exists():
            for root, dirs, files in os.walk(scan_dir):
                for file in files:
                    if file.endswith(('.py', '.sh', '.json', '.env')):
                        file_path = Path(root) / file
                        issues.extend(scan_file_for_ports(file_path, port_patterns, allowed_hardcoded))
    
    # Scan root files
    for file_name in root_files:
        file_path = Path(file_name)
        if file_path.exists():
            issues.extend(scan_file_for_ports(file_path, port_patterns, allowed_hardcoded))
    
    # Scan shell scripts
    for file_path in Path('.').glob('*.sh'):
        issues.extend(scan_file_for_ports(file_path, port_patterns, allowed_hardcoded))
    
    return issues

def scan_file_for_ports(file_path: Path, patterns: List[str], allowed: set) -> List[Dict]:
    """Scan a single file for hardcoded ports"""
    issues = []
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
        
        for line_num, line in enumerate(lines, 1):
            for pattern in patterns:
                matches = re.finditer(pattern, line)
                for match in matches:
                    # Extract the port number
                    port_match = re.search(r'80[0-9][0-9]|get_service_port("ngrok-api")', match.group())
                    if port_match:
                        port = port_match.group()
                        
                        # Skip if it's an allowed hardcoded port
                        if port in allowed:
                            continue
                        
                        # Skip if it's in a comment explaining port management
                        if 'port_manager' in line.lower() or 'managed by' in line.lower():
                            continue
                        
                        # Skip if it's in documentation or comments
                        if line.strip().startswith('#') or line.strip().startswith('//'):
                            continue
                        
                        issues.append({
                            'file': str(file_path),
                            'line': line_num,
                            'content': line.strip(),
                            'port': port,
                            'pattern': pattern
                        })
    
    except Exception as e:
        print(f"⚠️ Error scanning {file_path}: {e}")
    
    return issues

def check_port_manager_imports():
    """Check that all service files import port manager"""
    print("🔍 Checking port manager imports...")
    
    service_files = [
        "backend/app/main.py",
        "dispatcher/app/main.py",
        "agents/dialogue/app/main.py",
        "agents/planner/app/main.py",
        "agents/phone/app/main.py",
        "watchdog/main.py",
        "web_chat/main.py",
        "cli/main.py"
    ]
    
    missing_imports = []
    
    for service_file in service_files:
        file_path = Path(service_file)
        if file_path.exists():
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                
                has_import = (
                    'from shared.port_manager import' in content or
                    'import shared.port_manager' in content
                )
                
                if not has_import:
                    missing_imports.append(service_file)
            except Exception as e:
                print(f"⚠️ Error checking {service_file}: {e}")
    
    return missing_imports

def check_environment_files():
    """Check environment files for port references"""
    print("🔍 Checking environment files...")
    
    env_files = [
        "DEMO_MODE.env",
        "PRODUCTION_MODE.env",
        ".env.bak"
    ]
    
    issues = []
    
    for env_file in env_files:
        file_path = Path(env_file)
        if file_path.exists():
            try:
                with open(file_path, 'r') as f:
                    lines = f.readlines()
                
                for line_num, line in enumerate(lines, 1):
                    # Check for port assignments without comments
                    if re.search(r'_PORT=80[0-9][0-9]$', line.strip()):
                        issues.append({
                            'file': env_file,
                            'line': line_num,
                            'content': line.strip(),
                            'issue': 'Port assignment without port manager comment'
                        })
            except Exception as e:
                print(f"⚠️ Error checking {env_file}: {e}")
    
    return issues

def run_comprehensive_verification():
    """Run comprehensive verification of port management"""
    print("🔍 COMPREHENSIVE PORT VERIFICATION")
    print("=" * 60)
    
    all_issues = []
    
    # 1. Scan for hardcoded ports
    print("\n1. Scanning for hardcoded ports...")
    hardcoded_issues = scan_for_hardcoded_ports()
    if hardcoded_issues:
        print(f"❌ Found {len(hardcoded_issues)} hardcoded port issues:")
        for issue in hardcoded_issues:
            print(f"   📁 {issue['file']}:{issue['line']} - Port {issue['port']}")
            print(f"      {issue['content']}")
        all_issues.extend(hardcoded_issues)
    else:
        print("✅ No problematic hardcoded ports found")
    
    # 2. Check port manager imports
    print("\n2. Checking port manager imports...")
    missing_imports = check_port_manager_imports()
    if missing_imports:
        print(f"❌ Services missing port manager imports:")
        for service in missing_imports:
            print(f"   📁 {service}")
        all_issues.extend([{'type': 'missing_import', 'file': f} for f in missing_imports])
    else:
        print("✅ All services have port manager imports")
    
    # 3. Check environment files
    print("\n3. Checking environment files...")
    env_issues = check_environment_files()
    if env_issues:
        print(f"⚠️ Found {len(env_issues)} environment file issues:")
        for issue in env_issues:
            print(f"   📁 {issue['file']}:{issue['line']} - {issue['issue']}")
        # These are warnings, not critical issues
    else:
        print("✅ Environment files look good")
    
    # 4. Summary
    print("\n" + "=" * 60)
    critical_issues = len(hardcoded_issues) + len(missing_imports)
    
    if critical_issues == 0:
        print("🎉 VERIFICATION PASSED!")
        print("✅ All services use centralized port management")
        print("✅ No problematic hardcoded ports found")
        print("✅ Port manager integration is complete")
        return True
    else:
        print(f"❌ VERIFICATION FAILED!")
        print(f"Found {critical_issues} critical issues that need to be fixed")
        return False

if __name__ == "__main__":
    success = run_comprehensive_verification()
    sys.exit(0 if success else 1)
