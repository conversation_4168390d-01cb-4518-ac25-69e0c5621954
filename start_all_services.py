#!/usr/bin/env python3
"""
🚀 START ALL DEEPLICA SERVICES
Launches all Deeplica microservices in the correct order
"""

import os
import sys
import subprocess
import time
import asyncio
import signal
from datetime import datetime
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from shared.port_manager import get_service_port

class DeepplicaServiceLauncher:
    """Launches all Deeplica services in proper order"""
    
    def __init__(self):
        self.launcher_name = "START-ALL-SERVICES"
        self.is_stopping = False
        self.services = [
            {
                "name": "🐕 Watchdog",
                "command": ["python3", "watchdog/main.py"],
                "cwd": ".",
                "port": get_service_port("watchdog"),
                "wait_time": 3,
                "required": True
            },
            {
                "name": "🌐 Backend API", 
                "command": ["python3", "-m", "app.main"],
                "cwd": "backend",
                "port": get_service_port("backend"),
                "wait_time": 5,
                "required": True
            },
            {
                "name": "🎯 Dispatcher",
                "command": ["python3", "-m", "app.main"],
                "cwd": "dispatcher", 
                "port": get_service_port("dispatcher"),
                "wait_time": 3,
                "required": True
            },
            {
                "name": "💬 Dialogue Agent",
                "command": ["python3", "-m", "app.main"],
                "cwd": "agents/dialogue",
                "port": get_service_port("dialogue"),
                "wait_time": 2,
                "required": False
            },
            {
                "name": "🧠 Planner Agent",
                "command": ["python3", "-m", "app.main"],
                "cwd": "agents/planner",
                "port": get_service_port("planner"),
                "wait_time": 2,
                "required": False
            },
            {
                "name": "📞 Phone Agent",
                "command": ["python3", "-m", "app.main"],
                "cwd": "agents/phone",
                "port": get_service_port("phone"),
                "wait_time": 2,
                "required": False
            },
            {
                "name": "🖥️ CLI Terminal",
                "command": ["python3", "cli/main.py"],
                "cwd": ".",
                "port": None,
                "wait_time": 1,
                "required": False
            }
        ]
        self.processes = []

        # Set up signal handlers for graceful shutdown
        signal.signal(signal.SIGTERM, self.signal_handler)
        signal.signal(signal.SIGINT, self.signal_handler)

    def signal_handler(self, signum, frame):
        """Handle termination signals gracefully"""
        if not self.is_stopping:
            print(f"\n[{self.launcher_name}] 🛑 Received termination signal ({signum})")
            print(f"[{self.launcher_name}] 🛑 Initiating graceful shutdown...")
            self.is_stopping = True
            self.stop_all_services()
            sys.exit(0)

    def is_port_in_use(self, port):
        """Check if a port is already in use"""
        if not port:
            return False
        try:
            import socket
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                result = s.connect_ex(({get_localhost()}, port))
                return result == 0
        except:
            return False

    def start_service(self, service_config):
        """Start a single service in its own terminal"""
        name = service_config["name"]
        command = service_config["command"]
        cwd = service_config["cwd"]
        port = service_config.get("port")
        required = service_config.get("required", False)

        print(f"[{self.launcher_name}] 🚀 Starting {name} in separate terminal...")

        # Check if service is already running
        if port and self.is_port_in_use(port):
            print(f"[{self.launcher_name}] ⚠️ {name} already running on port {port}")
            return None

        try:
            # Set environment variables
            env = os.environ.copy()
            env["PYTHONPATH"] = os.path.abspath(".")
            if port:
                env["PORT"] = str(port)

            # Create terminal title
            terminal_title = f"Deeplica - {name}"

            # Detect platform and use appropriate terminal command
            import platform
            system = platform.system()

            if system == "Darwin":  # macOS
                # Use osascript to open new Terminal window
                script = f'''
                tell application "Terminal"
                    do script "cd '{os.path.abspath(cwd)}' && export PYTHONPATH='{os.path.abspath('.')}'"
                    delay 1
                    do script "{' '.join(command)}" in front window
                    set custom title of front window to "{terminal_title}"
                end tell
                '''
                terminal_process = subprocess.Popen(
                    ["osascript", "-e", script],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )

                # Wait a moment for terminal to start
                time.sleep(2)

                # Try to find the process by port
                service_pid = None
                for _ in range(10):  # Try for 10 seconds
                    if port and self.is_port_in_use(port):
                        service_pid = self.get_pid_for_port(port)
                        if service_pid:
                            break
                    time.sleep(1)

                if service_pid:
                    # Create a mock process object for tracking
                    import psutil
                    try:
                        actual_process = psutil.Process(service_pid)
                        self.processes.append({
                            "name": name,
                            "process": actual_process,
                            "port": port,
                            "required": required,
                            "terminal": True
                        })
                        print(f"[{self.launcher_name}] ✅ {name} started in new terminal (PID {service_pid})")
                        return actual_process
                    except psutil.NoSuchProcess:
                        pass

            elif system == "Linux":
                # Use gnome-terminal or xterm
                terminal_cmd = None
                if subprocess.run(["which", "gnome-terminal"], capture_output=True).returncode == 0:
                    terminal_cmd = [
                        "gnome-terminal",
                        "--title", terminal_title,
                        "--working-directory", os.path.abspath(cwd),
                        "--", "bash", "-c",
                        f"export PYTHONPATH='{os.path.abspath('.')}' && {' '.join(command)} && read -p 'Press Enter to close...'"
                    ]
                elif subprocess.run(["which", "xterm"], capture_output=True).returncode == 0:
                    terminal_cmd = [
                        "xterm", "-title", terminal_title,
                        "-e", f"cd '{os.path.abspath(cwd)}' && export PYTHONPATH='{os.path.abspath('.')}' && {' '.join(command)} && read -p 'Press Enter to close...'"
                    ]

                if terminal_cmd:
                    terminal_process = subprocess.Popen(terminal_cmd, env=env)
                    time.sleep(2)

                    # Try to find the process by port
                    service_pid = None
                    for _ in range(10):
                        if port and self.is_port_in_use(port):
                            service_pid = self.get_pid_for_port(port)
                            if service_pid:
                                break
                        time.sleep(1)

                    if service_pid:
                        import psutil
                        try:
                            actual_process = psutil.Process(service_pid)
                            self.processes.append({
                                "name": name,
                                "process": actual_process,
                                "port": port,
                                "required": required,
                                "terminal": True
                            })
                            print(f"[{self.launcher_name}] ✅ {name} started in new terminal (PID {service_pid})")
                            return actual_process
                        except psutil.NoSuchProcess:
                            pass

            # Fallback: start in background if terminal launch fails
            print(f"[{self.launcher_name}] ⚠️ Could not open separate terminal for {name}, starting in background...")
            process = subprocess.Popen(
                command,
                cwd=cwd,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            self.processes.append({
                "name": name,
                "process": process,
                "port": port,
                "required": required,
                "terminal": False
            })

            print(f"[{self.launcher_name}] ✅ {name} started in background (PID {process.pid})")
            return process

        except Exception as e:
            print(f"[{self.launcher_name}] ❌ Failed to start {name}: {e}")
            if required:
                print(f"[{self.launcher_name}] 🛑 Critical service failed - aborting startup")
                return False
            return None

    def get_pid_for_port(self, port):
        """Get the PID of the process using a specific port"""
        try:
            result = subprocess.run(
                ['lsof', '-ti', f':{port}'],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0 and result.stdout.strip():
                pid = int(result.stdout.strip().split('\n')[0])
                return pid
            return None

        except Exception:
            return None

    def wait_for_service(self, service_config, max_wait=30):
        """Wait for a service to become ready"""
        name = service_config["name"]
        port = service_config.get("port")
        
        if not port:
            return True
            
        print(f"[{self.launcher_name}] ⏳ Waiting for {name} to become ready...")
        
        start_time = time.time()
        while time.time() - start_time < max_wait:
            if self.is_port_in_use(port):
                print(f"[{self.launcher_name}] ✅ {name} is ready!")
                return True
            time.sleep(1)
        
        print(f"[{self.launcher_name}] ⚠️ {name} did not become ready within {max_wait}s")
        return False

    def start_all_services(self):
        """Start all services in proper order"""
        print("🚀 DEEPLICA SERVICE LAUNCHER")
        print("=" * 60)
        print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("🎯 Starting all Deeplica microservices in proper order")
        print("✅ Each service will run in background")
        print("🛑 Press Ctrl+C to stop all services")
        print("=" * 60)
        
        try:
            for i, service_config in enumerate(self.services, 1):
                name = service_config["name"]
                wait_time = service_config.get("wait_time", 2)
                required = service_config.get("required", False)
                
                print(f"\n[{self.launcher_name}] 📋 Step {i}/{len(self.services)}: {name}")
                
                # Start the service
                result = self.start_service(service_config)
                
                if result is False:  # Critical failure
                    return False
                
                if result:  # Service started successfully
                    # Wait for service to initialize
                    if wait_time > 0:
                        print(f"[{self.launcher_name}] ⏳ Allowing {wait_time}s for initialization...")
                        time.sleep(wait_time)
                    
                    # Verify service is ready
                    if service_config.get("port"):
                        self.wait_for_service(service_config, max_wait=15)
            
            # Final status
            print(f"\n[{self.launcher_name}] 📊 STARTUP SUMMARY:")
            running_count = 0
            terminal_count = 0
            for proc_info in self.processes:
                name = proc_info["name"]
                process = proc_info["process"]
                port = proc_info.get("port")
                in_terminal = proc_info.get("terminal", False)

                # Check if process is still running
                try:
                    if hasattr(process, 'poll'):
                        is_running = process.poll() is None
                    else:
                        # For psutil Process objects
                        is_running = process.is_running()
                except:
                    is_running = False

                if is_running:
                    status = "🟢 RUNNING"
                    running_count += 1
                    if in_terminal:
                        terminal_count += 1
                else:
                    status = "🔴 STOPPED"

                port_info = f" (Port {port})" if port else ""
                terminal_info = " [Terminal]" if in_terminal else " [Background]"
                print(f"[{self.launcher_name}]   {status}: {name}{port_info}{terminal_info}")

            success_rate = (running_count / len(self.processes)) * 100 if self.processes else 0
            print(f"[{self.launcher_name}] 🎯 Success Rate: {success_rate:.1f}% ({running_count}/{len(self.processes)})")
            print(f"[{self.launcher_name}] 🖥️ Services in separate terminals: {terminal_count}")
            print(f"[{self.launcher_name}] 📦 Services in background: {running_count - terminal_count}")
            
            if running_count >= 3:  # At least core services running
                print(f"\n🎉 DEEPLICA STARTUP COMPLETED SUCCESSFULLY!")
                print(f"✅ {running_count} services are running")
                print(f"🔗 System is ready for operations")
                print(f"🛑 Press Ctrl+C to stop all services")
                
                # Keep running until interrupted
                try:
                    while True:
                        time.sleep(1)
                        # Check if any critical services died
                        for proc_info in self.processes:
                            if proc_info["required"]:
                                try:
                                    process = proc_info["process"]
                                    if hasattr(process, 'poll'):
                                        is_running = process.poll() is None
                                    else:
                                        # For psutil Process objects
                                        is_running = process.is_running()

                                    if not is_running:
                                        print(f"\n💥 Critical service {proc_info['name']} died!")
                                        return False
                                except:
                                    # If we can't check the process, assume it died
                                    print(f"\n💥 Critical service {proc_info['name']} died!")
                                    return False
                except KeyboardInterrupt:
                    print(f"\n🛑 Shutdown requested by user")
                    return True
            else:
                print(f"\n⚠️ DEEPLICA STARTUP COMPLETED WITH WARNINGS")
                print(f"🔧 Only {running_count} services are running")
                return False
                
        except Exception as e:
            print(f"\n❌ DEEPLICA STARTUP FAILED: {e}")
            return False
        finally:
            # Clean up processes
            self.stop_all_services()

    def stop_all_services(self):
        """Stop all started services"""
        if not self.processes or self.is_stopping:
            return

        self.is_stopping = True
        print(f"\n[{self.launcher_name}] 🛑 Stopping all services...")

        for proc_info in reversed(self.processes):  # Stop in reverse order
            name = proc_info["name"]
            process = proc_info["process"]
            in_terminal = proc_info.get("terminal", False)

            try:
                # Check if process is still running
                if hasattr(process, 'poll'):
                    is_running = process.poll() is None
                else:
                    # For psutil Process objects
                    is_running = process.is_running()

                if is_running:
                    print(f"[{self.launcher_name}] 🛑 Stopping {name}{'[Terminal]' if in_terminal else '[Background]'}...")

                    if hasattr(process, 'terminate'):
                        process.terminate()

                        # Wait for graceful termination
                        if hasattr(process, 'wait'):
                            try:
                                process.wait(timeout=5)
                                print(f"[{self.launcher_name}] ✅ {name} stopped gracefully")
                            except (subprocess.TimeoutExpired, Exception):
                                print(f"[{self.launcher_name}] 💀 Force killing {name}...")
                                if hasattr(process, 'kill'):
                                    process.kill()
                                    if hasattr(process, 'wait'):
                                        process.wait()
                        else:
                            # For psutil processes
                            try:
                                process.wait(timeout=5)
                                print(f"[{self.launcher_name}] ✅ {name} stopped gracefully")
                            except Exception:
                                print(f"[{self.launcher_name}] 💀 Force killing {name}...")
                                process.kill()
                                process.wait()
                    else:
                        print(f"[{self.launcher_name}] ⚠️ Cannot terminate {name} - no terminate method")

            except Exception as e:
                print(f"[{self.launcher_name}] ⚠️ Error stopping {name}: {e}")

        print(f"[{self.launcher_name}] ✅ All services stopped")
        self.processes.clear()

def main():
    """Main function"""
    launcher = DeepplicaServiceLauncher()
    
    try:
        success = launcher.start_all_services()
        # Don't exit - let the launcher restart itself
        # sys.exit(0 if success else 1)  # REMOVED - launcher should never exit itself
    except KeyboardInterrupt:
        print(f"\n🛑 Service launcher cancelled by user")
        # sys.exit(0)  # REMOVED - launcher should never exit itself
    except Exception as e:
        print(f"\n🚨 Unexpected error: {e}")
        # sys.exit(1)  # REMOVED - launcher should never exit itself

if __name__ == "__main__":
    main()
