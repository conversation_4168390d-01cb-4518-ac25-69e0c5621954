"""
LLM Service for Planner Agent.
Uses shared LLM client to avoid code duplication.
"""

import sys
import os

# Add shared directory to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
shared_path = os.path.join(project_root, 'shared')
if shared_path not in sys.path:
    sys.path.insert(0, shared_path)

# Import shared LLM client
from llm_client import BackendLLMClient

# Re-export for convenience
__all__ = ["BackendLLMClient"]
