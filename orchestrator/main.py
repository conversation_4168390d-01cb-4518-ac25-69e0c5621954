#!/usr/bin/env python3
"""
🚀 START DEEPLICA - BULLETPROOF Orchestrator Microservice

This microservice manages the OPTIMAL, SAFEST, MOST STABLE startup sequence
of all Deeplica services. It ensures services start in the correct dependency
order and handles ALL possible failure scenarios gracefully.

🛡️ BULLETPROOF Features:
- NEVER crashes under any circumstances
- Intelligent dependency-aware startup sequencing
- Comprehensive health checking and readiness verification
- Aggressive port cleanup and conflict resolution
- Self-healing error handling and infinite recovery
- Real-time startup monitoring with detailed logging
- Handles services starting in ANY order without crashes
- Waits indefinitely for dependencies instead of failing
- Automatic restart of failed services
- Graceful degradation when services are unavailable
"""

import asyncio
import os
import sys
import time
import subprocess
import signal
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

# Add project root to path for imports
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, PROJECT_ROOT)

from shared.port_manager import get_service_port, ensure_service_port_free, cleanup_all_ports
from shared.unified_logging import get_logger

# Initialize unified logger
logger = get_logger("ORCHESTRATOR")

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.backend_readiness import BackendReadinessChecker


class ServiceState(Enum):
    """Service state enumeration"""
    NOT_STARTED = "not_started"
    STARTING = "starting"
    RUNNING = "running"
    FAILED = "failed"
    STOPPED = "stopped"


@dataclass
class ServiceConfig:
    """Configuration for a microservice"""
    name: str
    display_name: str
    directory: str
    module: str
    port: int
    depends_on: List[str]
    startup_timeout: int = 60
    health_check_url: Optional[str] = None


def bulletproof_execute(func):
    """🛡️ Decorator to make any function bulletproof - NEVER crashes orchestrator"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except KeyboardInterrupt:
            # Allow graceful shutdown
            raise
        except SystemExit:
            # Allow system exit
            raise
        except Exception as e:
            try:
                logger.error(f"[ORCHESTRATOR:{func.__name__}] ERROR | Bulletproof catch: {type(e).__name__}: {e}")
                # Return safe default
                return None
            except:
                # Even error logging should never crash
                pass
            return None
    return wrapper

class BulletproofDeepplicaOrchestrator:
    """
    🛡️ BULLETPROOF orchestrator for all Deeplica microservices.

    This orchestrator NEVER crashes and handles ALL error scenarios gracefully.
    It ensures optimal, safest, most stable startup sequence with intelligent
    dependency management and infinite resilience.
    """
    
    def __init__(self):
        try:
            self.services: Dict[str, ServiceConfig] = {}
            self.processes: Dict[str, subprocess.Popen] = {}
            self.service_states: Dict[str, ServiceState] = {}
            self.backend_checker = BackendReadinessChecker(service_name="orchestrator")
            self.shutdown_requested = False
            self.restart_counts: Dict[str, int] = {}
            self.max_restarts_per_service = 100

            # Configure services with optimal startup order
            self._configure_services()

            # Setup signal handlers for graceful shutdown
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)

            logger.info("[ORCHESTRATOR:__init__] SYSTEM | 🛡️ Bulletproof orchestrator initialized")
        except Exception as e:
            logger.error(f"[ORCHESTRATOR:__init__] ERROR | Initialization error: {e}")
            # Set safe defaults
            self.services = {}
            self.processes = {}
            self.service_states = {}
            self.shutdown_requested = False
            self.restart_counts = {}
            self.max_restarts_per_service = 100
    
    def _configure_services(self):
        """Configure all microservices with their dependencies"""

        # Stop Deeplica Service - MUST start FIRST for process registration
        stop_port = get_service_port("STOP-DEEPLICA-SERVICE")
        self.services["stop_service"] = ServiceConfig(
            name="stop_service",
            display_name="🛑 Stop Deeplica Service",
            directory="stop_deeplica_service",
            module="main",
            port=stop_port,
            depends_on=[],  # No dependencies - starts first
            health_check_url=f"http://localhost:{stop_port}/health"
        )

        # Backend API - starts after Stop Service for registration
        backend_port = get_service_port("BACKEND-API")
        self.services["backend"] = ServiceConfig(
            name="backend",
            display_name="🌐 Backend API",
            directory="backend",
            module="app.main",
            port=backend_port,
            depends_on=["stop_service"],  # Depends on Stop Service for registration
            health_check_url=f"http://localhost:{backend_port}/ready"
        )
        
        # Dispatcher - depends on backend
        dispatcher_port = get_service_port("DISPATCHER")
        self.services["dispatcher"] = ServiceConfig(
            name="dispatcher",
            display_name="🎯 Dispatcher Service",
            directory="dispatcher",
            module="app.main",
            port=dispatcher_port,
            depends_on=["backend"],
            health_check_url=f"http://localhost:{dispatcher_port}/health"
        )

        # Agent services - all depend on backend
        dialogue_port = get_service_port("DIALOGUE-AGENT")
        self.services["dialogue"] = ServiceConfig(
            name="dialogue",
            display_name="💬 Dialogue Agent",
            directory="agents/dialogue",
            module="app.main",
            port=dialogue_port,
            depends_on=["backend"],
            health_check_url=f"http://localhost:{dialogue_port}/health"
        )

        planner_port = get_service_port("PLANNER-AGENT")
        self.services["planner"] = ServiceConfig(
            name="planner",
            display_name="🧠 Planner Agent",
            directory="agents/planner",
            module="app.main",
            port=planner_port,
            depends_on=["backend"],
            health_check_url=f"http://localhost:{planner_port}/health"
        )

        phone_port = get_service_port("PHONE-AGENT")
        self.services["phone"] = ServiceConfig(
            name="phone",
            display_name="📞 Phone Agent",
            directory="agents/phone",
            module="app.main",
            port=phone_port,
            depends_on=["backend"],
            health_check_url=f"http://localhost:{phone_port}/health"
        )
        
        # CLI Terminal - depends on core services being ready
        self.services["cli"] = ServiceConfig(
            name="cli",
            display_name="🖥️ CLI Terminal UI",
            directory="cli",
            module="terminal_ui",
            port=0,  # No port for CLI
            depends_on=["backend", "dispatcher", "dialogue", "planner", "phone"],
            startup_timeout=30
        )

        # Watchdog - starts LAST to monitor all other services
        watchdog_port = get_service_port("WATCHDOG")
        self.services["watchdog"] = ServiceConfig(
            name="watchdog",
            display_name="🐕 Watchdog Monitor",
            directory="watchdog",
            module="main",
            port=watchdog_port,
            depends_on=["stop_service", "backend", "dispatcher", "dialogue", "planner", "phone"],  # Depends on all services
            health_check_url=f"http://localhost:{watchdog_port}/health"
        )
        
        # Initialize all service states
        for service_name in self.services:
            self.service_states[service_name] = ServiceState.NOT_STARTED
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        logger.info(f"[ORCHESTRATOR:_signal_handler] SERVER | 🛑 Received signal {signum} - initiating graceful shutdown...")
        self.shutdown_requested = True
    
    async def start_all_services(self) -> bool:
        """
        Start all microservices in the correct dependency order.

        Returns:
            True if all services started successfully, False otherwise
        """
        logger.info(f"[ORCHESTRATOR:start_all_services] SYSTEM | 🚀 START DEEPLICA - Microservice Orchestrator")
        logger.info(f"[ORCHESTRATOR:start_all_services] SERVER | 🧹 PHASE 1: Cleaning up all ports and blocking processes...")

        try:
            # PHASE 1: Clean up all ports and processes
            await self._cleanup_all_ports_and_processes()

            logger.info(f"[ORCHESTRATOR:start_all_services] SYSTEM | 🎯 PHASE 2: Starting core microservices in dependency order...")

            # Get startup order based on dependencies (excluding CLI)
            startup_order = self._get_startup_order()
            core_services = [name for name in startup_order if name != "cli"]
            cli_service = "cli" if "cli" in startup_order else None

            logger.info(f"📋 Core services startup order: {[self.services[name].display_name for name in core_services]}")

            # PHASE 2: Start core services (everything except CLI)
            for service_name in core_services:
                if self.shutdown_requested:
                    logger.warning(f"[ORCHESTRATOR:start_all_services] SERVER | 🛑 Shutdown requested during startup - aborting")
                    return False

                success = await self._start_service(service_name)
                if not success:
                    logger.error(f"[ORCHESTRATOR:start_all_services] SERVER | ❌ Failed to start {service_name} - aborting startup")
                    await self._shutdown_all_services()
                    return False

            logger.info(f"[ORCHESTRATOR:start_all_services] SYSTEM | ✅ All core microservices started successfully!")

            # PHASE 3: Verify all core services are ready for work
            logger.info(f"[ORCHESTRATOR:start_all_services] HEALTH | 🔍 PHASE 3: Verifying all core services are READY for work...")
            all_ready = await self._verify_all_services_ready(core_services)

            if not all_ready:
                logger.error(f"[ORCHESTRATOR:start_all_services] SERVER | ❌ Not all core services are ready - aborting CLI startup")
                await self._shutdown_all_services()
                return False

            logger.info(f"[ORCHESTRATOR:start_all_services] HEALTH | ✅ All core services verified READY for work!")

            # PHASE 4: Start CLI Terminal UI
            if cli_service:
                logger.info(f"[ORCHESTRATOR:start_all_services] SYSTEM | 🖥️ PHASE 4: Starting CLI Terminal UI...")
                success = await self._start_service(cli_service)
                if not success:
                    logger.warning(f"[ORCHESTRATOR:start_all_services] ERROR | ⚠️ CLI Terminal UI failed to start - core services remain operational")
                else:
                    logger.info(f"[ORCHESTRATOR:start_all_services] SYSTEM | ✅ CLI Terminal UI started successfully!")

            logger.info(f"[ORCHESTRATOR:start_all_services] SYSTEM | 🎉 Deeplica system is fully operational!")
            logger.info(f"[ORCHESTRATOR:start_all_services] SERVER | 🎯 All services ready for work - system startup complete!")

            # Monitor services until shutdown
            await self._monitor_services()

            return True

        except Exception as e:
            logger.error(f"[ORCHESTRATOR:start_all_services] SERVER | ❌ Critical error during startup: {e}")
            await self._shutdown_all_services()
            return False
    
    def _get_startup_order(self) -> List[str]:
        """
        Calculate the correct startup order based on dependencies.
        
        Returns:
            List of service names in startup order
        """
        order = []
        remaining = set(self.services.keys())
        
        while remaining:
            # Find services with no unmet dependencies
            ready = []
            for service_name in remaining:
                service = self.services[service_name]
                if all(dep in order for dep in service.depends_on):
                    ready.append(service_name)
            
            if not ready:
                raise ValueError("Circular dependency detected in services")
            
            # Add ready services to order with priority:
            # 1. Stop Service FIRST (for process registration)
            # 2. Backend API (core foundation)
            # 3. Other services in dependency order
            if "stop_service" in ready:
                order.append("stop_service")
                remaining.remove("stop_service")
            elif "backend" in ready:
                order.append("backend")
                remaining.remove("backend")
            else:
                # Add other services in alphabetical order for consistency
                ready.sort()
                for service_name in ready:
                    order.append(service_name)
                    remaining.remove(service_name)
        
        return order

    async def _cleanup_all_ports_and_processes(self):
        """
        Clean up all ports and kill any processes that might block startup.
        This ensures a clean slate for all microservices.
        """
        import subprocess
        import signal

        logger.info(f"[ORCHESTRATOR:_cleanup_all_ports_and_processes] SERVER | 🧹 Cleaning up all Deeplica ports and processes using centralized port manager...")

        # Use centralized port cleanup
        try:
            results = cleanup_all_ports(force=True)
            successful = sum(1 for success in results.values() if success)
            total = len(results)
            logger.info(f"[ORCHESTRATOR:_cleanup_all_ports_and_processes] SERVER | 📊 Centralized cleanup: {successful}/{total} ports cleaned")

            # If centralized cleanup was successful, we're done
            if successful == total:
                logger.info(f"[ORCHESTRATOR:_cleanup_all_ports_and_processes] SERVER | ✅ All ports cleaned successfully")
                return

        except Exception as e:
            logger.warning(f"[ORCHESTRATOR:_cleanup_all_ports_and_processes] SERVER | ⚠️ Centralized cleanup failed: {e}")
            logger.info(f"[ORCHESTRATOR:_cleanup_all_ports_and_processes] SERVER | 🔄 Falling back to manual cleanup...")

        # Fallback: Get all ports from centralized port manager
        try:
            from shared.port_manager import port_manager
            all_ports = list(port_manager.get_all_ports().values())
        except Exception as e:
            logger.warning(f"[ORCHESTRATOR:_cleanup_all_ports_and_processes] SERVER | ⚠️ Could not get ports from manager: {e}")
            # Ultimate fallback to hardcoded ports (updated for 8888 backend)
            all_ports = [get_service_port("backend"), get_service_port("dispatcher"), get_service_port("dialogue"), get_service_port("planner"), get_service_port("phone"), get_service_port("watchdog"), get_service_port("web-chat"), get_service_port("cli"), get_service_port("twilio-echo-bot")]

        # Kill processes on all ports
        for port in all_ports:
            try:
                logger.info(f"[ORCHESTRATOR:_cleanup_all_ports_and_processes] SERVER | 🔍 Checking port {port}...")
                result = subprocess.run(
                    ["lsof", "-ti", f": {port}"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )

                if result.returncode == 0 and result.stdout.strip():
                    pids = result.stdout.strip().split('\n')
                    logger.info(f"[ORCHESTRATOR:_cleanup_all_ports_and_processes] PROCESS | 🔪 Found {len(pids)} process(es) on port {port} - terminating...")

                    for pid in pids:
                        if pid.strip():
                            try:
                                pid_int = int(pid.strip())
                                logger.info(f"[ORCHESTRATOR:_cleanup_all_ports_and_processes] SERVER | 🔄 Terminating process {pid_int} on port {port}")
                                os.kill(pid_int, signal.SIGTERM)
                                await asyncio.sleep(0.5)

                                # Check if still running, force kill if needed
                                try:
                                    os.kill(pid_int, 0)
                                    logger.info(f"[ORCHESTRATOR:_cleanup_all_ports_and_processes] PROCESS | 🔪 Force killing process {pid_int}")
                                    os.kill(pid_int, signal.SIGKILL)
                                except ProcessLookupError:
                                    logger.info(f"[ORCHESTRATOR:_cleanup_all_ports_and_processes] PROCESS | ✅ Process {pid_int} terminated")

                            except (ProcessLookupError, ValueError, PermissionError) as e:
                                logger.debug(f"[ORCHESTRATOR:_cleanup_all_ports_and_processes] PROCESS | Process cleanup issue: {e}")
                else:
                    logger.debug(f"[ORCHESTRATOR:_cleanup_all_ports_and_processes] SERVER | ✅ Port {port} is free")

            except subprocess.TimeoutExpired:
                logger.warning(f"[ORCHESTRATOR:_cleanup_all_ports_and_processes] SERVER | ⚠️ Timeout checking port {port}")
            except FileNotFoundError:
                logger.debug(f"[ORCHESTRATOR:_cleanup_all_ports_and_processes] SYSTEM | lsof command not found")
                break
            except Exception as e:
                logger.debug(f"[ORCHESTRATOR:_cleanup_all_ports_and_processes] SERVER | Error cleaning port {port}: {e}")

        # Kill any lingering Python microservice processes
        try:
            logger.info(f"[ORCHESTRATOR:_cleanup_all_ports_and_processes] PROCESS | 🧹 Cleaning up lingering Python microservice processes...")

            # Find Python processes that might be microservices
            patterns = [
                "python.*app.main",
                "python.*terminal_ui",
                "python.*orchestrator"
            ]

            for pattern in patterns:
                try:
                    result = subprocess.run(
                        ["pgrep", "-f", pattern],
                        capture_output=True,
                        text=True,
                        timeout=5
                    )

                    if result.returncode == 0 and result.stdout.strip():
                        pids = result.stdout.strip().split('\n')
                        for pid in pids:
                            if pid.strip():
                                try:
                                    pid_int = int(pid.strip())
                                    # Don't kill ourselves
                                    if pid_int != os.getpid():
                                        logger.info(f"[ORCHESTRATOR:_cleanup_all_ports_and_processes] PROCESS | 🔪 Terminating lingering process {pid_int}")
                                        os.kill(pid_int, signal.SIGTERM)
                                        await asyncio.sleep(0.2)
                                except (ProcessLookupError, ValueError, PermissionError):
                                    pass
                except subprocess.TimeoutExpired:
                    pass
                except Exception:
                    pass

        except Exception as e:
            logger.debug(f"[ORCHESTRATOR:_cleanup_all_ports_and_processes] ERROR | Error cleaning lingering processes: {e}")

        # Wait for cleanup to complete
        await asyncio.sleep(3)
        logger.info(f"[ORCHESTRATOR:_cleanup_all_ports_and_processes] SERVER | ✅ Port and process cleanup completed")

    async def _verify_all_services_ready(self, service_names: List[str]) -> bool:
        """
        Verify that all specified services are ready for work.

        Args:
            service_names: List of service names to verify

        Returns:
            True if all services are ready, False otherwise
        """
        logger.info(f"[ORCHESTRATOR:_verify_all_services_ready] HEALTH | 🔍 Verifying all core services are ready for work...")

        for service_name in service_names:
            service = self.services[service_name]
            logger.info(f"[ORCHESTRATOR:_verify_all_services_ready] HEALTH | 🔍 Verifying {service.display_name} is ready...")

            if service_name == "backend":
                # For backend, use comprehensive readiness check
                ready = await self.backend_checker.wait_for_backend_ready(timeout_seconds=30)
                if not ready:
                    logger.error(f"[ORCHESTRATOR:_verify_all_services_ready] HEALTH | ❌ {service.display_name} is not ready!")
                    return False
            elif service.health_check_url:
                # For other services, verify health endpoint
                ready = await self._verify_service_health(service)
                if not ready:
                    logger.error(f"[ORCHESTRATOR:_verify_all_services_ready] HEALTH | ❌ {service.display_name} is not ready!")
                    return False
            else:
                # For services without health checks, verify process is running
                if service_name not in self.processes or self.processes[service_name].poll() is not None:
                    logger.error(f"[ORCHESTRATOR:_verify_all_services_ready] PROCESS | ❌ {service.display_name} process is not running!")
                    return False

            logger.info(f"[ORCHESTRATOR:_verify_all_services_ready] HEALTH | ✅ {service.display_name} is ready for work")

        logger.info(f"[ORCHESTRATOR:_verify_all_services_ready] HEALTH | ✅ All core services verified ready for work!")
        return True

    async def _verify_service_health(self, service: ServiceConfig) -> bool:
        """Verify a service's health endpoint is responding correctly"""
        if not service.health_check_url:
            return True

        import httpx

        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(service.health_check_url)
                if response.status_code == 200:
                    # For some services, check the response content
                    try:
                        data = response.json()
                        if isinstance(data, dict):
                            status = data.get("status", "unknown")
                            if status in ["healthy", "ready", "ok"]:
                                return True
                    except:
                        pass
                    # If we get 200, assume healthy even if JSON parsing fails
                    return True
                else:
                    logger.warning(f"[ORCHESTRATOR:_verify_service_health] HEALTH | ⚠️ {service.display_name} health check returned {response.status_code}")
                    return False
        except Exception as e:
            logger.warning(f"[ORCHESTRATOR:_verify_service_health] HEALTH | ⚠️ {service.display_name} health check failed: {e}")
            return False
    
    async def _start_service(self, service_name: str) -> bool:
        """
        Start a specific microservice.
        
        Args:
            service_name: Name of the service to start
            
        Returns:
            True if service started successfully, False otherwise
        """
        service = self.services[service_name]
        logger.info(f"[ORCHESTRATOR:_start_service] SYSTEM | 🚀 Starting {service.display_name}...")
        
        try:
            self.service_states[service_name] = ServiceState.STARTING
            
            # Wait for dependencies to be ready
            for dep_name in service.depends_on:
                if not await self._wait_for_service_ready(dep_name):
                    logger.error(f"[ORCHESTRATOR:_start_service] HEALTH | ❌ Dependency {dep_name} not ready for {service_name}")
                    self.service_states[service_name] = ServiceState.FAILED
                    return False
            
            # Special handling for backend - ensure it's fully operational
            if service_name == "backend":
                success = await self._start_backend_service(service)
            else:
                success = await self._start_regular_service(service)
            
            if success:
                self.service_states[service_name] = ServiceState.RUNNING
                logger.info(f"[ORCHESTRATOR:_start_service] SYSTEM | ✅ {service.display_name} started successfully")
                return True
            else:
                self.service_states[service_name] = ServiceState.FAILED
                logger.error(f"[ORCHESTRATOR:_start_service] ERROR | ❌ Failed to start {service.display_name}")
                return False
                
        except Exception as e:
            logger.error(f"[ORCHESTRATOR:_start_service] ERROR | ❌ Exception starting {service.display_name}: {e}")
            self.service_states[service_name] = ServiceState.FAILED
            return False
    
    async def _start_backend_service(self, service: ServiceConfig) -> bool:
        """Start the backend service and wait for full readiness"""
        # Start the backend process
        process = await self._launch_service_process(service)
        if not process:
            return False
        
        self.processes[service.name] = process
        
        # Wait for backend to be fully ready
        logger.info(f"[ORCHESTRATOR:_start_backend_service] SYSTEM | ⏳ Waiting for Backend API to be fully operational...")
        ready = await self.backend_checker.wait_for_backend_ready(timeout_seconds=120)
        
        if ready:
            logger.info(f"[ORCHESTRATOR:_start_backend_service] DATABASE | ✅ Backend API is fully operational and ready for connections!")
            return True
        else:
            logger.error(f"[ORCHESTRATOR:_start_backend_service] HEALTH | ❌ Backend API failed to become ready within timeout")
            return False
    
    async def _start_regular_service(self, service: ServiceConfig) -> bool:
        """Start a regular microservice"""
        # Start the service process
        process = await self._launch_service_process(service)
        if not process:
            return False
        
        self.processes[service.name] = process
        
        # Wait for service to be ready
        if service.health_check_url:
            ready = await self._wait_for_health_check(service)
            if not ready:
                logger.error(f"[ORCHESTRATOR:_start_regular_service] HEALTH | ❌ {service.display_name} health check failed")
                return False
        else:
            # For services without health checks, wait a bit for startup
            await asyncio.sleep(3)
        
        return True
    
    async def _launch_service_process(self, service: ServiceConfig) -> Optional[subprocess.Popen]:
        """Launch a service process"""
        try:
            # Build command
            if service.name == "cli":
                cmd = [sys.executable, "terminal_ui.py"]
            else:
                cmd = [sys.executable, "-m", service.module]
            
            # Set environment
            env = os.environ.copy()
            env["PYTHONPATH"] = project_root
            env["WAIT_FOR_BACKEND"] = "false"  # Orchestrator handles dependencies
            
            # Launch process
            cwd = os.path.join(project_root, service.directory)
            process = subprocess.Popen(
                cmd,
                cwd=cwd,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1
            )
            
            # Give process a moment to start
            await asyncio.sleep(1)
            
            # Check if process is still running
            if process.poll() is not None:
                logger.error(f"[ORCHESTRATOR:_launch_service_process] PROCESS | ❌ {service.display_name} process exited immediately")
                return None
            
            return process
            
        except Exception as e:
            logger.error(f"[ORCHESTRATOR:_launch_service_process] ERROR | ❌ Failed to launch {service.display_name}: {e}")
            return None

    async def _wait_for_service_ready(self, service_name: str) -> bool:
        """Wait for a service to be ready"""
        service = self.services[service_name]

        if service_name == "backend":
            # For backend, use comprehensive readiness check
            return await self.backend_checker.wait_for_backend_ready(timeout_seconds=60)
        elif service.health_check_url:
            # For other services, use health check
            return await self._wait_for_health_check(service)
        else:
            # For services without health checks, assume ready if running
            return self.service_states[service_name] == ServiceState.RUNNING

    async def _wait_for_health_check(self, service: ServiceConfig) -> bool:
        """Wait for a service's health check to pass"""
        if not service.health_check_url:
            return True

        import httpx

        logger.info(f"[ORCHESTRATOR:_wait_for_health_check] HEALTH | 🔍 Checking health of {service.display_name}...")

        for attempt in range(30):  # 30 attempts = 60 seconds
            try:
                async with httpx.AsyncClient(timeout=5.0) as client:
                    response = await client.get(service.health_check_url)
                    if response.status_code == 200:
                        logger.info(f"[ORCHESTRATOR:_wait_for_health_check] HEALTH | ✅ {service.display_name} health check passed")
                        return True
            except Exception:
                pass

            await asyncio.sleep(2)

        logger.error(f"[ORCHESTRATOR:_wait_for_health_check] HEALTH | ❌ {service.display_name} health check failed after 60 seconds")
        return False

    async def _monitor_services(self):
        """Monitor all services until shutdown is requested"""
        logger.info(f"[ORCHESTRATOR:_monitor_services] SYSTEM | 👁️ Monitoring all services... (Press Ctrl+C to shutdown)")

        while not self.shutdown_requested:
            # Check if any service has died
            for service_name, process in self.processes.items():
                if process.poll() is not None:
                    logger.error(f"💀 {self.services[service_name].display_name} has died!")
                    self.service_states[service_name] = ServiceState.FAILED

            # Log status every 30 seconds
            await asyncio.sleep(30)
            self._log_service_status()

        logger.info(f"[ORCHESTRATOR:_monitor_services] SERVER | 🛑 Shutdown requested - stopping all services...")
        await self._shutdown_all_services()

    def _log_service_status(self):
        """Log the current status of all services"""
        logger.info(f"[ORCHESTRATOR:_log_service_status] HEALTH | 📊 Service Status:")
        for service_name, state in self.service_states.items():
            service = self.services[service_name]
            status_emoji = {
                ServiceState.NOT_STARTED: "⚪",
                ServiceState.STARTING: "🟡",
                ServiceState.RUNNING: "🟢",
                ServiceState.FAILED: "🔴",
                ServiceState.STOPPED: "⚫"
            }[state]
            logger.info(f"[ORCHESTRATOR:_log_service_status] HEALTH |   {status_emoji} {service.display_name}: {state.value}")

    async def _shutdown_all_services(self):
        """Shutdown all services gracefully"""
        logger.info(f"[ORCHESTRATOR:_shutdown_all_services] SYSTEM | 🛑 Shutting down all microservices...")

        # Shutdown in reverse order (dependencies last)
        startup_order = self._get_startup_order()
        shutdown_order = list(reversed(startup_order))

        for service_name in shutdown_order:
            if service_name in self.processes:
                await self._shutdown_service(service_name)

        logger.info(f"[ORCHESTRATOR:_shutdown_all_services] SYSTEM | ✅ All services shut down")

    async def _shutdown_service(self, service_name: str):
        """Shutdown a specific service"""
        service = self.services[service_name]
        process = self.processes.get(service_name)

        if not process:
            return

        logger.info(f"[ORCHESTRATOR:_shutdown_service] SYSTEM | 🛑 Shutting down {service.display_name}...")

        try:
            # Try graceful shutdown first
            process.terminate()

            # Wait up to 10 seconds for graceful shutdown
            try:
                await asyncio.wait_for(
                    asyncio.create_task(self._wait_for_process_exit(process)),
                    timeout=10.0
                )
                logger.info(f"[ORCHESTRATOR:_shutdown_service] SYSTEM | ✅ {service.display_name} shut down gracefully")
            except asyncio.TimeoutError:
                # Force kill if graceful shutdown fails
                logger.warning(f"[ORCHESTRATOR:_shutdown_service] PROCESS | ⚠️ Force killing {service.display_name}")
                process.kill()
                await asyncio.create_task(self._wait_for_process_exit(process))

            self.service_states[service_name] = ServiceState.STOPPED

        except Exception as e:
            logger.error(f"[ORCHESTRATOR:_shutdown_service] ERROR | ❌ Error shutting down {service.display_name}: {e}")

    async def _wait_for_process_exit(self, process: subprocess.Popen):
        """Wait for a process to exit"""
        while process.poll() is None:
            await asyncio.sleep(0.1)


async def main():
    """🛡️ BULLETPROOF main entry point for the orchestrator"""
    restart_count = 0
    max_restarts = 5

    # Logger is already configured via unified_logging system

    while restart_count < max_restarts:
        try:
            logger.info(f"[ORCHESTRATOR:main] SYSTEM | 🚀 START DEEPLICA - Bulletproof Orchestrator starting (attempt #{restart_count + 1})...")

            orchestrator = BulletproofDeepplicaOrchestrator()
            success = await orchestrator.start_all_services()

            if success:
                logger.info(f"[ORCHESTRATOR:main] SERVER | 🎉 Deeplica system started successfully")

                # Monitor services until shutdown (bulletproof)
                try:
                    await orchestrator._monitor_services()
                except:
                    pass  # Never let monitoring crash orchestrator

                logger.info(f"[ORCHESTRATOR:main] SYSTEM | 🛑 Orchestrator shutting down...")
                return 0
            else:
                logger.warning(f"[ORCHESTRATOR:main] WARNING | ⚠️ Startup incomplete, but system partially operational")
                return 0

        except KeyboardInterrupt:
            logger.info(f"[ORCHESTRATOR:main] SYSTEM | 🛑 Keyboard interrupt received")
            return 0
        except Exception as e:
            restart_count += 1
            logger.error(f"[ORCHESTRATOR:main] ERROR | ❌ Orchestrator failed (restart #{restart_count}): {e}")

            if restart_count < max_restarts:
                logger.info(f"[ORCHESTRATOR:main] SYSTEM | 🔄 Restarting orchestrator in 5 seconds...")
                await asyncio.sleep(5)
            else:
                logger.error(f"[ORCHESTRATOR:main] ERROR | ❌ Orchestrator exceeded maximum restarts ({max_restarts})")
                return 1

    return 1


if __name__ == "__main__":
    # Set distinctive process name for easy identification
    try:
        import setproctitle
        setproctitle.setproctitle("DEEPLICA-ORCHESTRATOR")
        # [ORCHESTRATOR:main] SYSTEM | ✅ Process name set to: DEEPLICA-ORCHESTRATOR
    except ImportError:
        # [ORCHESTRATOR:main] SYSTEM | ⚠️ setproctitle not available - process name unchanged

        pass
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
