"""
Phone Agent Models - Data structures for phone call tasks.
"""

from typing import Optional, List
from pydantic import BaseModel, Field
from enum import Enum


class CallStatus(str, Enum):
    """Status values for phone calls"""
    INITIATED = "initiated"
    RINGING = "ringing"
    ANSWERED = "answered"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    NO_ANSWER = "no_answer"
    BUSY = "busy"
    DISCONNECTED = "disconnected"


class CallLanguage(str, Enum):
    """Supported languages for phone calls"""
    ENGLISH = "en"
    HEBREW = "he"


class PhoneCallConfig(BaseModel):
    """Configuration for a phone call task"""
    phone_number: str = Field(..., description="Phone number to call (any format)")
    contact_name: str = Field(..., description="Name of the person to call")
    context: str = Field(..., description="Context about the call purpose")
    question: str = Field(..., description="Specific question to be answered")
    language: CallLanguage = Field(default=CallLanguage.ENGLISH, description="Call language")
    voice: Optional[str] = Field(None, description="Specific voice to use (optional)")
    max_duration_minutes: int = Field(default=5, description="Maximum call duration in minutes")
    conversation_script: Optional[str] = Field(None, description="Pre-written script to say during call")


class ConversationTurn(BaseModel):
    """A single turn in the phone conversation"""
    timestamp: str = Field(..., description="Turn timestamp")
    speaker: str = Field(..., description="Speaker (user or agent)")
    text: str = Field(..., description="Transcribed or spoken text")
    confidence: Optional[float] = Field(None, description="Transcription confidence score")


class CallResult(BaseModel):
    """Result of a phone call task"""
    call_status: CallStatus = Field(..., description="Final call status")
    duration_seconds: Optional[int] = Field(None, description="Call duration in seconds")
    conversation_transcript: List[ConversationTurn] = Field(default_factory=list, description="Full conversation")
    extracted_answer: Optional[str] = Field(None, description="Answer extracted by LLM")
    question_answered: bool = Field(default=False, description="Whether the question was adequately answered")
    disconnect_reason: Optional[str] = Field(None, description="Reason for call ending")
    error_message: Optional[str] = Field(None, description="Error message if call failed")
    twilio_call_sid: Optional[str] = Field(None, description="Twilio call SID for reference")


class CallState(BaseModel):
    """Current state of an active phone call"""
    task_id: str = Field(..., description="Task identifier")
    mission_id: str = Field(..., description="Mission identifier")
    call_config: PhoneCallConfig = Field(..., description="Call configuration")
    call_status: CallStatus = Field(default=CallStatus.INITIATED, description="Current call status")
    twilio_call_sid: Optional[str] = Field(None, description="Twilio call SID")
    start_time: Optional[str] = Field(None, description="Call start timestamp")
    conversation: List[ConversationTurn] = Field(default_factory=list, description="Conversation so far")
    callback_url: str = Field(..., description="Dispatcher callback URL")
    webhook_base_url: Optional[str] = Field(None, description="Base URL for Twilio webhooks")


# Language configurations for Twilio
LANGUAGE_CONFIGS = {
    CallLanguage.ENGLISH: {
        'code': 'en-US',
        'voice': 'Google.en-US-Standard-C',
        'greeting': "Hello, this is an AI assistant from Deeplica. I have a question for you.",
        'no_speech': "I didn't hear anything. Could you please repeat that?",
        'goodbye': "Thank you for your time. Have a great day!",
        'timeout_message': "I need to end this call now due to time limits. Thank you."
    },
    CallLanguage.HEBREW: {
        'code': 'he-IL',
        'voice': 'Google.he-IL-Standard-A',
        'greeting': "שלום, זה עוזר דיגיטלי מדיפליקה. יש לי שאלה בשבילך.",
        'no_speech': "לא שמעתי כלום. אפשר לחזור על זה?",
        'goodbye': "תודה על הזמן. יום טוב!",
        'timeout_message': "אני צריך לסיים את השיחה עכשיו בגלל מגבלות זמן. תודה."
    }
}
