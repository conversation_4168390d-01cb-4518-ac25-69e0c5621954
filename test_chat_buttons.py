#!/usr/bin/env python3
"""
🔧 CHAT BUTTONS TEST

Tests that all chat buttons and functionality work properly.
"""

import requests
import time
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

from shared.port_manager import get_service_port

def test_chat_page_elements():
    """Test that chat page has all required elements"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔧 Testing chat page elements on port {web_chat_port}...")
        
        # Login and get chat page
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        
        print("📝 Logging in...")
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
        
        print("💬 Getting chat page...")
        chat_response = session.get(
            f"http://localhost:{web_chat_port}/chat",
            allow_redirects=True,
            timeout=10
        )
        
        if chat_response.status_code != 200:
            print(f"❌ Chat access failed: {chat_response.status_code}")
            return False
        
        content = chat_response.text
        
        # Check for essential UI elements
        ui_elements = [
            'id="messageInput"',
            'id="sendButton"',
            'id="messagesArea"',
            'id="chatDate"',
            'id="chatTime"',
            'id="connectionStatus"',
            'class="send-button"',
            'class="message-input"'
        ]
        
        missing_elements = []
        for element in ui_elements:
            if element not in content:
                missing_elements.append(element)
        
        if missing_elements:
            print(f"❌ Missing UI elements: {missing_elements}")
            return False
        
        print(f"✅ All UI elements found")
        
        # Check for JavaScript functions
        js_functions = [
            'function sendMessage()',
            'function initializeChat()',
            'function connectWebSocket()',
            'function updateConnectionStatus(',
            'function initializeChatDateTime()',
            'addEventListener(\'click\'',
            'addEventListener(\'keydown\''
        ]
        
        missing_functions = []
        for func in js_functions:
            if func not in content:
                missing_functions.append(func)
        
        if missing_functions:
            print(f"❌ Missing JavaScript functions: {missing_functions}")
            return False
        
        print(f"✅ All JavaScript functions found")
        
        # Check for session variables
        session_vars = [
            'const sessionId',
            'const username',
            'SECURITY_TOKENS',
            'TAB_TOKEN_DATA'
        ]
        
        missing_vars = []
        for var in session_vars:
            if var not in content:
                missing_vars.append(var)
        
        if missing_vars:
            print(f"❌ Missing session variables: {missing_vars}")
            return False
        
        print(f"✅ All session variables found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing chat page elements: {e}")
        return False

def test_chat_initialization():
    """Test that chat initialization is properly called"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🚀 Testing chat initialization on port {web_chat_port}...")
        
        # Login and get chat page
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login failed for initialization test")
            return False
        
        chat_response = session.get(
            f"http://localhost:{web_chat_port}/chat",
            allow_redirects=True,
            timeout=10
        )
        
        if chat_response.status_code != 200:
            print(f"❌ Chat access failed for initialization test")
            return False
        
        content = chat_response.text
        
        # Check initialization order
        init_calls = [
            'initializeHeaderVideo();',
            'initializeChat();',
            'initializeChatDateTime();',
            'loadFontPreference();'
        ]
        
        for call in init_calls:
            if call not in content:
                print(f"❌ Missing initialization call: {call}")
                return False
        
        print(f"✅ All initialization calls found")
        
        # Check that initialization happens in DOMContentLoaded
        if 'DOMContentLoaded' not in content:
            print(f"❌ DOMContentLoaded event handler missing")
            return False
        
        print(f"✅ DOMContentLoaded event handler found")
        
        # Check for non-blocking security
        if 'chat remains functional' in content:
            print(f"✅ Non-blocking security implementation found")
        else:
            print(f"⚠️ Non-blocking security implementation not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing chat initialization: {e}")
        return False

def test_websocket_configuration():
    """Test that WebSocket configuration is correct"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔗 Testing WebSocket configuration on port {web_chat_port}...")
        
        # Login and get chat page
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login failed for WebSocket test")
            return False
        
        chat_response = session.get(
            f"http://localhost:{web_chat_port}/chat",
            allow_redirects=True,
            timeout=10
        )
        
        if chat_response.status_code != 200:
            print(f"❌ Chat access failed for WebSocket test")
            return False
        
        content = chat_response.text
        
        # Check WebSocket elements
        ws_elements = [
            'let ws = null;',
            'let isConnected = false;',
            'connectWebSocket();',
            'new WebSocket(wsUrl);',
            'ws.onopen = function()',
            'ws.onmessage = function(',
            'ws.onclose = function(',
            'ws.onerror = function('
        ]
        
        missing_ws = []
        for element in ws_elements:
            if element not in content:
                missing_ws.append(element)
        
        if missing_ws:
            print(f"❌ Missing WebSocket elements: {missing_ws}")
            return False
        
        print(f"✅ All WebSocket elements found")
        
        # Check WebSocket URL construction
        if '${protocol}//${window.location.host}/ws?session_id=${sessionId}' not in content:
            print(f"❌ WebSocket URL construction incorrect")
            return False
        
        print(f"✅ WebSocket URL construction correct")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing WebSocket configuration: {e}")
        return False

def main():
    """Run all chat button tests"""
    print("🔧 CHAT BUTTONS AND FUNCTIONALITY TEST")
    print("=" * 50)
    print("Testing chat functionality:")
    print("- UI elements present")
    print("- JavaScript functions")
    print("- Initialization order")
    print("- WebSocket configuration")
    print()
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: Chat page elements
    print("🧪 TEST 1: Chat Page Elements")
    if test_chat_page_elements():
        tests_passed += 1
    
    # Test 2: Chat initialization
    print("\n🧪 TEST 2: Chat Initialization")
    if test_chat_initialization():
        tests_passed += 1
    
    # Test 3: WebSocket configuration
    print("\n🧪 TEST 3: WebSocket Configuration")
    if test_websocket_configuration():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"🔧 TEST RESULTS: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✅ ALL TESTS PASSED - Chat functionality should work!")
        print("🎉 Features verified:")
        print("   - All UI elements present")
        print("   - JavaScript functions implemented")
        print("   - Proper initialization order")
        print("   - WebSocket configuration correct")
        print("   - Event handlers attached")
    else:
        print("❌ SOME TESTS FAILED - Chat functionality needs fixes")
        
        if tests_passed >= 2:
            print("🔧 RECOMMENDATION: Minor chat issues remain")
        elif tests_passed >= 1:
            print("🔧 RECOMMENDATION: Some chat features work")
        else:
            print("🔧 RECOMMENDATION: Major chat fixes needed")
    
    print(f"\n🌐 Manual test:")
    print(f"1. Open http://localhost:8007")
    print(f"2. Login with: admin / admin123")
    print(f"3. Try typing in message input")
    print(f"4. Press Enter or click send button")
    print(f"5. Check date/time display")
    print(f"6. Check connection status")
    print(f"7. Click admin icon (⚙️)")

if __name__ == '__main__':
    main()
