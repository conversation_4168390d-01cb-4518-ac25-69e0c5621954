<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Access Restricted - DEEPLICA AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #ffffff;
            overflow: hidden;
        }

        /* Animated background particles */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .particle {
            position: absolute;
            background: rgba(0, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .particle:nth-child(1) { width: 4px; height: 4px; left: 10%; animation-delay: 0s; }
        .particle:nth-child(2) { width: 6px; height: 6px; left: 20%; animation-delay: 1s; }
        .particle:nth-child(3) { width: 3px; height: 3px; left: 30%; animation-delay: 2s; }
        .particle:nth-child(4) { width: 5px; height: 5px; left: 40%; animation-delay: 3s; }
        .particle:nth-child(5) { width: 4px; height: 4px; left: 50%; animation-delay: 4s; }
        .particle:nth-child(6) { width: 6px; height: 6px; left: 60%; animation-delay: 5s; }
        .particle:nth-child(7) { width: 3px; height: 3px; left: 70%; animation-delay: 0.5s; }
        .particle:nth-child(8) { width: 5px; height: 5px; left: 80%; animation-delay: 1.5s; }
        .particle:nth-child(9) { width: 4px; height: 4px; left: 90%; animation-delay: 2.5s; }

        @keyframes float {
            0%, 100% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-100px) rotate(360deg); opacity: 0; }
        }

        /* Main container */
        .container {
            position: relative;
            z-index: 10;
            text-align: center;
            max-width: 500px;
            padding: 40px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 20px;
            border: 2px solid rgba(0, 255, 255, 0.3);
            box-shadow: 
                0 0 50px rgba(0, 255, 255, 0.2),
                inset 0 0 50px rgba(0, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            animation: glow 3s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 50px rgba(0, 255, 255, 0.2), inset 0 0 50px rgba(0, 255, 255, 0.05); }
            to { box-shadow: 0 0 80px rgba(0, 255, 255, 0.4), inset 0 0 50px rgba(0, 255, 255, 0.1); }
        }

        /* Logo and branding */
        .logo {
            font-size: 2.5rem;
            font-weight: bold;
            background: linear-gradient(45deg, #00ffff, #0080ff, #8000ff);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
            text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
        }

        .subtitle {
            color: #888;
            font-size: 0.9rem;
            margin-bottom: 30px;
            letter-spacing: 1px;
        }

        /* Lock icon */
        .lock-icon {
            font-size: 4rem;
            color: #ff6b6b;
            margin-bottom: 20px;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.8; }
            50% { transform: scale(1.1); opacity: 1; }
        }

        /* Message */
        .message {
            margin-bottom: 30px;
        }

        .message h2 {
            color: #ff6b6b;
            font-size: 1.8rem;
            margin-bottom: 15px;
            text-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
        }

        .message p {
            color: #ccc;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        /* Login button */
        .login-btn {
            display: inline-block;
            padding: 15px 40px;
            background: linear-gradient(45deg, #00ffff, #0080ff);
            color: #000;
            text-decoration: none;
            border-radius: 50px;
            font-weight: bold;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 5px 20px rgba(0, 255, 255, 0.3);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .login-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 255, 255, 0.5);
            background: linear-gradient(45deg, #0080ff, #00ffff);
        }

        .login-btn:active {
            transform: translateY(-1px);
        }

        /* Action buttons container */
        .action-buttons {
            margin: 30px 0;
        }





        /* Footer */
        .footer {
            margin-top: 30px;
            color: #666;
            font-size: 0.8rem;
        }

        /* Responsive design */
        @media (max-width: 600px) {
            .container {
                margin: 20px;
                padding: 30px 20px;
            }
            
            .logo {
                font-size: 2rem;
            }
            
            .lock-icon {
                font-size: 3rem;
            }
            
            .message h2 {
                font-size: 1.5rem;
            }
        }

        /* Cyberpunk grid effect */
        .grid {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(0, 255, 255, 0.03) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 255, 0.03) 1px, transparent 1px);
            background-size: 50px 50px;
            z-index: 2;
        }
    </style>
</head>
<body>
    <!-- Animated background -->
    <div class="particles">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>
    
    <!-- Cyberpunk grid -->
    <div class="grid"></div>

    <!-- Main content -->
    <div class="container">
        <div class="logo">DEEPLICA</div>
        <div class="subtitle">AI WEB CHAT v3.1 • Advanced AI Personal Assistant Service</div>
        
        <div class="lock-icon">🔒</div>
        
        <div class="message">
            <h2>Access Restricted</h2>
            <p>You need to be authenticated to access DeepChat.</p>
            <p>Please log in with your credentials to continue.</p>
        </div>
        
        <div class="action-buttons">
            <a href="/login" class="login-btn">Go to Login</a>
        </div>



        <div class="footer">
            Version 3.1, July 2025 • By Deeplica.AI<br>
            Secure AI Communication Platform
        </div>
    </div>

    <script>


        // Add some interactive effects
        document.addEventListener('mousemove', function(e) {
            const container = document.querySelector('.container');
            const rect = container.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            if (x >= 0 && x <= rect.width && y >= 0 && y <= rect.height) {
                const xPercent = (x / rect.width - 0.5) * 20;
                const yPercent = (y / rect.height - 0.5) * 20;
                container.style.transform = `perspective(1000px) rotateY(${xPercent}deg) rotateX(${-yPercent}deg)`;
            } else {
                container.style.transform = 'perspective(1000px) rotateY(0deg) rotateX(0deg)';
            }
        });

        // Auto-redirect after 30 seconds (optional)
        setTimeout(function() {
            const loginBtn = document.querySelector('.login-btn');
            loginBtn.style.animation = 'pulse 1s ease-in-out infinite';
            loginBtn.innerHTML = 'Redirecting to Login...';
            setTimeout(function() {
                window.location.href = '/login';
            }, 3000);
        }, 30000);
    </script>
</body>
</html>
