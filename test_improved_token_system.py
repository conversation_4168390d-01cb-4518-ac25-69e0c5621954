#!/usr/bin/env python3
"""
🔒 IMPROVED TAB TOKEN SYSTEM TEST

Tests the improved token system that:
1. Doesn't require password in token generation
2. Has longer token validity (7 days)
3. More flexible fingerprint matching
4. Redirects to login instead of opening new tabs
5. Less aggressive verification
"""

import requests
import time
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

from shared.port_manager import get_service_port

def test_improved_login_flow():
    """Test the improved login flow with better token handling"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔐 Testing improved login flow on port {web_chat_port}...")
        
        # Create session
        session = requests.Session()
        
        # Login
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
        
        print(f"✅ Login successful")
        
        # Check if we're on the chat page with token
        final_url = login_response.url
        if 'tab_token' in final_url:
            print(f"✅ Tab token found in URL")
            print(f"🔗 URL: {final_url}")
        else:
            print(f"❌ No tab token in final URL: {final_url}")
            return False
        
        # Test that the page loads without issues
        if 'chat' in final_url:
            print(f"✅ Successfully reached chat page")
        elif 'admin' in final_url:
            print(f"✅ Successfully reached admin page")
        else:
            print(f"⚠️ Unexpected page: {final_url}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in improved login test: {e}")
        return False

def test_token_persistence():
    """Test that tokens persist for reasonable time"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"⏰ Testing token persistence on port {web_chat_port}...")
        
        # Login and get initial token
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Initial login failed")
            return False
        
        initial_url = login_response.url
        print(f"✅ Initial login successful")
        
        # Wait a bit and try to access the same URL
        print(f"⏳ Waiting 5 seconds...")
        time.sleep(5)
        
        # Access the same URL again
        persistence_response = session.get(initial_url, timeout=10)
        
        if persistence_response.status_code == 200:
            print(f"✅ Token persisted after 5 seconds")
        else:
            print(f"❌ Token failed persistence test: {persistence_response.status_code}")
            return False
        
        # Try multiple requests to simulate normal usage
        for i in range(3):
            time.sleep(1)
            test_response = session.get(initial_url, timeout=10)
            if test_response.status_code != 200:
                print(f"❌ Token failed on request {i+1}: {test_response.status_code}")
                return False
        
        print(f"✅ Token persisted through multiple requests")
        return True
        
    except Exception as e:
        print(f"❌ Error in token persistence test: {e}")
        return False

def test_graceful_error_handling():
    """Test that errors redirect to login instead of showing harsh messages"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔄 Testing graceful error handling on port {web_chat_port}...")
        
        # Try to access chat with invalid token
        invalid_token_url = f"http://localhost:{web_chat_port}/chat?tab_token=invalid_token_123"
        
        response = requests.get(invalid_token_url, allow_redirects=False, timeout=10)
        
        # Should redirect to login
        if response.status_code in [302, 303, 307]:
            location = response.headers.get('location', '')
            if '/login' in location:
                print(f"✅ Invalid token correctly redirects to login")
                print(f"🔗 Redirect location: {location}")
            else:
                print(f"❌ Invalid token redirects to unexpected location: {location}")
                return False
        else:
            print(f"❌ Invalid token handling failed: {response.status_code}")
            return False
        
        # Try to access chat with no token
        no_token_url = f"http://localhost:{web_chat_port}/chat"
        
        response = requests.get(no_token_url, allow_redirects=False, timeout=10)
        
        # Should redirect to add token or login
        if response.status_code in [302, 303, 307]:
            print(f"✅ No token correctly handled with redirect")
        else:
            print(f"❌ No token handling failed: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error in graceful error handling test: {e}")
        return False

def test_admin_page_tokens():
    """Test that admin page also has proper token handling"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔧 Testing admin page token handling on port {web_chat_port}...")
        
        # Login as admin
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        
        login_response = session.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=False,
            timeout=10
        )
        
        if login_response.status_code not in [302, 303, 307]:
            print(f"❌ Admin login failed: {login_response.status_code}")
            return False
        
        # Try to access admin page
        admin_response = session.get(
            f"http://localhost:{web_chat_port}/admin",
            allow_redirects=True,
            timeout=10
        )
        
        if admin_response.status_code != 200:
            print(f"❌ Admin page access failed: {admin_response.status_code}")
            return False
        
        # Check if admin URL has token
        admin_url = admin_response.url
        if 'tab_token' in admin_url:
            print(f"✅ Admin page has tab token")
            print(f"🔗 Admin URL: {admin_url}")
        else:
            print(f"❌ Admin page missing tab token: {admin_url}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error in admin page token test: {e}")
        return False

def test_cross_session_security():
    """Test that different sessions still can't use each other's tokens"""
    try:
        web_chat_port = get_service_port('web-chat')
        print(f"🔒 Testing cross-session security on port {web_chat_port}...")
        
        # Create two separate sessions
        session1 = requests.Session()
        session2 = requests.Session()
        
        login_data = {'username': 'admin', 'password': 'admin123'}
        
        # Login with first session
        response1 = session1.post(
            f"http://localhost:{web_chat_port}/login",
            data=login_data,
            allow_redirects=True,
            timeout=10
        )
        
        if response1.status_code != 200:
            print(f"❌ Session 1 login failed")
            return False
        
        url1 = response1.url
        print(f"✅ Session 1 login successful")
        
        # Try to use session 1's URL with session 2
        response2 = session2.get(url1, allow_redirects=False, timeout=10)
        
        # Should redirect to login or unauthorized
        if response2.status_code in [302, 303, 307, 401]:
            print(f"✅ Cross-session access correctly blocked")
        else:
            print(f"❌ Cross-session security failed: {response2.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error in cross-session security test: {e}")
        return False

def main():
    """Run all improved token system tests"""
    print("🔒 IMPROVED TAB TOKEN SYSTEM TEST")
    print("=" * 60)
    print("Testing improvements:")
    print("- No password dependency in tokens")
    print("- Longer token validity (7 days)")
    print("- More flexible fingerprint matching")
    print("- Graceful error handling")
    print("- Redirect to login (no new tabs)")
    print()
    
    tests_passed = 0
    total_tests = 5
    
    # Test 1: Improved login flow
    print("🧪 TEST 1: Improved Login Flow")
    if test_improved_login_flow():
        tests_passed += 1
    
    # Test 2: Token persistence
    print("\n🧪 TEST 2: Token Persistence")
    if test_token_persistence():
        tests_passed += 1
    
    # Test 3: Graceful error handling
    print("\n🧪 TEST 3: Graceful Error Handling")
    if test_graceful_error_handling():
        tests_passed += 1
    
    # Test 4: Admin page tokens
    print("\n🧪 TEST 4: Admin Page Token Handling")
    if test_admin_page_tokens():
        tests_passed += 1
    
    # Test 5: Cross-session security
    print("\n🧪 TEST 5: Cross-Session Security")
    if test_cross_session_security():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 60)
    print(f"🔒 TEST RESULTS: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✅ ALL TESTS PASSED - Improved token system is working!")
        print("🎉 Improvements verified:")
        print("   - Simplified token generation (no password)")
        print("   - Longer token validity for better UX")
        print("   - Graceful error handling")
        print("   - Proper redirect behavior")
        print("   - Cross-session security maintained")
    else:
        print("❌ SOME TESTS FAILED - Token system needs more improvements")
        
        if tests_passed >= 4:
            print("🔧 RECOMMENDATION: Minor issues remain")
        elif tests_passed >= 3:
            print("🔧 RECOMMENDATION: Most features work, some edge cases")
        else:
            print("🔧 RECOMMENDATION: Major improvements needed")
    
    print(f"\n🌐 Manual test: Open http://localhost:8007")
    print(f"🔐 Login with: admin / admin123")
    print(f"💡 Try refreshing page and using for extended time")

if __name__ == '__main__':
    main()
