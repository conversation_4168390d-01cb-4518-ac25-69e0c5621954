"""
Data models for the Dialogue Agent Service.
Now imports communication models from shared_models to avoid duplication.
"""

import sys
import os

# Add project root to path for shared models
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared_models import TaskRequest, TaskResponse
