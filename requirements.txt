# =============================================================================
# ROOT REQUIREMENTS FILE - MICROSERVICES ARCHITECTURE
# =============================================================================
# This file includes all service-specific requirements using -r references
# This approach avoids duplication and maintains single source of truth

# =============================================================================
# MICROSERVICE REQUIREMENTS
# =============================================================================

# Backend API Service
-r backend/requirements.txt

# Dispatcher Service
-r dispatcher/requirements.txt

# Dialogue Agent Service
-r agents/dialogue/requirements.txt

# Planner Agent Service
-r agents/planner/requirements.txt

# Phone Agent Service
-r agents/phone/requirements.txt

# =============================================================================
# NOTES
# =============================================================================
#
# For development dependencies, install separately:
#   pip install -r requirements-dev.txt

# =============================================================================
# USAGE
# =============================================================================
#
# Install all dependencies for all services:
#   pip install -r requirements.txt
#
# Install for specific service only:
#   pip install -r backend/requirements.txt
#   pip install -r dispatcher/requirements.txt
#   pip install -r agents/dialogue/requirements.txt
#   pip install -r agents/planner/requirements.txt
#
# Benefits of this approach:
# - No duplication of dependencies
# - Each service maintains its own requirements
# - Easy to manage service-specific dependencies
# - Single command installs everything
#
# =============================================================================
