#!/bin/bash
# =============================================================================
# DEEPLICA SERVICE SCRIPT - ALL PORTS MANAGED DYNAMICALLY
# =============================================================================
# ALL ports are assigned by shared/port_manager.py - NO HARDCODED PORTS
# External services adapt to assigned ports through configuration
# =============================================================================

# Resilient AI Mission Orchestration System Startup Script
# This script starts all microservices in the correct order with proper waiting

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')] ✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[$(date '+%H:%M:%S')] ⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}[$(date '+%H:%M:%S')] ❌ $1${NC}"
}

# Function to wait for a service to be healthy
wait_for_service() {
    local service_name=$1
    local port=$2
    local max_attempts=${3:-60}  # Default 2 minutes (60 * 2 seconds)
    
    print_status "Waiting for $service_name on port $port to be healthy..."
    
    local attempt=1
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "http://localhost:$port/health" > /dev/null 2>&1; then
            print_success "$service_name is healthy and responding!"
            return 0
        fi
        
        if [ $((attempt % 15)) -eq 0 ]; then
            print_status "Still waiting for $service_name... (attempt $attempt/$max_attempts)"
        fi
        
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to become healthy within $((max_attempts * 2)) seconds"
    return 1
}

# Function to start a service in background
start_service() {
    local service_name=$1
    local service_dir=$2
    local port=$3
    local current_dir=$(pwd)
    local log_file="$current_dir/logs/${service_name}.log"
    local pid_file="$current_dir/logs/${service_name}.pid"

    print_status "Starting $service_name..."

    # Create logs directory if it doesn't exist
    mkdir -p "$current_dir/logs"

    # Start the service in background
    cd "$service_dir"
    if [ "$service_name" = "backend" ]; then
        python3 -m app.main > "$log_file" 2>&1 &
    else
        PORT=$port python3 -m app.main > "$log_file" 2>&1 &
    fi
    local pid=$!
    cd "$current_dir"

    # Save PID for later cleanup
    echo $pid > "$pid_file"

    print_status "$service_name started with PID $pid"
    return 0
}

# Main startup sequence
main() {
    print_status "🚀 Starting AI Mission Orchestration System with Resilient Startup"
    print_status "💪 All services will wait properly and NEVER crash during startup"
    
    # Stop any existing services first
    print_status "🧹 Stopping any existing services..."
    ./stop_all_services.sh 2>/dev/null || true
    
    # Wait a moment for cleanup
    sleep 2
    
    # Step 1: Start Backend API first (it's the foundation)
    print_status "📡 Step 1: Starting Backend API (foundation service)"
    start_service "backend" "backend" "8000"
    
    # Wait for Backend API to be healthy before starting other services
    if ! wait_for_service "Backend API" "8000" 90; then  # 3 minutes for Backend API
        print_error "Backend API failed to start - cannot continue"
        exit 1
    fi
    
    # Step 2: Start Dispatcher (orchestration service)
    print_status "🎯 Step 2: Starting Dispatcher (orchestration service)"
    start_service "dispatcher" "dispatcher" "8001"
    
    # Step 3: Start all agent services in parallel (they all depend on Backend API)
    print_status "🤖 Step 3: Starting all agent services in parallel"
    start_service "dialogue-agent" "agents/dialogue" "8002" &
    start_service "planner-agent" "agents/planner" "8003" &
    start_service "phone-agent" "agents/phone" "8004" &
    
    # Wait for all background jobs to complete
    wait
    
    # Step 4: Wait for all services to be healthy
    print_status "🔍 Step 4: Verifying all services are healthy"
    
    services=(
        "Backend API:8000"
        "Dispatcher:8001" 
        "Dialogue Agent:8002"
        "Planner Agent:8003"
        "Phone Agent:8004"
    )
    
    all_healthy=true
    for service_info in "${services[@]}"; do
        IFS=':' read -r service_name port <<< "$service_info"
        if ! wait_for_service "$service_name" "$port" 45; then  # 1.5 minutes per service
            all_healthy=false
        fi
    done
    
    if [ "$all_healthy" = true ]; then
        print_success "🎉 ALL SERVICES ARE HEALTHY AND RUNNING!"
        print_success "System is ready for use"
        
        # Show service status
        echo ""
        print_status "📊 Service Status Summary:"
        for service_info in "${services[@]}"; do
            IFS=':' read -r service_name port <<< "$service_info"
            if curl -s "http://localhost:$port/health" | jq -r '.message' 2>/dev/null; then
                echo "  ✅ $service_name (port $port): $(curl -s "http://localhost:$port/health" | jq -r '.message' 2>/dev/null || echo 'Running')"
            else
                echo "  ❌ $service_name (port $port): Not responding"
            fi
        done
        
    else
        print_error "Some services failed to start properly"
        print_warning "Check the logs in the logs/ directory for details"
        exit 1
    fi
}

# Run main function
main "$@"
