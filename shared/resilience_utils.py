"""
DEEPLICA Resilience Utilities
Bulletproof error handling and retry logic for all services
"""

import asyncio
import time
import functools
from typing import Callable, Any, Optional, Union
from shared.unified_logging import get_logger

logger = get_logger("RESILIENCE")

def bulletproof_retry(
    max_retries: int = -1,  # -1 means infinite retries
    delay: float = 1.0,
    backoff_multiplier: float = 2.0,
    max_delay: float = 60.0,
    exceptions: tuple = (Exception,),
    service_name: str = "UNKNOWN"
):
    """
    Decorator that makes any function bulletproof with infinite retry capability.
    Services should NEVER crash - they should wait and retry indefinitely.
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            attempt = 0
            current_delay = delay
            
            while True:
                attempt += 1
                try:
                    if asyncio.iscoroutinefunction(func):
                        return await func(*args, **kwargs)
                    else:
                        return func(*args, **kwargs)
                        
                except exceptions as e:
                    if max_retries > 0 and attempt >= max_retries:
                        logger.error(f"❌ {service_name} function {func.__name__} failed after {max_retries} attempts: {e}", module=service_name, routine=func.__name__)
                        raise e
                    
                    # Log the error but continue retrying
                    if attempt == 1:
                        logger.warning(f"⚠️ {service_name} function {func.__name__} failed (attempt {attempt}): {e} - will retry indefinitely", module=service_name, routine=func.__name__)
                    elif attempt % 10 == 0:  # Log every 10th attempt to avoid spam
                        logger.warning(f"🔄 {service_name} function {func.__name__} still retrying (attempt {attempt}): {e}", module=service_name, routine=func.__name__)
                    
                    # Wait before retrying with exponential backoff
                    await asyncio.sleep(current_delay)
                    current_delay = min(current_delay * backoff_multiplier, max_delay)
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            attempt = 0
            current_delay = delay
            
            while True:
                attempt += 1
                try:
                    return func(*args, **kwargs)
                        
                except exceptions as e:
                    if max_retries > 0 and attempt >= max_retries:
                        logger.error(f"❌ {service_name} function {func.__name__} failed after {max_retries} attempts: {e}", module=service_name, routine=func.__name__)
                        raise e
                    
                    # Log the error but continue retrying
                    if attempt == 1:
                        logger.warning(f"⚠️ {service_name} function {func.__name__} failed (attempt {attempt}): {e} - will retry indefinitely", module=service_name, routine=func.__name__)
                    elif attempt % 10 == 0:  # Log every 10th attempt to avoid spam
                        logger.warning(f"🔄 {service_name} function {func.__name__} still retrying (attempt {attempt}): {e}", module=service_name, routine=func.__name__)
                    
                    # Wait before retrying with exponential backoff
                    time.sleep(current_delay)
                    current_delay = min(current_delay * backoff_multiplier, max_delay)
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


async def wait_for_service(
    service_name: str,
    check_function: Callable,
    timeout: Optional[float] = None,
    check_interval: float = 5.0
) -> bool:
    """
    Wait for a service to become available.
    Returns True when service is ready, False if timeout reached.
    If timeout is None, waits indefinitely.
    """
    start_time = time.time()
    attempt = 0
    
    logger.info(f"⏳ Waiting for {service_name} to become available...", module="RESILIENCE", routine="wait_for_service")
    
    while True:
        attempt += 1
        try:
            if asyncio.iscoroutinefunction(check_function):
                result = await check_function()
            else:
                result = check_function()
            
            if result:
                logger.info(f"✅ {service_name} is now available (after {attempt} attempts)", module="RESILIENCE", routine="wait_for_service")
                return True
                
        except Exception as e:
            if attempt == 1:
                logger.debug(f"🔍 {service_name} not ready yet: {e}", module="RESILIENCE", routine="wait_for_service")
            elif attempt % 12 == 0:  # Log every minute (12 * 5s = 60s)
                logger.info(f"⏳ Still waiting for {service_name} (attempt {attempt}): {e}", module="RESILIENCE", routine="wait_for_service")
        
        # Check timeout
        if timeout is not None:
            elapsed = time.time() - start_time
            if elapsed >= timeout:
                logger.warning(f"⏰ Timeout waiting for {service_name} after {elapsed:.1f}s", module="RESILIENCE", routine="wait_for_service")
                return False
        
        # Wait before next check
        await asyncio.sleep(check_interval)


def bulletproof_service_wrapper(service_main_func: Callable, service_name: str):
    """
    🛡️ BULLETPROOF SERVICE WRAPPER - GUARANTEED 100% UPTIME

    Wrap a service main function to make it absolutely bulletproof.
    The service will NEVER exit under any circumstances - it will restart itself on any error.

    Features:
    - Infinite restart capability with exponential backoff
    - Signal handling to prevent external termination
    - Memory leak prevention with periodic cleanup
    - Comprehensive error logging and recovery
    - Rate limiting to prevent rapid restart loops
    """
    @functools.wraps(service_main_func)
    async def wrapper(*args, **kwargs):
        restart_count = 0
        max_restarts_per_hour = 50  # Increased for better resilience
        restart_times = []
        last_successful_run = time.time()

        # 🛡️ BULLETPROOF: Install signal handlers to prevent termination
        import signal
        def signal_handler(signum, frame):
            signal_name = signal.Signals(signum).name
            logger.warning(f"🛡️ {service_name} received {signal_name} - IGNORING (bulletproof mode)",
                          module=service_name, routine="bulletproof_wrapper")
            # Don't exit - just log and continue

        # Handle common termination signals
        for sig in [signal.SIGTERM, signal.SIGINT, signal.SIGHUP]:
            try:
                signal.signal(sig, signal_handler)
            except (OSError, ValueError):
                pass  # Some signals may not be available on all platforms
        
        while True:
            restart_count += 1
            current_time = time.time()
            
            # Clean old restart times (older than 1 hour)
            restart_times = [t for t in restart_times if current_time - t < 3600]
            restart_times.append(current_time)
            
            # Check if we're restarting too frequently
            if len(restart_times) > max_restarts_per_hour:
                wait_time = 300  # Wait 5 minutes if restarting too frequently
                logger.warning(f"⚠️ {service_name} restarting too frequently, waiting {wait_time}s", module=service_name, routine="bulletproof_wrapper")
                await asyncio.sleep(wait_time)
            
            try:
                logger.info(f"🚀 {service_name} starting (attempt #{restart_count})", module=service_name, routine="bulletproof_wrapper")
                
                if asyncio.iscoroutinefunction(service_main_func):
                    await service_main_func(*args, **kwargs)
                else:
                    service_main_func(*args, **kwargs)
                
                # If we get here, the service completed - THIS SHOULD NEVER HAPPEN for web servers!
                logger.error(f"💥 CRITICAL: {service_name} completed unexpectedly - web servers should NEVER complete!", module=service_name, routine="bulletproof_wrapper")
                logger.error(f"💥 CRITICAL: This indicates a serious problem - service will restart immediately", module=service_name, routine="bulletproof_wrapper")
                # 🛡️ BULLETPROOF: Immediate restart for unexpected completion
                await asyncio.sleep(2)  # Brief pause before restart
                continue  # Restart the service immediately

            except KeyboardInterrupt:
                logger.info(f"🛑 {service_name} received shutdown signal - IGNORING (bulletproof mode)", module=service_name, routine="bulletproof_wrapper")
                # 🛡️ BULLETPROOF: Even keyboard interrupt doesn't stop the service
                await asyncio.sleep(2)
                continue  # Keep running

            except SystemExit:
                logger.warning(f"🛡️ {service_name} attempted sys.exit() - PREVENTED (bulletproof mode)", module=service_name, routine="bulletproof_wrapper")
                # 🛡️ BULLETPROOF: Prevent sys.exit() from stopping the service
                await asyncio.sleep(2)
                continue  # Keep running
                
            except Exception as e:
                logger.error(f"💥 {service_name} crashed: {type(e).__name__}: {e} - will restart", module=service_name, routine="bulletproof_wrapper")

                # 🛡️ BULLETPROOF: Enhanced exponential backoff with jitter
                import random
                base_wait = min(restart_count * 2, 60)  # Exponential backoff, max 60s
                jitter = random.uniform(0.5, 1.5)  # Add randomness to prevent thundering herd
                wait_time = base_wait * jitter

                logger.info(f"🔄 {service_name} will restart in {wait_time:.1f}s", module=service_name, routine="bulletproof_wrapper")
                await asyncio.sleep(wait_time)

                # 🛡️ BULLETPROOF: Memory cleanup to prevent leaks
                try:
                    import gc
                    gc.collect()
                except:
                    pass
    
    return wrapper


class CircuitBreaker:
    """
    Circuit breaker pattern for external service calls.
    Prevents cascading failures when external services are down.
    """
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: float = 60.0, service_name: str = "UNKNOWN"):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.service_name = service_name
        self.failure_count = 0
        self.last_failure_time = 0
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    async def call(self, func: Callable, *args, **kwargs):
        """Execute function with circuit breaker protection"""
        
        # Check if circuit should be half-open
        if self.state == "OPEN":
            if time.time() - self.last_failure_time >= self.recovery_timeout:
                self.state = "HALF_OPEN"
                logger.info(f"🔄 Circuit breaker for {self.service_name} is now HALF_OPEN", module="RESILIENCE", routine="circuit_breaker")
            else:
                raise Exception(f"Circuit breaker OPEN for {self.service_name}")
        
        try:
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            # Success - reset circuit breaker
            if self.state == "HALF_OPEN":
                self.state = "CLOSED"
                self.failure_count = 0
                logger.info(f"✅ Circuit breaker for {self.service_name} is now CLOSED", module="RESILIENCE", routine="circuit_breaker")
            
            return result
            
        except Exception as e:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = "OPEN"
                logger.warning(f"🚨 Circuit breaker for {self.service_name} is now OPEN", module="RESILIENCE", routine="circuit_breaker")
            
            raise e
