"""
Backend Database Client for Dispatcher.
HTTP client wrapper to communicate with backend database service.
"""

import httpx
import os
import sys
from typing import List, Optional, Dict, Any
from shared.unified_logging import get_dispatcher_logger

# Initialize unified logger
logger = get_dispatcher_logger()

# Add project root to path for shared models
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared_models import Mission, Task
from shared.port_manager import get_service_port, get_localhost
from shared.api_manager import get_service_url


class BackendDatabaseClient:
    """
    HTTP client wrapper for database operations.
    Communicates with backend service instead of direct database access.
    Makes it clear this is NOT a direct database service.
    """

    def __init__(self):
        # Use API manager for backend URL
        self.backend_url = get_service_url("backend")
        self.http_client = httpx.AsyncClient(timeout=30.0)
        logger.info(f"🔌 Database service initialized with backend: {self.backend_url}", module="DISPATCHER", routine="__init__")

    async def connect(self) -> None:
        """Test connection to backend service - RESILIENT to backend unavailability"""
        import asyncio
        attempt = 1

        while True:
            try:
                response = await self.http_client.get(f"{self.backend_url}/health")
                response.raise_for_status()
                logger.info("✅ Connected to backend database service", module="DISPATCHER", routine="connect")
                return  # Success - exit the retry loop

            except Exception as e:
                if attempt == 1:
                    logger.info("⏳ Backend service not ready yet - dispatcher will wait indefinitely", module="DISPATCHER", routine="connect")
                elif attempt % 12 == 0:  # Log every minute (12 * 5s = 60s)
                    logger.info(f"⏳ Still waiting for backend service (attempt {attempt}): {e}", module="DISPATCHER", routine="connect")

                # Wait before retrying
                await asyncio.sleep(5)
                attempt += 1
                # Continue infinite loop - dispatcher must never crash due to backend unavailability

    async def disconnect(self) -> None:
        """Close HTTP client"""
        await self.http_client.aclose()
        logger.info("🔌 Disconnected from backend service", module="DISPATCHER", routine="disconnect")

    async def get_mission(self, mission_id: str) -> Optional[Mission]:
        """Get mission from backend"""
        logger.debug("📖 Getting mission: {mission_id}", module="DISPATCHER", routine="get_mission")
        try:
            response = await self.http_client.get(f"{self.backend_url}/api/v1/internal/missions/{mission_id}")
            if response.status_code == 404:
                return None
            response.raise_for_status()
            data = response.json()
            return Mission(**data)
        except Exception as e:
            logger.error("❌ Failed to get mission {mission_id}: {e}", module="DISPATCHER", routine="get_mission")
            raise

    async def update_mission(self, mission: Mission) -> bool:
        """Update mission in backend"""
        try:
            response = await self.http_client.put(
                f"{self.backend_url}/api/v1/internal/missions/{mission.mission_id}",
                json=mission.model_dump(mode='json')
            )
            response.raise_for_status()
            return True
        except Exception as e:
            logger.error("❌ Failed to update mission {mission.mission_id}: {e}", module="DISPATCHER", routine="update_mission")
            raise

    async def get_task(self, task_id: str) -> Optional[Task]:
        """Get task from backend"""
        try:
            response = await self.http_client.get(f"{self.backend_url}/api/v1/internal/tasks/{task_id}")
            if response.status_code == 404:
                return None
            response.raise_for_status()
            data = response.json()
            return Task(**data)
        except Exception as e:
            logger.error("❌ Failed to get task {task_id}: {e}", module="DISPATCHER", routine="get_task")
            raise

    async def update_task(self, task: Task) -> bool:
        """Update task in backend"""
        try:
            response = await self.http_client.put(
                f"{self.backend_url}/api/v1/internal/tasks/{task.task_id}",
                json=task.model_dump(mode='json')
            )
            response.raise_for_status()
            return True
        except Exception as e:
            logger.error("❌ Failed to update task {task.task_id}: {e}", module="DISPATCHER", routine="update_task")
            raise

    async def get_tasks_by_mission(self, mission_id: str) -> List[Task]:
        """Get all tasks for a mission"""
        logger.debug("📖 Getting tasks for mission: {mission_id}", module="DISPATCHER", routine="get_tasks_by_mission")

        try:
            response = await self.http_client.get(f"{self.backend_url}/api/v1/internal/missions/{mission_id}/tasks")
            response.raise_for_status()

            tasks_data = response.json()
            return [Task(**task_data) for task_data in tasks_data]

        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                logger.debug("Mission not found: {mission_id}", module="DISPATCHER", routine="get_tasks_by_mission")
                return []
            logger.error("❌ HTTP error getting tasks for mission {mission_id}: {e}", module="DISPATCHER", routine="get_tasks_by_mission")
            raise
        except Exception as e:
            logger.error("❌ Error getting tasks for mission {mission_id}: {e}", module="DISPATCHER", routine="get_tasks_by_mission")
            raise

    async def get_executable_tasks(self, mission_id: str) -> List[Task]:
        """Get executable tasks for mission from backend"""
        try:
            response = await self.http_client.get(
                f"{self.backend_url}/api/v1/internal/missions/{mission_id}/executable-tasks"
            )
            response.raise_for_status()
            data = response.json()
            return [Task(**task_data) for task_data in data]
        except Exception as e:
            logger.error(f"❌ Failed to get executable tasks for {mission_id}: {e}", module="DISPATCHER", routine="get_executable_tasks")
            raise

    async def get_mission_progress(self, mission_id: str) -> Dict[str, Any]:
        """Get mission progress from backend"""
        try:
            response = await self.http_client.get(
                f"{self.backend_url}/api/v1/internal/missions/{mission_id}/progress"
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error("❌ Failed to get mission progress for {mission_id}: {e}", module="DISPATCHER", routine="get_mission_progress")
            raise

    async def is_mission_complete(self, mission_id: str) -> bool:
        """Check if mission is complete via backend"""
        try:
            response = await self.http_client.get(
                f"{self.backend_url}/api/v1/internal/missions/{mission_id}/complete"
            )
            response.raise_for_status()
            data = response.json()
            return data.get("is_complete", False)
        except Exception as e:
            logger.error("❌ Failed to check if mission {mission_id} is complete: {e}", module="DISPATCHER", routine="is_mission_complete")
            raise

    async def is_mission_failed(self, mission_id: str) -> bool:
        """Check if mission has failed via backend"""
        try:
            response = await self.http_client.get(
                f"{self.backend_url}/api/v1/internal/missions/{mission_id}/failed"
            )
            response.raise_for_status()
            data = response.json()
            return data.get("is_failed", False)
        except Exception as e:
            logger.error("❌ Failed to check if mission {mission_id} has failed: {e}", module="DISPATCHER", routine="is_mission_failed")
            raise

    async def list_missions(
        self,
        status: Optional[str] = None,
        user_id: Optional[str] = None,
        limit: int = 100,
        skip: int = 0
    ) -> List[Mission]:
        """List missions from backend"""
        try:
            params = {"limit": limit, "skip": skip}
            if status:
                params["status"] = status
            if user_id:
                params["user_id"] = user_id

            response = await self.http_client.get(
                f"{self.backend_url}/api/v1/internal/missions",
                params=params
            )
            response.raise_for_status()
            data = response.json()
            return [Mission(**mission_data) for mission_data in data]
        except Exception as e:
            logger.error("❌ Failed to list missions: {e}", module="DISPATCHER", routine="list_missions")
            raise

    async def get_mission_tasks(self, mission_id: str) -> List[Task]:
        """Get all tasks for a mission from backend"""
        try:
            response = await self.http_client.get(
                f"{self.backend_url}/api/v1/internal/missions/{mission_id}/tasks"
            )
            response.raise_for_status()
            data = response.json()
            return [Task(**task_data) for task_data in data]
        except Exception as e:
            logger.error("❌ Failed to get mission tasks for {mission_id}: {e}", module="DISPATCHER", routine="get_mission_tasks")
            raise

    async def create_task(self, task: Task) -> str:
        """Create task in backend"""
        try:
            response = await self.http_client.post(
                f"{self.backend_url}/api/v1/tasks",
                json=task.model_dump(mode='json')
            )
            response.raise_for_status()
            data = response.json()
            logger.info("✅ Task created via backend: {task.task_id}", module="DISPATCHER", routine="create_task")
            return data.get("task_id", task.task_id)
        except Exception as e:
            logger.error("❌ Failed to create task {task.task_id}: {e}", module="DISPATCHER", routine="create_task")
            raise

    async def get_pending_tasks(self) -> List[Task]:
        """Get all pending tasks from backend"""
        try:
            response = await self.http_client.get(
                f"{self.backend_url}/api/v1/internal/tasks/pending"
            )
            response.raise_for_status()
            data = response.json()
            return [Task(**task_data) for task_data in data]
        except Exception as e:
            logger.error(f"❌ Failed to get pending tasks: {e}", module="DISPATCHER", routine="get_pending_tasks")
            raise

    async def update_task(self, task: Task) -> bool:
        """Update task in backend"""
        try:
            response = await self.http_client.put(
                f"{self.backend_url}/api/v1/internal/tasks/{task.task_id}",
                json=task.model_dump(mode='json')
            )
            response.raise_for_status()
            return True
        except Exception as e:
            logger.error("❌ Failed to update task {task.task_id}: {e}", module="DISPATCHER", routine="update_task")
            raise


# Re-export for convenience
__all__ = ["BackendDatabaseClient"]
