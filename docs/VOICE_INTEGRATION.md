# 🎤 VOICE INTEGRATION GUIDE

## **Connecting Voice to Dialogue Service - Complete Implementation Guide**

---

## 🎯 **Overview**

This guide provides comprehensive instructions for integrating voice capabilities with the Deeplica V3 dialogue service, including web-based Push-to-Talk (PTT) interfaces, real-time voice processing, and seamless conversation management.

---

## 🌐 **Web-Based Voice Interface**

### **Option 1: Push-to-Talk (PTT) Web Interface**

#### **Frontend Implementation**
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deeplica Voice Interface</title>
    <style>
        .voice-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .ptt-button {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            border: none;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .ptt-button.idle {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
        }
        
        .ptt-button.recording {
            background: linear-gradient(45deg, #f44336, #da190b);
            color: white;
            animation: pulse 1s infinite;
        }
        
        .ptt-button.processing {
            background: linear-gradient(45deg, #ff9800, #e68900);
            color: white;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .conversation {
            width: 100%;
            max-width: 600px;
            margin-top: 20px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .message {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        
        .user-message {
            background: #e3f2fd;
            text-align: right;
        }
        
        .ai-message {
            background: #f1f8e9;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="voice-container">
        <h1>🎤 Deeplica Voice Interface</h1>
        
        <button id="pttButton" class="ptt-button idle">
            <div id="buttonText">Hold to Talk</div>
            <div id="buttonSubtext">Press and hold to record</div>
        </button>
        
        <div class="conversation" id="conversation">
            <div class="message ai-message">
                <strong>Deeplica:</strong> Hello! I'm ready to help you. Press and hold the button to speak.
            </div>
        </div>
    </div>

    <script>
        class VoiceInterface {
            constructor() {
                this.isRecording = false;
                this.mediaRecorder = null;
                this.audioChunks = [];
                this.conversationId = this.generateUUID();
                
                this.button = document.getElementById('pttButton');
                this.buttonText = document.getElementById('buttonText');
                this.buttonSubtext = document.getElementById('buttonSubtext');
                this.conversation = document.getElementById('conversation');
                
                this.initializeEventListeners();
            }
            
            generateUUID() {
                return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                    const r = Math.random() * 16 | 0;
                    const v = c == 'x' ? r : (r & 0x3 | 0x8);
                    return v.toString(16);
                });
            }
            
            initializeEventListeners() {
                // Mouse events
                this.button.addEventListener('mousedown', () => this.startRecording());
                this.button.addEventListener('mouseup', () => this.stopRecording());
                this.button.addEventListener('mouseleave', () => this.stopRecording());
                
                // Touch events for mobile
                this.button.addEventListener('touchstart', (e) => {
                    e.preventDefault();
                    this.startRecording();
                });
                this.button.addEventListener('touchend', (e) => {
                    e.preventDefault();
                    this.stopRecording();
                });
                
                // Keyboard events (spacebar)
                document.addEventListener('keydown', (e) => {
                    if (e.code === 'Space' && !this.isRecording) {
                        e.preventDefault();
                        this.startRecording();
                    }
                });
                document.addEventListener('keyup', (e) => {
                    if (e.code === 'Space' && this.isRecording) {
                        e.preventDefault();
                        this.stopRecording();
                    }
                });
            }
            
            async startRecording() {
                if (this.isRecording) return;
                
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                    this.mediaRecorder = new MediaRecorder(stream);
                    this.audioChunks = [];
                    
                    this.mediaRecorder.ondataavailable = (event) => {
                        this.audioChunks.push(event.data);
                    };
                    
                    this.mediaRecorder.onstop = () => {
                        this.processAudio();
                    };
                    
                    this.mediaRecorder.start();
                    this.isRecording = true;
                    
                    this.updateButtonState('recording');
                    
                } catch (error) {
                    console.error('Error starting recording:', error);
                    this.addMessage('System', 'Error: Could not access microphone', 'ai-message');
                }
            }
            
            stopRecording() {
                if (!this.isRecording) return;
                
                this.mediaRecorder.stop();
                this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
                this.isRecording = false;
                
                this.updateButtonState('processing');
            }
            
            async processAudio() {
                const audioBlob = new Blob(this.audioChunks, { type: 'audio/wav' });
                
                try {
                    // Convert audio to text using speech recognition
                    const transcript = await this.speechToText(audioBlob);
                    
                    if (transcript) {
                        this.addMessage('You', transcript, 'user-message');
                        
                        // Send to dialogue service
                        const response = await this.sendToDialogue(transcript);
                        this.addMessage('Deeplica', response, 'ai-message');
                        
                        // Convert response to speech
                        await this.textToSpeech(response);
                    }
                    
                } catch (error) {
                    console.error('Error processing audio:', error);
                    this.addMessage('System', 'Error processing audio', 'ai-message');
                }
                
                this.updateButtonState('idle');
            }
            
            async speechToText(audioBlob) {
                // Option 1: Use Web Speech API
                return new Promise((resolve, reject) => {
                    const recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
                    recognition.lang = 'en-US';
                    recognition.interimResults = false;
                    recognition.maxAlternatives = 1;
                    
                    recognition.onresult = (event) => {
                        const transcript = event.results[0][0].transcript;
                        resolve(transcript);
                    };
                    
                    recognition.onerror = (event) => {
                        reject(event.error);
                    };
                    
                    // Convert blob to audio for recognition
                    const audio = new Audio(URL.createObjectURL(audioBlob));
                    recognition.start();
                });
                
                // Option 2: Send to backend for processing
                /*
                const formData = new FormData();
                formData.append('audio', audioBlob, 'recording.wav');
                
                const response = await fetch('/api/speech-to-text', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                return result.transcript;
                */
            }
            
            async sendToDialogue(message) {
                try {
                    const response = await fetch('http://localhost:8002/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            message: message,
                            conversation_id: this.conversationId,
                            user_id: 'voice-user'
                        })
                    });
                    
                    const result = await response.json();
                    return result.response;
                    
                } catch (error) {
                    console.error('Error sending to dialogue service:', error);
                    return 'Sorry, I encountered an error processing your request.';
                }
            }
            
            async textToSpeech(text) {
                // Option 1: Use Web Speech API
                const utterance = new SpeechSynthesisUtterance(text);
                utterance.rate = 0.9;
                utterance.pitch = 1;
                utterance.volume = 0.8;
                
                speechSynthesis.speak(utterance);
                
                // Option 2: Use backend TTS service
                /*
                const response = await fetch('/api/text-to-speech', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ text: text })
                });
                
                const audioBlob = await response.blob();
                const audio = new Audio(URL.createObjectURL(audioBlob));
                audio.play();
                */
            }
            
            updateButtonState(state) {
                this.button.className = `ptt-button ${state}`;
                
                switch (state) {
                    case 'idle':
                        this.buttonText.textContent = 'Hold to Talk';
                        this.buttonSubtext.textContent = 'Press and hold to record';
                        break;
                    case 'recording':
                        this.buttonText.textContent = 'Recording...';
                        this.buttonSubtext.textContent = 'Release to send';
                        break;
                    case 'processing':
                        this.buttonText.textContent = 'Processing...';
                        this.buttonSubtext.textContent = 'Please wait';
                        break;
                }
            }
            
            addMessage(sender, message, className) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${className}`;
                messageDiv.innerHTML = `<strong>${sender}:</strong> ${message}`;
                
                this.conversation.appendChild(messageDiv);
                this.conversation.scrollTop = this.conversation.scrollHeight;
            }
        }
        
        // Initialize voice interface when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new VoiceInterface();
        });
    </script>
</body>
</html>
```

---

## 🔧 **Backend Voice Processing Service**

### **Speech-to-Text Endpoint**
```python
from fastapi import FastAPI, File, UploadFile, HTTPException
import speech_recognition as sr
import io
import wave

app = FastAPI()

@app.post("/api/speech-to-text")
async def speech_to_text(audio: UploadFile = File(...)):
    """Convert speech audio to text."""
    try:
        # Read audio file
        audio_data = await audio.read()
        
        # Use speech recognition
        recognizer = sr.Recognizer()
        
        # Convert to audio format
        audio_file = io.BytesIO(audio_data)
        
        with sr.AudioFile(audio_file) as source:
            audio_data = recognizer.record(source)
            
        # Recognize speech
        transcript = recognizer.recognize_google(audio_data)
        
        return {
            "transcript": transcript,
            "confidence": 0.95  # Google doesn't provide confidence
        }
        
    except sr.UnknownValueError:
        raise HTTPException(status_code=400, detail="Could not understand audio")
    except sr.RequestError as e:
        raise HTTPException(status_code=500, detail=f"Speech recognition error: {e}")
```

### **Text-to-Speech Endpoint**
```python
from fastapi import FastAPI
from fastapi.responses import StreamingResponse
import pyttsx3
import io

@app.post("/api/text-to-speech")
async def text_to_speech(request: dict):
    """Convert text to speech audio."""
    try:
        text = request.get("text", "")
        
        # Initialize TTS engine
        engine = pyttsx3.init()
        
        # Configure voice settings
        voices = engine.getProperty('voices')
        engine.setProperty('voice', voices[0].id)  # Use first available voice
        engine.setProperty('rate', 150)  # Speech rate
        engine.setProperty('volume', 0.8)  # Volume level
        
        # Generate audio
        audio_buffer = io.BytesIO()
        engine.save_to_file(text, audio_buffer)
        engine.runAndWait()
        
        audio_buffer.seek(0)
        
        return StreamingResponse(
            io.BytesIO(audio_buffer.read()),
            media_type="audio/wav",
            headers={"Content-Disposition": "attachment; filename=speech.wav"}
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"TTS error: {e}")
```

---

## 📱 **Mobile Voice Interface**

### **React Native Implementation**
```javascript
import React, { useState, useRef } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Audio } from 'expo-av';
import Voice from '@react-native-voice/voice';

const VoiceInterface = () => {
    const [isRecording, setIsRecording] = useState(false);
    const [isProcessing, setIsProcessing] = useState(false);
    const [conversation, setConversation] = useState([
        { sender: 'Deeplica', message: 'Hello! How can I help you today?', type: 'ai' }
    ]);

    const startRecording = async () => {
        try {
            setIsRecording(true);
            await Voice.start('en-US');
        } catch (error) {
            console.error('Error starting recording:', error);
        }
    };

    const stopRecording = async () => {
        try {
            setIsRecording(false);
            setIsProcessing(true);
            await Voice.stop();
        } catch (error) {
            console.error('Error stopping recording:', error);
        }
    };

    const onSpeechResults = async (event) => {
        const transcript = event.value[0];
        
        // Add user message to conversation
        setConversation(prev => [...prev, {
            sender: 'You',
            message: transcript,
            type: 'user'
        }]);

        // Send to dialogue service
        try {
            const response = await fetch('http://localhost:8002/chat', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    message: transcript,
                    conversation_id: 'mobile-conversation',
                    user_id: 'mobile-user'
                })
            });

            const result = await response.json();
            
            // Add AI response to conversation
            setConversation(prev => [...prev, {
                sender: 'Deeplica',
                message: result.response,
                type: 'ai'
            }]);

            // Speak the response
            await speakText(result.response);

        } catch (error) {
            console.error('Error communicating with dialogue service:', error);
        }

        setIsProcessing(false);
    };

    const speakText = async (text) => {
        try {
            await Audio.setAudioModeAsync({
                allowsRecordingIOS: false,
                staysActiveInBackground: false,
                playsInSilentModeIOS: true,
                shouldDuckAndroid: true,
                playThroughEarpieceAndroid: false,
            });

            const { sound } = await Audio.Sound.createAsync(
                { uri: `http://localhost:8000/api/text-to-speech?text=${encodeURIComponent(text)}` }
            );

            await sound.playAsync();
        } catch (error) {
            console.error('Error playing speech:', error);
        }
    };

    return (
        <View style={styles.container}>
            <Text style={styles.title}>🎤 Deeplica Voice</Text>
            
            <TouchableOpacity
                style={[
                    styles.pttButton,
                    isRecording && styles.recording,
                    isProcessing && styles.processing
                ]}
                onPressIn={startRecording}
                onPressOut={stopRecording}
                disabled={isProcessing}
            >
                <Text style={styles.buttonText}>
                    {isRecording ? 'Recording...' : 
                     isProcessing ? 'Processing...' : 
                     'Hold to Talk'}
                </Text>
            </TouchableOpacity>

            <View style={styles.conversation}>
                {conversation.map((msg, index) => (
                    <View key={index} style={[
                        styles.message,
                        msg.type === 'user' ? styles.userMessage : styles.aiMessage
                    ]}>
                        <Text style={styles.sender}>{msg.sender}:</Text>
                        <Text style={styles.messageText}>{msg.message}</Text>
                    </View>
                ))}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 20,
        backgroundColor: '#f5f5f5',
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 30,
    },
    pttButton: {
        width: 200,
        height: 200,
        borderRadius: 100,
        backgroundColor: '#4CAF50',
        justifyContent: 'center',
        alignItems: 'center',
        alignSelf: 'center',
        marginBottom: 30,
    },
    recording: {
        backgroundColor: '#f44336',
    },
    processing: {
        backgroundColor: '#ff9800',
    },
    buttonText: {
        color: 'white',
        fontSize: 18,
        fontWeight: 'bold',
    },
    conversation: {
        flex: 1,
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 15,
    },
    message: {
        marginBottom: 15,
        padding: 10,
        borderRadius: 8,
    },
    userMessage: {
        backgroundColor: '#e3f2fd',
        alignSelf: 'flex-end',
        maxWidth: '80%',
    },
    aiMessage: {
        backgroundColor: '#f1f8e9',
        alignSelf: 'flex-start',
        maxWidth: '80%',
    },
    sender: {
        fontWeight: 'bold',
        marginBottom: 5,
    },
    messageText: {
        fontSize: 16,
    },
});

export default VoiceInterface;
```

---

## 🔗 **Integration with Dialogue Service**

### **Enhanced Dialogue Service for Voice**
```python
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Optional, Dict, Any
import asyncio

app = FastAPI()

class VoiceRequest(BaseModel):
    message: str
    conversation_id: str
    user_id: str
    voice_metadata: Optional[Dict[str, Any]] = None

class VoiceResponse(BaseModel):
    response: str
    conversation_id: str
    intent: Optional[str] = None
    entities: Optional[Dict[str, Any]] = None
    voice_settings: Optional[Dict[str, Any]] = None

@app.post("/chat/voice", response_model=VoiceResponse)
async def process_voice_message(request: VoiceRequest):
    """Process voice message with enhanced context."""
    try:
        # Process the message
        response_text = await process_dialogue(request.message, request.conversation_id)
        
        # Determine voice settings based on response type
        voice_settings = {
            "rate": 0.9,
            "pitch": 1.0,
            "volume": 0.8,
            "voice_id": "en-US-Standard-A"
        }
        
        # Adjust settings based on content
        if "?" in response_text:
            voice_settings["pitch"] = 1.1  # Slightly higher for questions
        
        return VoiceResponse(
            response=response_text,
            conversation_id=request.conversation_id,
            voice_settings=voice_settings
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

async def process_dialogue(message: str, conversation_id: str) -> str:
    """Process dialogue message and return response."""
    # Your existing dialogue processing logic
    return "I understand your request. How can I help you further?"
```

---

## 🎛️ **Advanced Voice Features**

### **Voice Activity Detection (VAD)**
```javascript
class VoiceActivityDetector {
    constructor(audioContext, threshold = 0.01) {
        this.audioContext = audioContext;
        this.threshold = threshold;
        this.isActive = false;
        this.silenceTimeout = null;
    }
    
    async startDetection(stream) {
        const source = this.audioContext.createMediaStreamSource(stream);
        const analyser = this.audioContext.createAnalyser();
        const dataArray = new Uint8Array(analyser.frequencyBinCount);
        
        source.connect(analyser);
        
        const checkAudioLevel = () => {
            analyser.getByteFrequencyData(dataArray);
            
            const average = dataArray.reduce((a, b) => a + b) / dataArray.length;
            const normalized = average / 255;
            
            if (normalized > this.threshold) {
                if (!this.isActive) {
                    this.isActive = true;
                    this.onVoiceStart();
                }
                
                clearTimeout(this.silenceTimeout);
                this.silenceTimeout = setTimeout(() => {
                    this.isActive = false;
                    this.onVoiceEnd();
                }, 1500); // 1.5 seconds of silence
            }
            
            requestAnimationFrame(checkAudioLevel);
        };
        
        checkAudioLevel();
    }
    
    onVoiceStart() {
        console.log('Voice activity detected');
    }
    
    onVoiceEnd() {
        console.log('Voice activity ended');
    }
}
```

### **Real-time Streaming**
```javascript
class StreamingVoiceInterface {
    constructor() {
        this.websocket = null;
        this.mediaRecorder = null;
        this.isStreaming = false;
    }
    
    async startStreaming() {
        // Connect to WebSocket
        this.websocket = new WebSocket('ws://localhost:8002/voice/stream');
        
        this.websocket.onmessage = (event) => {
            const data = JSON.parse(event.data);
            if (data.type === 'transcript') {
                this.displayTranscript(data.text);
            } else if (data.type === 'response') {
                this.displayResponse(data.text);
                this.speakResponse(data.text);
            }
        };
        
        // Start audio streaming
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        this.mediaRecorder = new MediaRecorder(stream, {
            mimeType: 'audio/webm;codecs=opus'
        });
        
        this.mediaRecorder.ondataavailable = (event) => {
            if (this.websocket.readyState === WebSocket.OPEN) {
                this.websocket.send(event.data);
            }
        };
        
        this.mediaRecorder.start(100); // Send data every 100ms
        this.isStreaming = true;
    }
    
    stopStreaming() {
        if (this.mediaRecorder) {
            this.mediaRecorder.stop();
        }
        if (this.websocket) {
            this.websocket.close();
        }
        this.isStreaming = false;
    }
}
```

---

## 📊 **Performance Optimization**

### **Audio Compression**
```javascript
// Compress audio before sending
function compressAudio(audioBlob) {
    return new Promise((resolve) => {
        const audio = new Audio(URL.createObjectURL(audioBlob));
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        // Reduce sample rate and bit depth
        const compressedBlob = new Blob([audioBlob], {
            type: 'audio/webm;codecs=opus'
        });
        
        resolve(compressedBlob);
    });
}
```

### **Caching Strategies**
```javascript
class VoiceCache {
    constructor() {
        this.audioCache = new Map();
        this.transcriptCache = new Map();
    }
    
    cacheAudio(text, audioBlob) {
        const key = this.hashText(text);
        this.audioCache.set(key, audioBlob);
    }
    
    getCachedAudio(text) {
        const key = this.hashText(text);
        return this.audioCache.get(key);
    }
    
    hashText(text) {
        // Simple hash function
        let hash = 0;
        for (let i = 0; i < text.length; i++) {
            const char = text.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash;
    }
}
```

---

**This comprehensive voice integration guide provides multiple implementation options for connecting voice capabilities to the Deeplica V3 dialogue service, from simple PTT interfaces to advanced real-time streaming solutions.**
