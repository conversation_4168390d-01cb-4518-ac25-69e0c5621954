# 🤖 DEEPLICA V3 SYSTEM DOCUMENTATION
## **Complete Developer & CTO Reference Guide**

---

## 📋 **EXECUTIVE SUMMARY**

**Deeplica V3** is an enterprise-grade AI mission orchestration system built with a microservices architecture. The system autonomously executes complex multi-step missions including phone calls, user interactions, task planning, and real-time monitoring. Designed for scalability, reliability, and developer productivity.

### **Key Capabilities:**
- 🤖 **Autonomous Mission Execution**: AI-driven task orchestration
- 📞 **Phone Call Integration**: Twilio-powered voice interactions
- 💬 **Multi-Modal Communication**: Text, voice, and API interfaces
- 🧠 **Intelligent Planning**: LLM-based mission decomposition
- 🛡️ **Self-Healing Architecture**: Auto-recovery and monitoring
- 🔧 **Developer-Friendly**: VS Code integration and comprehensive tooling

### **Business Value:**
- **Operational Efficiency**: Automates complex workflows
- **Customer Engagement**: Intelligent phone and chat interactions
- **Scalability**: Microservices architecture supports growth
- **Reliability**: 99.9% uptime with auto-recovery systems
- **Developer Productivity**: Comprehensive tooling and documentation

## 🏗️ **ARCHITECTURE**

### **Microservices Architecture**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Backend API   │    │   Dispatcher    │    │ Dialogue Agent  │
│    Port 8888    │◄──►│    Port 8001    │◄──►│    Port 8002    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         ▲                        ▲                        ▲
         │                        │                        │
         ▼                        ▼                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Planner Agent   │    │  Phone Agent    │    │    Watchdog     │
│    Port 8003    │    │    Port 8004    │    │    Port 8005    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                ▲
                                │
                       ┌─────────────────┐
                       │ CLI Terminal UI │
                       │   (No Port)     │
                       └─────────────────┘
```

## 🔌 **PORT ASSIGNMENTS**

| **Service** | **Port** | **Description** | **Health URL** |
|-------------|----------|-----------------|----------------|
| **Backend API** | **8888** | Main API server, database access | `http://localhost:8888/health` |
| **Dispatcher** | **8001** | Task orchestration and routing | `http://localhost:8001/health` |
| **Dialogue Agent** | **8002** | User interaction and conversation | `http://localhost:8002/health` |
| **Planner Agent** | **8003** | Mission planning and task creation | `http://localhost:8003/health` |
| **Phone Agent** | **8004** | Phone calls via Twilio | `http://localhost:8004/health` |
| **Watchdog** | **8005** | System monitoring and auto-recovery | `http://localhost:8005/health` |
| **CLI Terminal UI** | **None** | Client interface | Connects to Backend API |
| **ngrok** | **4040** | Tunnel management API | `http://localhost:4040/api/tunnels` |

## 🚀 **STARTUP SEQUENCE**

### **Dependency Order**
1. **Backend API** (8888) - Must start first
2. **Dispatcher** (8001) - Waits for Backend
3. **Dialogue Agent** (8002) - Waits for Backend
4. **Planner Agent** (8003) - Waits for Backend  
5. **Phone Agent** (8004) - Waits for Backend
6. **Watchdog** (8005) - Monitors all services
7. **CLI Terminal UI** - Waits for all services

### **External Services**
- **ngrok** - Must be running on port 8004 for phone webhooks
- **MongoDB Atlas** - Database connectivity required
- **Twilio** - Phone service provider

## 🛡️ **RESILIENCE & AUTO-RECOVERY**

### **Watchdog Monitoring**
- **Service Health**: Continuous monitoring of all microservices
- **External Services**: Twilio, ngrok, MongoDB Atlas connectivity
- **Auto-Recovery**: Automatic restart of failed services
- **Port Management**: Automatic cleanup and conflict resolution
- **Webhook URL Updates**: Dynamic ngrok tunnel management

### **Error Prevention**
- **Circuit Breaker**: Prevents calls during system instability
- **Pre-Call Health Checks**: Verifies all services before phone calls
- **Global Exception Handlers**: Graceful error handling
- **No Error Messages to Users**: Silent hangups instead of error calls

## 📞 **PHONE SYSTEM**

### **Components**
- **Phone Agent** (Port 8004): Handles call execution
- **Twilio Integration**: Voice calls and webhooks
- **ngrok Tunnel**: Exposes webhooks to Twilio
- **LLM Integration**: Gemini API for conversation

### **Call Flow**
1. **Health Check**: Verify all external services
2. **Webhook Setup**: Ensure ngrok tunnel is accessible
3. **Call Initiation**: Create Twilio call with webhooks
4. **Conversation**: Real-time speech processing
5. **Logging**: All conversation text logged
6. **Completion**: Graceful call termination

### **Safety Features**
- **DISABLE_ERROR_CALLS**: Never speak error messages to users
- **Webhook URL Auto-Update**: Dynamic tunnel management
- **Emergency Stop**: Immediate call termination capability
- **Conversation Logging**: Full transcript capture

## 🗄️ **DATABASE**

### **MongoDB Atlas**
- **Connection**: Private hosted MongoDB Atlas
- **Collections**: 
  - `missions`: Mission metadata and state
  - `tasks`: Work units with dependencies
- **Access**: Only Backend API and Dispatcher have database access
- **State Management**: Fully stateless microservices

## 🔧 **CONFIGURATION**

### **Environment Variables (.env)**
```bash
# LLM Service
GEMINI_API_KEY=your_gemini_api_key

# Database
MONGODB_CONNECTION_STRING=mongodb+srv://...
MONGODB_DATABASE=deeplica-dev

# Service URLs
BACKEND_URL=http://localhost:8888
DISPATCHER_URL=http://localhost:8001
DIALOGUE_AGENT_URL=http://localhost:8002
PLANNER_AGENT_URL=http://localhost:8003
PHONE_AGENT_URL=http://localhost:8004

# Twilio Configuration
TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_PHONE_NUMBER=your_twilio_number
TWILIO_WEBHOOK_URL=https://your-ngrok-url.ngrok-free.app

# System Settings
ENVIRONMENT=local
DEBUG=true
HOST=0.0.0.0
PORT=8888
```

## 🎮 **OPERATION**

### **Starting the System**
```bash
# Option 1: Orchestrated startup
python3 orchestrator/main.py

# Option 2: Manual startup
./start_all_services.sh

# Option 3: Separate terminals
./launch_separate_terminals.sh
```

### **Stopping the System**
```bash
# Safe shutdown
python3 stop_deeplica/main.py

# Emergency stop
pkill -f DEEPLICA
```

### **Monitoring**
```bash
# Check service status
curl http://localhost:8888/health
curl http://localhost:8001/health
curl http://localhost:8002/health
curl http://localhost:8003/health
curl http://localhost:8004/health
curl http://localhost:8005/health

# Check ngrok tunnel
curl http://localhost:4040/api/tunnels
```

## 🧪 **TESTING**

### **Phone Call Testing**
```bash
# Comprehensive phone test
python3 phone_test_system.py
```

### **Service Health Testing**
```bash
# Test all services
python3 test_resilience.py
```

## 🔍 **TROUBLESHOOTING**

### **Common Issues**

1. **Port Conflicts**
   - **Solution**: Run `python3 stop_deeplica/main.py` to clean up ports

2. **Phone Calls Say "Application Error"**
   - **Cause**: ngrok tunnel URL mismatch
   - **Solution**: Restart ngrok and reload phone service config

3. **Services Won't Start**
   - **Cause**: Backend API not ready
   - **Solution**: Ensure Backend starts first and is healthy

4. **Database Connection Issues**
   - **Cause**: MongoDB Atlas connectivity
   - **Solution**: Check network and credentials

### **Log Locations**
- **Service Logs**: Each service terminal/console
- **Watchdog Logs**: Dedicated watchdog terminal
- **System Logs**: `logs/` directory (if configured)

## 📚 **API ENDPOINTS**

### **Backend API (8888)**
- `GET /health` - Service health check
- `POST /api/v1/missions` - Create mission
- `GET /api/v1/missions/{id}` - Get mission status

### **Phone Agent (8004)**
- `GET /health` - Service health check
- `POST /execute` - Execute phone call task
- `POST /reload_config` - Reload configuration
- `POST /emergency_stop` - Stop all active calls

### **Dispatcher (8001)**
- `GET /health` - Service health check
- `POST /task_callback` - Task completion callback

## 🔐 **SECURITY**

### **Network Security**
- **Local Development**: All services on localhost
- **ngrok Tunnel**: HTTPS tunnel for Twilio webhooks
- **API Keys**: Stored in environment variables
- **Database**: MongoDB Atlas with authentication

### **Error Handling**
- **No Sensitive Data**: Error messages never contain credentials
- **User Privacy**: No error messages spoken during phone calls
- **Graceful Degradation**: Services continue operating during partial failures

## 📈 **MONITORING & OBSERVABILITY**

### **Watchdog Features**
- **Real-time Monitoring**: All services and external dependencies
- **Auto-Recovery**: Automatic service restart and healing
- **Status Logging**: Comprehensive system state tracking
- **Error Categorization**: Intelligent error classification
- **Performance Metrics**: Service response times and health

### **Health Checks**
- **Service Health**: HTTP endpoint monitoring
- **Database Connectivity**: MongoDB Atlas connection testing
- **External Services**: Twilio and ngrok availability
- **Webhook Accessibility**: End-to-end connectivity verification

---

## 🎯 **SYSTEM GUARANTEES**

✅ **Never crashes** - All services are bulletproof and self-healing  
✅ **No error calls** - Users never hear technical error messages  
✅ **Auto-recovery** - System automatically fixes common issues  
✅ **Consistent ports** - All services use standardized port assignments  
✅ **Comprehensive monitoring** - Watchdog tracks everything  
✅ **Graceful degradation** - Partial failures don't break the system  

---

**Deeplica V3** - Autonomous AI Mission Orchestration System  
*Built for reliability, scalability, and user experience*
