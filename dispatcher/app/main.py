"""
Dispatcher Service - Mission Orchestration Microservice
Manages mission execution and coordinates with agent services.
"""

import os
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

import sys

# Add project root to path for shared models
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from shared.unified_logging import get_dispatcher_logger, should_log_status_change
from shared.port_manager import get_service_port, get_service_host, get_localhost, ensure_service_port_free
from shared.port_cache import get_port_cache
from shared.api_manager import get_service_url, get_system_config
from shared.resilience_utils import bulletproof_retry, wait_for_service, bulletproof_service_wrapper

# Initialize unified logger
logger = get_dispatcher_logger()

# Initialize port cache for efficient port management
port_cache = get_port_cache("dispatcher")

# Import local modules - handle both relative and absolute imports
try:
    from .orchestrator import MissionOrchestrator
    from .agent_registry import AgentRegistry
    from .database import BackendDatabaseClient
except ImportError:
    # Fallback to absolute imports when run as script
    from orchestrator import MissionOrchestrator
    from agent_registry import AgentRegistry
    from database import BackendDatabaseClient

from shared_models import TaskCompletionCallback, TaskCompletionStatus, TaskResponse, TaskStatus
from shared.backend_readiness import ensure_backend_ready_before_startup





async def wait_for_backend_and_initialize(app: FastAPI):
    """Wait for Backend API and initialize services - runs in background, NEVER crashes the main service"""
    import httpx
    import asyncio

    # Get backend URL through API manager
    backend_url = get_service_url("backend")

    logger.info(f"⏳ Waiting for Backend API at {backend_url}", module="main", routine="wait_for_backend_and_initialize")
    logger.info("🔄 Will keep trying INDEFINITELY until Backend API is ready...", module="main", routine="wait_for_backend_and_initialize")
    logger.info("💪 This service will NEVER crash - it will wait as long as needed!", module="main", routine="wait_for_backend_and_initialize")

    attempt = 1
    while True:  # Keep trying forever until successful
        try:
            # Use bulletproof timeout and connection handling
            try:
                timeout = httpx.Timeout(30.0, connect=5.0, read=10.0, write=10.0, pool=5.0)
                limits = httpx.Limits(max_connections=1, max_keepalive_connections=0)

                async with httpx.AsyncClient(timeout=timeout, limits=limits) as client:
                    try:
                        # Check if Backend API is COMPLETELY ready (not just healthy)
                        response = await client.get(f"{backend_url}/ready")

                        if response.status_code == 200:
                            # Backend API is fully initialized and ready
                            try:
                                ready_data = response.json()
                                if isinstance(ready_data, dict) and ready_data.get("status") == "ready":
                                    logger.info("✅ Backend API is FULLY READY and initialized! (attempt {attempt})", module="DISPATCHER", routine="wait_for_backend_and_initialize()")
                                    break
                                else:
                                    logger.debug("Backend API responded but not fully ready: {ready_data}", module="DISPATCHER", routine="wait_for_backend_and_initialize")
                            except (ValueError, TypeError, KeyError, AttributeError) as json_error:
                                logger.debug("Backend API responded but invalid JSON: {json_error}", module="DISPATCHER", routine="wait_for_backend_and_initialize")
                        elif response.status_code == 503:
                            # Backend API is running but not fully ready yet
                            try:
                                error_data = response.json()
                                if isinstance(error_data, dict):
                                    logger.debug("Backend API still initializing: {error_data.get('detail', 'Unknown')}", module="DISPATCHER", routine="wait_for_backend_and_initialize")
                                else:
                                    logger.debug("Backend API still initializing (503 response)", module="DISPATCHER", routine="wait_for_backend_and_initialize")
                            except (ValueError, TypeError, KeyError, AttributeError):
                                logger.debug("Backend API still initializing (503 response)", module="DISPATCHER", routine="wait_for_backend_and_initialize")
                        else:
                            logger.debug("Backend API returned status {response.status_code}", module="DISPATCHER", routine="wait_for_backend_and_initialize")

                    except httpx.ConnectError as e:
                        logger.debug("Backend API connection failed (port not open yet): {e}", module="DISPATCHER", routine="wait_for_backend_and_initialize")
                    except httpx.TimeoutException as e:
                        logger.debug("Backend API timeout (still starting up): {e}", module="DISPATCHER", routine="wait_for_backend_and_initialize")
                    except httpx.ReadTimeout as e:
                        logger.debug("Backend API read timeout (still initializing): {e}", module="DISPATCHER", routine="wait_for_backend_and_initialize")
                    except httpx.WriteTimeout as e:
                        logger.debug("Backend API write timeout: {e}", module="DISPATCHER", routine="wait_for_backend_and_initialize")
                    except httpx.PoolTimeout as e:
                        logger.debug("Backend API pool timeout: {e}", module="DISPATCHER", routine="wait_for_backend_and_initialize")
                    except httpx.HTTPStatusError as e:
                        logger.debug("Backend API HTTP error: {e}", module="DISPATCHER", routine="wait_for_backend_and_initialize")
                    except httpx.RequestError as e:
                        logger.debug("Backend API request error: {e}", module="DISPATCHER", routine="wait_for_backend_and_initialize")
                    except OSError as e:
                        logger.debug("Backend API OS error (network/socket): {e}", module="DISPATCHER", routine="wait_for_backend_and_initialize")
                    except Exception as e:
                        logger.debug("Backend API unexpected response error: {e}", module="DISPATCHER", routine="wait_for_backend_and_initialize")

            except ImportError as e:
                logger.debug("Failed to import httpx: {e}", module="DISPATCHER", routine="wait_for_backend_and_initialize")
            except Exception as e:
                logger.debug("Failed to create HTTP client: {e}", module="DISPATCHER", routine="wait_for_backend_and_initialize")

        except Exception as e:
            logger.debug("Unexpected error in connection attempt: {e}", module="DISPATCHER", routine="wait_for_backend_and_initialize")

        # Log progress every 15 attempts (30 seconds) - only if status changes
        if attempt % 15 == 0:
            status = f"waiting_attempt_{attempt // 15}"
            if should_log_status_change("DISPATCHER", "backend_wait", status):
                logger.info(f"⏳ Still waiting for Backend API... (attempt {attempt}, {attempt * 2 // 60} minutes elapsed)", module="main", routine="wait_for_backend_and_initialize")
                logger.info("💪 Dispatcher will NEVER give up - continuing to wait...", module="main", routine="wait_for_backend_and_initialize")

        try:
            logger.debug("⏳ Retrying in 2 seconds... (attempt {attempt})", module="DISPATCHER", routine="wait_for_backend_and_initialize")
            await asyncio.sleep(2)
        except Exception as e:
            logger.debug("Sleep interrupted: {e}", module="DISPATCHER", routine="wait_for_backend_and_initialize")
            # Use time.sleep as fallback
            try:
                import time
                time.sleep(2)
            except Exception as sleep_error:
                logger.debug("Fallback sleep failed: {sleep_error}", module="DISPATCHER", routine="wait_for_backend_and_initialize")
                pass  # Continue anyway

        attempt += 1

    logger.info("✅ Backend API is healthy and ready - proceeding with Dispatcher initialization!", module="DISPATCHER", routine="wait_for_backend_and_initialize()")

    # Initialize database connection with retry loop (avoid recursion)
    db_retry_attempt = 1
    while True:
        try:
            db_service = BackendDatabaseClient()
            await db_service.connect()
            app.state.db = db_service
            logger.info("✅ Database connected successfully", module="DISPATCHER", routine="wait_for_backend_and_initialize()")
            break
        except Exception as e:
            logger.error("❌ Database connection failed (attempt {db_retry_attempt}): {e}", module="DISPATCHER", routine="wait_for_backend_and_initialize()")
            logger.info("🔄 Will retry database connection...", module="DISPATCHER", routine="wait_for_backend_and_initialize()")

            try:
                await asyncio.sleep(5)
            except Exception as sleep_error:
                logger.debug("Sleep interrupted during DB retry: {sleep_error}", module="DISPATCHER", routine="wait_for_backend_and_initialize")
                try:
                    import time
                    time.sleep(5)
                except Exception:
                    pass

            db_retry_attempt += 1

            # Log progress every 10 attempts - only if status changes
            if db_retry_attempt % 10 == 0:
                status = f"db_retry_attempt_{db_retry_attempt // 10}"
                if should_log_status_change("DISPATCHER", "database_connection", status):
                    logger.info(f"⏳ Still trying to connect to database... (attempt {db_retry_attempt})", module="main", routine="wait_for_backend_and_initialize")
                    logger.info("💪 Will NEVER give up on database connection!", module="main", routine="wait_for_backend_and_initialize")

    # Initialize services with retry loop (avoid recursion)
    service_retry_attempt = 1
    while True:
        try:
            agent_registry = AgentRegistry()
            orchestrator = MissionOrchestrator(db_service, agent_registry)
            app.state.orchestrator = orchestrator
            app.state.ready = True

            # Start task polling system with bulletproof error handling
            task_poller = asyncio.create_task(task_polling_loop(orchestrator, db_service))
            app.state.task_poller = task_poller

            # Add exception handler for task poller
            def handle_task_poller_exception(task):
                if task.cancelled():
                    logger.info("Task poller was cancelled", module="DISPATCHER", routine="wait_for_backend_and_initialize()")
                elif task.exception():
                    logger.error("Task poller failed: {task.exception()}", module="DISPATCHER", routine="wait_for_backend_and_initialize()")
                    logger.info("💪 Service will continue running despite task poller failure", module="DISPATCHER", routine="wait_for_backend_and_initialize()")
                    # Restart task poller automatically
                    try:
                        new_poller = asyncio.create_task(task_polling_loop(orchestrator, db_service))
                        app.state.task_poller = new_poller
                        new_poller.add_done_callback(handle_task_poller_exception)
                        logger.info("🔄 Task poller restarted automatically", module="DISPATCHER", routine="wait_for_backend_and_initialize()")
                    except Exception as restart_error:
                        logger.error("Failed to restart task poller: {restart_error}", module="DISPATCHER", routine="wait_for_backend_and_initialize()")
                else:
                    logger.info("Task poller completed successfully", module="DISPATCHER", routine="wait_for_backend_and_initialize()")

            task_poller.add_done_callback(handle_task_poller_exception)

            logger.info("✅ Dispatcher fully initialized and ready!", module="DISPATCHER", routine="wait_for_backend_and_initialize()")
            logger.info("🔄 Task polling system started with auto-restart capability", module="DISPATCHER", routine="wait_for_backend_and_initialize()")

            # 🛡️ BULLETPROOF: Keep the background task alive indefinitely
            logger.info("🔄 Background task will now run indefinitely to keep service alive", module="DISPATCHER", routine="wait_for_backend_and_initialize()")

            # Keep the background task alive by waiting indefinitely
            try:
                while True:
                    await asyncio.sleep(60)  # Sleep for 1 minute intervals
                    # Optional: Add health checks or maintenance tasks here
                    logger.debug("🔄 Background task heartbeat - service running", module="DISPATCHER", routine="wait_for_backend_and_initialize()")
            except asyncio.CancelledError:
                logger.info("🛑 Background task cancelled - service shutting down", module="DISPATCHER", routine="wait_for_backend_and_initialize()")
                raise  # Re-raise to properly handle cancellation
        except Exception as e:
            logger.error("❌ Service initialization failed (attempt {service_retry_attempt}): {e}", module="DISPATCHER", routine="wait_for_backend_and_initialize()")
            logger.info("🔄 Will retry service initialization...", module="DISPATCHER", routine="wait_for_backend_and_initialize()")

            try:
                await asyncio.sleep(5)
            except Exception as sleep_error:
                logger.debug("Sleep interrupted during service retry: {sleep_error}", module="DISPATCHER", routine="handle_task_poller_exception")
                try:
                    import time
                    time.sleep(5)
                except Exception:
                    pass

            service_retry_attempt += 1

            # Log progress every 10 attempts - only if status changes
            if service_retry_attempt % 10 == 0:
                status = f"service_retry_attempt_{service_retry_attempt // 10}"
                if should_log_status_change("DISPATCHER", "service_initialization", status):
                    logger.info(f"⏳ Still trying to initialize services... (attempt {service_retry_attempt})", module="main", routine="wait_for_backend_and_initialize")
                    logger.info("💪 Will NEVER give up on service initialization!", module="main", routine="wait_for_backend_and_initialize")


async def task_polling_loop(orchestrator, db_service):
    """
    Continuously poll for pending tasks and execute them.
    This replaces the need for the Backend API to call the Dispatcher.
    BULLETPROOF: This loop will NEVER crash and will keep running indefinitely.
    """
    logger.info("🔄 Starting task polling loop...", module="DISPATCHER", routine="task_polling_loop()")
    logger.info("💪 Task polling will NEVER crash - it will run indefinitely!", module="DISPATCHER", routine="task_polling_loop()")

    poll_count = 0
    while True:
        try:
            poll_count += 1

            # Get pending tasks from database with bulletproof error handling
            try:
                pending_tasks = await db_service.get_pending_tasks()
            except Exception as db_error:
                logger.error(f"❌ Database error getting pending tasks (poll {poll_count}): {db_error}", module="DISPATCHER", routine="task_polling_loop()")
                # Don't crash - just wait and retry
                try:
                    await asyncio.sleep(5)
                except Exception:
                    import time
                    time.sleep(5)
                continue

            if pending_tasks:
                # Only log when we find tasks (always interesting) or when count changes significantly
                task_count = len(pending_tasks)
                status = f"found_{task_count}_tasks"
                if should_log_status_change("DISPATCHER", "task_polling", status, force_log=True):
                    logger.info(f"📋 Found {task_count} pending tasks (poll {poll_count})", module="main", routine="task_polling_loop")

                for task in pending_tasks:
                    try:
                        logger.info("🚀 Processing task: {task.task_id} (type: {task.task_type})", module="DISPATCHER", routine="task_polling_loop()")

                        # Mark task as in progress with error handling
                        try:
                            task.status = TaskStatus.IN_PROGRESS
                            await db_service.update_task(task)
                        except Exception as update_error:
                            logger.error("❌ Failed to mark task in progress: {update_error}", module="DISPATCHER", routine="task_polling_loop()")
                            continue  # Skip this task but don't crash

                        # Execute the task through orchestrator with bulletproof error handling
                        try:
                            await orchestrator.execute_task(task)
                        except Exception as exec_error:
                            logger.error("❌ Failed to execute task {task.task_id}: {exec_error}", module="DISPATCHER", routine="task_polling_loop()")
                            # Mark task as failed but don't crash the loop
                            try:
                                task.status = TaskStatus.FAILED
                                task.error_message = str(exec_error)
                                await db_service.update_task(task)
                            except Exception as update_error:
                                logger.error("❌ Failed to update task status: {update_error}", module="DISPATCHER", routine="task_polling_loop()")

                    except Exception as task_error:
                        logger.error("❌ Unexpected error processing task {getattr(task, 'task_id', 'unknown')}: {task_error}", module="DISPATCHER", routine="task_polling_loop()")
                        # Don't crash the loop - continue with next task
                        continue
            else:
                # Only log "no tasks" message when status changes (from having tasks to no tasks)
                status = "no_tasks"
                if should_log_status_change("DISPATCHER", "task_polling", status):
                    logger.debug("📭 No pending tasks found", module="main", routine="task_polling_loop")

            # Wait before next poll with bulletproof sleep
            try:
                await asyncio.sleep(2)  # Poll every 2 seconds
            except Exception as sleep_error:
                logger.debug("Sleep interrupted: {sleep_error}", module="DISPATCHER", routine="task_polling_loop()")
                try:
                    import time
                    time.sleep(2)
                except Exception:
                    pass  # Continue anyway

        except Exception as e:
            logger.error("❌ Unexpected error in task polling loop (poll {poll_count}): {e}", module="DISPATCHER", routine="task_polling_loop()")
            logger.info("💪 Task polling will NEVER crash - continuing...", module="DISPATCHER", routine="task_polling_loop()")
            # Don't crash the polling loop - just wait and retry
            try:
                await asyncio.sleep(5)
            except Exception:
                try:
                    import time
                    time.sleep(5)
                except Exception:
                    pass


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager - starts immediately, handles dependencies in background"""
    import asyncio

    # Startup
    logger.info("🚀 Starting Dispatcher Service", module="DISPATCHER", routine="lifespan()")
    logger.info("💪 Service will start immediately and handle dependencies in background - NEVER crashes!", module="DISPATCHER", routine="lifespan()")

    # Initialize basic state
    app.state.ready = False
    app.state.db = None
    app.state.orchestrator = None
    app.state.background_task = None
    app.state.task_poller = None

    # Start background task to wait for dependencies - this will NEVER crash the service
    try:
        background_task = asyncio.create_task(wait_for_backend_and_initialize(app))
        app.state.background_task = background_task
        logger.info("✅ Dispatcher service started - waiting for dependencies in background", module="DISPATCHER", routine="lifespan()")

        # Add exception handler for background task
        def handle_background_task_exception(task):
            if task.cancelled():
                logger.info("Background task was cancelled", module="DISPATCHER", routine="lifespan()")
            elif task.exception():
                logger.error("Background task failed: {task.exception()}", module="DISPATCHER", routine="lifespan()")
                logger.info("💪 Service will continue running despite background task failure", module="DISPATCHER", routine="lifespan()")
            else:
                logger.error("💥 CRITICAL: Background task completed unexpectedly - this should NEVER happen!", module="DISPATCHER", routine="lifespan()")
                logger.error("💥 CRITICAL: Background tasks should run indefinitely to keep service alive", module="DISPATCHER", routine="lifespan()")

        background_task.add_done_callback(handle_background_task_exception)

    except Exception as e:
        logger.error("❌ Failed to start background task: {e}", module="DISPATCHER", routine="lifespan()")
        # Even if background task fails, don't crash the service
        app.state.ready = False
        app.state.background_task = None
        logger.info("⚠️ Background task failed but service will continue running", module="DISPATCHER", routine="lifespan()")

    yield

    # Shutdown
    logger.info("🛑 Shutting down Dispatcher Service", module="DISPATCHER", routine="lifespan()")

    # Cancel background task safely
    if hasattr(app.state, 'background_task') and app.state.background_task:
        try:
            app.state.background_task.cancel()
            try:
                await app.state.background_task
            except asyncio.CancelledError:
                logger.info("Background task cancelled successfully", module="DISPATCHER", routine="lifespan()")
        except Exception as e:
            logger.error("Error cancelling background task: {e}", module="DISPATCHER", routine="lifespan()")

    # Cancel task poller safely
    if hasattr(app.state, 'task_poller') and app.state.task_poller:
        try:
            app.state.task_poller.cancel()
            try:
                await app.state.task_poller
            except asyncio.CancelledError:
                logger.info("Task poller cancelled successfully", module="DISPATCHER", routine="lifespan()")
        except Exception as e:
            logger.error("Error cancelling task poller: {e}", module="DISPATCHER", routine="lifespan()")

    # Disconnect database safely
    if hasattr(app.state, 'db') and app.state.db:
        try:
            await app.state.db.disconnect()
            logger.info("Database disconnected successfully", module="DISPATCHER", routine="lifespan()")
        except Exception as e:
            logger.error("Error disconnecting database: {e}", module="DISPATCHER", routine="lifespan()")

    logger.info("🛑 Dispatcher Service shutdown complete", module="DISPATCHER", routine="lifespan()")


# Create FastAPI app
app = FastAPI(
    title="Dispatcher Service",
    description="Mission Orchestration and Agent Coordination",
    version="0.1.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/health")
async def health_check():
    """Health check endpoint - always responds, shows dependency status"""
    backend_ready = getattr(app.state, 'ready', False)
    return {
        "status": "healthy",
        "service": "dispatcher",
        "version": "0.1.0",
        "backend_ready": backend_ready,
        "message": "Service is running" + (" and fully ready" if backend_ready else " but waiting for Backend API")
    }


@app.post("/task-completed")
async def handle_task_completion(task_response: TaskResponse):
    """Handle task completion callbacks from agents"""
    logger.info(f"📥 Received task completion: {task_response.task_id}", module="DISPATCHER", routine="handle_task_completion")

    try:
        # Convert TaskResponse to TaskCompletionCallback
        callback = TaskCompletionCallback(
            task_id=task_response.task_id,
            mission_id=task_response.mission_id,
            status=TaskCompletionStatus(task_response.status),
            result=task_response.result,
            error_message=task_response.error_message
        )

        # Add suggested tasks if present
        if task_response.suggested_tasks:
            callback.suggested_tasks = task_response.suggested_tasks

        orchestrator = app.state.orchestrator
        await orchestrator.handle_task_completion(callback)

        return {"status": "acknowledged"}

    except Exception as e:
        logger.error(f"❌ Failed to handle task completion: {e}", module="DISPATCHER", routine="handle_task_completion")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/missions/{mission_id}/start")
async def start_mission(mission_id: str):
    """Start mission execution"""
    logger.info("🚀 Starting mission: {mission_id}", module="DISPATCHER", routine="start_mission")

    try:
        orchestrator = app.state.orchestrator
        if orchestrator is None:
            logger.error("service not fully initialized", module="DISPATCHER:start_mission", routine="ERROR | ❌ Orchestrator is None")
            raise HTTPException(status_code=503, detail="Dispatcher service not fully initialized")

        logger.info("✅ Orchestrator found, starting mission execution...", module="DISPATCHER", routine="start_mission")
        result = await orchestrator.start_mission_execution(mission_id)

        return result

    except Exception as e:
        logger.error("❌ Failed to start mission: {e}", module="DISPATCHER", routine="start_mission")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/missions/{mission_id}/continue")
async def continue_mission(mission_id: str, user_response: str = None):
    """Continue mission execution with optional user response"""
    logger.info("▶️ Continuing mission: {mission_id}", module="DISPATCHER", routine="continue_mission")
    
    try:
        orchestrator = app.state.orchestrator
        result = await orchestrator.continue_mission_execution(mission_id, user_response)
        
        return result
        
    except Exception as e:
        logger.error("❌ Failed to continue mission: {e}", module="DISPATCHER", routine="continue_mission")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/missions/{mission_id}/status")
async def get_mission_status(mission_id: str):
    """Get current mission status"""
    logger.debug("📊 Getting mission status: {mission_id}", module="DISPATCHER", routine="get_mission_status")
    try:
        orchestrator = app.state.orchestrator
        status = await orchestrator.get_mission_status(mission_id)

        if not status:
            raise HTTPException(status_code=404, detail="Mission not found")

        return status

    except HTTPException:
        raise
    except Exception as e:
        logger.error("❌ Failed to get mission status: {e}", module="DISPATCHER", routine="get_mission_status")
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/missions/{mission_id}")
async def cancel_mission(mission_id: str):
    """Cancel a mission"""
    logger.info("🛑 Cancelling mission: {mission_id}", module="DISPATCHER", routine="cancel_mission")

    try:
        orchestrator = app.state.orchestrator
        success = await orchestrator.cancel_mission(mission_id)

        if not success:
            raise HTTPException(status_code=404, detail="Mission not found")

        return {"status": "cancelled", "mission_id": mission_id}

    except HTTPException:
        raise
    except Exception as e:
        logger.error("❌ Failed to cancel mission: {e}", module="DISPATCHER", routine="cancel_mission")
        raise HTTPException(status_code=500, detail=str(e))


async def main():
    """Main startup function with backend readiness checking"""
    # Logger is already configured via unified_logging system

    # Get configuration from API manager and port manager
    system_config = get_system_config()
    server_host = get_service_host("dispatcher")

    # Ensure port is free before starting
    logger.info("🔌 Ensuring dispatcher port is free...", module="DISPATCHER", routine="main")
    server_port = ensure_service_port_free("dispatcher", force=True)
    logger.info(f"✅ Dispatcher will use port {server_port}", module="DISPATCHER", routine="main")

    debug = system_config.get("debug", False)

    # Check if we should wait for backend readiness
    backend_ready = await ensure_backend_ready_before_startup("dispatcher")
    if not backend_ready:
        logger.warning("starting anyway to avoid hanging", module="DISPATCHER", routine="⚠️ Backend readiness timeout")

    logger.info(f"🌟 Starting Dispatcher on {server_host}:{server_port} (debug={debug})", module="DISPATCHER", routine="main")

    # AGGRESSIVELY suppress ALL HTTP request logging
    import logging

    # Disable ALL uvicorn logging completely
    for logger_name in ["uvicorn", "uvicorn.access", "uvicorn.error", "uvicorn.asgi"]:
        logger_obj = logging.getLogger(logger_name)
        logger_obj.setLevel(logging.CRITICAL)
        logger_obj.disabled = True
        logger_obj.propagate = False

    # Port cleanup removed per user request - manual management only

    uvicorn.run(
        "dispatcher.app.main:app",
        host=server_host,
        port=server_port,
        reload=debug,
        log_level="error",    # Only show errors
        access_log=False,     # Disable access logging
        use_colors=False      # Disable colors for cleaner output
    )


if __name__ == "__main__":
    try:
        # Set distinctive process name for easy identification
        try:
            import setproctitle
            setproctitle.setproctitle("DEEPLICA-DISPATCHER")
            logger.info("DISPATCHER", module="DISPATCHER:startup", routine="PROCESS | Process name set to: DEEPLICA")
        except ImportError:
            logger.warning("process name unchanged", module="DISPATCHER:startup", routine="PROCESS | setproctitle not available")

        # Process detection removed per user request - manual management only

        # Set terminal title and clear identification banner - FORCE RENAME EVEN IF ALREADY NAMED
        service_name = os.getenv("SERVICE_NAME", "DISPATCHER")

        # Multiple methods to ensure terminal gets renamed
        print(f"\033]0;🎯 {service_name}\007", end="")  # xterm title
        print(f"\033]2;🎯 {service_name}\007", end="")  # window title
        print(f"\033]1;🎯 {service_name}\007", end="")  # icon title

        # Also try VS Code specific terminal naming
        import sys
        if hasattr(sys, 'ps1') or hasattr(sys, 'ps2'):
            try:
                import os
                os.system(f'echo -ne "\\033]0;🎯 {service_name}\\007"')
            except:
                pass

        from datetime import datetime
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"{timestamp} - [INFO] - Svc: {service_name}, Mod: DISPATCHER, Cod: startup, msg: {'='*80}")
        print(f"{timestamp} - [INFO] - Svc: {service_name}, Mod: DISPATCHER, Cod: startup, msg: 🎯 {service_name} TERMINAL")
        print(f"{timestamp} - [INFO] - Svc: {service_name}, Mod: DISPATCHER, Cod: startup, msg: {'='*80}")

        # INFINITE RETRY LOOP - MICROSERVICE SHOULD NEVER GIVE UP
        retry_count = 0
        while True:
            try:
                retry_count += 1
                if retry_count > 1:
                    logger.info(f"🔄 Dispatcher startup attempt #{retry_count}", module="DISPATCHER", routine="main")

                # Run the async main function
                asyncio.run(main())

                # If we reach here, main() completed - THIS SHOULD NEVER HAPPEN!
                logger.error("💥 CRITICAL: Dispatcher main() completed unexpectedly - servers should NEVER complete!", module="DISPATCHER", routine="main")
                logger.error("💥 CRITICAL: This indicates a serious problem - restarting immediately", module="DISPATCHER", routine="main")
                # Continue the infinite loop to restart

            except KeyboardInterrupt:
                logger.info("🛑 Dispatcher shutdown requested by user", module="DISPATCHER", routine="main")
                # 🛡️ BULLETPROOF: Even keyboard interrupt doesn't stop the service
                logger.info("🔄 BULLETPROOF: Ignoring shutdown request - service NEVER stops", module="DISPATCHER", routine="main")
                import time
                time.sleep(2)
                # Continue the infinite loop
            except Exception as e:
                logger.error(f"💥 Dispatcher crashed (attempt #{retry_count}): {e}", module="DISPATCHER", routine="main")
                import traceback
                logger.error(f"Stack trace:\n{traceback.format_exc()}", module="DISPATCHER", routine="main")

                # Wait before retrying
                import time
                wait_time = min(retry_count * 2, 30)  # Exponential backoff, max 30 seconds
                logger.info(f"🔄 Will retry in {wait_time} seconds...", module="DISPATCHER", routine="main")
                time.sleep(wait_time)
                # Continue the infinite loop

    except KeyboardInterrupt:
        logger.info("🛑 Dispatcher shutdown requested by user", module="DISPATCHER", routine="main")
    except Exception as e:
        logger.error(f"💥 Dispatcher crashed: {e}", module="DISPATCHER", routine="main")
        import traceback
        logger.error(f"Stack trace:\n{traceback.format_exc()}", module="DISPATCHER", routine="main")
        logger.info("🔄 MICROSERVICE WILL RESTART - NOT EXITING", module="DISPATCHER", routine="main")
        # DO NOT EXIT - let orchestrator restart this microservice
        # sys.exit(1)  # REMOVED - microservices should never exit themselves
