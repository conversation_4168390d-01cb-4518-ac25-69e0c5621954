#!/usr/bin/env python3
"""
Simple backend starter script
"""

import os
import sys
import subprocess
from pathlib import Path

def start_backend():
    """Start the backend API"""
    print("🚀 Starting Backend API...")
    
    # Change to backend directory
    backend_dir = Path(__file__).parent / "backend"
    print(f"📁 Backend directory: {backend_dir}")
    
    # Start the backend
    try:
        process = subprocess.Popen([
            sys.executable, "-m", "app.main"
        ], cwd=str(backend_dir))
        
        print(f"✅ Backend started with PID: {process.pid}")
        return process
        
    except Exception as e:
        print(f"❌ Failed to start backend: {e}")
        return None

if __name__ == "__main__":
    backend_process = start_backend()
    if backend_process:
        try:
            backend_process.wait()
        except KeyboardInterrupt:
            print("🛑 Stopping backend...")
            backend_process.terminate()
