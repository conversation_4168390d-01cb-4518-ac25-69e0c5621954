<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌐 DEEPLICA Chat</title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&family=Orbitron:wght@400;700;900&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Rajdhani', sans-serif;
            background: var(--bg-gradient, linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%));
            min-height: 100vh;
            max-height: 100vh; /* Prevent overflow */
            overflow: hidden; /* Prevent page scrolling */
            color: var(--text-color, white);
            margin: 0;
            padding: 0;
            position: relative;
            transition: all 0.3s ease;
        }
        /* CSS Variables for Theming */
        :root {
            --bg-gradient: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            --text-color: white;
            --header-bg: white;
            --header-text: #2c3e50;
            --chat-bg: white;
            --chat-text: #2c3e50;
            --user-bubble-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --user-bubble-text: white;
            --deeplica-bubble-bg: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
            --deeplica-bubble-text: white;
            --input-bg: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            --input-text: #2c3e50;
            --input-border: #e9ecef;
            --scrollbar-track: #f1f1f1;
            --scrollbar-thumb: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --font-size: 16px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            height: 100vh;
            overflow: hidden; /* Prevent container overflow */
            display: flex;
            flex-direction: column;
        }

        .header {
            background: var(--header-bg);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            flex-shrink: 0; /* Don't shrink header */
            transition: all 0.3s ease;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .title-section {
            text-align: left;
        }

        .title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--header-text);
            margin-bottom: 10px;
            transition: color 0.3s ease;
        }

        .subtitle {
            color: var(--header-text);
            opacity: 0.7;
            font-size: 1.1rem;
            transition: color 0.3s ease;
        }

        /* Date/Time Display */
        .datetime-display {
            margin-top: 10px;
            display: flex;
            gap: 15px;
            align-items: center;
            font-family: 'Orbitron', monospace;
            flex-wrap: wrap;
        }

        .date-part {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            box-shadow: 0 3px 10px rgba(0, 212, 255, 0.3);
        }

        .time-part {
            background: linear-gradient(45deg, #ff6b35, #ff4757);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            box-shadow: 0 3px 10px rgba(255, 107, 53, 0.3);
            animation: timePulse 2s ease-in-out infinite;
        }

        @keyframes timePulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        /* Header Control Icons */
        .header-controls {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .control-icon-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            color: white;
            text-decoration: none;
            font-size: 2.4rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }

        .control-icon-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
            border-color: rgba(255, 255, 255, 0.4);
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        }

        .control-icon-btn.font-small {
            font-size: 1.6rem;
            font-weight: 700;
        }

        .control-icon-btn.font-large {
            font-size: 2.8rem;
            font-weight: 900;
        }

        .control-icon-btn.admin-btn:hover {
            background: linear-gradient(135deg, #e67e22, #f39c12);
            box-shadow: 0 6px 20px rgba(243, 156, 18, 0.4);
        }

        .control-icon-btn.logout-btn {
            color: white;
            text-shadow: 0 0 3px rgba(255, 255, 255, 0.8);
        }

        .control-icon-btn.customize-btn:hover {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            box-shadow: 0 6px 20px rgba(155, 89, 182, 0.4);
        }

        .control-icon-btn.logout-btn:hover {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
            color: white;
            text-shadow: 0 0 5px rgba(255, 255, 255, 1);
        }

        .chat-container {
            background: var(--chat-bg);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            color: var(--chat-text);
            display: flex;
            flex-direction: column;
            height: calc(100vh - 240px); /* Adjust based on header height */
            min-height: 400px;
            flex: 1; /* Take remaining space */
            overflow: hidden; /* Prevent overflow */
            transition: all 0.3s ease;
        }
        .chat-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--chat-text);
            margin-bottom: 20px;
            text-align: center;
            flex-shrink: 0; /* Don't shrink the title */
            transition: color 0.3s ease;
        }

        .messages {
            flex: 1; /* Take up remaining space */
            overflow-y: auto;
            border: 2px solid var(--input-border);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            background: var(--input-bg);
            box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.05);
            min-height: 200px;
            font-size: var(--font-size);
            /* Custom scrollbar styling */
            scrollbar-width: thin;
            scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
            transition: all 0.3s ease;
        }

        /* Custom scrollbar for webkit browsers */
        .messages::-webkit-scrollbar {
            width: 12px;
        }

        .messages::-webkit-scrollbar-track {
            background: var(--scrollbar-track);
            border-radius: 10px;
        }

        .messages::-webkit-scrollbar-thumb {
            background: var(--scrollbar-thumb);
            border-radius: 10px;
            border: 2px solid var(--scrollbar-track);
        }

        .messages::-webkit-scrollbar-thumb:hover {
            opacity: 0.8;
        }

        .message {
            margin: 15px 0;
            padding: 12px 18px;
            border-radius: 15px;
            max-width: 80%;
            word-wrap: break-word;
            word-break: break-word;
            white-space: pre-wrap; /* Preserve line breaks but wrap text */
            overflow-wrap: break-word;
            animation: messageSlideIn 0.3s ease-out;
            line-height: 1.4;
            font-size: var(--font-size);
            transition: all 0.3s ease;
        }

        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            background: var(--user-bubble-bg);
            color: var(--user-bubble-text);
            margin-left: auto;
            text-align: right;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .message.deeplica {
            background: var(--deeplica-bubble-bg);
            color: var(--deeplica-bubble-text);
            margin-right: auto;
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
        }

        .message.system {
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
            color: white;
            text-align: center;
            margin: 10px auto;
            max-width: 60%;
            font-style: italic;
            box-shadow: 0 2px 10px rgba(149, 165, 166, 0.3);
        }

        .input-container {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-shrink: 0; /* Don't shrink the input area */
            margin-top: auto; /* Push to bottom if needed */
        }

        #messageInput {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid var(--input-border);
            border-radius: 25px;
            font-size: var(--font-size);
            font-family: 'Rajdhani', sans-serif;
            background: var(--input-bg);
            color: var(--input-text);
            transition: all 0.3s ease;
            outline: none;
        }

        #messageInput:focus {
            border-color: #667eea;
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.2);
            background: var(--chat-bg);
        }

        .send-button {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .send-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        }

        /* Status indicator in header */
        .datetime-display .status {
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            margin-right: 10px;
        }

        .status.connected {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            box-shadow: 0 3px 10px rgba(46, 204, 113, 0.3);
        }

        .status.connecting {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
            box-shadow: 0 3px 10px rgba(243, 156, 18, 0.3);
            animation: pulse 1.5s ease-in-out infinite;
        }

        .status.disconnected {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            box-shadow: 0 3px 10px rgba(231, 76, 60, 0.3);
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* Customization Modal Styles */
        .customization-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: modalFadeIn 0.3s ease;
        }

        @keyframes modalFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .customization-content {
            background: var(--chat-bg);
            border-radius: 20px;
            width: 95%;
            max-width: 2200px;
            height: 90%;
            max-height: 900px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            border: 2px solid rgba(0, 212, 255, 0.3);
            animation: modalSlideIn 0.3s ease;
            display: flex;
            flex-direction: column;
        }

        @keyframes modalSlideIn {
            from { transform: scale(0.8) translateY(-20px); opacity: 0; }
            to { transform: scale(1) translateY(0); opacity: 1; }
        }

        .customization-header {
            padding: 25px;
            border-bottom: 2px solid rgba(0, 212, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(102, 126, 234, 0.1));
        }

        .customization-header h2 {
            color: var(--chat-text);
            font-size: 1.8rem;
            font-weight: 700;
            margin: 0;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--chat-text);
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: rgba(231, 76, 60, 0.2);
            color: #e74c3c;
            transform: scale(1.1);
        }

        .customization-body {
            padding: 25px;
            flex: 1;
            display: flex;
            gap: 30px;
            overflow: hidden;
        }

        .customization-controls {
            flex: 1;
            overflow-y: auto;
            padding-right: 15px;
        }

        .customization-preview {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: rgba(0, 212, 255, 0.05);
            border: 2px solid rgba(0, 212, 255, 0.2);
            border-radius: 15px;
            padding: 20px;
            overflow: hidden;
        }

        /* Enhanced Button Styles */
        .theme-btn:hover, .preset-btn:hover {
            background: rgba(102, 126, 234, 0.1) !important;
            border-color: rgba(102, 126, 234, 0.5) !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
        }

        .theme-btn.active, .preset-btn.active {
            background: linear-gradient(135deg, #667eea, #764ba2) !important;
            color: white !important;
            border-color: #667eea !important;
        }

        /* Font Size Controls */
        .font-size-controls button:hover {
            background: rgba(102, 126, 234, 0.1) !important;
            border-color: rgba(102, 126, 234, 0.5) !important;
            transform: scale(1.05);
        }

        /* Preset Theme Hover Effects */
        .theme-preset {
            transition: all 0.3s ease;
            cursor: pointer;
            border-radius: 8px;
            padding: 8px;
        }

        .theme-preset:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .theme-preset.active {
            background: rgba(102, 126, 234, 0.2);
            border: 2px solid #667eea;
        }

        .customization-section {
            margin-bottom: 30px;
        }

        .customization-section h3 {
            color: var(--chat-text);
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid rgba(0, 212, 255, 0.2);
        }

        .theme-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .theme-btn {
            padding: 12px 20px;
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 12px;
            background: rgba(0, 212, 255, 0.1);
            color: var(--chat-text);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 120px;
        }

        .theme-btn:hover, .theme-btn.active {
            background: rgba(0, 212, 255, 0.2);
            border-color: #00d4ff;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
        }

        .preset-themes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 15px;
        }

        .theme-preset {
            text-align: center;
            cursor: pointer;
            padding: 15px;
            border: 2px solid rgba(0, 212, 255, 0.2);
            border-radius: 12px;
            transition: all 0.3s ease;
            background: rgba(0, 212, 255, 0.05);
        }

        .theme-preset:hover, .theme-preset.active {
            border-color: #00d4ff;
            background: rgba(0, 212, 255, 0.1);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 212, 255, 0.2);
        }

        .theme-preview {
            display: flex;
            gap: 5px;
            margin-bottom: 10px;
            justify-content: center;
        }

        .preview-user, .preview-deeplica {
            width: 30px;
            height: 20px;
            border-radius: 8px;
        }

        .theme-preset span {
            color: var(--chat-text);
            font-size: 0.9rem;
            font-weight: 600;
        }

        .font-size-controls {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .font-size-controls button {
            padding: 8px 15px;
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            background: rgba(0, 212, 255, 0.1);
            color: var(--chat-text);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .font-size-controls button:hover {
            background: rgba(0, 212, 255, 0.2);
            transform: translateY(-1px);
        }

        #fontSizeSlider {
            flex: 1;
            min-width: 150px;
        }

        #fontSizeDisplay {
            color: var(--chat-text);
            font-weight: 600;
            min-width: 50px;
            text-align: center;
        }

        .color-controls-container {
            display: flex;
            gap: 25px;
            align-items: flex-start;
            height: 85vh;
            max-height: 800px;
            width: 100%;
            max-width: 2000px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .color-controls {
            flex: 1;
            min-width: 700px;
            max-width: 900px;
            height: 100%;
            overflow-y: auto;
            padding-right: 15px;
        }

        .color-controls::-webkit-scrollbar {
            width: 8px;
        }

        .color-controls::-webkit-scrollbar-track {
            background: var(--scrollbar-track);
            border-radius: 4px;
        }

        .color-controls::-webkit-scrollbar-thumb {
            background: var(--scrollbar-thumb);
            border-radius: 4px;
        }

        .color-controls::-webkit-scrollbar-thumb:hover {
            background: var(--scrollbar-thumb-hover, #555);
        }

        .color-controls-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .color-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .color-group label {
            color: var(--chat-text);
            font-weight: 600;
            font-size: 0.9rem;
        }

        .color-group small {
            color: var(--chat-text);
            opacity: 0.7;
            font-size: 0.75rem;
            margin-top: 2px;
        }

        .color-group input[type="color"] {
            width: 100%;
            height: 35px;
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            cursor: pointer;
            background: none;
        }

        .color-preview-side {
            flex: 1;
            min-width: 700px;
            max-width: 1000px;
            padding: 30px;
            background: rgba(0, 212, 255, 0.05);
            border: 2px solid rgba(0, 212, 255, 0.2);
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .color-preview-side h4 {
            color: var(--chat-text);
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 12px;
            text-align: center;
        }

        .preview-chat-container {
            background: var(--chat-bg);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(0, 212, 255, 0.2);
            transition: all 0.3s ease;
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 600px;
            height: 100%;
        }

        .preview-messages {
            display: flex;
            flex-direction: column;
            gap: 20px;
            min-height: 500px;
            margin-bottom: 25px;
            padding: 20px;
            background: var(--input-bg);
            border-radius: 12px;
            flex: 1;
            overflow-y: auto;
            max-height: 70vh;
            transition: all 0.3s ease;
        }

        .preview-input-container {
            display: flex;
            gap: 8px;
            align-items: center;
            padding: 8px;
            background: var(--input-bg);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .preview-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid var(--input-border);
            border-radius: 15px;
            font-size: var(--preview-font-size, 0.8rem);
            font-family: 'Rajdhani', sans-serif;
            background: var(--input-bg);
            color: var(--input-text);
            transition: all 0.3s ease;
        }

        .preview-send-btn {
            width: 30px;
            height: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .preview-message {
            padding: 10px 15px;
            border-radius: 12px;
            max-width: 80%;
            font-size: var(--preview-font-size, 0.9rem);
            line-height: 1.4;
            transition: all 0.3s ease;
            font-family: 'Rajdhani', sans-serif;
            font-weight: 500;
        }

        .user-preview {
            margin-left: auto;
            text-align: right;
        }

        .deeplica-preview {
            margin-right: auto;
        }

        .preview-text {
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        .customization-footer {
            padding: 25px;
            border-top: 2px solid rgba(0, 212, 255, 0.2);
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.05), rgba(102, 126, 234, 0.05));
        }

        .reset-btn, .apply-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .reset-btn {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
            border: 2px solid rgba(231, 76, 60, 0.3);
        }

        .reset-btn:hover {
            background: rgba(231, 76, 60, 0.2);
            transform: translateY(-2px);
        }

        .apply-btn {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            color: white;
            border: 2px solid #00d4ff;
        }

        .apply-btn:hover {
            background: linear-gradient(135deg, #0099cc, #00d4ff);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header {
                padding: 20px;
            }

            .title {
                font-size: 2rem;
            }

            .header-controls {
                gap: 8px;
            }

            .control-icon-btn {
                width: 60px;
                height: 60px;
                font-size: 1.8rem;
            }

            .control-icon-btn.font-small {
                font-size: 1.2rem;
            }

            .control-icon-btn.font-large {
                font-size: 2.2rem;
            }

            .chat-container {
                height: calc(100vh - 150px); /* Adjust for mobile */
                padding: 20px;
            }

            .messages {
                min-height: 150px; /* Smaller minimum on mobile */
            }

            .customization-content {
                width: 95%;
                max-height: 90vh;
            }

            .customization-header, .customization-body, .customization-footer {
                padding: 15px;
            }

            .preset-themes {
                grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
                gap: 10px;
            }

            .font-size-controls {
                flex-direction: column;
                align-items: stretch;
            }

            .color-controls-container {
                flex-direction: column;
            }

            .color-controls {
                grid-template-columns: 1fr;
                min-width: auto;
            }

            .color-preview-side {
                min-width: auto;
                max-width: none;
            }

            .preview-chat-container {
                padding: 8px;
            }

            .preview-messages {
                min-height: 80px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <div class="title-section">
                    <div class="title">🌐 DEEPLICA Chat</div>
                    <div class="subtitle">Real-time AI Communication Interface</div>
                    <div class="datetime-display">
                        <div id="status" class="status connecting">🟡 Connecting to Deeplica...</div>
                        <div class="date-part" id="currentDate">Loading...</div>
                        <div class="time-part" id="currentTime">Loading...</div>
                    </div>
                </div>
                <div class="header-controls">
                    <button class="control-icon-btn font-small" onclick="decreaseFontSize()" title="Decrease font size">
                        A
                    </button>
                    <button class="control-icon-btn font-large" onclick="increaseFontSize()" title="Increase font size">
                        A
                    </button>
                    {% if user.role and user.role.value == "admin" %}
                    <button class="control-icon-btn admin-btn" onclick="goToAdmin()" title="Admin Panel">
                        ⚙️
                    </button>
                    {% endif %}
                    <button class="control-icon-btn customize-btn" onclick="openCustomization()" title="Customize Theme">
                        🎨
                    </button>
                    <button class="control-icon-btn logout-btn" onclick="logoutUser()" title="Log Out">
                        🔓
                    </button>
                </div>
            </div>
        </div>

        <div class="chat-container">
            <div class="chat-title">💬 Chat with Deeplica</div>
            <div id="messages" class="messages"></div>
            <div class="input-container">
                <input type="text" id="messageInput" placeholder="Type your message to Deeplica..." onkeypress="handleKeyPress(event)">
                <button class="send-button" onclick="sendMessage()" title="Send Message">➤</button>
            </div>
        </div>
    </div>

    <!-- Customization Modal -->
    <div id="customizationModal" class="customization-modal" style="display: none;">
        <div class="customization-content">
            <div class="customization-header">
                <h2>🎨 Customize Your Chat</h2>
                <button class="close-btn" onclick="closeCustomization()">✕</button>
            </div>

            <div class="customization-body">
                <!-- Left Half: Controls -->
                <div class="customization-controls">
                    <!-- Header -->
                    <div class="controls-header" style="text-align: center; margin-bottom: 25px; padding: 20px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 12px; border: 1px solid rgba(102, 126, 234, 0.2);">
                        <h2 style="margin: 0; color: var(--chat-text); font-size: 1.6rem; font-weight: 600;">🎨 Chat Customization</h2>
                        <p style="margin: 10px 0 0 0; color: var(--text-muted); font-size: 0.95rem;">Personalize your chat experience with custom colors, fonts, and themes</p>
                    </div>

                    <!-- Theme Mode Selection -->
                    <div class="customization-section" style="margin-bottom: 25px;">
                        <h3 style="color: var(--chat-text); font-size: 1.2rem; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                            🌓 <span>Theme Mode</span>
                        </h3>
                        <div class="theme-buttons" style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                            <button class="theme-btn" data-mode="light" onclick="setThemeMode('light')" style="padding: 12px 16px; border: 2px solid var(--input-border); border-radius: 10px; background: var(--input-bg); color: var(--chat-text); cursor: pointer; transition: all 0.3s ease; font-weight: 500;">
                                ☀️ Light Mode
                            </button>
                            <button class="theme-btn" data-mode="dark" onclick="setThemeMode('dark')" style="padding: 12px 16px; border: 2px solid var(--input-border); border-radius: 10px; background: var(--input-bg); color: var(--chat-text); cursor: pointer; transition: all 0.3s ease; font-weight: 500;">
                                🌙 Dark Mode
                            </button>
                        </div>
                    </div>

                <!-- Preset Themes -->
                <div class="customization-section" style="margin-bottom: 25px;">
                    <h3 style="color: var(--chat-text); font-size: 1.2rem; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                        🎨 <span>Preset Themes</span>
                    </h3>
                    <div class="preset-themes">
                        <div class="theme-preset" data-theme="default" onclick="applyPresetTheme('default')">
                            <div class="theme-preview">
                                <div class="preview-user" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);"></div>
                                <div class="preview-deeplica" style="background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);"></div>
                            </div>
                            <span>Default</span>
                        </div>

                        <div class="theme-preset" data-theme="ocean" onclick="applyPresetTheme('ocean')">
                            <div class="theme-preview">
                                <div class="preview-user" style="background: linear-gradient(135deg, #2980b9 0%, #3498db 100%);"></div>
                                <div class="preview-deeplica" style="background: linear-gradient(135deg, #1abc9c 0%, #16a085 100%);"></div>
                            </div>
                            <span>Ocean</span>
                        </div>

                        <div class="theme-preset" data-theme="sunset" onclick="applyPresetTheme('sunset')">
                            <div class="theme-preview">
                                <div class="preview-user" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);"></div>
                                <div class="preview-deeplica" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);"></div>
                            </div>
                            <span>Sunset</span>
                        </div>

                        <div class="theme-preset" data-theme="forest" onclick="applyPresetTheme('forest')">
                            <div class="theme-preview">
                                <div class="preview-user" style="background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);"></div>
                                <div class="preview-deeplica" style="background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);"></div>
                            </div>
                            <span>Forest</span>
                        </div>

                        <div class="theme-preset" data-theme="royal" onclick="applyPresetTheme('royal')">
                            <div class="theme-preview">
                                <div class="preview-user" style="background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);"></div>
                                <div class="preview-deeplica" style="background: linear-gradient(135deg, #f1c40f 0%, #f39c12 100%);"></div>
                            </div>
                            <span>Royal</span>
                        </div>
                    </div>
                </div>

                <!-- Font Size -->
                <div class="customization-section" style="margin-bottom: 25px;">
                    <h3 style="color: var(--chat-text); font-size: 1.2rem; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                        📝 <span>Font Size</span>
                    </h3>
                    <div class="font-size-controls" style="display: flex; align-items: center; gap: 12px; padding: 15px; background: rgba(102, 126, 234, 0.05); border-radius: 10px; border: 1px solid rgba(102, 126, 234, 0.2);">
                        <button onclick="decreaseFontSize()" style="padding: 8px 12px; border: 1px solid var(--input-border); border-radius: 6px; background: var(--input-bg); color: var(--chat-text); cursor: pointer; font-weight: 600;">A-</button>
                        <span id="fontSizeDisplay" style="min-width: 50px; text-align: center; font-weight: 600; color: var(--chat-text);">16px</span>
                        <button onclick="increaseFontSize()" style="padding: 8px 12px; border: 1px solid var(--input-border); border-radius: 6px; background: var(--input-bg); color: var(--chat-text); cursor: pointer; font-weight: 600;">A+</button>
                        <input type="range" id="fontSizeSlider" min="12" max="24" value="16" onchange="setFontSize(this.value)" style="flex: 1; margin: 0 10px;">
                    </div>
                </div>

                <!-- Custom Colors and Fonts -->
                <div class="customization-section" style="margin-bottom: 25px;">
                    <h3 style="color: var(--chat-text); font-size: 1.2rem; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                        🎨 <span>Advanced Customization</span>
                    </h3>
                    <div class="color-controls-container">
                        <div class="color-controls">
                            <!-- Compact Grid Layout -->
                            <div style="display: grid; gap: 12px;">

                                <!-- Font Controls Section -->
                                <div style="border: 1px solid var(--input-border); border-radius: 6px; padding: 12px; background: rgba(255,255,255,0.05);">
                                    <h4 style="margin: 0 0 10px 0; font-size: 0.9rem; color: var(--input-text);">🔤 Font Settings</h4>

                                    <!-- Font Family Row -->
                                    <div style="display: grid; grid-template-columns: 2fr 1fr 1fr 80px; gap: 8px; margin-bottom: 8px;">
                                        <div>
                                            <label style="font-size: 0.8rem; color: var(--input-text); display: block; margin-bottom: 2px;">Font Family:</label>
                                            <select id="chatFontFamily" onchange="updateCustomFont()" style="width: 100%; padding: 4px; border: 1px solid var(--input-border); border-radius: 4px; background: var(--input-bg); color: var(--input-text); font-size: 0.8rem;">
                                                <option value="'Orbitron', monospace">🌟 Orbitron</option>
                                                <option value="'Rajdhani', sans-serif">🤖 Rajdhani</option>
                                                <option value="'Calibri', sans-serif">📊 Calibri</option>
                                                <option value="'Segoe UI', sans-serif">🖥️ Segoe UI</option>
                                                <option value="'Roboto', sans-serif">🤖 Roboto</option>
                                                <option value="'Arial', sans-serif">📄 Arial</option>
                                                <option value="'Helvetica', sans-serif">🎨 Helvetica</option>
                                                <option value="'Fira Code', monospace">💻 Fira Code</option>
                                                <option value="'Monaco', monospace">🖥️ Monaco</option>
                                                <option value="'Consolas', monospace">⌨️ Consolas</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label style="font-size: 0.8rem; color: var(--input-text); display: block; margin-bottom: 2px;">Size:</label>
                                            <select id="chatFontSize" onchange="updateCustomFont()" style="width: 100%; padding: 4px; border: 1px solid var(--input-border); border-radius: 4px; background: var(--input-bg); color: var(--input-text); font-size: 0.8rem;">
                                                <option value="12px">12px</option>
                                                <option value="14px">14px</option>
                                                <option value="16px" selected>16px</option>
                                                <option value="18px">18px</option>
                                                <option value="20px">20px</option>
                                                <option value="22px">22px</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label style="font-size: 0.8rem; color: var(--input-text); display: block; margin-bottom: 2px;">Weight:</label>
                                            <select id="chatFontWeight" onchange="updateCustomFont()" style="width: 100%; padding: 4px; border: 1px solid var(--input-border); border-radius: 4px; background: var(--input-bg); color: var(--input-text); font-size: 0.8rem;">
                                                <option value="300">Light</option>
                                                <option value="400" selected>Normal</option>
                                                <option value="500">Medium</option>
                                                <option value="600">Semi Bold</option>
                                                <option value="700">Bold</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label style="font-size: 0.8rem; color: var(--input-text); display: block; margin-bottom: 2px;">Bold:</label>
                                            <div style="display: flex; align-items: center; justify-content: center; height: 26px;">
                                                <input type="checkbox" id="chatFontBold" onchange="updateCustomFont()" style="transform: scale(1.2);">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Font Preview -->
                                    <div>
                                        <label style="font-size: 0.8rem; color: var(--input-text); display: block; margin-bottom: 2px;">Preview:</label>
                                        <div id="chatFontPreview" style="padding: 6px; border: 1px solid var(--input-border); border-radius: 4px; background: var(--input-bg); color: var(--input-text); font-family: 'Orbitron', monospace; font-size: 14px; font-weight: 400;">
                                            The quick brown fox jumps over the lazy dog. 1234567890
                                        </div>
                                    </div>
                                </div>

                                <!-- Color Controls Section -->
                                <div style="border: 1px solid var(--input-border); border-radius: 6px; padding: 12px; background: rgba(255,255,255,0.05);">
                                    <h4 style="margin: 0 0 10px 0; font-size: 0.9rem; color: var(--input-text);">🎨 Message Colors</h4>

                                    <!-- Color Grid -->
                                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px;">
                                        <div>
                                            <label style="font-size: 0.8rem; color: var(--input-text); display: block; margin-bottom: 2px;">💬 Your Message BG:</label>
                                            <input type="color" id="userBubbleColor" value="#667eea" onchange="updateCustomColors()" style="width: 100%; height: 30px; border: 1px solid var(--input-border); border-radius: 4px;">
                                        </div>
                                        <div>
                                            <label style="font-size: 0.8rem; color: var(--input-text); display: block; margin-bottom: 2px;">📝 Your Message Text:</label>
                                            <input type="color" id="userTextColor" value="#ffffff" onchange="updateCustomColors()" style="width: 100%; height: 30px; border: 1px solid var(--input-border); border-radius: 4px;">
                                        </div>
                                        <div>
                                            <label style="font-size: 0.8rem; color: var(--input-text); display: block; margin-bottom: 2px;">🤖 Deeplica Message BG:</label>
                                            <input type="color" id="deepplicaBubbleColor" value="#00d4ff" onchange="updateCustomColors()" style="width: 100%; height: 30px; border: 1px solid var(--input-border); border-radius: 4px;">
                                        </div>
                                        <div>
                                            <label style="font-size: 0.8rem; color: var(--input-text); display: block; margin-bottom: 2px;">✨ Deeplica Message Text:</label>
                                            <input type="color" id="deepplicaTextColor" value="#ffffff" onchange="updateCustomColors()" style="width: 100%; height: 30px; border: 1px solid var(--input-border); border-radius: 4px;">
                                        </div>
                                    </div>

                                    <!-- Chat Background -->
                                    <div style="margin-top: 8px;">
                                        <label style="font-size: 0.8rem; color: var(--input-text); display: block; margin-bottom: 2px;">🎨 Chat Area Background:</label>
                                        <input type="color" id="chatBackgroundColor" value="#ffffff" onchange="updateCustomColors()" style="width: 100%; height: 30px; border: 1px solid var(--input-border); border-radius: 4px;">
                                        <small style="font-size: 0.7rem; color: var(--input-text); opacity: 0.7;">Background for messages area and input field</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Half: Live Preview -->
                    <div class="customization-preview">
                        <div class="preview-header" style="display: flex; align-items: center; justify-content: center; margin-bottom: 20px; padding: 15px; background: rgba(0, 212, 255, 0.1); border-radius: 12px; border: 1px solid rgba(0, 212, 255, 0.3);">
                            <h4 style="font-size: 1.5rem; margin: 0; color: var(--chat-text); font-weight: 600;">💬 Live Chat Preview</h4>
                        </div>

                        <div class="preview-chat-container" style="flex: 1; display: flex; flex-direction: column; background: var(--chat-bg); border-radius: 15px; border: 2px solid rgba(0, 212, 255, 0.2); overflow: hidden;">
                            <!-- Chat Header -->
                            <div class="preview-chat-header" style="padding: 15px 20px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; display: flex; align-items: center; gap: 10px;">
                                <div style="width: 12px; height: 12px; background: #4CAF50; border-radius: 50%;"></div>
                                <span style="font-weight: 600;">DEEPLICA AI Assistant</span>
                                <div style="margin-left: auto; font-size: 0.9rem; opacity: 0.9;">Online</div>
                            </div>

                            <!-- Messages Area -->
                            <div class="preview-messages" id="previewMessages" style="flex: 1; overflow-y: auto; padding: 20px; background: var(--input-bg); min-height: 400px;">
                                <div class="preview-message user-preview" id="userPreview" style="margin-bottom: 20px; text-align: right;">
                                    <div class="preview-text" style="display: inline-block; padding: 14px 18px; border-radius: 20px 20px 5px 20px; max-width: 75%; background: var(--user-bubble-bg, #667eea); color: var(--user-bubble-text, #fff); box-shadow: 0 2px 8px rgba(0,0,0,0.1);">Hello Deeplica! How are you today?</div>
                                    <div style="font-size: 0.75rem; color: var(--text-muted); margin-top: 5px;">You • 2:30 PM</div>
                                </div>

                                <div class="preview-message deeplica-preview" id="deepplicaPreview" style="margin-bottom: 20px; text-align: left;">
                                    <div class="preview-text" style="display: inline-block; padding: 14px 18px; border-radius: 20px 20px 20px 5px; max-width: 75%; background: var(--deeplica-bubble-bg, #00d4ff); color: var(--deeplica-bubble-text, #fff); box-shadow: 0 2px 8px rgba(0,0,0,0.1);">Hello! I'm doing great, thank you for asking. How can I assist you today?</div>
                                    <div style="font-size: 0.75rem; color: var(--text-muted); margin-top: 5px;">DEEPLICA • 2:30 PM</div>
                                </div>

                                <div class="preview-message user-preview" style="margin-bottom: 20px; text-align: right;">
                                    <div class="preview-text" style="display: inline-block; padding: 14px 18px; border-radius: 20px 20px 5px 20px; max-width: 75%; background: var(--user-bubble-bg, #667eea); color: var(--user-bubble-text, #fff); box-shadow: 0 2px 8px rgba(0,0,0,0.1);">Can you help me customize the chat interface?</div>
                                    <div style="font-size: 0.75rem; color: var(--text-muted); margin-top: 5px;">You • 2:31 PM</div>
                                </div>

                                <div class="preview-message deeplica-preview" style="margin-bottom: 20px; text-align: left;">
                                    <div class="preview-text" style="display: inline-block; padding: 14px 18px; border-radius: 20px 20px 20px 5px; max-width: 75%; background: var(--deeplica-bubble-bg, #00d4ff); color: var(--deeplica-bubble-text, #fff); box-shadow: 0 2px 8px rgba(0,0,0,0.1);">Absolutely! I'd be happy to help you customize the chat interface. You can adjust fonts, colors, themes, and more using the controls on the left.</div>
                                    <div style="font-size: 0.75rem; color: var(--text-muted); margin-top: 5px;">DEEPLICA • 2:31 PM</div>
                                </div>

                                <div class="preview-message user-preview" style="margin-bottom: 20px; text-align: right;">
                                    <div class="preview-text" style="display: inline-block; padding: 14px 18px; border-radius: 20px 20px 5px 20px; max-width: 75%; background: var(--user-bubble-bg, #667eea); color: var(--user-bubble-text, #fff); box-shadow: 0 2px 8px rgba(0,0,0,0.1);">This preview shows how my customized colors and fonts will look in real-time!</div>
                                    <div style="font-size: 0.75rem; color: var(--text-muted); margin-top: 5px;">You • 2:32 PM</div>
                                </div>

                                <div class="preview-message deeplica-preview" style="margin-bottom: 20px; text-align: left;">
                                    <div class="preview-text" style="display: inline-block; padding: 14px 18px; border-radius: 20px 20px 20px 5px; max-width: 75%; background: var(--deeplica-bubble-bg, #00d4ff); color: var(--deeplica-bubble-text, #fff); box-shadow: 0 2px 8px rgba(0,0,0,0.1);">Exactly! This live preview updates instantly as you make changes. Try adjusting the font family, size, weight, or colors to see the changes in real-time.</div>
                                    <div style="font-size: 0.75rem; color: var(--text-muted); margin-top: 5px;">DEEPLICA • 2:32 PM</div>
                                </div>
                            </div>

                            <!-- Input Area -->
                            <div class="preview-input-container" style="padding: 20px; background: var(--input-bg); border-top: 1px solid rgba(0, 212, 255, 0.2);">
                                <div style="display: flex; gap: 12px; align-items: center;">
                                    <input type="text" class="preview-input" placeholder="Type your message here..." readonly style="flex: 1; padding: 14px 18px; border: 1px solid var(--input-border); border-radius: 25px; background: var(--chat-bg); color: var(--chat-text); font-size: inherit; font-family: inherit;">
                                    <button class="preview-send-btn" style="padding: 14px 18px; background: linear-gradient(135deg, #00d4ff, #0099cc); color: white; border: none; border-radius: 25px; cursor: pointer; font-weight: 600; box-shadow: 0 2px 8px rgba(0, 212, 255, 0.3);">Send ➤</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="customization-footer">
                <button class="reset-btn" onclick="resetToDefaults()">🔄 Reset to Defaults</button>
                <button class="apply-btn" onclick="saveCustomization()">✅ Save & Apply</button>
            </div>
        </div>
    </div>

    <script>
        console.log('🚀 SIMPLE CHAT SCRIPT STARTED');

        // CLIENT-SIDE SESSION CALCULATION
        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
            return null;
        }

        function calculateSessionToken(sessionId) {
            // Client-side token calculation - should match server logic
            // For enhanced security, this could include:
            // 1. Timestamp validation
            // 2. HMAC signature verification
            // 3. Additional entropy mixing

            // For now, return the session_id directly
            // This can be enhanced with crypto.subtle.digest() for hashing
            return sessionId;
        }

        async function enhancedTokenCalculation(sessionId) {
            // Future enhancement: Use Web Crypto API for secure hashing
            // const encoder = new TextEncoder();
            // const data = encoder.encode(sessionId + timestamp + secret);
            // const hashBuffer = await crypto.subtle.digest('SHA-256', data);
            // return Array.from(new Uint8Array(hashBuffer)).map(b => b.toString(16).padStart(2, '0')).join('');
            return sessionId;
        }

        // Get session from cookie and calculate token
        const rawSessionId = getCookie('session_id');
        const sessionId = rawSessionId ? calculateSessionToken(rawSessionId) : '{{ session_id if session_id else "fallback-session" }}';
        const username = '{{ user.username }}';

        console.log('🔑 Raw Session ID from cookie:', rawSessionId);
        console.log('📋 Calculated Session Token:', sessionId);
        console.log('👤 Username:', username);
        
        let ws = null;
        let isConnected = false;
        let reconnectAttempts = 0;
        let maxReconnectAttempts = 10;
        let reconnectDelay = 1000; // Start with 1 second delay
        
        const messagesDiv = document.getElementById('messages');
        const statusDiv = document.getElementById('status');
        const messageInput = document.getElementById('messageInput');
        
        function updateStatus(status, text) {
            statusDiv.className = `status ${status}`;
            statusDiv.textContent = text;
            console.log('📊 Status updated:', status, text);
        }
        
        function addMessage(sender, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            // Create message content with proper text wrapping
            const messageContent = document.createElement('div');
            messageContent.style.whiteSpace = 'pre-wrap'; // Preserve line breaks and wrap text
            messageContent.style.wordWrap = 'break-word';
            messageContent.style.overflowWrap = 'break-word';
            messageContent.textContent = `${sender === 'user' ? 'You' : 'Deeplica'}: ${content}`;

            messageDiv.appendChild(messageContent);
            messagesDiv.appendChild(messageDiv);

            // Smooth scroll to bottom to show latest message
            messagesDiv.scrollTo({
                top: messagesDiv.scrollHeight,
                behavior: 'smooth'
            });

            console.log('💬 Message added:', sender, content);
        }
        
        function connectWebSocket() {
            console.log('🔗 Starting WebSocket connection...');
            
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws?session_id=${sessionId}`;
            
            console.log('🌐 WebSocket URL:', wsUrl);
            updateStatus('connecting', '🟡 Connecting...');
            
            try {
                ws = new WebSocket(wsUrl);
                console.log('✅ WebSocket object created');
                
                ws.onopen = function() {
                    console.log('🎉 WebSocket opened');
                    isConnected = true;
                    reconnectAttempts = 0; // Reset reconnection attempts on successful connection
                    updateStatus('connected', '🟢 Connected');
                };
                
                ws.onmessage = function(event) {
                    console.log('📨 WebSocket message:', event.data);
                    try {
                        const data = JSON.parse(event.data);
                        if (data.type === 'message') {
                            addMessage(data.sender, data.content);
                        }
                    } catch (e) {
                        console.error('❌ Error parsing message:', e);
                    }
                };
                
                ws.onclose = function(event) {
                    console.log('🔴 WebSocket closed:', event.code, event.reason);
                    isConnected = false;
                    updateStatus('disconnected', '🔴 Disconnected');

                    // Attempt to reconnect with delay
                    if (reconnectAttempts < maxReconnectAttempts) {
                        reconnectAttempts++;
                        const delay = Math.min(reconnectDelay * Math.pow(2, reconnectAttempts - 1), 30000); // Max 30 seconds
                        console.log(`🔄 Reconnecting in ${delay}ms (attempt ${reconnectAttempts}/${maxReconnectAttempts})`);
                        updateStatus('connecting', `🟡 Reconnecting in ${Math.ceil(delay/1000)}s...`);

                        setTimeout(() => {
                            connectWebSocket();
                        }, delay);
                    } else {
                        console.log('❌ Max reconnection attempts reached');
                        updateStatus('disconnected', '❌ Connection Failed');
                    }
                };

                ws.onerror = function(error) {
                    console.error('❌ WebSocket error:', error);
                    updateStatus('disconnected', '❌ Connection Error');
                };
                
            } catch (error) {
                console.error('❌ Error creating WebSocket:', error);
                updateStatus('disconnected', '❌ Failed to Connect');
            }
        }
        
        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            console.log('📤 Sending message:', message);

            if (!isConnected) {
                console.log('❌ Not connected');
                alert('Not connected to Deeplica');
                return;
            }

            // Don't add message here - server will echo it back to avoid duplicates

            const data = {
                type: 'message',
                content: message
            };

            try {
                ws.send(JSON.stringify(data));
                messageInput.value = '';
                console.log('✅ Message sent successfully');
            } catch (error) {
                console.error('❌ Error sending message:', error);
            }
        }
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // Font size management for chat and input
        let currentFontSize = 16;

        function increaseFontSize() {
            currentFontSize = Math.min(currentFontSize + 2, 24);
            updateFontSizes();
            console.log('🔤 Font size increased to:', currentFontSize);
        }

        function decreaseFontSize() {
            currentFontSize = Math.max(currentFontSize - 2, 12);
            updateFontSizes();
            console.log('🔤 Font size decreased to:', currentFontSize);
        }

        function updateFontSizes() {
            // Update chat messages
            const messagesContainer = document.getElementById('messages');
            if (messagesContainer) {
                messagesContainer.style.fontSize = currentFontSize + 'px';
            }

            // Update input field
            const messageInput = document.getElementById('messageInput');
            if (messageInput) {
                messageInput.style.fontSize = currentFontSize + 'px';
            }

            // Update all existing messages
            const messages = document.querySelectorAll('.message');
            messages.forEach(message => {
                message.style.fontSize = currentFontSize + 'px';
            });
        }

        // Navigation functions
        function goToAdmin() {
            console.log('🔧 Navigating to admin panel...');
            window.location.href = '/admin';
        }

        function logoutUser() {
            console.log('🔓 Logging out user...');
            // Create a form to POST to logout endpoint
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/logout';
            document.body.appendChild(form);
            form.submit();
        }

        // Date/Time display
        function updateDateTime() {
            const now = new Date();
            const dateOptions = {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                weekday: 'short'
            };
            const timeOptions = {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            };

            const dateElement = document.getElementById('currentDate');
            const timeElement = document.getElementById('currentTime');

            if (dateElement) {
                dateElement.textContent = now.toLocaleDateString('en-US', dateOptions);
            }
            if (timeElement) {
                timeElement.textContent = now.toLocaleTimeString('en-US', timeOptions);
            }
        }

        // Initialize
        console.log('🚀 Initializing DEEPLICA Chat...');

        // Start date/time updates
        updateDateTime();
        setInterval(updateDateTime, 1000);

        // Connect WebSocket
        connectWebSocket();

        console.log('✅ DEEPLICA Chat initialization complete');

        // 🎨 CUSTOMIZATION SYSTEM

        // Predefined theme configurations
        const themePresets = {
            default: {
                name: 'Default',
                userBubbleBg: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                userBubbleText: '#ffffff',
                deepplicaBubbleBg: 'linear-gradient(135deg, #00d4ff 0%, #0099cc 100%)',
                deepplicaBubbleText: '#ffffff'
            },
            ocean: {
                name: 'Ocean',
                userBubbleBg: 'linear-gradient(135deg, #2980b9 0%, #3498db 100%)',
                userBubbleText: '#ffffff',
                deepplicaBubbleBg: 'linear-gradient(135deg, #1abc9c 0%, #16a085 100%)',
                deepplicaBubbleText: '#ffffff'
            },
            sunset: {
                name: 'Sunset',
                userBubbleBg: 'linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)',
                userBubbleText: '#ffffff',
                deepplicaBubbleBg: 'linear-gradient(135deg, #f39c12 0%, #e67e22 100%)',
                deepplicaBubbleText: '#ffffff'
            },
            forest: {
                name: 'Forest',
                userBubbleBg: 'linear-gradient(135deg, #27ae60 0%, #2ecc71 100%)',
                userBubbleText: '#ffffff',
                deepplicaBubbleBg: 'linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%)',
                deepplicaBubbleText: '#ffffff'
            },
            royal: {
                name: 'Royal',
                userBubbleBg: 'linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%)',
                userBubbleText: '#ffffff',
                deepplicaBubbleBg: 'linear-gradient(135deg, #f1c40f 0%, #f39c12 100%)',
                deepplicaBubbleText: '#ffffff'
            },
            neon: {
                name: 'Neon',
                userBubbleBg: 'linear-gradient(135deg, #ff006e 0%, #8338ec 100%)',
                userBubbleText: '#ffffff',
                deepplicaBubbleBg: 'linear-gradient(135deg, #06ffa5 0%, #00d4ff 100%)',
                deepplicaBubbleText: '#000000'
            },
            vintage: {
                name: 'Vintage',
                userBubbleBg: 'linear-gradient(135deg, #d4a574 0%, #b8860b 100%)',
                userBubbleText: '#ffffff',
                deepplicaBubbleBg: 'linear-gradient(135deg, #8b4513 0%, #a0522d 100%)',
                deepplicaBubbleText: '#ffffff'
            },
            ice: {
                name: 'Ice',
                userBubbleBg: 'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)',
                userBubbleText: '#ffffff',
                deepplicaBubbleBg: 'linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%)',
                deepplicaBubbleText: '#ffffff'
            },
            fire: {
                name: 'Fire',
                userBubbleBg: 'linear-gradient(135deg, #fd79a8 0%, #e84393 100%)',
                userBubbleText: '#ffffff',
                deepplicaBubbleBg: 'linear-gradient(135deg, #fdcb6e 0%, #e17055 100%)',
                deepplicaBubbleText: '#ffffff'
            },
            cosmic: {
                name: 'Cosmic',
                userBubbleBg: 'linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%)',
                userBubbleText: '#000000',
                deepplicaBubbleBg: 'linear-gradient(135deg, #ffd93d 0%, #ff6b6b 100%)',
                deepplicaBubbleText: '#000000'
            },
            matrix: {
                name: 'Matrix',
                userBubbleBg: 'linear-gradient(135deg, #00ff41 0%, #00d4aa 100%)',
                userBubbleText: '#000000',
                deepplicaBubbleBg: 'linear-gradient(135deg, #000000 0%, #1a1a1a 100%)',
                deepplicaBubbleText: '#00ff41'
            },
            cyberpunk: {
                name: 'Cyberpunk',
                userBubbleBg: 'linear-gradient(135deg, #ff0080 0%, #ff8c00 100%)',
                userBubbleText: '#ffffff',
                deepplicaBubbleBg: 'linear-gradient(135deg, #00d4ff 0%, #ff00ff 100%)',
                deepplicaBubbleText: '#ffffff'
            },
            retro: {
                name: 'Retro',
                userBubbleBg: 'linear-gradient(135deg, #ff6b9d 0%, #c44569 100%)',
                userBubbleText: '#ffffff',
                deepplicaBubbleBg: 'linear-gradient(135deg, #f8b500 0%, #feca57 100%)',
                deepplicaBubbleText: '#000000'
            },
            aurora: {
                name: 'Aurora',
                userBubbleBg: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                userBubbleText: '#ffffff',
                deepplicaBubbleBg: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                deepplicaBubbleText: '#ffffff'
            },
            midnight: {
                name: 'Midnight',
                userBubbleBg: 'linear-gradient(135deg, #2c3e50 0%, #34495e 100%)',
                userBubbleText: '#ffffff',
                deepplicaBubbleBg: 'linear-gradient(135deg, #4a69bd 0%, #1e3799 100%)',
                deepplicaBubbleText: '#ffffff'
            }
        };

        // Dark and Light mode configurations
        const modeConfigs = {
            light: {
                bgGradient: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%)',
                textColor: '#2c3e50',
                headerBg: '#ffffff',
                headerText: '#2c3e50',
                chatBg: '#ffffff',
                chatText: '#2c3e50',
                inputBg: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
                inputText: '#2c3e50',
                inputBorder: '#e9ecef',
                scrollbarTrack: '#f1f1f1',
                scrollbarThumb: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
            },
            dark: {
                bgGradient: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%)',
                textColor: '#ffffff',
                headerBg: 'rgba(26, 26, 46, 0.9)',
                headerText: '#ffffff',
                chatBg: 'rgba(22, 33, 62, 0.9)',
                chatText: '#ffffff',
                inputBg: 'rgba(26, 26, 46, 0.8)',
                inputText: '#ffffff',
                inputBorder: 'rgba(0, 212, 255, 0.3)',
                scrollbarTrack: 'rgba(255, 255, 255, 0.1)',
                scrollbarThumb: 'linear-gradient(135deg, #00d4ff 0%, #0099cc 100%)'
            }
        };

        // Current customization state
        let currentCustomization = {
            mode: 'light',
            theme: 'default',
            fontSize: 16,
            fontFamily: "'Orbitron', monospace",
            fontWeight: '400',
            fontBold: false,
            chatBackground: '#ffffff',
            userBubbleBg: themePresets.default.userBubbleBg,
            userBubbleText: themePresets.default.userBubbleText,
            deepplicaBubbleBg: themePresets.default.deepplicaBubbleBg,
            deepplicaBubbleText: themePresets.default.deepplicaBubbleText
        };

        // Load saved customization from cookies
        function loadCustomization() {
            try {
                const saved = getCookie('deeplica_customization');
                if (saved) {
                    const parsed = JSON.parse(decodeURIComponent(saved));
                    currentCustomization = { ...currentCustomization, ...parsed };
                    applyCustomization();
                    updateCustomizationUI();
                }
            } catch (error) {
                console.error('Error loading customization:', error);
            }
        }

        // Save customization to cookies
        function saveCustomization() {
            try {
                const customizationString = JSON.stringify(currentCustomization);
                document.cookie = `deeplica_customization=${encodeURIComponent(customizationString)}; path=/; max-age=${365 * 24 * 60 * 60}`;
                console.log('🎨 Customization saved');
                closeCustomization();
            } catch (error) {
                console.error('Error saving customization:', error);
            }
        }

        // Apply current customization to the page
        function applyCustomization() {
            const root = document.documentElement;
            const mode = modeConfigs[currentCustomization.mode];

            // Apply mode-specific styles
            root.style.setProperty('--bg-gradient', mode.bgGradient);
            root.style.setProperty('--text-color', mode.textColor);
            root.style.setProperty('--header-bg', mode.headerBg);
            root.style.setProperty('--header-text', mode.headerText);
            root.style.setProperty('--chat-bg', mode.chatBg);
            root.style.setProperty('--chat-text', mode.chatText);
            root.style.setProperty('--scrollbar-track', mode.scrollbarTrack);
            root.style.setProperty('--scrollbar-thumb', mode.scrollbarThumb);

            // Apply custom chat area background (messages area and input field) or mode default
            const chatAreaBg = currentCustomization.chatBackground || mode.inputBg;
            root.style.setProperty('--input-bg', chatAreaBg);
            root.style.setProperty('--input-text', mode.inputText);
            root.style.setProperty('--input-border', mode.inputBorder);

            // Apply bubble colors
            root.style.setProperty('--user-bubble-bg', currentCustomization.userBubbleBg);
            root.style.setProperty('--user-bubble-text', currentCustomization.userBubbleText);
            root.style.setProperty('--deeplica-bubble-bg', currentCustomization.deepplicaBubbleBg);
            root.style.setProperty('--deeplica-bubble-text', currentCustomization.deepplicaBubbleText);

            // Apply font settings
            const effectiveWeight = currentCustomization.fontBold ? 'bold' : currentCustomization.fontWeight;
            root.style.setProperty('--font-size', currentCustomization.fontSize + 'px');
            root.style.setProperty('--font-family', currentCustomization.fontFamily);
            root.style.setProperty('--font-weight', effectiveWeight);
            root.style.setProperty('--preview-font-size', (currentCustomization.fontSize * 0.9) + 'px');

            // Apply font to body and chat elements
            document.body.style.fontFamily = currentCustomization.fontFamily;
            document.body.style.fontWeight = effectiveWeight;

            console.log('🎨 Customization applied:', currentCustomization);
        }

        // Open customization modal
        function openCustomization() {
            document.getElementById('customizationModal').style.display = 'flex';
            updateCustomizationUI();
        }

        // Close customization modal
        function closeCustomization() {
            document.getElementById('customizationModal').style.display = 'none';
        }

        // Update UI elements in customization modal
        function updateCustomizationUI() {
            // Update theme mode buttons
            document.querySelectorAll('.theme-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.mode === currentCustomization.mode);
            });

            // Update preset theme selection
            document.querySelectorAll('.theme-preset').forEach(preset => {
                preset.classList.toggle('active', preset.dataset.theme === currentCustomization.theme);
            });

            // Update font size display and slider
            document.getElementById('fontSizeDisplay').textContent = currentCustomization.fontSize + 'px';
            document.getElementById('fontSizeSlider').value = currentCustomization.fontSize;

            // Update color pickers (extract colors from gradients and current settings)
            const userBgColor = extractColorFromGradient(currentCustomization.userBubbleBg);
            const deepplicaBgColor = extractColorFromGradient(currentCustomization.deepplicaBubbleBg);

            document.getElementById('chatBackgroundColor').value = currentCustomization.chatBackground || '#ffffff';
            document.getElementById('userBubbleColor').value = userBgColor;
            document.getElementById('userTextColor').value = currentCustomization.userBubbleText;
            document.getElementById('deepplicaBubbleColor').value = deepplicaBgColor;
            document.getElementById('deepplicaTextColor').value = currentCustomization.deepplicaBubbleText;

            // Update font selectors
            document.getElementById('chatFontFamily').value = currentCustomization.fontFamily;
            document.getElementById('chatFontSize').value = currentCustomization.fontSize + 'px';
            document.getElementById('chatFontWeight').value = currentCustomization.fontWeight;
            document.getElementById('chatFontBold').checked = currentCustomization.fontBold;

            // Update font preview
            updateChatFontPreview();

            // Update live preview
            updateLivePreview();
        }

        // Extract first color from gradient for color picker
        function extractColorFromGradient(gradient) {
            const match = gradient.match(/#[0-9a-fA-F]{6}/);
            return match ? match[0] : '#667eea';
        }

        // Set theme mode (light/dark)
        function setThemeMode(mode) {
            currentCustomization.mode = mode;
            applyCustomization();
            updateCustomizationUI();
            updateLivePreview();
        }

        // Apply preset theme
        function applyPresetTheme(themeKey) {
            const theme = themePresets[themeKey];
            if (theme) {
                currentCustomization.theme = themeKey;
                currentCustomization.userBubbleBg = theme.userBubbleBg;
                currentCustomization.userBubbleText = theme.userBubbleText;
                currentCustomization.deepplicaBubbleBg = theme.deepplicaBubbleBg;
                currentCustomization.deepplicaBubbleText = theme.deepplicaBubbleText;
                applyCustomization();
                updateCustomizationUI();
                updateLivePreview();
            }
        }

        // Set font size
        function setFontSize(size) {
            currentCustomization.fontSize = parseInt(size);
            applyCustomization();
            updateCustomizationUI();

            // Also update the existing font size system
            currentFontSize = parseInt(size);
            updateFontSizes();
        }

        // Update live preview bubbles
        function updateLivePreview() {
            const userPreview = document.getElementById('userPreview');
            const deepplicaPreview = document.getElementById('deepplicaPreview');
            const previewMessages = document.getElementById('previewMessages');
            const previewInputContainer = document.querySelector('.preview-input-container');
            const previewInput = document.querySelector('.preview-input');
            const effectiveWeight = currentCustomization.fontBold ? 'bold' : currentCustomization.fontWeight;

            if (userPreview) {
                userPreview.style.background = currentCustomization.userBubbleBg;
                userPreview.style.color = currentCustomization.userBubbleText;
                userPreview.style.fontSize = (currentCustomization.fontSize * 0.9) + 'px';
                userPreview.style.fontFamily = currentCustomization.fontFamily;
                userPreview.style.fontWeight = effectiveWeight;
            }

            if (deepplicaPreview) {
                deepplicaPreview.style.background = currentCustomization.deepplicaBubbleBg;
                deepplicaPreview.style.color = currentCustomization.deepplicaBubbleText;
                deepplicaPreview.style.fontSize = (currentCustomization.fontSize * 0.9) + 'px';
                deepplicaPreview.style.fontFamily = currentCustomization.fontFamily;
                deepplicaPreview.style.fontWeight = effectiveWeight;
            }

            // Apply chat area background to messages area and input container
            const chatAreaBg = currentCustomization.chatBackground || 'var(--input-bg)';
            if (previewMessages) {
                previewMessages.style.background = chatAreaBg;
            }
            if (previewInputContainer) {
                previewInputContainer.style.background = chatAreaBg;
            }
            if (previewInput) {
                previewInput.style.background = chatAreaBg;
                previewInput.style.fontSize = (currentCustomization.fontSize * 0.9) + 'px';
                previewInput.style.fontFamily = currentCustomization.fontFamily;
                previewInput.style.fontWeight = effectiveWeight;
            }
        }

        // Update custom colors
        function updateCustomColors() {
            const chatBgColor = document.getElementById('chatBackgroundColor').value;
            const userBgColor = document.getElementById('userBubbleColor').value;
            const userTextColor = document.getElementById('userTextColor').value;
            const deepplicaBgColor = document.getElementById('deepplicaBubbleColor').value;
            const deepplicaTextColor = document.getElementById('deepplicaTextColor').value;

            currentCustomization.chatBackground = chatBgColor;
            currentCustomization.userBubbleBg = `linear-gradient(135deg, ${userBgColor}, ${adjustBrightness(userBgColor, -20)})`;
            currentCustomization.userBubbleText = userTextColor;
            currentCustomization.deepplicaBubbleBg = `linear-gradient(135deg, ${deepplicaBgColor}, ${adjustBrightness(deepplicaBgColor, -20)})`;
            currentCustomization.deepplicaBubbleText = deepplicaTextColor;
            currentCustomization.theme = 'custom';

            applyCustomization();
            updateLivePreview();
        }

        // Update custom font
        function updateCustomFont() {
            const fontFamily = document.getElementById('chatFontFamily').value;
            const fontSize = document.getElementById('chatFontSize').value;
            const fontWeight = document.getElementById('chatFontWeight').value;
            const fontBold = document.getElementById('chatFontBold').checked;

            currentCustomization.fontFamily = fontFamily;
            currentCustomization.fontSize = parseInt(fontSize);
            currentCustomization.fontWeight = fontWeight;
            currentCustomization.fontBold = fontBold;

            applyCustomization();
            updateLivePreview();
            updateChatFontPreview();
        }

        function updateChatFontPreview() {
            const preview = document.getElementById('chatFontPreview');
            if (preview) {
                const effectiveWeight = currentCustomization.fontBold ? 'bold' : currentCustomization.fontWeight;
                preview.style.fontFamily = currentCustomization.fontFamily;
                preview.style.fontSize = currentCustomization.fontSize + 'px';
                preview.style.fontWeight = effectiveWeight;
            }
        }

        // Adjust color brightness for gradient effect
        function adjustBrightness(hex, percent) {
            const num = parseInt(hex.replace("#", ""), 16);
            const amt = Math.round(2.55 * percent);
            const R = (num >> 16) + amt;
            const G = (num >> 8 & 0x00FF) + amt;
            const B = (num & 0x0000FF) + amt;
            return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
                (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
                (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
        }

        // Reset to default settings
        function resetToDefaults() {
            currentCustomization = {
                mode: 'light',
                theme: 'default',
                fontSize: 16,
                userBubbleBg: themePresets.default.userBubbleBg,
                userBubbleText: themePresets.default.userBubbleText,
                deepplicaBubbleBg: themePresets.default.deepplicaBubbleBg,
                deepplicaBubbleText: themePresets.default.deepplicaBubbleText
            };
            applyCustomization();
            updateCustomizationUI();
        }

        // Enhanced font size functions that work with customization
        function increaseFontSize() {
            const newSize = Math.min(currentCustomization.fontSize + 2, 24);
            setFontSize(newSize);
        }

        function decreaseFontSize() {
            const newSize = Math.max(currentCustomization.fontSize - 2, 12);
            setFontSize(newSize);
        }

        // Close modal when clicking outside
        document.addEventListener('click', function(e) {
            const modal = document.getElementById('customizationModal');
            if (e.target === modal) {
                closeCustomization();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeCustomization();
            }
        });

        // Initialize customization system
        loadCustomization();
    </script>
</body>
</html>
