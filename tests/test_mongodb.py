#!/usr/bin/env python3
"""
🗄️ MONGODB ATLAS CONNECTION TEST
Tests connection to MongoDB Atlas database
"""

import os
import sys
from datetime import datetime

def test_mongodb_connection():
    """Test MongoDB Atlas connection"""
    print("🗄️ MONGODB ATLAS CONNECTION TEST")
    print("=" * 50)
    print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Try to import pymongo
        try:
            import pymongo
            print("✅ PyMongo library available")
        except ImportError:
            print("❌ PyMongo not installed")
            print("💡 Install with: pip install pymongo")
            return False
        
        # Get MongoDB connection string from environment
        mongodb_uri = os.getenv('MONGODB_URI')
        if not mongodb_uri:
            print("⚠️ MONGODB_URI environment variable not set")
            print("💡 Set your MongoDB Atlas connection string")
            return False
        
        print(f"🔗 Connecting to MongoDB Atlas...")
        
        # Create client
        client = pymongo.MongoClient(mongodb_uri, serverSelectionTimeoutMS=5000)
        
        # Test connection
        client.admin.command('ping')
        print("✅ Successfully connected to MongoDB Atlas")
        
        # List databases
        db_list = client.list_database_names()
        print(f"📋 Available databases: {db_list}")
        
        # Test Deeplica database
        db = client['deeplica']
        collections = db.list_collection_names()
        print(f"📁 Collections in 'deeplica' database: {collections}")
        
        # Test basic operations
        test_collection = db['connection_test']
        test_doc = {
            "test": True,
            "timestamp": datetime.now(),
            "message": "MongoDB Atlas connection successful"
        }
        
        result = test_collection.insert_one(test_doc)
        print(f"✅ Test document inserted with ID: {result.inserted_id}")
        
        # Clean up test document
        test_collection.delete_one({"_id": result.inserted_id})
        print("🧹 Test document cleaned up")
        
        client.close()
        print("🎉 MongoDB Atlas connection test PASSED")
        return True
        
    except Exception as e:
        print(f"❌ MongoDB Atlas connection test FAILED: {e}")
        return False

if __name__ == "__main__":
    try:
        success = test_mongodb_connection()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Test cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n🚨 Unexpected error: {e}")
        sys.exit(1)
