#!/usr/bin/env python3
"""
Final Verification Script - Comprehensive check of all fixes and improvements.
This script verifies that all syntax errors are eliminated and all improvements are working.
"""

import os
import sys
import ast
import glob
import subprocess
from typing import List, <PERSON><PERSON>

def check_syntax_errors() -> Tuple[int, int]:
    """Check for syntax errors across all Python files"""
    print("🔍 [VERIFY:check_syntax_errors] SYSTEM | Checking syntax errors...")
    
    python_files = []
    for pattern in ["**/*.py"]:
        python_files.extend(glob.glob(pattern, recursive=True))
    
    python_files = [f for f in python_files if '__pycache__' not in f and f.endswith('.py')]
    
    error_count = 0
    total_count = len(python_files)
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            ast.parse(content)
        except SyntaxError:
            error_count += 1
            print(f"❌ [VERIFY:check_syntax_errors] ERROR | Syntax error in: {file_path}")
        except Exception:
            pass  # Skip files that can't be read
    
    return error_count, total_count

def check_import_capabilities() -> Tuple[int, int]:
    """Check that main modules can be imported"""
    print("🔍 [VERIFY:check_import_capabilities] SYSTEM | Checking import capabilities...")
    
    modules_to_test = [
        ("backend.app.main", "backend"),
        ("dispatcher.app.main", "dispatcher"), 
        ("agents.dialogue.app.main", "agents/dialogue"),
        ("agents.phone.app.main", "agents/phone"),
        ("agents.planner.app.main", "agents/planner"),
        ("cli.terminal_ui", "."),
        ("watchdog.main", "."),
        ("orchestrator.main", "."),
        ("stop_deeplica.main", ".")
    ]
    
    success_count = 0
    total_count = len(modules_to_test)
    
    for module_name, working_dir in modules_to_test:
        try:
            # Change to appropriate directory
            original_cwd = os.getcwd()
            if working_dir != ".":
                os.chdir(working_dir)
            
            # Try to import the module
            result = subprocess.run([
                sys.executable, "-c", f"import {module_name.split('.')[-2] if working_dir != '.' else module_name}; print('SUCCESS')"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and "SUCCESS" in result.stdout:
                print(f"✅ [VERIFY:check_import_capabilities] SUCCESS | {module_name}")
                success_count += 1
            else:
                print(f"❌ [VERIFY:check_import_capabilities] ERROR | {module_name}: {result.stderr.strip()}")
            
            # Restore original directory
            os.chdir(original_cwd)
            
        except Exception as e:
            print(f"❌ [VERIFY:check_import_capabilities] ERROR | {module_name}: {e}")
            os.chdir(original_cwd)
    
    return success_count, total_count

def check_cli_cleanliness():
    """Check that CLI has been cleaned of debug messages"""
    print("🔍 [VERIFY:check_cli_cleanliness] SYSTEM | Checking CLI cleanliness...")
    
    try:
        with open("cli/terminal_ui.py", "r") as f:
            content = f.read()
        
        # Count debug/info comments that should have been removed
        debug_comments = content.count("# [CLI-TERMINAL:")
        
        # Check for watchdog logger integration
        has_watchdog_logger = "watchdog_logger" in content
        
        # Check for clean print statements (user-facing messages)
        clean_prints = content.count('print(f"🤖') + content.count('print(f"✅') + content.count('print(f"📊') + content.count('print("🤖') + content.count('print("✅') + content.count('print("📊') + content.count('print("🧠') + content.count('print("🎉')
        
        print(f"📊 [VERIFY:check_cli_cleanliness] SYSTEM | Debug comments remaining: {debug_comments}")
        print(f"📊 [VERIFY:check_cli_cleanliness] SYSTEM | Watchdog logger integrated: {has_watchdog_logger}")
        print(f"📊 [VERIFY:check_cli_cleanliness] SYSTEM | Clean print statements: {clean_prints}")
        
        if debug_comments == 0 and has_watchdog_logger and clean_prints > 0:
            print("✅ [VERIFY:check_cli_cleanliness] SUCCESS | CLI is clean and properly integrated")
            return True
        else:
            print("❌ [VERIFY:check_cli_cleanliness] ERROR | CLI still needs cleaning")
            return False
            
    except Exception as e:
        print(f"❌ [VERIFY:check_cli_cleanliness] ERROR | Error checking CLI: {e}")
        return False

def check_watchdog_enhancements():
    """Check that watchdog has log receiving capabilities"""
    print("🔍 [VERIFY:check_watchdog_enhancements] SYSTEM | Checking watchdog enhancements...")
    
    try:
        with open("watchdog/main.py", "r") as f:
            content = f.read()
        
        # Check for FastAPI integration
        has_fastapi = "from fastapi import FastAPI" in content
        has_log_endpoint = "@app.post(\"/log\")" in content
        has_log_model = "class LogMessage(BaseModel)" in content
        has_uvicorn = "uvicorn.run" in content
        
        print(f"📊 [VERIFY:check_watchdog_enhancements] SYSTEM | FastAPI integration: {has_fastapi}")
        print(f"📊 [VERIFY:check_watchdog_enhancements] SYSTEM | Log endpoint: {has_log_endpoint}")
        print(f"📊 [VERIFY:check_watchdog_enhancements] SYSTEM | Log model: {has_log_model}")
        print(f"📊 [VERIFY:check_watchdog_enhancements] SYSTEM | Uvicorn server: {has_uvicorn}")
        
        if has_fastapi and has_log_endpoint and has_log_model and has_uvicorn:
            print("✅ [VERIFY:check_watchdog_enhancements] SUCCESS | Watchdog properly enhanced")
            return True
        else:
            print("❌ [VERIFY:check_watchdog_enhancements] ERROR | Watchdog missing enhancements")
            return False
            
    except Exception as e:
        print(f"❌ [VERIFY:check_watchdog_enhancements] ERROR | Error checking watchdog: {e}")
        return False

def main():
    """Main verification function"""
    print("🔍 [VERIFY:main] SYSTEM | Starting final verification...")
    print("=" * 80)
    
    # Check syntax errors
    syntax_errors, total_files = check_syntax_errors()
    print()
    
    # Check import capabilities
    import_success, total_modules = check_import_capabilities()
    print()
    
    # Check CLI cleanliness
    cli_clean = check_cli_cleanliness()
    print()
    
    # Check watchdog enhancements
    watchdog_enhanced = check_watchdog_enhancements()
    print()
    
    # Final summary
    print("=" * 80)
    print("🎉 [VERIFY:main] SYSTEM | Final Verification Complete!")
    print(f"📊 [VERIFY:main] SYSTEM | Syntax Errors: {syntax_errors}/{total_files} files")
    print(f"📊 [VERIFY:main] SYSTEM | Import Success: {import_success}/{total_modules} modules")
    print(f"📊 [VERIFY:main] SYSTEM | CLI Clean: {'✅' if cli_clean else '❌'}")
    print(f"📊 [VERIFY:main] SYSTEM | Watchdog Enhanced: {'✅' if watchdog_enhanced else '❌'}")
    
    if syntax_errors == 0 and import_success == total_modules and cli_clean and watchdog_enhanced:
        print()
        print("🎉 [VERIFY:main] SUCCESS | ALL REQUIREMENTS SUCCESSFULLY IMPLEMENTED!")
        print("✅ [VERIFY:main] SUCCESS | Zero syntax errors")
        print("✅ [VERIFY:main] SUCCESS | All modules import successfully")
        print("✅ [VERIFY:main] SUCCESS | CLI interface is clean and user-focused")
        print("✅ [VERIFY:main] SUCCESS | Watchdog receives and displays all debug/info/error logs")
        print("✅ [VERIFY:main] SUCCESS | System is ready for production use!")
    else:
        print()
        print("⚠️ [VERIFY:main] WARNING | Some issues remain - see details above")

if __name__ == "__main__":
    main()
